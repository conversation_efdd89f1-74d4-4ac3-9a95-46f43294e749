@Library('frontend-pipeline-library')_

podTemplate(inheritFrom: "node-16",
  containers: [
        containerTemplate(name: 'curl', image: 'harbor.nroad.com.cn/library/curl:7.79.1', command: 'cat', ttyEnabled: true, runAsUser: "1000"),
        containerTemplate(name: 'node-cdxgen', image: 'harbor.nroad.com.cn/library/node-cdxgen', command: 'cat', ttyEnabled: true),
    ]) {
    node(POD_LABEL){
        stage('checkout') {
            checkout scm
            env.WORKSPACE = pwd()
            sh(script: "git checkout ${TAG}")

            env.DOCKER_HOST = "tcp://dm0.nroad.com.cn:2375"
            env.REGISTRY = "harbor.nroad.com.cn/horus"
            def projectInfo = getProjectInfo(TAG)
            env.APP_NAME = projectInfo.get('appName')
            env.APP_VERSION = projectInfo.get('appVersion')
            env.RELEASE_NOTE = projectInfo.get('releaseNote')
            env.COMMIT_MESSAGE = projectInfo.get('commitMessage')
            env.CURRENT_ENV = projectInfo.get('currentEnv')
            env.PACKAGE_JSON_VERSION = sh(returnStdout: true,
                script: "cat package.json | grep version | head -1 | awk -F: '{ print \$2 }' | sed 's/[\",]//g' | tr -d '[[:space:]]'")
            if (CURRENT_ENV == 'prod') {
              env.BUILD_MODE = "prod"
            } else {
              env.BUILD_MODE = "pre"
            }
        }

        stage('build-h5') {
            container('node-16') {
                sh "yarn install"
                sh "yarn run build:h5:${env.BUILD_MODE}"
            }
        }

        stage('build-weapp') {
            container('node-16') {
                sh "yarn run build:weapp:${env.BUILD_MODE}"
            }
        }

        stage('upload-weapp') {
          container('node-16') {
            sh "node scripts/wx-upload-code.js --mode=${env.BUILD_MODE} --version=${PACKAGE_JSON_VERSION} --desc=更新了若干功能，修复了一些已知问题"
          }
        }

        stage('docker') {
            container('docker') {
                sh "docker --host ${env.DOCKER_HOST} build \
                           --file ${env.WORKSPACE}/Dockerfile \
                           --tag ${env.REGISTRY}/${env.APP_NAME}:${env.APP_VERSION} \
                            ${env.WORKSPACE}/dist/build/h5"
                sh '''echo azp9oaJ7vfJhZpTsoUH4J64s7rdQUy8e | docker login --username robot\\$ci --password-stdin harbor.nroad.com.cn'''
                sh "docker --host ${env.DOCKER_HOST} push ${env.REGISTRY}/${env.APP_NAME}:${env.APP_VERSION}"
            }
        }

        stage('send') {
          def params = [
            imageUrl: "${env.REGISTRY}/${env.APP_NAME}:${env.APP_VERSION}",
            currentEnv: env.CURRENT_ENV,
            appName: env.APP_NAME,
            commitMessage: env.COMMIT_MESSAGE,
            releaseNote: env.RELEASE_NOTE,
            appVersion: env.APP_VERSION,
          ]
          sendToDingtalk(params)
        }

        stage('upload-sbom') {
          genSBOM(env.APP_NAME, env.APP_VERSION)
        }

    }
}

const fs = require('fs');

// 配置 uni-read-pages : 自动构建路由表
const TransformPages = require('uni-read-pages')
const tfPages = new TransformPages({
    includes:['path','name','meta']
})


function replaceManifest(path, value) {
  const manifestPath = './src/manifest.json'
  let Manifest = fs.readFileSync(manifestPath, { encoding: 'utf-8' })
  const arr = path.split('.')
  const len = arr.length
  const lastItem = arr[len - 1]

  let i = 0
  let ManifestArr = Manifest.split(/\n/)

  for (let index = 0; index < ManifestArr.length; index++) {
    const item = ManifestArr[index]
    if (new RegExp(`"${arr[i]}"`).test(item)) ++i;
    if (i === len) {
      const hasComma = /,/.test(item)
      ManifestArr[index] = item.replace(new RegExp(`"${lastItem}"[\\s\\S]*:[\\s\\S]*`), `"${lastItem}": ${value}${hasComma ? ',' : ''}`)
      break;
    }
  }

  Manifest = ManifestArr.join('\n')
  fs.writeFileSync(manifestPath, Manifest, {
    "flag": "w"
  })
}

// 使用生产环境的小程序，需要替换 manifest.json 中的 appid
replaceManifest('mp-weixin.appid', `"${process.env.VUE_APP_WX_APP_ID}"`)

module.exports = {
	configureWebpack: {
		plugins: [
			new tfPages.webpack.DefinePlugin({
				ROUTES: JSON.stringify(tfPages.routes)
			})
		]
	},
  transpileDependencies: ['mp-html'],
}

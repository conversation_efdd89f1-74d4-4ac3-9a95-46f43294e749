const fs = require('fs')
const path = require('path')
const zhObj = require('./zh.js');

const fileNameList = [
  'pages/index/index',
  'pages/user/user',
  'pages/set/set',
  'pages/good-store/good-store',
  'pages/video-list/video-list',
  'pages/video-detail/video-detail',
  'pages/find/find',
  'pages/find-detail/find-detail',
  'pages/basket/basket',
  'pages/category/category',
  'pages/search-page/search-page',
  'pages/orderList/orderList',
  'pages/order-detail/order-detail',
  'pages/submit-order/submit-order',
  'pages/binding-phone/binding-phone',
  'pages/pay-result/pay-result',
  'pages/search-prod-show/search-prod-show',
  'pages/discount-package-detail/discount-package-detail',
  'pages/prod/prod',
  'pages/login/login',
  'pages/prod-classify/prod-classify',
  'pages/bind-wenet/bind-wenet',
  'packageUser/pages/recent-news/recent-news',
  'pages/news-detail/news-detail',
  'pages/claimer/claimer',
  'pages/prodComm/prodComm',
  'packageShop/pages/applyRefund/applyRefund',
  'packageShop/pages/afterSales/afterSales',
  'packageUser/pages/invoice-detail/invoice-detail',
  'packageUser/history/history',
  'packageShop/pages/DetailsOfRefund/DetailsOfRefund',
  'pages/chooseRefundWay/chooseRefundWay',
  'packageShop/pages/writeReturnLogistics/writeReturnLogistics',
  'pages/sub-category/sub-category',
  'pages/accountLogin/accountLogin',
  'pages/register/register',
  'pages/payWay/payWay',
  'pages/pay-for-friend/pay-for-friend',
  'packageShop/pages/logisticsInfo/logisticsInfo',
  'packageShop/pages/searchShopShow/searchShopShow',
  'pages/selectStore/selectStore',
  'packageShop/pages/stationOrderList/stationOrderList',
  'pages/paySuccess/paySuccess',
  'pages/deliveryCertificate/deliveryCertificate',
  'pages/feature-index/feature-index0',
  'packageUser/pages/termsPage/termsPage',
  'pages/pay-paypal/appWebView',
  'pages/voucherCode/voucherCode',
  'pages/share-coupon/share-coupon',
  'pages/buyVip/buyVip',
  'pages/exchangeDetails/exchangeDetails',
  'pages/memberCenter/memberCenter',
  'pages/convertProdDet/convertProdDet',
  'pages/integralSubmitOrder/integralSubmitOrder',
  'pages/memberIndex/memberIndex',
  'pages/integralDetail/integralDetail',
  'pages/integralIndex/integralIndex',
  'pages/luckyDraw/luckyDraw',
  'pages/applyDist/applyDist',
  'pages/draw-rule/draw-rule',
  'pages/applyDistCon/applyDistCon',
  'pages/dis-center/dis-center',
  'pages/withdrawal/withdrawal',
  'pages/promotionProd/promotionProd',
  'pages/InvitationCards/InvitationCards',
  'pages/my-users/my-users',
  'pages/promotion-order/promotion-order',
  'pages/income-details/income-details',
  'pages/take-notes/take-notes',
  'pages/salesmanLevel/salesmanLevel',
  'pages/shopPage/shopPage',
  'pages/shopProds/shopProds',
  'pages/shopCategory/shopCategory',
  'pages/shopSearch/shopSearch',
  'pages/shopSearchResult/shopSearchResult',
  'pages/feature-index/feature-index0',
  'pages/openAShop/openAShop',
  'pages/activity/activity',
  'pages/specialDiscount/specialDiscount',
  'pages/discountDetail/discountDetail',
  'pages/aBulkList/aBulkList',
  'pages/snapUpList/snapUpList',
  'pages/snapUpDetail/snapUpDetail',
  'pages/couponCenter/couponCenter',
  'pages/confirmOrder/confirmOrder',
  'pages/groupConfirmOrder/groupConfirmOrder',
  'pages/spellGroupDetails/spellGroupDetails',
  'pages/spellMembersDetails/spellMembersDetails',
  'pages/myCoupon/myCoupon',
  'pages/chat/chat',
  'pages/chat/chatIm',
  'pages/delivery-address/delivery-address',
  'pages/editAddress/editAddress',
  'pages/personalInformation/personalInformation',
  'pages/myWallet/myWallet',
  'pages/rechargeBalance/rechargeBalance',
  'pages/improve-info/improve-info',
  'pages/forget-password/forget-password',
  'pages/ics/ics'
]

const dirList = [
  'packageActivities',
  'packageDistribution',
  'packageMemberIntegral',
  'packageShop',
  'packageUser'
]

function getTargetFilePath(fileName) {
  const tryPath = path.resolve(__dirname, `./src/${fileName}.vue`)
  if (fs.existsSync(tryPath)) {
    return tryPath
  } else {
    for (let i = 0; i < dirList.length; i++) {
      const tryPath = path.resolve(__dirname, `./src/${dirList[i]}/${fileName}.vue`)
      if (fs.existsSync(tryPath)) {
        return tryPath
      }
    }
  }
}

function readFile() {
  fileNameList.forEach(fileName => {
    const targetFilePath = getTargetFilePath(fileName)
    if (targetFilePath) {
      const fileContent = fs.readFileSync(targetFilePath, 'utf-8')
      // 匹配 title: this.i18n这一行
      const reg = /title:\s*this\.i18n\.(.+)/;
      const matchRes = fileContent.match(reg)
      // console.log(matchRes);
      if (matchRes) {
        const title = matchRes[1]
        const pureTitle = title.replace(',', '')
        console.log(`文件：${fileName}，title：${title}, 中文：${zhObj[pureTitle] || '未找到'}`)
        fs.appendFileSync('./pagetitle.txt', `${fileName}\t${zhObj[pureTitle] || '未找到'}\n`)
      }
    } else {
      console.log(`文件：${fileName}，未找到`)
    }
  })
}

readFile()
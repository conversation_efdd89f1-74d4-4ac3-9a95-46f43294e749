{"name": "wenetshop-mobile-web", "version": "3.15.4", "private": true, "scripts": {"build:h5:prod": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --mode=production", "build:h5:pre": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --mode=pre", "build:h5:dev": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-build --mode=development", "build:weapp:prod": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin IS_PROD_MP=true vue-cli-service uni-build --mode=production", "build:weapp:pre": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --mode=pre", "build:weapp:dev": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --mode=development", "build:weapp:prod:auto": "./scripts/wx-build.sh production", "build:weapp:pre:auto": "./scripts/wx-build.sh pre", "build:weapp:dev:auto": "./scripts/wx-build.sh development", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve --mode=development", "dev:weapp": "./scripts/wx-open-dev.sh", "dev:weapp:legacy": "yarn run cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --mode=development --watch"}, "dependencies": {"@dcloudio/uni-app": "^2.0.2-3071120230427001", "@dcloudio/uni-app-plus": "^2.0.2-3071120230427001", "@dcloudio/uni-h5": "^2.0.2-3071120230427001", "@dcloudio/uni-i18n": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-360": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-alipay": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-baidu": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-jd": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-kuaishou": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-lark": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-qq": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-toutiao": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-vue": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-weixin": "^2.0.2-3071120230427001", "@dcloudio/uni-mp-xhs": "^2.0.2-3071120230427001", "@dcloudio/uni-quickapp-native": "^2.0.2-3071120230427001", "@dcloudio/uni-quickapp-webview": "^2.0.2-3071120230427001", "@dcloudio/uni-stacktracey": "^2.0.2-3071120230427001", "@dcloudio/uni-stat": "^2.0.2-3071120230427001", "@vue/shared": "^3.0.0", "core-js": "^3.6.5", "dayjs": "^1.11.13", "flyio": "^0.6.2", "miniprogram-ci": "^1.9.8", "mp-html": "^2.4.2", "umtrack-wx": "^2.8.0", "uni-read-pages": "1.0.5", "uni-simple-router": "2.0.7", "vue": "^2.6.11", "vue-i18n": "8.26.7", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^2.0.2-3071120230427001", "@dcloudio/uni-cli-i18n": "^2.0.2-3071120230427001", "@dcloudio/uni-cli-shared": "^2.0.2-3071120230427001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-migration": "^2.0.2-3071120230427001", "@dcloudio/uni-template-compiler": "^2.0.2-3071120230427001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2-3071120230427001", "@dcloudio/vue-cli-plugin-uni": "^2.0.2-3071120230427001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2-3071120230427001", "@dcloudio/webpack-uni-mp-loader": "^2.0.2-3071120230427001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-3071120230427001", "@vue/babel-preset-app": "^5.0.8", "@vue/cli-plugin-babel": "~4.5.13", "@vue/cli-service": "~4.5.13", "babel-loader": "~8.0.0", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "dotenv": "^16.3.1", "jest": "^25.4.0", "mini-types": "*", "minimist": "^1.2.8", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}
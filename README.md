# Wenet商城

## 业务问题

### 特殊页面路径

新人专区: /packageShop/pages/new-customer-zone/new-customer-zone，使用vuex中的currentShop中的店铺信息
免费商品页: /packageShop/pages/free-prods/free-prods，使用vuex中的currentShop中的店铺信息
中转页: /pages/station/station，在这里获取登录信息和店铺信息，根据target跳转到新人专区或免费商品
自动登录页: /pages/auto-login/auto-login

### 页面埋点

在`uma.js`中定义友盟埋点，在页面中使用this.$umaTracker.xxxx() 上报埋点数据

#### 场景介绍
自动登录页的使用：从bas自理跳转会携带`basusername`、`ou`、`uid`，需要使用这些信息自动登录，而自动登录后又可能需要跳转新人专区或免费商品，因此`auto-login`页面支持`redirectTo`参数，`redirectTo`需要url编码。

http://localhost:3366/pages/auto-login/auto-login?basUsername=man&ou=10000&uid=6646551c-bdea-465d-ad06-d1a3fdac76d2&redirectTo=%2Fpages%2Fstation%2Fstation%3Ftarget%3Dfree-prods

http://localhost:3366/pages/auto-login/auto-login?basUsername=man&ou=10000&uid=6646551c-bdea-465d-ad06-d1a3fdac76d2&redirectTo=%2FpackageShop%2Fpages%2Ffree-prods%2Ffree-prods

### 关于购买商品的留言
1. 目前商品分为实物商品和虚拟商品，虚拟商品中有一种特殊的商品：号卡套餐
2. 留言分为默认留言和自定义留言，自定义留言支持多选一填写
2. 一般虚拟商品的留言在商品详情页底部，点击购买时填写；实物商品的留言在订单提交页上方填写；使用了自定义留言的商品(号卡套餐)在订单提交页下方填写留言。

## 技术问题
### CSS单位
在iPhone6设计稿下，1px=2rpx，使用rpx会等比例缩放，因此也会使元素在宽屏幕设备上看起来很大。而用px，则无论宽屏窄屏，都是一样的大小。
遵循在大屏幕上展示更多内容，而不是更大的内容的原则：尽量使用flex做宽屏幕适配，除了需要等比例适配的元素，尽量少用rpx。

### 全局loading
在`main.js`中使用Vue.component 注册了`n-loading`，在vuex的store中增加了请求数量的state: `requestNum`，`n-loading`中读取`requestNum`，大于0时显示loading图片。
使用方法：在需要显示loading的页面写上标签<n-loading />，微信小程序暂时不支持布局和全局创建组件，只能在需要的地方写标签。





import {RouterMount,createRouter,runtimeQuit} from 'uni-simple-router';
import flow from '../utils/flow.js'
import http from '../utils/http.js'
var systemType = ''
// #ifdef H5
systemType = 2
// #endif
// #ifdef MP-WEIXIN
systemType = 3
// #endif
// #ifdef APP-PLUS
systemType = uni.getSystemInfoSync().platform=='android'? 4 : 5
// #endif
uni.setStorageSync('systemType', systemType)

const router = createRouter({
	platform: process.env.VUE_APP_PLATFORM,
	routerErrorEach:(error, router) => {
		if(error.type === 3) {
			router.$lockStatus = false;
			// #ifdef APP-PLUS
				runtimeQuit();
			// #endif
		}
	},
	routes: [
		...ROUTES,
		{
			path: '*',
			redirect: (to) => {
				return { path: '/' }
			}
		}
	]
});
router.beforeEach((to, from, next) => {
		// 初始化log数据
		if (!uni.getStorageSync('initLog')) {
			uni.setStorageSync('initLog','initLog')
			uni.setStorageSync('uuid',flow.getUuid())
			uni.setStorageSync('uuidSession',flow.getUuid())
			uni.setStorageSync('step',0)
			uni.setStorageSync('sessionTimeStamp',new Date().getTime())
			uni.setStorageSync('systemType', systemType)
		}
		// 操作间隔时间大于30分钟则更新 uuidSession(会话uuid),重置 step
		if ((new Date().getTime() - uni.getStorageSync('sessionTimeStamp')) / 1000 / 60 > 30) {
			uni.setStorageSync('uuidSession', flow.getUuid())
			uni.setStorageSync('step', 0)
			uni.removeStorageSync('flowAnalysisLogDto')
		}
		var step = uni.getStorageSync('step') * 1

		// 获取页面信息
		var pageInfo = flow.getPageInfo(to)
		var flowAnalysisLogDto = {
			pageId: pageInfo.pageId,
			bizData: pageInfo.bizData,
			bizType: pageInfo.bizType,
			step: step,
			systemType: uni.getStorageSync('systemType'),
			uuid: uni.getStorageSync('uuid'),
			uuidSession: uni.getStorageSync('uuidSession'),
			visitType: 1,
			userId: uni.getStorageSync('loginResult').userId,
			prevPageId: uni.getStorageSync('flowAnalysisLogDto').pageId || undefined
		}
		uni.setStorageSync('flowAnalysisLogDto', flowAnalysisLogDto)
		if (pageInfo.pageId && to.path != from.path) {
			step += 1
			flowAnalysisLogDto.step = step
			uni.setStorageSync('step', step)
			uni.setStorageSync('flowAnalysisLogDto', flowAnalysisLogDto)
			uni.setStorageSync('sessionTimeStamp',new Date().getTime()) // 更新会话时间
			if (to.path!='/pages/pay-result/pay-result') {
				http.saveLog(flowAnalysisLogDto, 1)
			}
		}
	next();
})
router.afterEach((to, from) => {

})

export {
	router,
	RouterMount
}


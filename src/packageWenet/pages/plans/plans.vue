<template>

  <view class="container">
    <view class="plan-tabs">
      <view class="plan-tabs-item-wrap" v-for="tab in tabs" :key="tab.key" :class="{ active: activeTab === tab.key }" @click="changeTab(tab.key)">
        <view class="plan-tabs-item">{{ tab.label }}</view>
      </view>
    </view>
    <view class="plan-list" v-if="!isFetching">
      <PlanCard v-for="plan in planList" :key="plan.id" :plan="plan" :active-tab="activeTab" @refresh="refreshPlanList" />
    </view>
    <NLoading />
  </view>
</template>

<script>
import { getPlans } from '@/packageWenet/api'
import Tabs from '@/components/tabs/tabs'
import PlanCard from '@/packageWenet/components/plan-card/plan-card.vue'
import NLoading from '@/components/n-loading/n-loading.vue'
export default {
  data() {
    return {
      tabs: [
        { label: '已生效', key: 'IS_USING' },
        { label: '待生效', key: 'PENDING_USE' },
        { label: '已过期', key: 'HAS_USED' },
      ],
      activeTab: 'IS_USING',
      planList: [],
      isFetching: false,
    }
  },
  components: {
    Tabs,
    PlanCard,
    NLoading
  },
  mounted() {

  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo
    },
  },
  methods: {
    async getPlanList(status = 'IS_USING') {
      this.isFetching = true
      const res = await getPlans({status})
      this.planList = res.content || []
      this.isFetching = false
    },
    changeTab(key) {
      this.activeTab = key
      this.getPlanList(key)
    },
    refreshPlanList() {
      this.activeTab = 'IS_USING'
      this.getPlanList('IS_USING')
    }
  },
  onLoad() {
    this.getPlanList()
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: $page-background;
  min-height: 100vh;
}

.plan-tabs {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 0 16rpx;
  position: relative;
}

.plan-tabs-item-wrap {
  flex: 1;
  position: relative;
  font-weight: 400;
  color: $wenet-color-text-primary;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
}

.plan-tabs-item-wrap.active {
  font-weight: 500;
  color: #000;
}

.plan-tabs-item-wrap.active::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 52rpx;
  height: 4rpx;
  background-color: $wenet-color-brand;
  box-shadow: 0 2px 4px 0 wenet-color-brand-opacity(0.5);
}

</style>
<template>
  <view class="online-record">
    <view v-if="hasPermission" class="record-container">
      <!-- 标签页 -->
      <view class="tabs">
        <view v-for="tab in tabList" :key="tab.key" :class="['tab-item', { active: activeKey === tab.key }]"
          @click="handleChangeTabs(tab.key)">
          {{ tab.name }}
        </view>
      </view>

      <!-- 记录列表 -->
      <view v-if="list && list.length > 0" class="record-list">
        <view v-for="(item, index) in list" :key="index" class="record-item">
          <view class="record-header">
            <view class="record-time">
              <text class="record-time-label">上线:</text>
              <text class="record-time-value">{{ formatTime(item.acctStartTime) }}</text>
            </view>
            <view class="record-traffic">
              <text class="record-traffic-value">{{ formatTraffic(item.acctInputOctets, item.acctOutputOctets) }}</text>
            </view>
          </view>
          <view class="record-content">
            <view class="record-row">
              <text class="label">MAC地址:</text>
              <text class="value">{{ item.callingStationId }}</text>
            </view>
            <view class="record-row">
              <text class="label">下线时间:</text>
              <text class="value">
                <text v-if="item.acctStopTime">{{ formatTime(item.acctStopTime) }}</text>
                <text v-else class="online-status">未下线</text>
              </text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more">
          <NButton v-if="hasMore" type="primary" @click="handleLoadMore">
            加载更多
          </NButton>
          <text v-else class="no-more">没有更多了</text>
        </view>
      </view>

      <!-- 无数据 -->
      <view v-else class="no-data">
        <image class="no-data-image" src="/static/images/no-data.png" mode="aspectFit"></image>
        <text class="no-data-text">暂无上网记录</text>
      </view>
    </view>

    <!-- 返回顶部 -->
    <view v-if="showBackTop" class="back-top" @click="scrollToTop">
      <text class="back-top-icon">↑</text>
    </view>
  </view>
</template>

<script>
import { getSessions } from '@/packageWenet/api/index.js';
import NButton from '@/components/n-button';
export default {
  name: 'BrowseHistory',
  components: {
    NButton,
  },
  data() {
    return {
      activeKey: 3,
      customLastDay: 10,
      list: [],
      currentPage: 0,
      hasMore: false,
      showBackTop: false,
      hasPermission: false,
      tabList: [
        { name: '最近三天', key: 3 },
        { name: '最近一周', key: 7 },
        { name: '最近一月', key: 30 }
      ]
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo
    }
  },
  onLoad() {
    this.checkPermission()
    this.getOnlineRecordList(3)
  },
  onPageScroll(e) {
    // 显示返回顶部按钮
    this.showBackTop = e.scrollTop > 200
  },
  methods: {
    // 检查权限
    checkPermission() {
      // console.log('window.school_config', window.school_config)
      try {
        this.hasPermission = true
      } catch (error) {
        console.error('权限检查失败:', error)
        this.hasPermission = false
      }
    },

    // 获取上网记录列表
    getOnlineRecordList(lastDay, page = 0) {
      const params = {
        lastDay,
        page,
        ou: this.userInfo.ou,
        uid: this.userInfo.uid,
      };

      // 显示loading
      uni.showLoading({ title: '加载中...' })

      getSessions(params)
        .then(response => {
          console.log('获取上网记录列表:', response)
          if (response) {
            const data = response.content
            if (page === 0) {
              this.list = data || []
            } else {
              this.list = [...this.list, ...(data || [])]
            }
            console.log('上网记录列表:', this.list, page)
            this.currentPage = page;
            if (page * 5 >= response.page.totalElements) {
              this.hasMore = false
            } else {
              this.hasMore = true
            }
          }
        })
        .catch(error => {
          console.error('获取记录失败:', error)
          uni.showToast({
            title: '获取记录失败',
            icon: 'none'
          })
        })
        .finally(() => {
          uni.hideLoading()
        })
    },

    // 切换标签页
    handleChangeTabs(key) {
      this.activeKey = key
      this.currentPage = 0
      this.getOnlineRecordList(key)

    },

    // 加载更多
    handleLoadMore() {
      if (this.hasMore) {
        this.getOnlineRecordList(this.activeKey, this.currentPage + 1)
      }
    },

    // 返回顶部
    scrollToTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      })
    },

    // 格式化流量
    formatTraffic(inputOctets, outputOctets) {
      if (inputOctets && outputOctets) {
        const totalMB = (inputOctets + outputOctets) / 1024 / 1024
        return `${totalMB.toFixed(2)} MB`
      }
      return '0.00 MB'
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    // 获取标签页名称
    getTabName(key) {
      const tab = this.tabList.find(t => t.key === key)
      return tab ? tab.name : ''
    },
  }
}
</script>

<style lang="scss" scoped>
.online-record {
  padding: 12rpx 0;
  min-height: 100vh;
  background-color: $page-background;
}

.record-container {
  margin: 0 24rpx;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: $wenet-color-text-secondary;
  position: relative;
  transition: all 0.3s;
}

.tab-item.active {
  color: $wenet-color-brand;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: $wenet-color-brand;
  border-radius: 2rpx;
}

/* 记录列表样式 */
.record-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.record-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 24rpx 0;
  margin-bottom: 8rpx;
}

.record-time {
  display: flex;
  align-items: center;
}

.record-time-label {
  font-size: 26rpx;
  color: $wenet-color-text-secondary;
  margin-right: 8rpx;
}

.record-time-value {
  font-size: 26rpx;
  color: $wenet-color-text-primary;
  font-weight: 500;
}

.record-traffic {
  background-color: wenet-color-brand-opacity(0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.record-traffic-value {
  font-size: 24rpx;
  color: $wenet-color-brand;
  font-weight: 500;
}

.record-content {
  padding: 16rpx 24rpx 24rpx;
}

.record-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.record-row:last-child {
  margin-bottom: 0;
}

.label {
  min-width: 160rpx;
  font-size: 26rpx;
  color: $wenet-color-text-secondary;
  margin-right: 16rpx;
}

.value {
  flex: 1;
  font-size: 26rpx;
  color: $wenet-color-text-primary;
  word-break: break-all;
}

.online-status {
  color: $wenet-color-success;
  font-weight: 500;
}

/* 加载更多样式 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.no-more {
  font-size: 26rpx;
  color: $wenet-color-text-secondary;
}

/* 无数据样式 */
.no-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.no-data-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 24rpx;
}

.no-data-text {
  font-size: 28rpx;
  color: $wenet-color-text-secondary;
}

/* 返回顶部样式 */
.back-top {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: $wenet-color-brand;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(47, 84, 212, 0.3);
  z-index: 999;
}

.back-top:active {
  background-color: $wenet-color-gradient-end;
}

.back-top-icon {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}
</style>
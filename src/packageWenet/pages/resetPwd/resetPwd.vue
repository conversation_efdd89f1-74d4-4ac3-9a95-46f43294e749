<template>
  <view class="card">
    <view class="form-item">
      <text class="label">账号</text>
      <NInput class="input input-disabled"  :value="userInfo.nickName" disabled />
    </view>
    <view class="form-item">
      <text class="label"><text class="star">*</text> 新密码</text>
      <NInput class="input" type="password"  v-model="newPwd" placeholder="请输入新密码" />
    </view>
    <view class="form-item">
      <text class="label"><text class="star">*</text> 确认密码</text>
      <NInput class="input" type="password"  v-model="confirmPwd" placeholder="请确认新密码" />
    </view>
    <view class="btn-row">
      <NButton type="primary" block @click="onSubmit">确认修改</NButton>
    </view>
  </view>
</template>

<script>
import { restPwd } from '@/packageWenet/api/index';
import NInput from '@/components/form/n-input';
import NButton from '@/components/n-button'
import http from '@/utils/http';
import util from '@/utils/util';
import { AppType } from '@/utils/constant';
export default {
  data() {
    return {
      newPwd: '',
      confirmPwd: '',
      loading: false
    }
  },
  components: {
    NInput,
    NButton
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    },
  },
  methods: {
    onSubmit() {
      if (!this.newPwd) {
        uni.showToast({ title: '请输入新密码', icon: 'none' })
        return
      }
      if (this.newPwd.length < 6) {
        uni.showToast({ title: '新密码至少6位', icon: 'none' })
        return
      }
      if (!this.confirmPwd) {
        uni.showToast({ title: '请确认新密码', icon: 'none' })
        return
      }
      if (this.newPwd !== this.confirmPwd) {
        uni.showToast({ title: '两次输入的新密码不一致', icon: 'none' })
        return
      }
      this.handleRestPwd();
    },
    logout() {
      const appType = uni.getStorageSync("appType");
      util.tapLog(3)
      const params = {
        url: appType === AppType.MINI ? '/social/logOut' : '/logOut',
        method: 'POST',
        data: {
          basToken: uni.getStorageSync('wenetToken')
        },
        callBack: res => {
          this.isAuthInfo = false
          util.clearLoginData();
          let emptyObj = {}
          this.setData({
            orderAmount: emptyObj,
            couponNum: 0,  //优惠券数量
            score: 0, //用户积分
            totalBalance: 0, //用户余额
            notifyNum: 0,  // 消息提醒
            messageCount: 0,
          })
          // this.$Router.pushTab('/pages/index/index')
          setTimeout(() => {
            this.newPwd = ''
            this.confirmPwd = ''
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500); // 1.5秒后跳转
          // uni.reLaunch({
          //   url: '/pages/index/index'
          // })
          uni.setStorageSync("unUseCouponCount", 0)
          uni.removeTabBarBadge({index:2})
        },
        errCallBack: errMsg => {
          console.log(errMsg)
        }
      }
      http.request(params)
    },
    handleRestPwd() {
      const passwordStr = `newPassword=${this.newPwd}&confirm=${this.confirmPwd}`;
      this.loading = true;
      restPwd(passwordStr).then(res => {
        if (!res) {
          uni.showToast({ title: '密码修改成功，请重新登录', icon: 'none' })
          this.logout();
        }
      }).finally(() => {
        this.loading = false;
      })
    }
  }
}
</script>

<style scoped>
.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 32rpx 32rpx 24rpx 32rpx;
  margin: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.star {
  color: #e4393c;
  margin-right: 4rpx;
}

.input-disabled {
  background: #f5f5f5;
  color: #aaa;
}

.btn-row {
  display: flex;
  justify-content: flex-end;
}
</style>

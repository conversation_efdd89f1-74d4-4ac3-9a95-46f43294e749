<template>
  <view class="device-card-wrap">
    <view class="device-card-info">
      <view class="device-card-info__header">
        <text class="device-card-info__start-time">{{ formatTime(device.acct_start_time) }}</text>
        <view class="device-card-info__device-type">{{ getDeviceType(device.deviceType) }}</view>
      </view>
      <view class="device-card-info__other">
        <text class="device-card-info__other__label">MAC:</text>
        <text class="device-card-info__other__value">{{ device.calling_station_id }}</text>
      </view>
      <view class="device-card-info__other">
        <text class="device-card-info__other__label">IP地址:</text>
        <text class="device-card-info__other__value">{{ device.framed_ip_address }}</text>
      </view>
    </view>
    <view class="device-card-actions">
      <button class="device-card-actions__btn" @tap="offline">下线</button>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    device: Object
  },
  methods: {
    formatTime(time) {
      // 可用dayjs等格式化
      return time ? time.replace('T', ' ').slice(0, 19) : ''
    },
    getDeviceType(type) {
      if (!type) return '未知'
      if (type.toLowerCase() === 'pc') return 'PC'
      if (type.toLowerCase() === 'mobile') return 'mobile'
      return type
    },
    offline() {
      this.$emit('offline', this.device)
    }
  }
}
</script>

<style lang="scss" scoped>
.device-card-wrap {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 32rpx;
  margin: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.device-card-info {
  flex: 1;
  
  &__header {
  display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
  }
  
  &__start-time {
    font-size: 28rpx;
    color: $wenet-color-text-primary;
    font-weight: 500;
  }
  
  &__device-type {
  font-size: 24rpx;
    color: $wenet-color-brand;
    background-color: wenet-color-brand-opacity(0.1);
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
  }
  
  &__other {
    display: flex;
    font-size: 26rpx;
  margin-bottom: 16rpx;
    
    &__label {
      color: $wenet-color-text-secondary;
      width: 120rpx;
      flex-shrink: 0;
    }
    
    &__value {
      color: $wenet-color-text-primary;
      word-break: break-all;
    }
  }
}

.device-card-actions {
  padding-left: 20rpx;
  
  &__btn {
    background-color: $wenet-color-brand;
    color: #fff;
    font-size: 26rpx;
    border-radius: 8rpx;
    padding: 12rpx 24rpx;
    line-height: 1;
    border: none;
    
    &:active {
      background-color: $wenet-color-gradient-end;
    }
  }
}
</style>
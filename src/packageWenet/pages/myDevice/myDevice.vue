<template>
  <view class="device-list-container">
    <view class="device-list-header">
      <text class="device-list-title">我的设备</text>
      <text class="device-list-count">共 {{ deviceList.length }} 台设备</text>
    </view>
    
    <view v-if="deviceList.length === 0" class="no-data-wrap">
      <image class="no-data-image" src="/static/images/no-data.png" mode="aspectFit"></image>
      <view class="no-data-text">暂无设备数据</view>
    </view>
    <block v-else>
      <DeviceCard v-for="item in deviceList" :key="item.acct_session_id" :device="item" @offline="handleClickOffline" />
    </block>
    
    <view class="refresh-tip" v-if="deviceList.length > 0">
      <text class="refresh-tip-text">数据每5秒自动刷新</text>
    </view>
  </view>
</template>

<script>
import DeviceCard from './DeviceCard.vue';
import { getDeviceList, offline } from '@/packageWenet/api/index.js';

export default {
  components: { DeviceCard },
  data() {
    return {
      deviceList: [],
      timer: null,
      isLast: false
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo
    }
  },
  onLoad() {
    this.getList()
    this.timer = setInterval(() => this.getList(), 5000)
  },
  onUnload() {
    clearInterval(this.timer)
  },
  onPullDownRefresh() {
    this.getList().finally(() => uni.stopPullDownRefresh())
  },
  onReachBottom() {
    if (!this.isLast) this.getList()
  },
  watch: {
    userInfo: {
      handler(newVal) {
        this.getList()
      },
    }
  },
  methods: {
    async getList() {
      const ou = this.userInfo?.ou;
      if (!ou) return;
      
      try {
      const res = await getDeviceList(ou);
        this.deviceList = res.sessions || [];
      this.isLast = true;
      } catch (error) {
        console.error('获取设备列表失败:', error);
        uni.showToast({
          title: '获取设备列表失败',
          icon: 'none'
        });
      }
    },
    handleClickOffline(device) {
      uni.showModal({
        title: '下线设备',
        content: '确定下线所选设备吗?',
        success: (res) => {
          if (res.confirm) {
            // 调用下线API
            const deviceObj = {
              acctSessionId: device.acct_session_id,
              nasIpAddress: device.nas_ip_address,
              userIp: device.framed_ip_address,
              ou: this.userInfo?.ou,
            }
            offline(deviceObj).then(res2 => {
              uni.showToast({ title: '设备下线成功', icon: 'success' });
              this.getList();
            }).catch(err => {
              uni.showToast({ title: '设备下线失败', icon: 'none' });
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.device-list-container {
  min-height: 100vh;
  background-color: $page-background;
  padding-bottom: 32rpx;
}

.device-list-header {
  padding: 32rpx 24rpx 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-list-title {
  font-size: 32rpx;
  font-weight: 600;
  color: $wenet-color-text-primary;
}

.device-list-count {
  font-size: 24rpx;
  color: $wenet-color-text-secondary;
}

.no-data-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  background-color: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
}

.no-data-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 24rpx;
}

.no-data-text {
  font-size: 28rpx;
  color: $wenet-color-text-secondary;
}

.refresh-tip {
  text-align: center;
  padding: 24rpx 0;
}

.refresh-tip-text {
  font-size: 24rpx;
  color: $wenet-color-text-secondary;
}
</style>
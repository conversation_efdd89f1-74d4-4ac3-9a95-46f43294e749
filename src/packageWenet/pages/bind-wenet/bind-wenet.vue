<template>
  <view class="bind-wenet-wrap">
    <view class="logo">
      <image :src="uniLoginLogoImg" @tap="toIndex" mode="heightFix"></image>
    </view>

    <view class="bind-wenet">
      <view class="bind-wenet-title">绑定wenet账号</view>
        <tabs
          :tabs="tabs"
          :active-tab="activeTab"
          @change="changeTab"
        />
      <view class="org">
        <picker class="input-item" @change="handlePickerChange" mode="selector" range-key="name" :range="orgList" :value="pickerSelectedIndex">
          <view>
            {{ orgShowInPicker }}
          </view>
        </picker>
      </view>
      <view class="account">
        <input class="input-item" type="text" v-model="formValue.username" data-type="account" placeholder-class="inp-palcehoder"
          :placeholder="accountInputPlaceholder">
      </view>

      <view class="password">
        <input  class="input-item" type="password" v-model="formValue.password" data-type="password" placeholder-class="inp-palcehoder"
          placeholder="密码">
      </view>
      <button :loading="loading" :disabled="loading" @tap="handleConfirm" class="confirm-btn">确定</button>
    </view>
  </view>
</template>

<script>
import http from '@/utils/http';
import config from '@/utils/config'
import utils from '@/utils/util';
import Tabs from '@/components/tabs/tabs'

export default {
  data() {
    return {
      formValue: {
        username: '',
        password: '',
        ou: '',
      },
      uniLoginLogoImg: '',
      pickerSelectedIndex: undefined,
      orgList: [],
      loading: false,
      activeTab: 'STUDENT_ID',
      tabs: [
        { key: 'STUDENT_ID', label: '学号' },
        { key: 'TELEPHONE', label: '手机号'},
      ]
    };
  },
  components: {
    Tabs
  },

  onLoad() {
    const uniLoginLogoImg = uni.getStorageSync("uniLoginLogoImg");
    if (uniLoginLogoImg) {
      this.uniLoginLogoImg = uniLoginLogoImg
    } else {
      // 获取uni-app相关配置
      this.getUniWebConfig()
    }
  },

  created() {
    this.getOrgList()
  },
  methods: {
    changeTab(key) {
      this.activeTab = key;
    },
    /**
     * 获取uni-app相关配置
     */
    getUniWebConfig: function() {
      const params = {
        url: "/webConfig/getUniWebConfig",
        method: "GET",
        data: {},
        callBack: res => {
          this.setData({
            uniLoginLogoImg: res.uniLoginLogoImg
          });
          uni.setStorageSync("uniLoginLogoImg",this.uniLoginLogoImg)
        }
      };
      http.request(params);
    },

    startLoading() {
      this.loading = true;
    },
    stopLoading() {
      this.loading = false;
    },

    /**
     * 获取学校列表
     */
    getOrgList() {
      const params = {
        url: '/sys/org/list',
        method: 'GET',
        data: {},
        callBack: res => {
          this.orgList = res
        }
      }
      http.request(params);
    },

    handleConfirm() {
      const tips = {
        ou: '请选择学校',
        username: '请输入账号',
        password: '请输入密码',
      }
      let isFullInfo = true
      Object.keys(tips).forEach(key => {
        if (!this.formValue[key]) {
          uni.showToast({
            title: tips[key],
            icon: 'none'
          })
          isFullInfo = false
          return
        }
      })
      if (!isFullInfo) return
      this.checkWenetAccount()
    },

    /**
     * 绑定uid
     */
    bindWenetAccount(uid) {
      const that = this;
      const params = {
        url: '/p/user/account/bind',
        method: 'POST',
        data: {
          uid,
          ou: this.formValue.ou,
          basUsername: this.formValue.username,
        },
        callBack: () => {
          uni.showToast({
            title: '绑定成功',
            icon: 'none'
          })
          utils.refreshTokenForBindWenet({
            onSuccess: () => {
              that.$store.commit('refreshUserInfo', {
                forceRefresh: true,
                callBack: () => {
                  this.stopLoading();
                  uni.navigateBack()
                }
              });
            }
          })
        },
        errCallBack: () => {
          this.stopLoading();
          uni.showToast({
            title: '该账号已被绑定',
            icon: 'none'
          })
        }
      }
      http.request(params)
    },

    /**
     * 根据检查wenet账号获得的token 查询uid
     */
    getWenetUid(token) {
      
      var params = {
        domain: config.wenetUrl,
        url: '/api/v1/account/me',
        method: "GET",
        data: {},
        dontTrunLogin: true,
        overWriteToken: token,
        callBack: (res) => {
          this.bindWenetAccount(res.person.uid)
        },
        errCallBack: () => {
          this.stopLoading();
        }
      };
      http.request(params);
    },

    /**
     * 检查wenet账号
     */
    checkWenetAccount() {
      const username = this.formValue.username;
      const password = this.formValue.password;
      const prefix = this.orgList[this.pickerSelectedIndex].prefix || '';
      const fullUsername = this.activeTab === 'STUDENT_ID' ? `${prefix}${username}` : username
      this.startLoading()
      http.request({
        domain: config.wenetUrl,
        url: `/api/v1/auth?username=${fullUsername}&password=${password}`,
        method: 'POST',
        callBack:(res) => {
          const accessToken = res.access_token;
          const tokenType = 'Bearer'
          this.getWenetUid(`${tokenType} ${accessToken}`)
        },
        errCallBack: (err) => {
          this.stopLoading()
          if (err.data.code?.toString() === '1022') {
            uni.showToast({
              title: '账号或密码错误',
              icon: 'none'
            })
          } else {
            uni.showToast({
              title: '绑定失败',
              icon: 'none'
            })
          }
        }
      });
    },

    handlePickerChange(e) {
        this.pickerSelectedIndex = e.detail.value
        this.formValue.ou = this.orgList[e.detail.value].ou
    },

    // 跳转到首页
    toIndex() {
				this.$Router.pushTab('/pages/index/index')
    },
  },
  computed: {
    orgShowInPicker() {
      if (typeof this.pickerSelectedIndex !== 'undefined') {
        return this.orgList[this.pickerSelectedIndex].name;
      }
      return '请选择'
    },
    accountInputPlaceholder() {
      return this.activeTab === 'STUDENT_ID' ? '学号' : '手机号'
    }
  }

}
</script>

<style scoped>
.bind-wenet-title {
  font-size: 36rpx;
  margin-bottom: 24rpx;
}
.bind-wenet-wrap {
  width: 100vw;
  height: 100vh;
  padding: 64rpx 48rpx 0;
  box-sizing: border-box;
}

.bind-wenet {
  width: 100%;;
  display: flex;
  flex-direction: column;
  align-items: center;
  column-gap: 8rpx;
}
.account, .password, .org{
  display: flex;
  box-sizing: border-box;
  width: 100%;
  height: 84rpx;
  padding: 0rpx 32rpx;
  align-items: center;
  border-radius: 16rpx;
  background: #F5F5F5;
  margin-bottom: 48rpx;
}
.account .input-item, .password .input-item, .org .input-item {
  font-size: 30rpx;
  outline: none;
  width: calc(100% - 32rpx);
  border: none;
  background: transparent;

}

/* .account input, .password input, .org .picker {
  padding-left: 20rpx;
  width:75%;
} */
.account .set-pass{
	position: absolute;
	right: 10px;
	font-size: 28rpx;
	color: #00AAFF;
}

.confirm-btn {
  width: 80%;
  background-color: var(--primary-color);
  color: #fff;
}

.logo {
  display: flex;
  justify-content: center;
  height: 150rpx;
  margin-bottom: 8%;
  object-fit: contain;
}
.logo image {
  display: block;
  width: 100%;
  height: 100%;
}
</style>
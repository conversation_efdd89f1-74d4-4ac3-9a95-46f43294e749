<template>
  <view class="container active-isp-network">
    <view class="isp-network-tip">{{ ispTip }}</view>
    
    <view class="isp-network-selector-wrap">
      <UniDataSelect
        class="isp-selector" 
        :localdata="ispList"
        v-model="selectedIsp"
        @change="handleIspChange"
        placeholder="选择运营商"
        height="32rpx"
      />
    </view>
    
    <NButton type="primary" block @click="handleClickNext">下一步</NButton>
    
    <!-- 弹窗组件 -->
    <uni-popup ref="popup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="提示"
        content="您已成功激活宽带，无须再次激活！"
        :before-close="true"
        @confirm="goToPlanPage"
        @close="goToHomePage"
        confirmText="修改激活信息"
        cancelText="关闭"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import UniDataSelect from '@/components/uni-data-select/uni-data-select';
import { checkHasActivedIspNetworkProd } from '@/packageWenet/api';
import UniPopupDialog from '@/components/uni-popup-dialog/uni-popup-dialog.vue'
import UniPopup from '@/components/uni-popup/uni-popup.vue'
import NButton from '@/components/n-button'

// 不同学校的提示信息
const ispTipMap = {
  '10080': '所有手机卡融合宽带业务的同学都可激活宽带，需准确填写上网账号及密码，若输入错误则无法正常上网，若账号密码忘记需要咨询运营商确认。',
  '10427': '所有在济南大学范围内办理过运营商手机卡（需要号卡状态正常）的同学都可以免费激活宽带，请同学们务必按提示正确填写。'
};

export default {
  components: {
    UniDataSelect,
    UniPopupDialog,
    UniPopup,
    NButton
  },
  data() {
    return {
      ispList: [
        { value: 'CMCC', text: '中国移动' },
        { value: 'CTCC', text: '中国电信' },
        { value: 'CUCC', text: '中国联通' },
      ],
      selectedIsp: '',
      hasActivated: false
    }
  },
  onShow() {
    if (this.selectedIsp) {
      this.checkHasActivated()
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo
    },
    originalWenetAccountInfo() {
      return this.$store.state.originalWenetAccountInfo
    },
    ou() {
      return this.userInfo?.ou || ''
    },
    uid() {
      return this.userInfo?.uid || ''
    },
    ispTip() {
      return ispTipMap[this.ou] || '限新办理手机卡融合宽带业务的同学激活宽带。'
    }
  },
  watch: {
    selectedIsp(newVal) {
      if (newVal && this.ou) {
        this.checkHasActivated()
      }
    }
  },
  methods: {
    handleIspChange(e) {
      this.selectedIsp = e
    },
    
    async checkHasActivated() {
      try {
        const res = await checkHasActivedIspNetworkProd({
          isp: this.selectedIsp,
          ou: this.ou,
          uid: this.uid
        })
        this.hasActivated = res
      } catch (error) {
        console.error('检查是否已激活失败:', error)
      }
    },
    
    handleClickNext() {
      if (!this.selectedIsp) {
        uni.showToast({
          title: '请选择运营商',
          icon: 'none'
        })
        return
      }
      
      // 是否已存在该校、该运营商的、生效中的融合账期
      if (this.hasActivated) {
        // 显示弹窗
        this.$refs.popup.open()
      } else {
        // 跳转到提交页面
        uni.navigateTo({
          url: `/packageWenet/pages/active-isp-network/submit?dproxyISP=${this.selectedIsp}`
        })
      }
    },
    
    // 前往计划页面（修改激活信息）
    goToPlanPage() {
      uni.navigateTo({
        url: '/packageWenet/pages/plans/plans'
      })
    },
    
    // 返回首页
    goToHomePage() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.active-isp-network {
  padding: 30rpx;
  background-color: #f4f4f4;
  min-height: 100vh;
}

.isp-network-tip {
  font-size: 28rpx;
  color: $wenet-color-text-secondary;
  line-height: 1.5;
  margin-bottom: 30rpx;
  padding: 20rpx;
  border-radius: 12rpx;
}

.isp-network-selector-wrap {
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  height: 84rpx;
  padding: 0 32rpx;
}

.isp-selector {
  width: 100%;
}

</style>
<template>
  <view class="bind-isp">
    
    <!-- 网络建设中提示 -->
    <view v-if="!isOnlineProduct && !targetProd" class="empty-tip">
      <text>网络正在建设中，敬请期待……</text>
    </view>
    
    <!-- 表单内容 -->
    <view v-else class="bind-isp__form">
      <view class="form-item">
        <view class="form-label">手机号</view>
        <n-input 
          v-model="formValue.phone" 
          placeholder="请输入新卡手机号"
          :class="{'input-error': errors.phone}"
        />
        <view v-if="errors.phone" class="error-message">{{ errors.phone }}</view>
      </view>
      
      <view class="form-item">
        <view class="form-label">验证码</view>
        <view class="verify-code-container">
          <n-input 
            v-model="formValue.verifyCode" 
            placeholder="请输入验证码"
            :class="{'input-error': errors.verifyCode}"
          />
          <view class="send-code-btn" @click="sendVerifyCode" :class="{'disabled': countdown > 0}">
            {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
          </view>
        </view>
        <view v-if="errors.verifyCode" class="error-message">{{ errors.verifyCode }}</view>
      </view>
      
      <!-- 宽带账号和密码（仅在需要时显示） -->
      <template v-if="dproxyInfoRequired">
        <view class="form-item">
          <view class="form-label">宽带账号</view>
          <n-input 
            v-model="formValue.dproxyUsername" 
            :placeholder="targetProd.usernameTips || '请输入宽带账号'"
            :class="{'input-error': errors.dproxyUsername}"
          />
          <view v-if="errors.dproxyUsername" class="error-message">{{ errors.dproxyUsername }}</view>
        </view>
        
        <view class="form-item">
          <view class="form-label">宽带密码</view>
          <n-input 
            v-model="formValue.dproxyPassword" 
            :placeholder="targetProd.passwordTips || '请输入宽带密码'"
            :class="{'input-error': errors.dproxyPassword}"
          />
          <view v-if="errors.dproxyPassword" class="error-message">{{ errors.dproxyPassword }}</view>
        </view>
      </template>
      
      <view class="form-item">
        <NButton type="primary" block @click="handleSubmit" :loading="isSubmitting">
          {{ isSubmitting ? '提交中...' : '提交' }}
        </NButton>
      </view>
    </view>
    
    <!-- 提示弹窗 -->
    <uni-popup ref="doubleChargePopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="提示"
        :content="doubleChargeContent"
        :before-close="true"
        @confirm="goToChargeProd"
        @close="handleCancelDoubleCharge"
        confirmText="前往办理"
        cancelText="取消"
      ></uni-popup-dialog>
    </uni-popup>
    
    <!-- 激活成功弹窗 -->
    <uni-popup ref="successPopup" type="dialog">
      <uni-popup-dialog
        type="success"
        title="温馨提示"
        :content="successContent"
        :before-close="true"
        @confirm="goToPlanPage"
        @close="goToHomePage"
        confirmText="查看服务"
        cancelText="返回首页"
      ></uni-popup-dialog>
    </uni-popup>
    <NLoading />
  </view>
</template>

<script>
import NInput from '@/components/form/n-input'
import NButton from '@/components/n-button'
import UniPopupDialog from '@/components/uni-popup-dialog/uni-popup-dialog.vue'
import UniPopup from '@/components/uni-popup/uni-popup.vue'
import NLoading from '@/components/n-loading/n-loading.vue'
import { 
  getOfflineIspNetworkProd, 
  getOnlineIspNetworkProd, 
  getToBeActivedPlan,
  placeAnOrderForOfflineProd,
  checkSmsCode,
  sendSmsCode,
  bindDproxy
} from '@/packageWenet/api'

const REBIND_MSG_TYPE = 'REBIND'
const PLACE_ORDER_MSG_TYPE = 'SELF_FUSE_ORDER'

export default {
  components: {
    NInput,
    UniPopupDialog,
    UniPopup,
    NLoading,
    NButton
  },
  data() {
    return {
      // 表单数据
      formValue: {
        phone: '',
        dproxyUsername: '',
        dproxyPassword: '',
        verifyCode: ''
      },
      // 表单错误
      errors: {
        phone: '',
        dproxyUsername: '',
        dproxyPassword: '',
        verifyCode: ''
      },
      // 加载状态
      isLoading: true,
      isSubmitting: false,
      verifyCodeLoading: false,
      placeOrderLoading: false,
      
      // 数据
      query: {},
      offlineProd: null,
      onlineProds: [],
      toBeActivedPlan: null,
      
      // 倒计时
      countdown: 0,
      timer: null,
      
      // 加载状态
      toBeActivedPlanLoading: true,
      onlineProdsLoading: true,
      offlineProdLoading: true,

    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo
    },
    originalWenetAccountInfo() {
      return this.$store.state.originalWenetAccountInfo
    },
    ou() {
      return this.userInfo?.ou || ''
    },
    uid() {
      return this.userInfo?.uid || ''
    },
    // 高优先级参数
    highPriorityParams() {
      return {
        pid: this.query?.pid, // 账期id
        uid: this.query?.uid || this.uid,
        sid: this.query?.sid,
        oid: this.query?.oid,
        productId: this.query?.productId,
        productName: this.query?.productName,
        isOnline: this.query?.isOnline === 'true'
      }
    },
    // 是否为线上产品
    isOnlineProduct() {
      return !!(this.highPriorityParams.isOnline || this.toBeActivedPlan?.pid)
    },
    // 目标产品
    targetProd() {
      if (this.isOnlineProduct) {
        const comparedProductId = this.highPriorityParams.productId || this.toBeActivedPlan?.productId || ''
        console.log(comparedProductId, this.onlineProds)
        return this.onlineProds.find(item => item.pid === comparedProductId)
      }
      return this.offlineProd
    },
    // 是否需要宽带账号信息
    dproxyInfoRequired() {
      return this.targetProd?.dproxyInfoRequired
    },
    // 双重收费内容
    doubleChargeContent() {
      if (this.targetProd?.forceDoubleCharge) {
        return '为了确保您的网络稳定性，我们将提供无感知认证等增值服务，若未办理将无法正常连接校园网（若已成功办理，请刷新页面）'
      } else {
        return '根据您的用网习惯，推荐升级适配套餐。80%同学已升级，请前往确认（若已成功办理，请刷新页面）'
      }
    },
    successContent() {
      if (this.query?.action === 'update') {
        return '修改成功，修改后若依旧无法认证成功，请联系营业厅解决。'
      } else {
        return '您的网络服务已成功激活'
      }
    }
  },
  onLoad(options) {
    this.query = options
    
    // 初始化表单数据
    if (options.dproxyUsername) {
      this.formValue.dproxyUsername = options.dproxyUsername
    }
    
    // 日志输出
    if (this.highPriorityParams.pid && options.action === 'update') {
      console.log('从账期列表进入，想要更换账期绑定的宽带账号')
    } else if (this.highPriorityParams.pid && options.action === 'active') {
      console.log('从账期列表进入，想要激活账期')
    }
    
    // 加载数据
    this.loadData()
  },
  onShow() {
    // 页面显示时检查是否需要显示双重收费弹窗
    this.checkShowDoubleChargeModal()
  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    // 加载数据
    async loadData() {
      if (!this.ou) {
        return
      }
      this.isLoading = true
      try {
        // 1. 加载待激活计划
        if (!this.highPriorityParams.pid) {
          await this.loadToBeActivedPlan()
        } else {
          this.toBeActivedPlanLoading = false
        }
        
        // 2. 加载线上产品
        await this.loadOnlineProds()
        
        // 3. 加载线下产品
        await this.loadOfflineProd()
        
        // 4. 检查是否需要显示双重收费弹窗
        this.checkShowDoubleChargeModal()
        
        // 日志输出
        if (!this.highPriorityParams.pid && this.isOnlineProduct && this.toBeActivedPlan) {
          console.log('从"宽带激活"进入，查询到线上订单，需要激活', this.toBeActivedPlan)
        } else if (!this.isOnlineProduct && this.targetProd) {
          console.log('从"宽带激活"进入，未查询到线上订单，需要下单', this.offlineProd)
        }
      } catch (error) {
        console.error('加载数据失败', error)
      } finally {
        this.isLoading = false
      }
    },
    
    // 加载待激活计划
    async loadToBeActivedPlan() {
      this.toBeActivedPlanLoading = true
      try {
        const res = await getToBeActivedPlan({
          uid: this.uid,
          ou: this.ou,
          isp: this.query.dproxyISP
        })
        this.toBeActivedPlan = res
      } finally {
        this.toBeActivedPlanLoading = false
      }
    },
    
    // 加载线上产品
    async loadOnlineProds() {
      this.onlineProdsLoading = true
      try {
        const res = await getOnlineIspNetworkProd({
          isp: this.query.dproxyISP,
          ou: this.ou
        })
        this.onlineProds = res || []
      } finally {
        this.onlineProdsLoading = false
      }
    },
    
    // 加载线下产品
    async loadOfflineProd() {
      this.offlineProdLoading = true
      try {
        const res = await getOfflineIspNetworkProd({
          isp: this.query.dproxyISP,
          ou: this.ou
        })
        const prods = res || []
        this.offlineProd = prods[0]
      } finally {
        this.offlineProdLoading = false
      }
    },
    
    // 检查是否需要显示双重收费弹窗
    checkShowDoubleChargeModal() {
      if (!this.isOnlineProduct && !this.toBeActivedPlanLoading && 
          !!this.targetProd?.doubleCharge && this.targetProd?.doubleChargeUrl) {
        this.$refs.doubleChargePopup.open()
      }
    },
    // 表单验证
    validate() {
      let isValid = true
      this.errors = {
        phone: '',
        dproxyUsername: '',
        dproxyPassword: '',
        verifyCode: ''
      }
      
      // 验证手机号
      if (!this.formValue.phone) {
        this.errors.phone = '请输入手机号'
        isValid = false
      } else {
        const phoneRegular = /^1[3456789]\d{9}$/
        if (!phoneRegular.test(this.formValue.phone)) {
          this.errors.phone = '请输入正确的手机号'
          isValid = false
        }
      }
      
      // 验证验证码
      if (!this.formValue.verifyCode) {
        this.errors.verifyCode = '请输入验证码'
        isValid = false
      }
      
      // 验证宽带账号
      if (this.dproxyInfoRequired) {
        if (!this.formValue.dproxyUsername) {
          this.errors.dproxyUsername = '请输入宽带账号'
          isValid = false
        } else {
          const chinesePattern = /[\u4e00-\u9fa5]/
          if (chinesePattern.test(this.formValue.dproxyUsername)) {
            this.errors.dproxyUsername = '输入信息格式错误，无法绑定'
            isValid = false
          }
        }
        
        // 验证宽带密码
        if (!this.formValue.dproxyPassword) {
          this.errors.dproxyPassword = '请输入宽带密码'
          isValid = false
        } else if (/\s/.test(this.formValue.dproxyPassword)) {
          this.errors.dproxyPassword = '密码不能包含空格'
          isValid = false
        } else {
          const chinesePattern = /[\u4e00-\u9fa5]/
          if (chinesePattern.test(this.formValue.dproxyPassword)) {
            this.errors.dproxyPassword = '输入信息格式错误，无法绑定'
            isValid = false
          }
        }
      }
      
      return isValid
    },
    
    // 发送验证码
    async sendVerifyCode() {
      // 如果正在倒计时，不能再次发送
      if (this.countdown > 0) return
      
      // 验证手机号
      const phoneRegular = /^1[3456789]\d{9}$/
      if (!this.formValue.phone || !phoneRegular.test(this.formValue.phone)) {
        this.errors.phone = '请输入正确的手机号'
        return
      }
      
      try {
        const msgType = this.isOnlineProduct ? REBIND_MSG_TYPE : PLACE_ORDER_MSG_TYPE
        await sendSmsCode({
          phone: this.formValue.phone,
          type: msgType
        })
        
        // 开始倒计时
        this.countdown = 60
        this.timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.timer)
          }
        }, 1000)
        
        uni.showToast({
          title: '验证码已发送',
          icon: 'none'
        })
      } catch (error) {
      }
    },
    
    // 验证短信验证码
    async checkMsgCode() {
      this.verifyCodeLoading = true
      try {
        const msgType = this.isOnlineProduct ? REBIND_MSG_TYPE : PLACE_ORDER_MSG_TYPE
        const result = await checkSmsCode({
          phone: this.formValue.phone,
          code: this.formValue.verifyCode,
          type: msgType
        })
        if (!result) {
          uni.showToast({
            title: '验证码错误或已过期',
            icon: 'none'
          })
          return false
        }
        return true
      } catch (error) {
        return false
      } finally {
        this.verifyCodeLoading = false
      }
    },
    
    // 激活账期
    async activePlan() {
      try {
        const { dproxyISP } = this.query
        const uid = this.highPriorityParams.uid || this.uid
        const pid = this.highPriorityParams.pid || this.toBeActivedPlan?.pid || ''
        const sid = this.highPriorityParams.sid || this.toBeActivedPlan?.sid || ''
        const oid = this.highPriorityParams.oid || this.toBeActivedPlan?.oid || ''
        
        // 构建请求参数
        const params = {
          pid,
          username: this.targetProd?.dproxyInfoRequired ? this.formValue.dproxyUsername : this.formValue.phone,
          password: this.targetProd?.dproxyInfoRequired ? this.formValue.dproxyPassword : this.targetProd?.deafultDproxyPassword,
          sid,
          uid,
          oid,
          dproxyISP,
          extField1: this.formValue.phone,
        }
        await bindDproxy(params)
        this.$refs.successPopup.open()
      } catch (error) {
      }
    },
    
    // 线下产品下单
    async placeAnOrder() {
      this.placeOrderLoading = true
      try {
        if (this.targetProd) {
          await placeAnOrderForOfflineProd({
            isp: this.query?.dproxyISP,
            ou: this.ou,
            uid: this.uid,
            dproxyPassword: this.targetProd?.dproxyInfoRequired ? this.formValue.dproxyPassword : this.targetProd?.deafultDproxyPassword,
            dproxyUsername: this.targetProd?.dproxyInfoRequired ? this.formValue.dproxyUsername : this.formValue.phone,
            phone: this.formValue.phone,
            verifyCode: this.formValue.verifyCode
          })
          
          // 显示成功弹窗
          this.$refs.successPopup.open()
        }
      } catch (error) {
      } finally {
        this.placeOrderLoading = false
      }
    },
    
    // 提交表单
    async handleSubmit() {
      // 表单验证
      if (!this.validate()) return
      
      this.isSubmitting = true
      
      try {
        // 如果传了pid，则一定是更换绑定，使用激活方法
        if (this.highPriorityParams.pid) {
          const isCodeValid = await this.checkMsgCode()
          if (isCodeValid) {
            await this.activePlan()
          }
        } else if (this.isOnlineProduct) {
          // 线上产品，需要激活
          const isCodeValid = await this.checkMsgCode()
          if (isCodeValid) {
            await this.activePlan()
          }
        } else {
          // 线下产品，需要下单
          await this.placeAnOrder()
        }
      } finally {
        this.isSubmitting = false
      }
    },
    
    // 前往收费产品页面
    goToChargeProd() {
      if (this.targetProd?.doubleChargeUrl) {
        uni.redirectTo({
          url: this.targetProd.doubleChargeUrl
        })
      }
    },
    
    // 取消双重收费弹窗
    handleCancelDoubleCharge() {
      console.log('handleCancelDoubleCharge', this.targetProd)
      // 强制收费将返回上一页
      if (this.targetProd?.forceDoubleCharge) {
        uni.navigateBack()
      } else {
        this.$refs.doubleChargePopup.close()
      }
    },
    
    // 前往计划页面
    goToPlanPage() {
      uni.redirectTo({
        url: '/packageWenet/pages/plans/plans'
      })
    },
    
    // 返回首页
    goToHomePage() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    }
  },
  watch: {
    ou() {
      this.loadData()
    }
  }
}
</script>

<style lang="scss" scoped>
.bind-isp {
  padding: 30rpx;
  min-height: 100vh;
}

.empty-tip {
  padding-top: 96rpx;
  text-align: center;
  font-size: 32rpx;
  color: $wenet-color-text-secondary;
}

.bind-isp__form {
  padding: 20rpx 0;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: $wenet-color-text-primary;
  margin-bottom: 16rpx;
}

.verify-code-container {
  display: flex;
  align-items: center;
}

.send-code-btn {
  min-width: 200rpx;
  height: 84rpx;
  line-height: 84rpx;
  text-align: center;
  font-size: 26rpx;
  color: $wenet-color-brand;
  margin-left: 20rpx;
}

.send-code-btn.disabled {
  color: $wenet-color-text-secondary;
}

.error-message {
  color: $wenet-color-error;
  font-size: 24rpx;
  margin-top: 8rpx;
}

.input-error {
  border: 1px solid $wenet-color-error;
}
</style>
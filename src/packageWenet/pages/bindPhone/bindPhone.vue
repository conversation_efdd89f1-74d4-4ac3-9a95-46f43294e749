<template>
  <view v-if="simpleWenetAccountInfo.basMobile" class="has-bind">
      <!-- :src="`${staticPicDomain}images/icon/bindPhone.png`" -->
      <!-- <image :src= "require('@/static/images/bindPhone.png')" class="imgs"  /> -->
      <view class="tip1">
        {{ `+86${simpleWenetAccountInfo.basMobile.substring(0,3)}****${simpleWenetAccountInfo.basMobile.substring(7,11)}` }}
      </view>
    <NButton @click="toUpdatePhone">换绑手机号</NButton>
  </view>
  <view class="no-bind" v-else>
    <view class="tip">
      为了你的账号安全和校园网正常使用，请绑定手机号
    </view>
    <form @submit="handleSubmit" report-submit="true">
      <!-- 手机号 -->
      <view class="form-item">
        <NInput
          v-model="formData.username"
          placeholder="请输入手机号"
          maxlength="11"
        />
        <text class="error" v-if="error.username">{{ error.username }}</text>
      </view>

      <!-- 验证码 -->
      <view class="form-item code-box">
        <NInput
          type="number"
          v-model="formData.verifyCode"
          placeholder="请输入验证码"
          maxlength="6"
        />
        <view class="send-code" @click="sendMsg">
          {{ timer > 0 ? `${timer}s后重发` : '发送验证码' }}
        </view>
        <text class="error" v-if="error.verifyCode">{{ error.verifyCode }}</text>
      </view>

      <!-- 提交按钮 -->
      <view class="form-item">
        <NButton :loading="loading" type="primary" block @click="handleSubmit">确认绑定</NButton>
      </view>
    </form>
  </view>
</template>

<script>
import { bindPhone, checkPhoneIsRegister, sendSmsCodes } from '@/packageWenet/api/index';
import NInput from '@/components/form/n-input';
import NButton from '@/components//n-button'
export default {
  data() {
    return {
      loading: false,
      timer: 0,
      formData: {
        username: '',
        verifyCode: ''
      },
      error: {
        username: '',
        verifyCode: ''
      },
      timerInterval: null
    };
  },
  methods: {
    validate() {
      let valid = true;
      this.error = {
        username: '',
        verifyCode: ''
      };
      const newUsernameRegular = /^1[3456789]\d{9}$/;

      if (!this.formData.username || this.formData.username.trim() === '' || !newUsernameRegular.test(this.formData.username)) {
        this.error.username = '请输入正确的手机号';
        valid = false;
      }
      if (!this.formData.verifyCode || this.formData.verifyCode.trim() === '') {
        this.error.verifyCode = '请输入验证码';
        valid = false;
      }

      return valid;
    },
    toUpdatePhone() {
      uni.navigateTo({
        url: '/packageWenet/pages/bindPhone/updatePhone',
      });
    },
    handleSubmit(e) {
      if (!this.validate()) return;

      this.loading = true;
      // 模拟请求
      bindPhone(this.formData.username, this.formData.verifyCode).then(res => {
        // 模拟请求成功
        uni.showToast({ title: '绑定成功', icon: 'success' });
        this.$store.commit('refreshUserInfo', { forceRefresh: true });
        setTimeout(() => {
          uni.navigateTo({
            url: '/packageWenet/pages/bindPhone/bindPhone',
          });
        }, 1500);
      }).catch(err => {
        // 模拟请求失败
        uni.showToast({ title: '绑定失败', icon: 'error' });
        console.log('提交数据：', this.formData);
      }).finally(() => {
        this.loading = false;
      });
    },
    sendMsg() {
      if (this.timer > 0) return;

      if (!this.formData.username) {
        this.error.username = '请先填写手机号';
        return;
      }
      const type = 'REBIND';
      checkPhoneIsRegister(this.formData.username).then((res) => { 
        uni.showToast({
          title: '此手机号码已存在请修改',
          icon: 'none'
        })
      }).catch((error) => {
        console.log('this.formData.username', this.formData.username, type) 
        sendSmsCodes(this.formData.username, type).then(res => { 
          uni.showToast({
          title: '验证码发送成功请注意查收',
          icon: 'none'
        })
        })
      })

      // 模拟发送验证码
      uni.showToast({ title: '验证码已发送', icon: 'none' });

      this.timer = 60;
      this.timerInterval = setInterval(() => {
        this.timer--;
        if (this.timer <= 0) {
          clearInterval(this.timerInterval);
        }
      }, 1000);
    }
  },
  onUnload() {
    clearInterval(this.timerInterval);
  },
  components: {
    NInput,
    NButton
  },
  computed: {
    simpleWenetAccountInfo() {
      return this.$store.state.simpleWenetAccountInfo;
    },
  },
}
</script>

<style scoped>
.has-bind {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 200rpx;
}

.no-bind {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 200rpx;
}
.imgs {
  width: 100rpx;
  height: 140rpx;
  padding-bottom: 30rpx;
}
.tip {
  font-size: 28rpx;
  margin-bottom: 30rpx;
  display: block;
  text-indent: 28rpx;
}
.tip1 {
  font-size: 28rpx;
  margin-bottom: 30rpx;
  display: block;
}
.form-item {
  margin-bottom: 30rpx;
}
input {
  width: 100%;
  height: 90rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 1px solid #ccc;
  border-radius: 10rpx;
  box-sizing: border-box;
}
.code-box {
  display: flex;
  align-items: center;
}
.send-code {
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  color: var(--primary-color);
  font-size: 26rpx;
  border-radius: 6rpx;
}
.error {
  color: red;
  font-size: 24rpx;
  margin-top: 10rpx;
}
</style>
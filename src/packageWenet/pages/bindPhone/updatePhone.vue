<template>
  <view class="re-bind">
    <form @submit="handleSubmit" report-submit="true">
      <!-- 手机号 -->
      <view class="form-item">
        <NInput v-model="formData.newUsername" placeholder="请输入新手机号" maxlength="11" />
        <text class="error" v-if="error.newUsername">{{ error.newUsername }}</text>
      </view>

      <!-- 验证码 -->
      <view class="form-item code-box">
        <NInput type="number" v-model="formData.newUsernameVerifyCode" placeholder="请输入验证码" maxlength="6" />
        <view class="send-code" @click="sendMsg">
          {{ timer > 0 ? `${timer}s后重发` : '发送验证码' }}
        </view>
        <text class="error" v-if="error.newUsernameVerifyCode">{{ error.newUsernameVerifyCode }}</text>
      </view>
      <!-- 提交按钮 -->
      <view class="form-item">
        <NButton :loading="loading" type="primary" block @click="handleSubmit">确认绑定</NButton>
      </view>
    </form>
  </view>
</template>

<script>
import { rebindPhone, sendSmsCodes, checkPhoneIsRegister } from '@/packageWenet/api/index';
import NInput from '@/components/form/n-input';
import NButton from '@/components/n-button'
export default {
  data() {
    return {
      loading: false,
      timer: 0,
      formData: {
        newUsername: '',
        newUsernameVerifyCode: ''
      },
      error: {
        newUsername: '',
        newUsernameVerifyCode: ''
      },
      timerInterval: null
    };
  },
  methods: {
    validate() {
      let valid = true;
      this.error = {
        newUsername: '',
        newUsernameVerifyCode: ''
      };
      const newUsernameRegular = /^1[3456789]\d{9}$/;

      if (!this.formData.newUsername || this.formData.newUsername.trim() === '' || !newUsernameRegular.test(this.formData.newUsername)) {
        this.error.newUsername = '请输入正确的手机号';
        valid = false;
      }
      if (!this.formData.newUsernameVerifyCode || this.formData.newUsernameVerifyCode.trim() === '') {
        this.error.newUsernameVerifyCode = '请输入验证码';
        valid = false;
      }

      return valid;
    },
    handleSubmit(e) {
      
      if (!this.validate()) return;
      this.loading = true;
      // 模拟请求
      rebindPhone(this.formData).then(res => {
        // 模拟请求成功
        uni.showToast({ title: '绑定成功', icon: 'success' });
        this.$store.commit('refreshUserInfo', { forceRefresh: true });
        setTimeout(() => {
          uni.navigateTo({
            url: '/packageWenet/pages/bindPhone/bindPhone',
          });
        }, 1500);
      }).catch(err => {
        // 模拟请求失败
        console.log('提交数据eee：', err);
        uni.showToast({ title: '绑定失败', icon: 'error' });
      }).finally(() => {
        this.loading = false;
      });
    },
    sendMsg() {
      if (this.timer > 0) return;

      if (!this.formData.newUsername) {
        this.error.newUsername = '请先填写手机号';
        return;
      }
      const type = 'REBIND';
      checkPhoneIsRegister(this.formData.newUsername).then((res) => { 
        uni.showToast({
          title: '此手机号码已存在请修改',
          icon: 'none'
        })
      }).catch((error) => {
        console.log('this.formData.newUsername', this.formData.newUsername, type) 
        sendSmsCodes(this.formData.newUsername, type).then(res => { 
          uni.showToast({
          title: '验证码发送成功请注意查收',
          icon: 'none'
        })
        })
      })

      // 模拟发送验证码
      this.timer = 60;
      this.timerInterval = setInterval(() => {
        this.timer--;
        if (this.timer <= 0) {
          clearInterval(this.timerInterval);
        }
      }, 1000);
    }
  },
  onUnload() {
    clearInterval(this.timerInterval);
  },
  components: {
    NInput,
    NButton
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    },
  },
}
</script>

<style scoped>
.re-bind {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.code-box {
  display: flex;
  align-items: center;
}

.send-code {
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  color: var(--primary-color);
  font-size: 26rpx;
  border-radius: 6rpx;
}

.error {
  color: red;
  font-size: 24rpx;
  margin-top: 10rpx;
}
</style>
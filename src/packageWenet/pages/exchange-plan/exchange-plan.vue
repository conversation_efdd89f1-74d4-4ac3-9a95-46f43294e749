<template>
  <view class="container exchange-plan">
    <view class="form">
      <view class="form-item">
        <n-input 
          v-model="formValue.code" 
          placeholder="请输入兑换码"
          :class="{'input-error': errors.code}"
        />
        <view v-if="errors.code" class="error-message">{{ errors.code }}</view>
      </view>
      <view class="form-item">
        <n-button type="primary" block @click="handleClickExchangeButton" :loading="loading">兑换</n-button>
      </view>
      <view class="tips">
        <view class="tips-title text-primary text-size-16 font-weight-600">重要提示</view>
        <view class="tips-item text-secondary text-size-12">·兑换成功后立即生效，且无法更改。</view>
        <view class="tips-item text-secondary text-size-12">·兑换码为商城购买后获得。</view>
      </view>
    </view>
    <uni-popup ref="multiplePlanPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="提示"
        :content="multiplePlanContent"
        :before-close="true"
        @confirm="handleConfirmExchange"
        @close="handleCancelExchange"
        confirmText="继续兑换"
        cancelText="取消"
      ></uni-popup-dialog>
    </uni-popup>

    <uni-popup ref="makeSurePopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="提示"
        :content="makeSureContent"
        :before-close="true"
        @confirm="handleMakeSureExchange"
        @close="handleCancelMakeSureExchange"
        confirmText="继续兑换"
        cancelText="取消"
      >
        <view class="make-sure-content">
          <view class="make-sure-content-title">信息确认</view>
          <view class="make-sure-content-item">套餐兑换后不可更改，请再次核对以下信息：</view>
          <view class="make-sure-content-item" v-if="realname">姓名：{{ realname }}</view>
          <view class="make-sure-content-item" v-if="simpleWenetAccountInfo" >账号：{{ simpleWenetAccountInfo.basUsername }}</view>
        </view>
      </uni-popup-dialog>
    </uni-popup>

  </view>
</template>

<script>
import NInput from '@/components/form/n-input'
import NButton from '@/components/n-button'
import UniPopupDialog from '@/components/uni-popup-dialog/uni-popup-dialog.vue'
import UniPopup from '@/components/uni-popup/uni-popup.vue'
import NLoading from '@/components/n-loading/n-loading.vue'
import { getAvailablePlans, getProductInfoByCode, exchangePlan } from '@/packageWenet/api'

export default {
  components: {
    NInput,
    NButton,
    UniPopupDialog,
    UniPopup,
    NLoading
  },
  data() {
    return {
      formValue: {
        code: ''
      },
      errors: {
        code: ''
      },
      loading: false,
      currentPlanCount: 0,
      productInfo: null
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo
    },
    originalWenetAccountInfo() {
      return this.$store.state.originalWenetAccountInfo
    },
    ou() {
      return this.userInfo?.ou || ''
    },
    uid() {
      return this.userInfo?.uid || ''
    },
    multiplePlanContent() {
      return `当前账号有${this.currentPlanCount}个可用套餐，确定要继续兑换吗？`
    },
    makeSureContent() {
      return `兑换套餐后不可更改，请再次核对以下信息：`
    },
    realname() {
      return this.originalWenetAccountInfo?.identification?.realName || ''
    },
    simpleWenetAccountInfo() {
      return this.$store.state.simpleWenetAccountInfo
    }
  },
  onLoad() {
    this.getCurrentPlanCount()
  },
  methods: {
    async checkCodeIsAvailable() {
      this.loading = true
      try {
        const productInfo = await getProductInfoByCode(this.formValue.code)
        this.productInfo = productInfo
        return true
      } catch (error) {
        return false
      } finally {
        this.loading = false
      }
    },
    async handleClickExchangeButton() {
      if (!this.formValue.code) {
        this.errors.code = '请输入兑换码'
        return
      }
      const isAvailable = await this.checkCodeIsAvailable()
      if (!isAvailable) {
        return
      }
      if (this.currentPlanCount > 0) {
        this.$refs.multiplePlanPopup.open()
      } else {
        this.handleConfirmExchange()
      }
    },
    async getCurrentPlanCount() {
      const res = await getAvailablePlans({
        page: 0,
        size: 100
      })
      this.currentPlanCount = res.totalElements
    },
    async handleConfirmExchange() {
      this.$refs.multiplePlanPopup.close()
      this.$refs.makeSurePopup.open()
    },
    async handleMakeSureExchange() {
      // 跳转到 exchange-plan-confirm 页面
      this.$refs.multiplePlanPopup.close()
      this.$refs.makeSurePopup.close()
      const pid = this.productInfo?.shopProduct?.product?.pid
      if (pid) {
        uni.navigateTo({
          url: `/packageWenet/pages/exchange-plan/confirm?pid=${pid}&code=${this.formValue.code}`
        })
      }
    },
    handleCancelExchange() {
      this.$refs.multiplePlanPopup.close()
    },
    handleCancelMakeSureExchange() {
      this.$refs.makeSurePopup.close()
    },
  },
  
}
</script>

<style lang="scss" scoped>
.exchange-plan {
  min-height: 100vh;
  background-color: #f4f4f4;
}

.form {
  padding: 20rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: $wenet-color-text-primary;
  margin-bottom: 16rpx;
}

.form .error-message {
  color: $wenet-color-error;
  font-size: 24rpx;
  margin-top: 10rpx;
}

.tips {
  padding: 20rpx;
  border-radius: 16rpx;
}

.tips-title {
  margin-bottom: 16rpx;
}

</style>
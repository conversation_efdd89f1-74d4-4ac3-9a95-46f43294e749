<template>
  <view class="container exchange-plan-confirm">
    <!-- 加载状态 -->
    <view v-if="!productInfo" class="loading-container">
      <n-loading />
    </view>
    
    <!-- 套餐信息卡片 -->
    <view v-else class="confirm-card">
      <view class="title">
        <view class="name">套餐信息</view>
      </view>
      <view class="divider"></view>
      
      <!-- 套餐名称和运营商标签 -->
      <view class="desc-name" v-if="productInfo.product">
        {{ productInfo.product.name }}
      </view>
      <view class="desc-tag">
        <view class="isp-text" v-if="ispInfo">
          <text :style="{ color: ispInfo.textColor, background: ispInfo.bgColor }">
            {{ ispInfo.name }}
          </text>
        </view>
      </view>
      
      <!-- 表单部分 -->
      <view class="form-container">
        <view class="divider"></view>
        
        <!-- 手机号输入 -->
        <view class="form-item">
          <view class="label">专享手机号</view>
          <n-input 
            v-model="formValue.username" 
            placeholder="请输入手机号"
            maxlength="11"
            :class="{'input-error': errors.username}"
          />
          <view v-if="errors.username" class="error-message">{{ errors.username }}</view>
        </view>
        
        <!-- 验证码输入 -->
        <view class="form-item" v-if="isSendMs">
          <view class="label">验证码</view>
          <view class="verify-code-container">
            <n-input 
              v-model="formValue.code" 
              placeholder="请输入验证码"
              maxlength="6"
              :class="{'input-error': errors.code}"
            />
            <view class="send-code-btn" @click="sendVerifyCode" :class="{'disabled': countdown > 0}">
              {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
            </view>
          </view>
          <view v-if="errors.code" class="error-message">{{ errors.code }}</view>
        </view>
        
        <!-- 运营商宽带密码 -->
        <view class="form-item" v-if="isNeedBind && dproxyBindType === 'PASSWORD'">
          <view class="label">运营商宽带密码</view>
          <n-input 
            v-model="formValue.password" 
            placeholder="运营商处生成的6位数密码"
            :class="{'input-error': errors.password}"
          />
          <view v-if="errors.password" class="error-message">{{ errors.password }}</view>
          <view class="extra-tip">您办理的校园网套餐包含运营商卡类业务，请绑定相应运营商宽带账号使用</view>
        </view>
        
        <!-- 返费规则说明 -->
        <view class="isp-des" v-if="ispFeeInfo">
          <view class="des-title">详情</view>
          <view class="des-content">{{ ispFeeInfo }}</view>
        </view>
      </view>
    </view>
    
    <!-- 底部确认按钮 -->
    <view class="confirm-btn-container">
      <NButton type="primary" block @click="handleSubmit" :loading="isSubmitting">
        {{ isSubmitting ? '提交中...' : '确认兑换' }}
      </NButton>
    </view>

    <uni-popup ref="exchangeSuccessPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="提示"
        content="兑换套餐成功"
        :before-close="true"
        @confirm="handleConfirmExchangeSuccess"
        @close="handleCancelExchangeSuccess"
        confirmText="查看套餐"
        cancelText="返回首页"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import NInput from '@/components/form/n-input'
import NLoading from '@/components/n-loading/n-loading.vue'
import UniPopupDialog from '@/components/uni-popup-dialog/uni-popup-dialog.vue'
import UniPopup from '@/components/uni-popup/uni-popup.vue'
import { getProductInfoByPid, sendSmsCode, sendSmsCodeForNewOrder, exchangePlan } from '@/packageWenet/api'
import { ouThatBindIspWithPassword } from '@/utils/config'
import NButton from '@/components/n-button'


const phoneRegular = /^1[3456789]\d{9}$/

// 运营商颜色配置
const IspColor = {
  CMCC: { textColor: "#2AA7FF", bgColor: '#e9f6ff' },
  CUCC: { textColor: "#D71E18", bgColor: '#FBE8E7' },
  CTCC: { textColor: "#FF8200", bgColor: '#FFF2E5' },
  EXCLUSIVE: { textColor: "#2AA7FF", bgColor: '#e9f6ff' }
}

// 运营商名称映射
const IspNameMap = {
  CMCC: '移动',
  CUCC: '联通',
  CTCC: '电信',
  EXCLUSIVE: '专享'
}

export default {
  components: {
    NInput,
    NLoading,
    UniPopupDialog,
    UniPopup,
    NButton
  },
  data() {
    return {
      productInfo: null,
      exchangeCode: '',
      // 表单数据
      formValue: {
        username: '',
        code: '',
        password: ''
      },
      
      // 表单错误
      errors: {
        username: '',
        code: '',
        password: ''
      },
      
      // 状态标记
      isSendMs: true, // 是否接收验证码
      isRefund: false, // 是否返话费
      isNeedBind: false, // 是否二次认证
      cmccOpenAccount: false, // 是否需要大网开户
      isInPhoneArea: false, // 是否校验号码库
      
      // 倒计时
      countdown: 0,
      timer: null,
      
      // 提交状态
      isSubmitting: false
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo || {}
    },
    ou() {
      return this.userInfo.ou || ''
    },
    uid() {
      return this.userInfo.uid || ''
    },
    // 运营商信息
    ispInfo() {
      if (!this.productInfo) return null
      
      const ispType = this.getFeatureValue('ISP')
      if (!ispType || !IspColor[ispType]) return null
      
      return {
        type: ispType,
        name: IspNameMap[ispType] || '未知',
        textColor: IspColor[ispType].textColor,
        bgColor: IspColor[ispType].bgColor
      }
    },
    // 返费信息
    ispFeeInfo() {
      if (!this.productInfo) return null
      return this.getFeatureValue('IspFeeInfo')
    },
    // 取值：PHONE、PASSWORD,为 PHONE 时输入手机号/验证码，为 PASSWORD 时输入宽带账号/密码
    dproxyBindType() {
      if (ouThatBindIspWithPassword.includes(this.ou)) {
        return 'PASSWORD'
      } else {
        return 'PHONE'
      }
    },
  },
  onLoad(options) {
    // 初始化用户手机号
    if (uni.getStorageSync('mobile')) {
      this.formValue.username = uni.getStorageSync('mobile')
    }
    
    // 获取产品信息
    this.getProductInfo(options.pid)
    if (options.code) {
      this.exchangeCode = options.code
    }
  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    // 获取产品信息
    async getProductInfo(pid) {
      try {
        const productInfo = await getProductInfoByPid(pid)
        this.productInfo = productInfo
        
        // 解析产品特性，设置相关状态
        this.parseProductFeatures()
      } catch (error) {
      }
    },
    
    // 解析产品特性
    parseProductFeatures() {
      if (!this.productInfo || !this.productInfo.product || !this.productInfo.product.features) {
        return
      }
      
      const features = this.productInfo.product.features
      if (!Array.isArray(features)) return
      
      // 重置状态
      const state = {
        isSendMs: true,
        isRefund: false,
        isNeedBind: false,
        cmccOpenAccount: false,
        isInPhoneArea: false
      }
      
      // 遍历特性，设置相关状态
      features.forEach(item => {
        const featureTypeId = item.featureType?.featureTypeId
        const description = item.description
        
        // 是否返费
        if (featureTypeId === 'IspFeeIncluded' && description === 'true') {
          state.isRefund = true
        }
        
        // 是否校验号码库
        if (featureTypeId === 'PHONE_VERIFICAITON_SET' && description === 'true') {
          state.isInPhoneArea = true
        }
        
        // 是否发送验证码
        if (featureTypeId === 'PHONE_VERIFICATION_REQUIRED') {
          state.isSendMs = description !== 'false'
        }
        
        // 是否支持二次认证
        if (featureTypeId === 'DPROXY_DESTINATION_NAME' && description) {
          state.isNeedBind = description
        }
        
        // 是否需要大网开户
        if (featureTypeId === 'cmccOpenAccount') {
          state.cmccOpenAccount = description === 'true'
        }
      })
      
      // 如果不返费，强制发送验证码
      if (state.isRefund === false) {
        state.isSendMs = true
      }
      
      // 更新状态
      Object.assign(this, state)
    },
    
    // 获取特性值
    getFeatureValue(featureTypeId) {
      if (!this.productInfo || !this.productInfo.product || !this.productInfo.product.features) {
        return null
      }
      
      const features = this.productInfo.product.features
      if (!Array.isArray(features)) return null
      
      const feature = features.find(item => item.featureType?.featureTypeId === featureTypeId)
      return feature ? feature.description : null
    },
    
    // 表单验证
    validate() {
      let isValid = true
      this.errors = {
        username: '',
        code: '',
        password: ''
      }
      
      // 验证手机号
      if (!this.formValue.username) {
        this.errors.username = '请输入手机号'
        isValid = false
      } else if (!phoneRegular.test(this.formValue.username)) {
        this.errors.username = '请输入正确的手机号'
        isValid = false
      }
      
      // 验证验证码
      if (this.isSendMs && !this.formValue.code) {
        this.errors.code = '请输入验证码'
        isValid = false
      }
      
      // 验证密码
      if (this.isNeedBind && this.dproxyBindType === 'PASSWORD') {
        if (this.formValue.password && /\s/.test(this.formValue.password)) {
          this.errors.password = '密码不能包含空格'
          isValid = false
        }
      }
      
      return isValid
    },
    
    // 发送验证码
    async sendVerifyCode() {
      // 如果正在倒计时，不能再次发送
      if (this.countdown > 0) return
      
      // 验证手机号
      if (!this.formValue.username || !phoneRegular.test(this.formValue.username)) {
        this.errors.username = '请输入正确的手机号'
        return
      }
      
      try {
        // 发送验证码
        const type = this.isRefund && this.isSendMs ? null : 'ORDERPHONE'
        if (type === 'ORDERPHONE') {
          await sendSmsCode({
            phone: this.formValue.username,
            type
          })
        } else {
          await sendSmsCodeForNewOrder({
            phone: this.formValue.username,
            pid: this.productInfo.product.pid,
          })
        }
        this.countdown = 60
        this.timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.timer)
          }
        }, 1000)
        
        uni.showToast({
          title: '验证码已发送',
          icon: 'none'
        })
      } catch (error) {
      }
    },
    
    // 提交订单
    async handleSubmit() {
      // 表单验证
      if (!this.validate()) return
      
      this.isSubmitting = true
      
      try {
        // 构建规格数组
        const specs = []
        const phone = this.formValue.username
        const password = this.formValue.password
        
        // 是否返话费
        if (this.isRefund) {
          specs.push({ fid: 'IspFeeIncluded', value: phone })
        }
        
        // 是否向大网开户套餐提交手机号
        if (this.cmccOpenAccount) {
          specs.push({ fid: 'cmccOpenAccount', value: phone })
        }
        
        // 是否二次认证
        if (this.isNeedBind) {
          // 密码处理逻辑
          let dproxyPassword = '123456'
          if (this.dproxyBindType === 'PASSWORD') {
            dproxyPassword = password || '123456'
          }
          
          specs.push({ fid: 'DPROXY_USERNAME', value: phone })
          specs.push({ fid: 'DPROXY_PASSWORD', value: dproxyPassword })
        }
        
        // 是否校验号码库
        if (this.isInPhoneArea) {
          specs.push({ fid: 'PHONE_VERIFICAITON_SET', value: phone })
        }
        
        // 所有套餐都用户都需要联系方式
        specs.push({ fid: 'purchaseCheckin', value: phone })
        
        // 构建订单数据
        const order = {
          activeCode: this.exchangeCode,
          order: {
            items: [
              {
                pid: this.productInfo.product.pid,
                pricingId: this.productInfo.price.id,
                quantity: 1,
                specs,
                unitPrice: this.productInfo.price.value
              }
            ],
            verifyCode: this.formValue.code
          }
        }
        
        // 提交订单
        const res =await exchangePlan(order)
        console.log('res', res)
        this.$refs.exchangeSuccessPopup.open()
      } catch (error) {
        console.log(error)
      } finally {
        this.isSubmitting = false
      }
    },
    handleConfirmExchangeSuccess() {
      this.$refs.exchangeSuccessPopup.close()
      uni.navigateTo({
        url: '/packageWenet/pages/plans/plans'
      })
    },
    handleCancelExchangeSuccess() {
      this.$refs.exchangeSuccessPopup.close()
      uni.switchTab({
        url: '/pages/index/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.exchange-plan-confirm {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f4f4f4;
}

.confirm-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 120rpx;
}

.title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  color: $wenet-color-text-primary;
}

.divider {
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 20rpx 0;
}

.desc-name {
  font-size: 28rpx;
  color: $wenet-color-text-primary;
  margin-bottom: 16rpx;
}

.desc-tag {
  margin-bottom: 20rpx;
}

.isp-text text {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.form-container {
  margin-top: 20rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: $wenet-color-text-primary;
  margin-bottom: 16rpx;
}

.verify-code-container {
  display: flex;
  align-items: center;
}

.send-code-btn {
  min-width: 180rpx;
  height: 66rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 26rpx;
  color: $wenet-color-brand;
  margin-left: 20rpx;
}

.send-code-btn.disabled {
  color: $wenet-color-text-secondary;
}

.error-message {
  color: red;
  font-size: 24rpx;
  margin-top: 8rpx;
}

.input-error {
  border: 1px solid red;
}

.extra-tip {
  font-size: 24rpx;
  color: $wenet-color-text-secondary;
  margin-top: 8rpx;
}

.isp-des {
  margin-top: 30rpx;
}

.des-title {
  font-size: 28rpx;
  color: $wenet-color-text-primary;
  margin-bottom: 10rpx;
}

.des-content {
  font-size: 26rpx;
  color: $wenet-color-text-secondary;
  line-height: 1.5;
}

</style>
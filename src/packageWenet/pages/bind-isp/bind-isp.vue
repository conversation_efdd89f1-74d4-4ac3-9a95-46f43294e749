<template>
  <view class="bind-isp">
    <view class="bind-isp__header">
      <view class="bind-isp__title">绑定运营商账号</view>
      <view class="bind-isp__desc">您办理的校园网套餐包含运营商卡类业务，请绑定相应运营商宽带账号使用</view>
    </view>

    <!-- 手机号绑定方式 -->
    <view v-if="dproxyBindType === 'PHONE'" class="bind-isp__form">
      <view class="form-item">
        <view class="form-label">手机号</view>
        <n-input 
          v-model="formValue.dproxyUsername" 
          placeholder="要绑定的手机号"
          :class="{'input-error': errors.dproxyUsername}"
        />
        <view v-if="errors.dproxyUsername" class="error-message">{{ errors.dproxyUsername }}</view>
      </view>

      <view class="form-item">
        <view class="form-label">验证码</view>
        <view class="verify-code-container">
          <n-input 
            v-model="formValue.verifyCode" 
            placeholder="请输入验证码"
            :class="{'input-error': errors.verifyCode}"
          />
          <view class="send-code-btn" @click="sendVerifyCode" :class="{'disabled': countdown > 0}">
            {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
          </view>
        </view>
        <view v-if="errors.verifyCode" class="error-message">{{ errors.verifyCode }}</view>
      </view>

      <view class="form-item">
        <NButton :loading="isSubmitting" type="primary" block @click="handleSubmit">{{ isSubmitting ? '绑定中...' : '绑定' }}</NButton>
      </view>

      <view class="notice">
        <view class="notice-title">* 注意：</view>
        <view class="notice-item">1. 一个手机号限制绑定一次，为维护您的利益安全，请勿将手机号绑定第三人账期。</view>
        <view class="notice-item">2. 若要换绑自己的手机号，请在Wenet联系客服处理。</view>
      </view>
    </view>

    <!-- 账号密码绑定方式 -->
    <view v-else class="bind-isp__form">
      <view class="form-item">
        <view class="form-label">运营商账号</view>
        <n-input 
          v-model="formValue.dproxyUsername" 
          placeholder="请输入运营商账号"
          :class="{'input-error': errors.dproxyUsername}"
        />
        <view v-if="errors.dproxyUsername" class="error-message">{{ errors.dproxyUsername }}</view>
      </view>

      <view class="form-item">
        <view class="form-label">运营商密码</view>
        <n-input 
          v-model="formValue.dproxyPassword" 
          type="password"
          placeholder="请输入密码"
          :class="{'input-error': errors.dproxyPassword}"
        />
        <view v-if="errors.dproxyPassword" class="error-message">{{ errors.dproxyPassword }}</view>
        <view class="password-tip">此密码只用来绑定运营商账号，不会作为上网密码使用。上网密码请使用WeNet平台登录密码。</view>
      </view>

      <NButton :loading="isSubmitting" type="primary" block @click="handleSubmit">{{ isSubmitting ? '绑定中...' : '绑定' }}</NButton>
    </view>
  </view>
</template>

<script>
import NInput from '@/components/form/n-input'
import { bindDproxy, sendSmsCode, checkSmsCode } from '@/packageWenet/api'
import { ouThatBindIspWithPassword } from '@/utils/config'
import NButton from '@/components/n-button'

const MSG_TYPE = 'REBIND'

export default {
  components: {
    NInput,
    NButton
  },
  data() {
    return {
      formValue: {
        dproxyUsername: '',
        dproxyPassword: '',
        verifyCode: ''
      },
      errors: {
        dproxyUsername: '',
        dproxyPassword: '',
        verifyCode: ''
      },
      countdown: 0,
      timer: null,
      isSubmitting: false,
      query: {}
    }
  },
  computed: {
    ou() {
      return this.$store.state.userInfo?.ou || ''
    },
    dproxyBindType() {
      if (ouThatBindIspWithPassword.includes(this.ou)) {
        return 'PASSWORD'
      } else {
        return 'PHONE'
      }
    },
  },
  onLoad(options) {
    this.query = options
    
    // 如果有传入的账号，设置到表单中
    if (options.dproxyUsername) {
      this.formValue.dproxyUsername = options.dproxyUsername
    }
  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    // 表单验证
    validate() {
      let isValid = true
      this.errors = {
        dproxyUsername: '',
        dproxyPassword: '',
        verifyCode: ''
      }
      
      // 验证用户名
      if (!this.formValue.dproxyUsername) {
        this.errors.dproxyUsername = '请输入' + (this.dproxyBindType === 'PHONE' ? '手机号' : '运营商账号')
        isValid = false
      } else if (this.dproxyBindType === 'PHONE') {
        // 手机号格式验证
        const phoneRegular = /^1[3456789]\d{9}$/
        if (!phoneRegular.test(this.formValue.dproxyUsername)) {
          this.errors.dproxyUsername = '请输入正确的手机号'
          isValid = false
        }
      }
      
      // 验证密码
      if (this.dproxyBindType === 'PASSWORD' && !this.formValue.dproxyPassword) {
        this.errors.dproxyPassword = '请输入运营商密码'
        isValid = false
      } else if (this.dproxyBindType === 'PASSWORD' && /\s/.test(this.formValue.dproxyPassword)) {
        this.errors.dproxyPassword = '密码不能包含空格'
        isValid = false
      }
      
      // 验证验证码
      if (this.dproxyBindType === 'PHONE' && !this.formValue.verifyCode) {
        this.errors.verifyCode = '请输入验证码'
        isValid = false
      }
      
      return isValid
    },
    
    // 发送验证码
    async sendVerifyCode() {
      // 如果正在倒计时，不能再次发送
      if (this.countdown > 0) return
      
      // 验证手机号
      const phoneRegular = /^1[3456789]\d{9}$/
      if (!this.formValue.dproxyUsername || !phoneRegular.test(this.formValue.dproxyUsername)) {
        this.errors.dproxyUsername = '请输入正确的手机号'
        return
      }
      
      try {
        await sendSmsCode({
          phone: this.formValue.dproxyUsername,
          type: MSG_TYPE
        })
        
        // 开始倒计时
        this.countdown = 60
        this.timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.timer)
          }
        }, 1000)
        
        uni.showToast({
          title: '验证码已发送',
          icon: 'none'
        })
      } catch (error) {
      }
    },
    
    // 验证短信验证码
    async checkMsgCode() {
      try {
        const result = await checkSmsCode({
          phone: this.formValue.dproxyUsername,
          code: this.formValue.verifyCode,
          type: MSG_TYPE
        })
        return result
      } catch (error) {
        return false
      }
    },
    
    // 绑定运营商账号
    async bindIsp() {
      try {
        const { pid, uid, sid, dproxyISP, oid } = this.query
        
        // 如果是手机号方式且没有密码，使用手机号后6位作为密码
        let password = this.formValue.dproxyPassword
        if (this.dproxyBindType === 'PHONE' && !password) {
          const username = this.formValue.dproxyUsername
          password = username.substring(username.length - 6)
        }

        const params = {
          pid,
          username: this.formValue.dproxyUsername,
          password: password || '123456',
          sid,
          uid,
          oid,
        }
        if (this.dproxyBindType === 'PHONE') {
          params.extField1 = this.formValue.dproxyUsername
        }
        
        // 调用绑定API
        await bindDproxy(params)
        
        uni.showToast({
          title: '绑定成功',
          icon: 'success'
        })
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
      }
    },
    
    // 提交表单
    async handleSubmit() {
      // 表单验证
      if (!this.validate()) return
      
      this.isSubmitting = true
      
      try {
        // 如果是手机号绑定方式，需要先验证短信验证码
        if (this.dproxyBindType === 'PHONE') {
          const isCodeValid = await this.checkMsgCode()
          if (!isCodeValid) {
            this.isSubmitting = false
            return
          }
        }
        
        // 绑定运营商账号
        await this.bindIsp()
      } finally {
        this.isSubmitting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bind-isp {
  padding: 30rpx;
  background-color: #fff;
  min-height: 100vh;
}

.bind-isp__header {
  margin-bottom: 40rpx;
}

.bind-isp__title {
  font-size: 36rpx;
  font-weight: bold;
  color: $wenet-color-text-primary;
  margin-bottom: 16rpx;
}

.bind-isp__desc {
  font-size: 28rpx;
  color: $wenet-color-text-secondary;
  line-height: 1.5;
}

.bind-isp__form {
  padding: 20rpx 0;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: $wenet-color-text-primary;
  margin-bottom: 16rpx;
}

.verify-code-container {
  display: flex;
  align-items: center;
}

.send-code-btn {
  min-width: 200rpx;
  height: 84rpx;
  line-height: 84rpx;
  text-align: center;
  font-size: 26rpx;
  color: $wenet-color-brand;
  margin-left: 20rpx;
}

.send-code-btn.disabled {
  color: $wenet-color-text-secondary;
}

.error-message {
  color: $wenet-color-error;
  font-size: 24rpx;
  margin-top: 8rpx;
}

.input-error {
  border: 1px solid $wenet-color-error;
}

.password-tip {
  font-size: 24rpx;
  color: $wenet-color-error;
  margin-top: 8rpx;
  line-height: 1.5;
}

.notice {
  margin-top: 40rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
}

.notice-title {
  font-size: 28rpx;
  color: $wenet-color-error;
  margin-bottom: 10rpx;
}

.notice-item {
  font-size: 26rpx;
  color: $wenet-color-error;
  line-height: 1.5;
  margin-bottom: 10rpx;
}
</style>
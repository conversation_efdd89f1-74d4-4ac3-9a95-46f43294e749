<template>

  <view>
    在线设备
  </view>
</template>

<script>
import callWenetApi from '@/utils/call-wenet-api'

export default {
  data() {
    return {}
  },
  mounted() {

  },
  methods: {
    getDevices() {
      callWenetApi({
        url: '/api/v1/wenet/cloud/downstream',
        method: 'POST',
        data: {
          ou: '10000',
          service: 'portal-service',
          method: 'POST',
          body: {},
          uri: "/api/v3/session/wechat/list"
        }
      })
    }
  },
  onLoad() {
    this.getDevices()
  }
}
</script>
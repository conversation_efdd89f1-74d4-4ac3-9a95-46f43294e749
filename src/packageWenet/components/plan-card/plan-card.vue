<template>
  <view class="plan-card">
    <view class="plan-card-inner">
      <!-- 卡片头部 -->
      <view class="plan-header">
        <view class="plan-title-area text-primary text-size-14 font-weight-600">
          {{ ou === '11600' ? plan.alias : plan.productName }}
        </view>
        
        <!-- 右上角状态展示 -->
        <view v-if="activeTab !== 'HAS_USED'" class="status-area">
          <!-- 当是使用中的账期或允许多账期同时使用时 -->
          <template v-if="plan.default_plan || plan.enableMultPlan">
            <!-- 优先展示未激活状态 -->
            <view v-if="plan.ispOnline && !plan.isBind" class="status-tag not-activated">未激活</view>
            <view v-else class="status-tag using">使用中</view>
          </template>
          
          <!-- 其他情况 -->
          <template v-else>
            <view v-if="plan.ispOnline && !plan.isBind" class="status-tag not-activated">未激活</view>
            <view v-else-if="(plan.ispOnline && plan.isBind) || (!plan.ispOnline && plan.isIspNetwork)" class="status-tag activated">已激活</view>
            
            <!-- 启用按钮 -->
            <NButton
              v-if="showEnableButton"
              type="secondary"
              @click="enablePlan"
              :loading="isEnabling"
            >
              {{ isEnabling ? '启用中...' : '启用' }}
            </NButton>
          </template>
        </view>
      </view>
      
      <!-- 卡片内容 -->
      <view class="plan-info">

        <!-- 剩余流量和剩余时长信息 -->
        <view v-if="plan.default_plan" class="remaining-info">
          <view v-if="flowInfo.flow" class="remaining-item">
            <view class="remaining-label">剩余流量：</view>
            <view class="remaining-value" :class="{'value-red': flowInfo.flowColorIsRed}">
              {{ flowInfo.flow }}
              <text v-if="flowInfo.flowUnit" class="unit" :class="{'value-red': flowInfo.flowColorIsRed}">{{ flowInfo.flowUnit }}</text>
            </view>
          </view>
          <view class="remaining-item">
            <view class="remaining-label">剩余时长：</view>
            <view class="remaining-value" :class="{'value-red': flowInfo.timeColorIsRed}">
              {{ flowInfo.time }}
              <text v-if="flowInfo.timeUnit" class="unit" :class="{'value-red': flowInfo.timeColorIsRed}">{{ flowInfo.timeUnit }}</text>
            </view>
          </view>
        </view>
        <view class="info-row">
          <view class="info-item">
            <view class="text-primary text-size-14 font-weight-400">创建日期</view>
            <view class="text-secondary text-size-14 font-weight-400">{{ formatTimeExceptSecond(new Date(plan.createdAt)) || '--' }}</view>
          </view>
        </view>
        <view class="info-row">
          <view class="info-item">
            <view class="text-primary text-size-14 font-weight-400">生效日期</view>
            <view class="text-secondary text-size-14 font-weight-400">{{ plan.startAt ? formatTimeExceptSecond(new Date(plan.startAt)) : '--' }}</view>
          </view>
        </view>
        <view class="info-row">
          <view class="info-item">
            <view class="text-primary text-size-14 font-weight-400">截止日期</view>
            <view class="text-secondary text-size-14 font-weight-400">{{ discontinueAt }}</view>
          </view>
        </view>

        
        <!-- 已过期且有运营商账号时显示 -->
        <view v-if="activeTab === 'HAS_USED' && plan.dproxyUsername" class="info-row">
          <view class="info-item">
            <view class="label">运营商账号</view>
            <view class="value">{{ plan.dproxyUsername }}</view>
          </view>
        </view>
        
        <!-- 速率包信息 -->
        <view v-if="hasSpeedPack" class="speed-pack-info">
          <view class="speed-pack-name">{{ plan.speedPackName }}</view>
          <view v-if="plan.speedBandwidth || plan.speedInputBandwidth" class="speed-bandwidth">
            下行: {{ formatBandwidth(plan.speedBandwidth) }} 
            <text v-if="plan.speedInputBandwidth">上行: {{ formatBandwidth(plan.speedInputBandwidth) }}</text>
          </view>
          <view v-if="speedTimeInfo" class="speed-time">{{ speedTimeInfo }}</view>
        </view>
        
        <view v-if="showFlowInfo && !plan.default_plan" class="flow-info">
          <view class="flow-label">剩余流量</view>
          <view class="flow-value">{{ formatFlow(plan.flowRemaining) }}</view>
          <view v-if="plan.flowQuota" class="flow-progress-container">
            <view class="flow-progress-bg">
              <view class="flow-progress-bar" :style="{width: flowPercentage + '%'}"></view>
            </view>
            <view class="flow-percentage">{{ flowPercentage }}%</view>
          </view>
        </view>
      </view>
      
      <!-- 卡片底部按钮区域 -->
      <view class="plan-footer">
        <!-- 激活按钮 -->
        <view 
          v-if="activeTab !== 'HAS_USED' && plan.ispOnline && !plan.isBind" 
          class="action-btn active-btn"
          @click="navigateToActive"
        >
          激活
        </view>
        
        <!-- 绑定运营商账号 -->
        <view v-if="plan.needIspBind && activeTab !== 'HAS_USED' && !plan.isIspNetwork" class="bind-isp-area">
          <NButton type="primary" @click="bindIsp">绑定运营商账号</NButton>
        </view>
        
        <!-- 修改激活信息 -->
        <NButton
          v-if="activeTab !== 'HAS_USED' && ((plan.ispOnline && plan.isBind) || (!plan.ispOnline && plan.isIspNetwork))"
          type="secondary"
          @click="navigateToUpdateActive"
        >
          修改激活信息
        </NButton>
      </view>
    </view>
  </view>
</template>

<script>
import { formatTimeExceptSecond } from '@/utils/util.js'
import { activePlan } from '@/packageWenet/api'
import NButton from '@/components/n-button'

export default {
  props: {
    plan: {
      type: Object,
      required: true
    },
    activeTab: {
      type: String,
      required: true
    }
  },
  components: {
    NButton
  },
  data() {
    return {
      isEnabling: false,
      showBindTipVisible: false
    }
  },
  computed: {
    showFlowInfo() {
      return this.plan.flowRemaining !== undefined && this.plan.status === 'ACTIVE'
    },
    discontinueAt(){
      if (this.plan.exhaustedAt) {
        return formatTimeExceptSecond(new Date(this.plan.exhaustedAt));
      }
      if (this.plan.expireAt) {
        return formatTimeExceptSecond(new Date(this.plan.expireAt));
      }
      if (this.plan.expiredAt) {
        return formatTimeExceptSecond(new Date(this.plan.expiredAt));
      }
      return formatTimeExceptSecond(new Date(this.plan.discontinueAt));
    },
    userInfo() {
      return this.$store.state.userInfo
    },
    originalWenetAccountInfo() {
      return this.$store.state.originalWenetAccountInfo
    },
    ou() {
      return this.userInfo?.ou
    },
    flowPercentage() {
      if (!this.plan.flowQuota || !this.plan.flowRemaining) return 0
      const percentage = Math.floor((this.plan.flowRemaining / this.plan.flowQuota) * 100)
      return percentage > 100 ? 100 : percentage
    },
    // 是否显示启用按钮
    showEnableButton() {
      // 西北大学不让学生启用待生效账期
      const isPendingDisabled = this.activeTab !== 'IS_USING' && this.PENDING_PLAN_ACTIVE_BUTTON_DISABLED
      if (isPendingDisabled) return false
      
      // 非使用中且满足条件时显示启用按钮
      return !this.plan.default_plan && (!this.plan.isIspNetwork || (this.plan.isIspNetwork && this.plan.ispOnline && Boolean(this.plan.isBind)) || (this.plan.isIspNetwork && !this.plan.ispOnline))
    },
    // 是否有速率包信息
    hasSpeedPack() {
      return this.plan.speedPackName || this.plan.speedBandwidth || this.plan.speedInputBandwidth
    },
    // 速率包时间信息
    speedTimeInfo() {
      if (this.plan.speedFinishAt) {
        return `到期时间: ${formatTimeExceptSecond(new Date(this.plan.speedFinishAt))}`
      }
      if (this.plan.speedFinishedAt) {
        return `到期时间: ${formatTimeExceptSecond(new Date(this.plan.speedFinishedAt))}`
      }
      if (this.plan.speededAt) {
        return `开通时间: ${formatTimeExceptSecond(new Date(this.plan.speededAt))}`
      }
      return ''
    },
    // 是否禁用PENDING状态下的激活按钮
    PENDING_PLAN_ACTIVE_BUTTON_DISABLED() {
      return this.ou === '10697' // 西北大学不提供启用按钮
    },
    // 剩余流量和剩余时长信息
    flowInfo() {
      const result = {
        flow: null,
        flowUnit: null,
        time: this.getPlanTime(),
        timeUnit: '',
        flowColorIsRed: false,
        timeColorIsRed: false
      }
      
      // 如果不是生效套餐，直接返回空结果
      if (!this.plan.default_plan) {
        return result
      }
      
      const { flowQuota, timeQuota, timeRemaining, flowRemaining } = this.plan
      
      // 处理流量信息
      if (flowQuota !== 0 && flowRemaining > 1000 * 1000) {
        result.flow = (flowRemaining / 1000 / 1000).toFixed(1)
        result.flowUnit = 'GB'
      } else if (flowQuota !== 0 && flowRemaining > 1000 && flowRemaining < 1000 * 1000) {
        result.flow = (flowRemaining / 1000).toFixed(1)
        result.flowUnit = 'MB'
        result.flowColorIsRed = result.flow < 50
      } else if (flowQuota !== 0 && flowRemaining < 1000) {
        result.flow = Math.round(flowRemaining)
        result.flowUnit = 'KB'
        result.flowColorIsRed = true
      } else if (flowQuota === 0 && timeQuota === 0) {
        result.flow = '无限流量'
      }
      
      // 处理时长信息
      if (timeQuota !== 0 && timeRemaining >= 36000) {
        result.time = (timeRemaining / 60 / 60).toFixed(1)
        result.timeUnit = '小时'
      } else if (timeQuota !== 0 && timeRemaining > 3600 && timeRemaining < 36000) {
        result.time = (timeRemaining / 60 / 60).toFixed(1)
        result.timeUnit = '小时'
        result.timeColorIsRed = true
      } else if (timeQuota !== 0 && timeRemaining <= 3600) {
        result.time = (timeRemaining / 60).toFixed(1)
        result.timeUnit = '分钟'
        result.timeColorIsRed = true
      }
      
      return result
    }
  },
  methods: {
    formatTimeExceptSecond,
    formatDate(dateStr) {
      if (!dateStr) return '--'
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    },
    formatFlow(flow) {
      if (flow === undefined) return '--'
      // 转换为MB
      const mb = flow / 1024 / 1024
      if (mb < 1024) {
        return mb.toFixed(2) + 'MB'
      } else {
        // 转换为GB
        return (mb / 1024).toFixed(2) + 'GB'
      }
    },
    formatBandwidth(bandwidth) {
      if (!bandwidth) return '--'
      // 转换为Mbps
      const mbps = bandwidth / 1000 / 1000
      return mbps + 'Mbps'
    },
    getPlanTime() {
      // 这里根据实际情况实现获取计划时间的逻辑
      // 暂时返回天数
      if (this.plan.duration) {
        return this.plan.duration
      }
      return '--'
    },
    bindIsp() {
      // 跳转到绑定运营商页面
      const params = {
        pid: this.plan.pid,
        oid: this.plan.oid,
        uid: this.plan.uid,
        sid: this.plan.sid,
        dproxyISP: this.plan.dproxyISP,
        dproxyUsername: this.plan.dproxyUsername || ''
      }
      
      const query = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')
      
      uni.navigateTo({
        url: `/packageWenet/pages/bind-isp/bind-isp?${query}`
      })
    },
    navigateToActive() {
      // 跳转到激活页面
      const params = {
        dproxyISP: this.plan.isp,
        pid: this.plan.pid,
        oid: this.plan.oid,
        sid: this.plan.sid,
        productId: this.plan.productId,
        action: 'active',
        from: 'plan',
        isOnline: !!this.plan.ispOnline ? 'true' : 'false'
      }
      
      const query = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')
      
      uni.navigateTo({
        url: `/packageWenet/pages/active-isp-network/submit?${query}`,
      })
    },
    navigateToUpdateActive() {
      // 跳转到修改激活信息页面
      const params = {
        pid: this.plan.pid,
        oid: this.plan.oid,
        uid: this.plan.uid,
        sid: this.plan.sid,
        dproxyISP: this.plan.dproxyISP,
        dproxyUsername: this.plan.dproxyUsername || '',
        productId: this.plan.productId,
        action: 'update',
        from: 'plan',
        isOnline: !!this.plan.ispOnline ? 'true' : 'false'
      }
      
      const query = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')
      
      uni.navigateTo({
        url: `/packageWenet/pages/active-isp-network/submit?${query}`
      })
    },
    async enablePlan() {
      // 启用当前账期
      this.isEnabling = true
      try {
        await activePlan(this.plan.pid)
        setTimeout(() => {
          // 刷新列表
          this.$emit('refresh')
          this.isEnabling = false
          uni.showToast({
            title: '启用成功',
            icon: 'success'
          })
        }, 3000)
      } catch (error) {
        this.isEnabling = false
          uni.showToast({
            title: '启用失败',
            icon: 'none'
          })
      }
    },
  }
}
</script>

<style scoped lang="scss">
.plan-card {
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
}

.plan-card-inner {
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
}

/* 卡片头部 */
.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 19rpx;
  position: relative;
  padding: 32rpx 40rpx 19rpx;
  border-bottom: 2rpx solid #BDBDBD;

}

.plan-title-area {
  flex-direction: column;
  width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-area {
  display: flex;
  align-items: center;
}

.status-tag {
  font-size: 24rpx;
  font-weight: 700;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.status-tag.using {
  color: $wenet-color-brand;
  position: relative;
}

.status-tag.using::before {
  content: '';
  display: inline-block;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: $wenet-color-brand;
  position: absolute;
  top: 50%;
  left: -8rpx;
  transform: translateY(-50%);
}

.status-tag.not-activated {
  color: $wenet-color-error;
}

.status-tag.activated {
  color: $wenet-color-success;
}

/* 卡片内容 */
.plan-info {
  padding: 22rpx 40rpx;
  display: flex;
  flex-direction: column;
  row-gap: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
}

.info-item {
  flex: 1;
  display: flex;
  width: 100%;
  justify-content: space-between;
}

/* 剩余流量和时长信息 */
.remaining-info {
  display: flex;
  justify-content: space-between;
}

.remaining-item {
  display: flex;
  align-items: center;
}

.remaining-label {
  color: $wenet-color-text-secondary;
  font-size: 26rpx;
}

.remaining-value {
  color: $wenet-color-text-primary;
  font-size: 30rpx;
  font-weight: 600;
}

.value-red {
  color: $wenet-color-error !important;
}

.unit {
  font-size: 24rpx;
  margin-left: 4rpx;
}

/* 速率包信息 */
.speed-pack-info {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.speed-pack-name {
  font-size: 26rpx;
  font-weight: 600;
  color: $wenet-color-text-primary;
  margin-bottom: 8rpx;
}

.speed-bandwidth {
  font-size: 24rpx;
  color: $wenet-color-text-primary;
  margin-bottom: 4rpx;
}

.speed-time {
  font-size: 24rpx;
  color: $wenet-color-text-secondary;
}

/* 流量信息 */
.flow-info {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 16rpx;
}

.flow-label {
  color: $wenet-color-text-secondary;
  font-size: 26rpx;
  margin-bottom: 8rpx;
}

.flow-value {
  color: $wenet-color-brand;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.flow-progress-container {
  display: flex;
  align-items: center;
}

.flow-progress-bg {
  flex: 1;
  height: 12rpx;
  background-color: #eee;
  border-radius: 6rpx;
  overflow: hidden;
  margin-right: 16rpx;
}

.flow-progress-bar {
  height: 100%;
  background: linear-gradient(to right, $wenet-color-brand, wenet-color-brand-opacity(0.5));
  border-radius: 6rpx;
}
.flow-percentage {
  font-size: 24rpx;
  color: $wenet-color-text-secondary;
  min-width: 60rpx;
}

/* 卡片底部 */
.plan-footer {
  margin-top: 30rpx;
  padding: 0 40rpx 30rpx;
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  flex-wrap: wrap;
}

.bind-isp-area {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.question-icon {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: $wenet-color-text-secondary;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
}
</style>
import callWenetApi from '@/utils/call-wenet-api';
import dayjs from 'dayjs';

export const getPlans = ({status = '', page = 0, size = 100} = {}) => callWenetApi({url: '/api/v2/plan/plans', method: 'GET', data: { useStatus: status, page, size}})

// 获取可用账期
export const getAvailablePlans = ({page = 0, size = 100} = {}) => callWenetApi({url: '/api/v1/plans', method: 'GET', data: {page, size}})

export const activePlan = (planId) => callWenetApi({
  url: '/api/v1/self/activate/plan',
  method: 'GET',
  data: {
    pid: planId
  }
})

export const restPwd = (passwordStr) => callWenetApi({url: `/api/v1/auth/password/wx?${passwordStr}`, method: 'POST'})

export const sendSmsCode = ({phone, type}) => callWenetApi({url: `/api/v1/verifyCode?phone=${phone}&type=${type}`, method: 'POST'})

export const checkSmsCode = ({phone, code, type}) => callWenetApi({url: `/api/v1/verify?phone=${phone}&verifyCode=${code}&type=${type}`, method: 'POST'})

export const bindDproxy = ({pid, username, password, uid, sid, oid, extField1}) => {
  let queryString = `pid=${pid}&username=${username}&password=${password}&uid=${uid}&sid=${sid}&oid=${oid}`
  if (extField1) {
    queryString += `&extField1=${extField1}`
  }
  return callWenetApi({
    url: `/api/v1/plan/wenet/bindIspAccount?${queryString}`,
    method: 'POST',
  })
}
  
export const bindPhone = (username, verifyCode) => callWenetApi({url: `/api/v1/bindUser?username=${username}&verifyCode=${verifyCode}`, method: 'POST'})

export const sendSmsCodes = (phone, type) => {
    const queryString = `/api/v1/verifyCode2?phone=${phone}&type=${type}`;
    return callWenetApi({url: queryString, method: 'POST'})
}

// 验证是否注册
export const checkPhoneIsRegister = (phone) => {
    return callWenetApi({url: `/v1/account/user/${phone}/person`, method: 'GET'})
}

export const rebindPhone = ({newUsername, newUsernameVerifyCode}) => { 
    return callWenetApi({url: `/api/v1/rebind/wx?newUsername=${newUsername}&newUsernameVerifyCode=${newUsernameVerifyCode}`, method: 'POST'})
}

export const getDeviceList = (ou) => {
  return callWenetApi({
    url: '/api/v1/wenet/cloud/downstream',
    method: 'POST',
    data: {
      ou: ou,
      service: 'portal-service',
      method: 'POST',
      body: {},
      uri: "/api/v3/session/wechat/list"
    }
  })
}

export const offline = ({ ou, acctSessionId, nasIpAddress, userIp }) => {
  return callWenetApi({
    url: '/api/v1/wenet/cloud/downstream',
    method: 'POST',
    data: {
      ou,
      service: 'radacct-service',
      uri: '/api/v2/disconnect/request',
      method: "POST",
      body: {
        userRequestList: [
          {
            acctSessionId,
            nasIp: nasIpAddress,
            userIp: userIp,
          }
        ]
      }
    }
  })
}
// 宽带激活相关接口
export const getOfflineIspNetworkProd = (params) => callWenetApi({url: '/v1/projection/org/getOffline/org/prodFuse', method: 'GET', data: params})
export const getOnlineIspNetworkProd = (params) => callWenetApi({url: '/v1/projection/org/getOnline/org/prodFuse', method: 'GET', data: params})
export const checkHasActivedIspNetworkProd = (params) => callWenetApi({url: '/v2/projection/has/isp/bind/plan', method: 'GET', data: params})
export const getToBeActivedPlan = (params) => callWenetApi({url: '/api/v2/plan/wenet/fusePlans', method: 'GET', data: params})
export const placeAnOrderForOfflineProd = (params) => callWenetApi({url: '/api/v1/self/fuseOrder', method: 'POST', data: params})

export const getSessions = ({ ou, lastDay, page=0, uid }) => {
  const params = {
    uid: uid,
    startTime: dayjs().subtract(lastDay, 'days').format(),
    endTime: dayjs().format(),
    page: page,
    size: 5,
  };
  return callWenetApi({
    url: '/api/v1/wenet/cloud/downstream',
    method: 'POST',
    data: {
      method: 'POST',
      ou,
      service: 'radacct-service',
      uri: '/api/v2/radacct/search',
      body: {
        ...params
      }
    }
  })
};  
// 套餐兑换相关接口

/**
 * 根据code获取产品信息
 * @param {*} code 
 * @returns 
 */
export const getProductInfoByCode = (code) => callWenetApi({url: `/api/v1/sale/${code}`, method: 'GET'})

/**
 * 套餐兑换
 * @param {*} params 
 * @returns 
 */
export const exchangePlan = (params) => callWenetApi({url: `/api/v1/sale/activation`, method: 'POST', data: params})
  
/**
 * 根据pid获取bas产品信息
 * @param {*} pid 
 * @returns 
 */
export const getProductInfoByPid = (pid) => callWenetApi({url: `/api/v1/shop/product/${pid}`, method: 'GET'})

export const sendSmsCodeForNewOrder = ({phone, pid}) => callWenetApi({
  url: `/api/v1/order/phone?phone=${phone}&pid=${pid}`,
  method: 'POST',
})

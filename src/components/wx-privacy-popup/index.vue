<template>
  <popup :is-show="needAuthorization" >
    <view class="content">
      <text class="title">用户隐私保护指引</text>
      <view class="content__p">
        <text>欢迎使用</text>
        <text style="font-weight: bold;">{{ wxMpName }}小程序</text>。
        <text>我们将通过</text>
        <text class="content__text link" @click="handleOpenPrivacyContract">{{ privacyContractName }}</text>
        <text>帮助您了解我们收集、使用和存储个人信息的情况。</text>
      </view>
      <view class="content__p">
        <text>当您使用一些功能服务时，我们可能会在获得您的明示同意后，从第三方处获取您的信息。您可以通过阅读我们的</text>
        <text class="content__text link" @click="handleClickLink('servicePolicy')">《隐私策略》</text>
        <text>和</text>
        <text class="content__text link" @click="handleClickLink('serviceTerms')">《服务条款》</text>
        <text>充分了解信息/权限申请情况。</text>
      </view>
    </view>
    <view class="btns">
      <navigator class="btn disagree-btn" open-type="exit" target="miniProgram">暂不使用</navigator>
      <view class="btns__divider"></view>
      <button class="btn agree-btn" open-type="agreePrivacyAuthorization" @agreeprivacyauthorization="handleAgreePrivacyAuthorization">同意</button>
    </view>
  </popup>

</template>

<script>
import config from '@/utils/config'
import Popup from "../popup/popup.vue"
export default {
  data() {
    return {
      needAuthorization: false,
      privacyContractName: '',
      wxMpName: config.wxMpName
    }
  },
  components: {
    Popup,
  },
  mounted() {
  },
  methods: {
    checkIsAgree() {
      return new Promise((resolve, reject) => {
        wx.getPrivacySetting({
          success: (res) => {
            this.privacyContractName = res.privacyContractName
            resolve(res)
          },
          fail: (err) => {
            reject(err)
          }
        })
      })
    },
    show() {
      this.needAuthorization = true
    },
    close() {
      this.needAuthorization = false
    },
    
    handleAgreePrivacyAuthorization() {
      this.$emit('agree')
    },
    handleOpenPrivacyContract() {
      // 打开隐私协议页面
      wx.openPrivacyContract({
        success: () => {
        }, // 打开成功
        fail: () => {}, // 打开失败
        complete: () => {}
      })
    },
    handleClickLink(sts) {
      uni.navigateTo({
        url: `/packageUser/pages/termsPage/termsPage?sts=${sts}`,
      })
    },
  }
}
</script>

<style scoped>
.content {
  font-size: 28rpx;
  margin: 0 0 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
  text-align: center;
}

.content__p {
  text-indent: 2em;
  display: block;
  margin-bottom: 16px;
}
.content__text {
  display: inline;
}
.content__text.link {
  color: #007aff;
}

.btns {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  border-top: 1px solid #bfbfbf;
  padding: 4px 0 0;
  column-gap: 8px;
  align-items: center;
}

.btns__divider {
  width: 1px;
  background-color: #bfbfbf;
  height: 40rpx;
}

.btn {
  flex: 1;
  background-color: #fff;
  border-radius: 0;
  font-size: 32rpx;
  border: none;
  outline: none;
  box-shadow: none;
}

.btn:after {
    content: none;
}
 
.btn::after {
  border: none;
}

.disagree-btn {
  color: #999999;
  text-align: center;
}

.agree-btn {
  color: #007aff;
}
</style>
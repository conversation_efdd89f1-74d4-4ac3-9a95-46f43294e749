<template>
  <view>
    <movable-area class="movable-area">
      <movable-view class="movable-view" :x="computedX" :y="computedY" direction="all">
        <slot></slot>
      </movable-view>
    </movable-area>
  </view>
</template>

<script>
export default {
  data() {
    return {
      x: 0, // x坐标
      y: 0, // y坐标
    };
  },
  props: {
    top: {
      default: null,
      type: Number,
    },
    bottom: {
      default: null,
      type: Number,
    },
    left: {
      default: null,
      type: Number,
    },
    right: {
      default: null,
      type: Number,
    },
  },
  computed: {
    computedX() {
    	const clientWidth = uni.getSystemInfoSync().windowWidth;
			
      if (this.left !== null) {
				return this.left
			}
			else if (this.right !== null) {
				return clientWidth - this.right
			}
			return 0
    },
    computedY() {
    	const clientHeight = uni.getSystemInfoSync().windowHeight;
			
      if (this.top !== null) {
				return this.top
			}
			else if (this.bottom !== null) {
				return clientHeight - this.bottom
			}
			return 0
    },
  },
  mounted() {},
};
</script>

<style lang="scss">
$all_width: 96rpx;
$all_height: 96rpx;

.movable-area {
  height: 100vh;
  width: 750rpx;
  top: 0;
  position: fixed;
  pointer-events: none; // 此处要加，鼠标事件可以渗透
 .movable-view {
    width: $all_width;
    height: $all_height;
    pointer-events: auto; // 恢复鼠标事件
  }
}
</style>
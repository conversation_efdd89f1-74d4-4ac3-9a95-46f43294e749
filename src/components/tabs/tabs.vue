<template>
  <view class="tab-buttons" :class="{ 'legacy-primary-color': useLegacyPrimaryColor }">
    <view
      v-for="(tab) in tabs"
      :key="tab.key"
      @click="changeTab(tab.key)"
      class="tab-button"
      :class="{ 'active': activeTab === tab.key }"
    >
      {{ tab.label }}
    </view>
  </view>
</template>

<script>
export default {
  options: {
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data() {
    return {
      
    };
  },
  props: {
    tabs: {
      type: Array,
      default: () => []
    },
    activeTab: {
      type: String,
      default: ''
    },
    useLegacyPrimaryColor: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    changeTab(key) {
      this.$emit('change', key)
    }
  }
};
</script>

<style lang="scss" scoped>
.tab-buttons {
  display: flex;
  width: 100%;
  margin-bottom: 52rpx;
}

.tab-buttons .tab-button {
  padding: 16rpx;
  margin: 0;
  border: none;
  background-color: #fff;
  color: $wenet-color-brand;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-radius: 16rpx;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid $wenet-color-brand;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

.tab-buttons.legacy-primary-color {
  .tab-button {
    border: 1px solid $wenet-color-legacy-primary;
    color: $wenet-color-legacy-primary;
  }
}

.tab-buttons .tab-button.active {
  background-color: $wenet-color-brand;
  color: #fff;
}

.tab-buttons.legacy-primary-color .tab-button.active {
  background-color: $wenet-color-legacy-primary;
  color: #fff;
}


</style>

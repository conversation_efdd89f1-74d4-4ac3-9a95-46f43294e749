<template>
  <view class="container">
    <view class="headline">
      <view class="headline-title">WeNet商城</view>
      <view class="headline-msg">网络产品一站服务</view>
    </view>
    <view class="login-container">
      <view class="login-with-wenet">
        <slot></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'login-layout',
  data () {
    return {
    }
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  props: {
  },
  methods: {

  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
page {
  position: relative;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background-color: var(--primary-color);
}

.headline {
  width: 100%;
  height: calc(100vh - 1228rpx);
  min-height: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.headline-title {
  color: #FEFEFE;
  font-size: 48rpx;
  font-weight: 600;
  line-height: 64rpx; 
  letter-spacing: 1rpx;
}
.headline-msg {
  color: #fff;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 44rpx;
}

.login-container {
  position: absolute;
  bottom: 0rpx;
  width: 100%;
  height: 1228rpx;
  max-height: 80%;
  box-sizing: border-box;
  padding: 94rpx 48rpx;
  border-radius: 60rpx 60rpx 0rpx 0rpx;
  background-color: #fff;
}
</style>
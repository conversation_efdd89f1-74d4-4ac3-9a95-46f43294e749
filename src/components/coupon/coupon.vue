<template>
  <view :class="'coupon-item ' + (canUse?'':'gray')">
    <view style="display: initial;">
      <view class="left">
        <view class="num"
              v-if="item.couponType == 1 || item.couponType == 3">￥<text class="coupon-price">{{item.reduceAmount}}</text>
        </view>
        <view class="num" v-if="item.couponType == 2">
          <text class="coupon-price">{{item.couponDiscount}}</text>{{i18n.discount}}</view>
        <view class="coupon-condition">{{i18n.available}}{{item.cashCondition}}{{i18n.availableUse}}</view>
      </view>
      <view class="right">
        <view class="c-des">
          <text>
            <text class="c-type">{{item.shopId==0?i18n.universal:i18n.shop}}</text>
            {{ item.couponName }}
          </text>
        </view>
        <!-- 使用范围 -->
        <view v-if="item.shopId!=0 && item.shopName" class="c-desc">
          {{i18n.purchaseLimit}} [{{item.shopName}}]
        </view>
        <view v-else class="c-desc">
          {{item.suitableProdType == 0 ? i18n.allUniversal : i18n.specifiedItems}}
        </view>
        <!-- 使用时间 -->
        <view v-if="item && update">
          <view v-if="showTimeType==1 && item.validTimeType==2"  class="c-desc">
            {{i18n.getCoupons + ' '}}{{item.validDays}}{{i18n.invalid}}
          </view>
          <view v-else  class="c-desc">{{ formatTime }}</view>
        </view>
       
        <view class="c-date" v-if="item && update" >
          <view class="c-data-info" @tap="changeShowDetail">
            <text>详细信息</text>
            <text class="top-arrow" v-if="isShowDetail"></text>
            <text class="bottom-arrow" v-else></text>
          </view>
          <view class="c-btn"
                v-if="item.canReceive "
                @tap="receiveCoupon">{{i18n.getIt}}</view>
          <view class="c-btn get-btn"
                v-if="!item.canReceive "
                @tap="useCoupon">{{i18n.useItNow}}</view>
          <view class="no-use-btn"
                v-if="item.canUse==false "
                @tap="receiveCoupon">{{i18n.getIt}}</view>
        </view>
        <view v-if="order && canUse"
              class="sel-btn">
          <checkbox color="#eb2444"
                    :data-couponid="item.couponId"
                    :checked="item.choose"
                    @tap="checkCoupon"></checkbox>
        </view>
      </view>
    </view>
    <view class="bottom" v-if="isShowDetail">
      <view v-if="item.shopId!=0 && item.shopName">
        使用范围：{{i18n.purchaseLimit}} [{{item.shopName}}]，{{item.suitableProdType==0?i18n.allUniversal:i18n.specifiedItems}}
      </view>
      <view v-else>
        使用范围：{{item.suitableProdType == 0 ? i18n.allUniversal : i18n.specifiedItems}}
      </view>
      <view v-if="showTimeType == 1 && item.validTimeType == 2">
        使用时间：{{i18n.getCoupons + ' '}}{{item.validDays}}{{i18n.invalid}}
      </view>
      <view v-else >使用时间：{{formatStartTime}} ~ {{formatEndTime}}</view>
    </view>
    <!-- 我的优惠券状态(优惠券状态 0:失效 1:有效 2:使用过) -->
    <!-- 已使用 -->
    <image class="tag-img"
           :src="isEn ? `${staticPicDomain}images/icon/coupon-used-en.png`:`${staticPicDomain}images/icon/coupon-used.png`"
           v-if="type==2 && myCoupon"></image>
    <!-- 已过期 -->
    <image class="tag-img"
           :src="`${staticPicDomain}/icon/coupon-ot.png`"
           v-if="type==0 && myCoupon"></image>
  </view>
</template>


<script>
var http = require("../../utils/http.js");
var util = require("../../utils/util.js");

export default {
  data() {
    return {
      stsType: 4,
			item: this.couponItem, // 领券操作会直接修改父组件传递的数据引起报错,这个用来存数据
			update: true,  // 组件内部数据改变不触发视图重绘,这个用来手动重绘
			isEn: uni.getStorageSync('lang') == 'en' ? true : false, // 是否为英文
      isShowDetail: false,
    };
  },

  components: {},
  props: {
    // item: Object,
		couponItem: Object,
    type: [Number, String],
    order: Boolean,
    canUse: Boolean,
    index: Number,
    showTimeType: Number,
		myCoupon: Boolean
  },

  computed:{
  	i18n() {
  		return this.$t('index')
  	},
    formatStartTime: function(){
      return util.formatTimeExceptSecond(new Date(this.item.startTime)) 
    },
    formatEndTime: function(){
      return util.formatTimeExceptSecond(new Date(this.item.endTime)) 
    },
    formatTime: function() {
      return util.calculateTimeStatus(this.item.startTime, this.item.endTime)
    }
  },

  // 生命周期函数，可以为函数，或一个在methods段中定义的方法名
  beforeMount: function () {

  },
  methods: {
    receiveCoupon(e) {
      util.checkAuthInfo(() => {
        var couponId = this.item.couponId;
        http.request({
          url: "/p/myCoupon/receive",
          method: "POST",
          data: couponId,
          callBack: (res) => {
            this.update = false
            var coupon = this.item;
            coupon.canReceive = false;
            this.setData({
              item: coupon
            });
						uni.showToast({
              title: res,
              // #ifndef MP-TOUTIAO
              mask: true
              // #endif
            })
            this.update = true
            this.$emit("refreshDeductPrice")
          }
        });
      });
    },

    checkCoupon(e) {
      this.$emit('checkCoupon', {
        couponId: e.currentTarget.dataset.couponid
      });
    },

    /**
     * 立即使用
     */
    useCoupon() {
      util.checkAuthInfo(() => {
        var url = '/pages/prod-classify/prod-classify?sts=' + this.stsType;
        var id = this.item.couponId;
        var title = this.i18n.couponEventGoods;

        if (id) {
          url += "&tagid=" + id + "&title=" + title;
        }

        uni.navigateTo({
          url: url
        });
      });
    },
    changeShowDetail() {
      this.isShowDetail = !this.isShowDetail
    }
  }
};
</script>
<style>
@import "./coupon.css";
</style>

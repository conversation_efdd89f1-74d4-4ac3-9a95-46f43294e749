.coupon-item{
  margin: 15px 0;
  position: relative;
  min-height: 200rpx;
  background: #fff;
  border-radius: 20rpx;
}
.coupon-item .left{
  float: left;
  color: #fff;
  text-align: center;
  box-sizing: border-box;
  padding-top: 40rpx;
  min-width: 200rpx;
  min-height: 200rpx;
  background: linear-gradient(0deg, var(--gradient-ramp-light), var(--gradient-ramp-deep));
  background: -webkit-linear-gradient(0deg, var(--gradient-ramp-light), var(--gradient-ramp-deep));
  border-radius: 20rpx 0 0 20rpx;
}
.coupon-item .left .num{
  font-weight:600;
  font-size:30rpx;
  height:70rpx;
  line-height:70rpx;
  font-family:arial;
  color: #fff;
}
.coupon-item .left .num .coupon-price{
  font-size: 60rpx;
  line-height: 1em;
  display: inline-block;
  font-family: arial;
}
.coupon-item .left .coupon-condition{
  font-size: 28rpx;
  line-height: 28rpx;
  padding: 0 2px;
  display: block;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.coupon-item .right{
  margin-left: 200rpx;
  padding: 20rpx 20rpx;
  min-height: 200rpx;
  box-sizing: border-box;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	box-sizing: border-box;
}
.coupon-item .right .c-des{
  font-size: 26rpx;
  font-weight: 600;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
  margin-bottom: 10rpx;
}
.coupon-item .right .c-des .c-type{
  font-size: 24rpx;
  background: var(--light-background);
  color: var(--primary-color);
  border-radius: 4px;
  padding:1px 5px;
  margin-right: 6rpx;
}
.coupon-item .right .c-desc {
	font-size: 24rpx;
	line-height: 36rpx;
	color: #AAAAAA;
	font-weight: normal;
	text-overflow: -o-ellipsis-lastline;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
.coupon-item .right .c-date{
  font-size: 24rpx;
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
}
.coupon-item .right .c-date .c-data-info{
  font-family: arial;
  color: #AAAAAA;
  display: flex;
  align-items: center;
  vertical-align: bottom;
}
.coupon-item .right .c-date .c-btn{
	display: inline-block;
  color: #fff;
  font-size: 24rpx;
  font-family: arial;
  border-radius: 14px;
  padding:3px 7px;
	background-color: var(--primary-color);
	border: 1px solid var(--primary-color);
}

.coupon-item .right .c-date .c-btn.get-btn{
  background: #fff;
  border: 1px solid var(--primary-color);
  color:var(--primary-color);
}

.coupon-item .right .c-date .no-use-btn{
	color: #fff;
	font-size: 24rpx;
	font-family: arial;
	border-radius: 14px;
	padding:3px 7px;
	background: #bbbbbb;
	border: 1px solid #bbbbbb;
}

.coupon-item.gray .left{
  background: #bbb;
}

.coupon-item.gray .right .c-des .c-type{
  background: #bbb;
    color: #fff;
}

.coupon-item.gray .right .c-date .c-btn{
  display: none;
}

.coupon-item .tag-img{
  position: absolute;
  top:0;
  right:0;
  width:120rpx;
  height:120rpx;
}

.coupon-item .sel-btn{
  position:absolute;
  right:10px;
  top:35px;
}

.bottom {
  color: #AAAAAA;
  font-size: 20rpx;
  border-top: solid 1px #dfdede;
  margin: 16rpx 20rpx 20rpx;
  padding: 20rpx 0rpx;
}

.top-arrow {
  display: inline-block;
  width: 10rpx;
  height: 10rpx;
  border-top: 3rpx solid #AAAAAA;
  border-left: 3rpx solid #AAAAAA;
  margin-left: 10rpx;
  transform: rotate(45deg);
}

.bottom-arrow {
  display: inline-block;
  width: 10rpx;
  height: 10rpx;
  margin-left: 10rpx;
  border-bottom: 3rpx solid #AAAAAA;
  border-right: 3rpx solid #AAAAAA;
  transform: rotate(45deg);
}

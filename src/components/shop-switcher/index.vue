<template>
  <view>
    <view class="shop-switcher" v-if="!hide">
      <view class="left" @tap="toPersonalInformation">
        <image v-if="!!userInfo" :src="userInfo.pic? userInfo.pic : `${staticPicDomain}images/icon/head04.png`"></image>
      </view>
      <view class="right">
        <view class="headline">
          <text class="nickName" v-if="!!userInfo" @tap="toPersonalInformation">Hi, {{userInfo.nickName}}</text>
          <view class="message-box">
            <!-- 公告 -->
            <view class="message" @tap="toNotification">
              <n-i icon="Notification" width="44rpx" height="44rpx" />
              <view v-if="!!notifyCount" class="message-icon"></view>
            </view>
            <!-- 消息 -->
            <view  class="message" @tap="toMessage">
              <n-i icon="Message" width="44rpx" height="44rpx" />
              <view v-if="!!messageCount" class="message-icon"></view>
            </view>
          </view>
        </view>
        <view class="picker-box">
          <picker class="picker" @change="handlePickerChange" mode="selector" range-key="shopName" :range="shopList" :value="selectedIndex">
          <view class="picker-value">
            <view class="picker-value__text">
              {{ currentShop.shopName || '请选择店铺' }}
              <text mode="widthFix" class="iconfont icon-xia"></text>
            </view>
          </view>
        </picker>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import util from '@/utils/util'
import http from '@/utils/http'
import NI from '@/components/n-i'

export default {
  name: 'shop-switcher',
  data () {
    return {
      selectedIndex: 0,
      notifyCount: 0,
      messageCount: 0,
    }
  },
  computed: {
    shopList: function () {
      const fullShopList = this.$store.state.userInfo?.shops
      if (fullShopList) {
        return fullShopList.filter((item) => !!item.renovationId)
      }
      return []
    },
    currentShop: function () {
      return this.$store.state?.currentShop || {}
    },
    hide: function () {
      return this.shopList.length <= 1
    },
    userInfo() {
      return this.$store.state.userInfo
    },
  },
  components: {
    NI,
  },
  mounted () {
    if (this.currentShop && this.currentShop.shopId) {
      const index = this.shopList.findIndex((item) => item.shopId === this.currentShop.shopId)
      if (index !== -1) {
        this.selectedIndex = index
      }
    }
    this.getMessageRemind()
  },
  methods: {
    handlePickerChange(e) {
      const selectedIndex = e.detail.value;
      const target = this.shopList[selectedIndex]
      this.$store.commit('setCurrentShop', target)
      const pageStack = getCurrentPages()
      let reLauchUrl = pageStack[pageStack.length - 1].route || 'pages/index/index'
      if (pageStack.length && pageStack[pageStack.length - 1]?.route) {
        reLauchUrl = pageStack[pageStack.length - 1].route
      }
      uni.reLaunch({
        url: reLauchUrl
      })
    },
    // 跳转到修改用户头像昵称资料
    toPersonalInformation: function() {
      util.tapLog(3)
      uni.navigateTo({
        url: '/packageUser/pages/personalInformation/personalInformation'
      })
    },
    // 获取未读消息数量
    getMessageRemind() {
      var params = {
        url: '/p/myNotifyLog/unReadCount',
        method: 'GET',
        dontTrunLogin: true,
        data: {},
        callBack: res => {
          if (uni.getStorageSync('token')) {
            this.setData({
              messageCount: res
            });
          }
        }
      };
      http.request(params)
    },
    toNotification() {
      uni.navigateTo({
        url: '/packageUser/pages/recent-news/recent-news?shopId=' + this.currentShop.shopId
      });
    },
    toMessage() {
      uni.navigateTo({
        url: '/packageUser/pages/recent-news/recent-news?showMessage=1'
      })
    },
  }
}
</script>

<style scoped>
.shop-switcher {
  width: 100%;
  height: 100%;
  padding: 48rpx 32rpx;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  gap: 30rpx;
  align-items: center;
}

.left {
  width: 80rpx;
  height: 80rpx;
  flex-shrink: 0;
}
.left image{
  display: inline-block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.right .headline {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}
.headline .nickName {
  color: #000;
  font-size: 36rpx;
  font-weight: 600;
  line-height: 52rpx; 
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
}
.headline .message-box {
  width: 116rpx;
  height: 44rpx;
  display: flex;
  gap: 32rpx;
}
.message-box .message {
  width: 44rpx;
  height: 44rpx;
  position: relative;
}
.message-box .message image{
  width: 100%;
  height: 100%;
}

.picker-box {
  color: #999;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 40rpx; 
}
.picker {
  display: inline-block;
  vertical-align: middle;
}
.school-selector .picker-value {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.school-selector .picker-value__text {
  flex: 1;
}

.message-icon {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 26rpx;
  height: 26rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #EE0979 0%, #FF6A00 100%);
}
</style>
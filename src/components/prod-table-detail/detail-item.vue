<template>
  <view class="detail-item" @tap="handleTapMore">
      <view class="detail-item__name">
        <n-i :icon="icon" width="28rpx" height="28rpx" />
        <text class="detail-item__name__text">{{ title }}</text>
      </view>
      <view class="detail-item__content" >
        <view>
          <slot></slot>
        </view>
        <view class="detail-item-more-icon-wrap" v-if="reserveMoreIconPlace">
          <n-i v-show="showMoreIcon" icon="right" width="32rpx" height="32rpx"></n-i>
        </view>
      </view>

    </view>
</template>

<script>
import NI from '@/components/n-i'
export default {
  name: 'detail-item.vue',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  props: {
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    // 是否预留更多图标的位置
    reserveMoreIconPlace: {
      type: Boolean,
      default: true,
    },
    // 是否显示更多图标
    showMoreIcon: {
      type: Boolean,
      default: true,
    }
  },
  components: {
    NI,
  },
  data () {
    return {
    }
  },
  methods: {
    handleTapMore() {
      this.$emit('tapMore')
    }
  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32rpx;
  column-gap: 16rpx;
}

.detail-item__name {
  display: flex;
  flex-shrink: 0;
}

.detail-item__name__text {
  margin-left: 8rpx;
  color: #999;
  font-size: 24rpx;
  font-weight: 600;
  letter-spacing: 0.06px;
}

.detail-item__content {
  color: #000;
  font-size: 24rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  column-gap: 8rpx;
}

.detail-item-more-icon-wrap {
  width: 32rpx;
  height: 32rpx;
}
</style>
<template>
  <view class="prod-table-detail">
    <DetailItem
      title="配送"
      icon="truck"
      :show-more-icon="false"
      v-if="showDelivery"
    >
      <text>{{ deliveryWayText }}</text>
    </DetailItem>
    <DetailItem
      title="已选"
      icon="grid-01"
      @tapMore="handleTapSku"
    >
      <view>
        <text v-for="(skuItem,index) in selectedProp" :key="index" decode="true">{{index < selectedProp.length-1 ? skuItem.value +'，' : skuItem.value}}</text>
      </view>
    </DetailItem>
    <DetailItem
      title="数量"
      icon="coin-number"
      :show-more-icon="false"
      :reserve-more-icon-place="false"
    >
    <view class="num-wrap">
      <view class="minus" @tap="$emit('countMinus')">
        <n-i icon="sub" width="44rpx" height="44rpx" />
      </view>
      <view class="text-wrap">
        <input type="number" :value="prodNum" @input="$emit('prodNumInp')" />
      </view>
      <view class="plus" @tap="$emit('countPlus')">
        <n-i icon="add" width="44rpx" height="44rpx" />
      </view>
    </view>
    </DetailItem>
    <DetailItem v-if="couponList.length" title="优惠券" icon="ticket-01" @tapMore="handleTapCoupons">
      <text v-for="(item, index) in couponList" :key="index" v-if="index<1">
        <block v-if="item.couponType == 1">{{parseDiscountProd(item.couponType,item.cashCondition,item.reduceAmount )}}</block>
        <block v-if="item.couponType == 2">{{parseDiscountProd(item.couponType,item.cashCondition,item.couponDiscount)}}</block>
      </text>
    </DetailItem>
    <DetailItem v-if="giveawayList.length" title="赠品" icon="coin-hand" @tapMore="handleTapGiveaway">
      <text>{{giveawayList[0].prodName}}
        <text v-if="giveawayList[0].skuName" decode>{{'&nbsp;&nbsp;' + giveawayList[0].skuName}}</text>
      </text>
    </DetailItem>
    <DetailItem v-if="showPreSelling" title="预售" :show-more-icon="false">
      <text decode="true">预计 {{preSellTime}} 开始发货</text>
    </DetailItem>
    <DetailItem v-if="showDiscoutList" title="促销" @tapMore="handleTapDiscount">
      <view class="coupon-con discount-con">
        <block v-for="(item,index) in prodDiscountList" :key="index" v-if="index<1">
          <view class="discount-item">
            <view class="discount-tag">{{ [i18n.amount, i18n.pieces, i18n.amountDiscount, i18n.piecesDiscount][item.discountRule] }}</view>
            <text class="discount-content">
              <text v-if="item.discountType">{{i18n.every}}</text> {{item.discountName}}，{{i18n.maximumDiscount}}<text class="num">{{item.maxReduceAmount}}</text>{{i18n.yuan}}
            </text>
          </view>
        </block>
      </view>
    </DetailItem>
  </view>
</template>

<script>
import NI from '@/components/n-i'
import DetailItem from './detail-item.vue'
export default {
  name: 'prod-table-detail',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
    }
  },
  props: {
    showDelivery: {
      type: Boolean,
      default: true,
    },
    deliveryWayText: {
      type: String,
      default: '',
    },
    // 选择的商品规格
    selectedProp: {
      type: Array,
      default: () => ([]),
    },
    prodNum: {
      type: Number,
      default: 1,
    },
    couponList: {
      type: Array,
      default: () => ([]),
    },
    giveawayList: {
      type: Array,
      default: () => ([]),
    },
    showPreSelling: {
      type: Boolean,
      default: false,
    },
    preSellTime: {
      type: String,
      default: '',
    },
    prodDiscountList: {
      type: Array,
      default: () => ([]),
    },
    showDiscoutList: {
      type: Boolean,
      default: true,
    }
  },
  components: {
    NI,
    DetailItem,
  },
  methods: {
    parseDiscountProd(discountRule, needAmount, discount) {
      if (discountRule == 1) {
        return '满' + needAmount + '减' + discount
      } else if (discountRule == 2) {
        return '满' + needAmount + '打' + discount
      } else {
        return '';
      }
    },
    handleTapSku() {
      this.$emit('tapSku')
    },
    handleTapCoupons() {
      this.$emit('tapCoupons')
    },
    handleTapGiveaway() {
      this.$emit('tapGiveaway')
    },
    handleTapDiscount() {
      this.$emit('tapDiscount')
    },
  },
  computed: {
    i18n() {
				return this.$t('index')
			},
  },
  mounted () {

  },
}
</script>

<style scoped>
.prod-table-detail {
  width: 100%;
  padding: 0 48rpx;
  box-sizing: border-box;
}
.title {
  color: #000;
  font-size: 28rpx;
  font-weight: 600;
}

.very-vip{
	width: 100%;
	height: 100rpx;
	background: #F7D2A8;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx;
	box-sizing: border-box;
	border-radius: 24rpx;
	margin-top: 20rpx;
}
.vip{
	padding: 0 14rpx;
	height: 48rpx;
	background: #3B2A1A;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #EEDECD;
	white-space: nowrap;
	border-radius: 12rpx;
}
.vip-tips{
	font-size: 24rpx;
	color: #3D3323;
	margin: 0 10rpx;
	flex: 1;
}
.vip-nums{
	color: #F05738;
}
.open-vip-btn{
	border-radius: 12rpx;
	padding: 0 14rpx;
	height: 48rpx;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #3D3323;
	border: 2rpx solid #3D3323;
	white-space: nowrap;
}

.num-wrap {
  display: flex;
  width: 162rpx;
  height: 44rpx;
}

.num-wrap .minus,
.num-wrap .plus {
  position: relative;
  max-width: 44rpx;
  min-width: 44rpx;
  height: 44rpx;
  line-height: 44rpx;
  background: #f7f7f7;
  text-align: center;
  border-radius: 50%;
}

.num-wrap .text-wrap {
  flex: 1;
  text-align: center;
  font-size: 24rpx;
  color: #000;
  line-height: 44rpx;
  margin: 0 20rpx;
}

/* 商品促销活动(优惠 */

.discount-con {
  display: flex;
  flex-direction: column;
}

.discount-item {
  display: flex;
  align-items: center;
}
.popup-discount .discount-item {
  margin-bottom: 20rpx;
  padding-bottom: 8rpx;
}

.popup-discount .discount-item:last-child {
  padding-bottom: 8rpx;
}

.discount-tag {
  min-width: 100rpx;
  text-align: center;
  box-sizing: border-box;
  font-size: 20rpx;
  padding: 0 6rpx;
  background: var(--light-background);
  color: var(--primary-color);
  margin-right: 10rpx;
  height: 36rpx;
  line-height: 36rpx;
  font-family:arial;
}
.num{
	color: var(--primary-color);
	font-size: 28rpx;
}
.discount-content {
  font-size: 24rpx;
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 商品促销活动(优惠-end*/

</style>
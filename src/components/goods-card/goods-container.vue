<template>
  <view class="goods-container-wrap">
    <view class="goods-container" :class="{onlybottomradius: onlyBottomRadius}">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'goods-container',
  data () {
    return {
    }
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  props: {
    onlyBottomRadius: {
      type: Boolean,
      default: false
    },
  },
  methods: {

  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
.goods-container-wrap {
  box-sizing: border-box;
  margin: 0 48rpx 96rpx;
}
.goods-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  box-sizing: border-box;
  border-radius: 8px;
}
.goods-container.onlybottomradius {
  border-radius: 0 0 8px 8px;
}
</style>
<template>
  <view :class="['so-bottom-wrap', cardSize]">
    <view class="so-bottom">
      <view class="so-bottom__origin-price">
        <text class="so-bottom__origin-price__symbol">¥ </text>
        <text class="so-bottom__origin-price__big-num">{{parsePrice(goodsItem.price)[0]}}</text>
        <text class="so-bottom__origin-price__small-num">.{{parsePrice(goodsItem.price)[1]}}</text>
      </view>
      <view class="so-bottom__discount-tip">
        <view class="so-bottom__discount-tip__line">
          <n-i icon="so-line" mode="widthFix" width="100%" height="44rpx"/>
        </view>
        <view :class="['so-bottom__discount-tip__text', cardSize]">限时优惠</view>
      </view>
    </view>
    <view :class="['so-bottom-price-box', cardSize]">
      <view class="so-bottom-price-box__arrow">
        <n-i icon="so-price" width="100%" height="100%" ext="png"/>
      </view>
      <view class="so-bottom-price-box__price">
        <text class="so-bottom-price-box__price__symbol">¥ </text>
        <text class="so-bottom-price-box__price__big-num">{{parsePrice(goodsItem.activityPrice)[0]}}</text>
        <text class="so-bottom-price-box__price__small-num">.{{parsePrice(goodsItem.activityPrice)[1]}}</text>
      </view>
    </view>
  </view>
</template>

<script>
import NI from '@/components/n-i'
export default {
  name: 'special-offer-bottom',
  data () {
    return {
    }
  },
  components: {
    NI
  },
  props: {
    goodsItem: {
      type: Object,
      default: () => ({})
    },
    cardSize: {
      type: String,
      default: '',
    }
  },
  methods: {

  },
  computed: {

  },
  mounted () {
  },
}
</script>

<style scoped>
.so-bottom-wrap {
  width: 100%;
  height: 80rpx;
  position: relative;
}
.large.so-bottom-wrap {
  min-height: 80rpx;
}

.so-bottom {
  width: 100%;
  min-height: 80rpx;
  position: relative;
  display: flex;
  flex-direction: column;
}

.so-bottom__origin-price {
  flex: 1;
  color: #999999;
  font-size: 20rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.so-bottom__discount-tip {
  width: 100%;
  min-height: 44rpx;
}

.so-bottom__discount-tip__line {
  width: 100%;
  min-height: 44rpx;
}

.so-bottom__discount-tip__line img {
  width: 100%;
  height: 100%;
}

.so-bottom__discount-tip__text {
  position: absolute;
  bottom: 0px;
  left: 16rpx;
  color: #999;
  font-size: 20rpx;
  font-weight: 400;
}

.large.so-bottom__discount-tip__text{
  bottom: 10px;
}

.so-bottom-price-box {
  position: absolute;
  right: -16rpx;
  bottom: -8rpx;
  width: 180rpx;
  height: 96rpx;
  transform: scale(0.9);
  transform-origin: bottom right;
}

.small.so-bottom-price-box {
  width: 140rpx;
}

.so-bottom-price-box__arrow {
  width: 100%;
  height: 100%;
}

.so-bottom-price-box__price {
  position: absolute;
  top: 16rpx;
  right: 50%;
  transform: translateX(50%);
  font-weight: 600;
  color: #fff;
  display: flex;
  line-height: 32rpx;
}
.so-bottom-price-box__price__symbol {
  font-size: 20rpx;
  margin-right: 4rpx
}
.so-bottom-price-box__price__big-num {
  font-size: 28rpx
}
.so-bottom-price-box__price__small-num {
  font-size: 26rpx
}
</style>
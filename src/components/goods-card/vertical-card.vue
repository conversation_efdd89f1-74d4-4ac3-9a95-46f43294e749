<template>
  <view class="vertical-card-wrap" :class="[cardSize]">
    <view class="vertical-card-content" :class="[cardSize]" @tap.stop="handleTapGoods">
      <view class="vertical-card-content__img__wrap" :class="[cardSize]">
        <image lazy-load class="vertical-card-content__img" :src="goodsItem.pic" mode="scaleToFill" />
        <LabelsOverImage :labels="goodsItem.labels"/>
        <view class="sk-card-content__countdown"  v-if="activityType === 2">
          <text>
            {{ 
              status === 0 ? '距开始:' : 
              status === 1 ? '' : 
              '已结束'
            }}
          </text>
          <text v-if="times.day && times.day !== '00'">{{ times.day }}天</text>
          <text v-if="times.hou && times.min && times.sec">
            {{ times.hou }}:{{ times.min }}:{{ times.sec }}
          </text>
        </view>
      </view>

      <view class="vertical-card-content__info__wrap" >
        <view class="vertical-card-content__info__name" v-if="showProductName">
          {{ goodsItem.prodName }}
        </view>
        <view class="specialOfferBottom-wrap">
          <SpecialOfferBottom 
            v-if="activityType === 6"
            :cardSize="cardSize"
            :goods-item="{
              price: goodsItem.oriPrice,
              activityPrice: goodsItem.price,
            }"
          />
        </view>
        <LabelsBelowHeadline v-if="showLabels && activityType !== 6" :labels="goodsItem.labels"/>
        <view class="vertical-card-content__info__price-box" v-if="showPrice && activityType !== 6">
          <view class="price">
            <text class="symbol">¥ </text>
            <text class="big-num">{{parsePrice(goodsItem.price)[0]}}</text>
            <text class="small-num">.{{parsePrice(goodsItem.price)[1]}}</text>
          </view>
          <view class="origin-price" v-if="showOriPrice">
            <text class="">¥{{parsePrice(goodsItem.oriPrice)[0]}}.{{parsePrice(goodsItem.oriPrice)[1]}}</text>
          </view>
        </view>
      </view>

      <slot name="bottom"></slot>
    </view>
  </view>
</template>

<script>
import LabelsBelowHeadline from '@/components/labels-below-headline'
import LabelsOverImage from '@/components/labels-over-image'
import SpecialOfferBottom from '@/components/goods-card/special-offer-bottom';
import util from '@/utils/util.js'

export default {
  name: 'vertical-card',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
      times: {},
      status: 0, //活动状态 0未开始 1进行中 2已结束
    }
  },
  props: {
    goodsItem: {
      type: Object,
      default: () => ({})
    },
    showProductName: {
      type: Boolean,
      default: true
    },
    showLabels: {
      type: Boolean,
      default: true
    },
    showPrice: {
      type: Boolean,
      default: true
    },
    showOriPrice: {
      type: Boolean,
      default: true
    },
    cardSize: {
      type: String,
      default: 'medium', // 一行一个:large  一行两个:medium  一行滑动:small
    },
    activityType: {
      type: Number,
      default: 0, // 默认不是任何活动
    },
  },
  components: {
    LabelsBelowHeadline,
    LabelsOverImage,
    SpecialOfferBottom,
  },
  computed: {

  },
  mounted () {
    if (this.activityType === 2) {
      this.countdown()
    }
  },
  methods: {
    handleTapGoods() {
      this.$emit('tap-goods', this.goodsItem)
    },
    countdown() {
      // 获取当前时间，同时得到活动结束时间数组
      const startTime = this.goodsItem?.discountStart.replace(/-/g, '/');
      const endTime = this.goodsItem?.discountEnd.replace(/-/g, '/');
      const nowTimeStamps = new Date().getTime();
      const startTimeStamps = new Date(startTime).getTime();
      const endTimeStamps = new Date(endTime).getTime();
      if (nowTimeStamps < startTimeStamps) {
        //活动未开始，倒计时
        const times = util.endOfStartTime(nowTimeStamps, startTimeStamps);
        this.setData({
          status: 0, //活动未开始
          times
        });
        setTimeout(this.countdown, 1000);
      } else if (nowTimeStamps < endTimeStamps) {
        //活动正在进行，倒计时
        const times = util.endOfStartTime(nowTimeStamps, endTimeStamps);
        this.setData({
          status: 1, //活动正在进行
          times
        });
        setTimeout(this.countdown, 1000);
      } else {
        //活动已结束，清空倒计时
        this.setData({
          status: 2 //活动已结束
        });
        return;
      }
    },
  },
}
</script>

<style scoped>

.vertical-card-wrap {
  box-sizing: border-box;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.vertical-card-wrap.medium {
  padding-top: 16rpx;
  width: 50%;
}

.vertical-card-wrap.medium:nth-child(2n+1) {
  padding-right: 8rpx;
}

.vertical-card-wrap.medium:nth-child(2n) {
  padding-left: 8rpx;
}
.vertical-card-wrap.medium:first-child,.vertical-card-wrap.medium:nth-child(2) {
  padding-top: 0;
}

.vertical-card-wrap.large {
  width: 324rpx;
  padding: 8rpx 8rpx;
}

.vertical-card-wrap.small {
  width: 208rpx;
  padding: 16rpx 8rpx;
}

.vertical-card-wrap.large:first-child,.vertical-card-wrap.small:first-child {
  padding: 16rpx 16rpx 16rpx 0rpx;
}
.vertical-card-wrap.large:last-child,.vertical-card-wrap.small:last-child {
  padding: 16rpx 0 16rpx 16rpx;
}

.vertical-card-content {
  width: 100%;
  min-height: 300rpx;
  position: relative;
  border-radius: 8px;
  font-size: 0;
  flex-shrink: 0;
  box-sizing: border-box;
  flex: auto;
  display: flex;
  flex-direction: column;
}

.vertical-card-content.medium {
  padding: 16rpx;
  box-shadow: 0px 0px 40rpx 0px rgba(0, 0, 0, 0.08);
}

.vertical-card-content.large {
  padding: 0 0 16rpx 0;
}

.vertical-card-content.small {
  padding: 0 0 16rpx 0;
}

.vertical-card-content__img__wrap {
  width: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}
.vertical-card-content__img__wrap.large {
  height: 308rpx;
}
.vertical-card-content__img__wrap.small {
  height: 192rpx;
}
.vertical-card-content__img__wrap.medium {
  height: 286rpx;
}

.vertical-card-content__img {
  width: 100%;
  height: 100%;
  display: block;
}

.vertical-card-content__info__wrap {
  margin-top: 16rpx;
  flex: auto;
  display: grid;
  flex-direction: column;
  grid-template-columns: 100%;
}

.vertical-card-content__info__name {
  font-size: 28rpx;
  display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
  font-weight: 600;
  line-height: 35rpx;
  max-height: 70rpx;
}
.specialOfferBottom-wrap {
  position: relative;
  display: flex;
  flex-direction: column-reverse;
}

.vertical-card-content__info__price-box {
  margin-top: 16rpx;
  display: flex;
  align-items: flex-end;
  justify-self: flex-end;
  width: 100%;
}

.sk-card-content__countdown {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 64rpx;
  background-color: rgba(0,0,0,.5);
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  color: #fff;
  font-size: 18rpx;
}

</style>
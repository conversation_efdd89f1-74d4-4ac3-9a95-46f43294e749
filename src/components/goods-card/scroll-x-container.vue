<template>
  <view class="scroll-x-container">
    <view style="width: 48rpx;height: 30rpx;flex-shrink: 0;"></view>
    <view class="scroll-x-container-content" :class="{onlybottomradius: onlyBottomRadius}">
      <slot></slot>
    </view>
    <view style="width: 48rpx;height: 30rpx;flex-shrink: 0;"></view>
  </view>
</template>

<script>
export default {
  name: 'scroll-x-container',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
    }
  },
  props: {
    onlyBottomRadius: {
      type: Boolean,
      default: false
    },
  },
  methods: {

  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
  .scroll-x-container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    overflow-x: scroll;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: none;
    scrollbar-width: none;
    margin: -42rpx 0rpx 54rpx;
    padding: 42rpx 0;
  }
  

  /* 隐藏滚动条 */
  .scroll-x-container::-webkit-scrollbar {
    display: none;
  }

  .scroll-x-container-content {
    display: flex;
    flex: 1;
    border-radius: 8px;
  }

  .scroll-x-container-content.onlybottomradius {
    border-radius: 0 0 8px 8px;
  }

</style>
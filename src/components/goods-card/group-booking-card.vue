<template>
  <view class="gb-card-wrap">
    <view class="gb-card-content" @tap.stop="handleTapGoods">
      <view class="gb-card-content__img__wrap">
        <image lazy-load class="gb-card-content__img" :src="goodsItem.prodPic" mode="scaleToFill"></image>
        <view class="gb-card-grouped-num gb-card-label">
          {{ goodsItem.groupNumber }}人团
        </view>
        <!-- <LabelsOverImage :labels="goodsItem.labels"/> -->
      </view>

      <view class="gb-card-content__info__wrap">
        <view>
          <view class="gb-card-content__info__name" v-if="showProductName">
            {{ goodsItem.prodName }}
          </view>
          <!-- <LabelsBelowHeadline v-if="showLabels" :labels="goodsItem.labels"/> -->
          <view class="gb-card-content__info__grouped-num gb-card-label">
            拉{{ restGroupedNumChinese }}人成团发货
          </view>
        </view>
        <view class="gb-card-content__info__price-box" v-if="showPrice">
          <view class="gb-card-content__info__price-box__popup">
            <n-i icon="gb-popup" width="166rpx" height="34rpx" />
            <text class="gb-card-content__info__price-box__popup__subsidy">补贴{{ subsidyPrice }}元</text>
          </view>
          <view class="gb-card-content__info__price-box__price">
            <view class="gb-card-content__info__price-box__price__left">
              <view class="gb-card-content__info__price-box__price__left__group-price">
                <text class="price-box__price__left__group-price__text">拼单价</text>
                <text class="price-box__price__left__group-price__symbol">￥</text>
                <text class="price-box__price__left__group-price__price">{{ toPrice(goodsItem.actPrice, 2) }}</text>
              </view>
              <view class="gb-card-content__info__price-box__price__left__ori-price">
                <text class="price-box__price__left__ori-price__text">单买价</text>
                <text class="price-box__price__left__ori-price__symbol">￥</text>
                <text class="price-box__price__left__ori-price__price">{{ goodsItem.price }}</text>
              </view>

            </view>
            <view class="gb-card-content__info__price-box__price__right">
              <n-i icon="gb-buy-btn" width="170rpx" height="100rpx" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// import LabelsBelowHeadline from '@/components/labels-below-headline'
// import LabelsOverImage from '@/components/labels-over-image'
import NI from '@/components/n-i'

const chineseNum = ['一', '两', '三', '四', '五', '六', '七', '八', '九']

export default {
  name: 'gb-card',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
    }
  },
  props: {
    goodsItem: {
      type: Object,
      default: () => ({})
    },
    showProductName: {
      type: Boolean,
      default: true
    },
    showLabels: {
      type: Boolean,
      default: true
    },
    showPrice: {
      type: Boolean,
      default: true
    },
    showOriPrice: {
      type: Boolean,
      default: true
    },
  },
  components: {
    // LabelsBelowHeadline,
    // LabelsOverImage,
    NI
  },
  methods: {
    handleTapGoods() {
      this.$emit('tap-goods', this.goodsItem)
    }
  },
  computed: {
    restGroupedNumChinese() {
      const restNum = this.goodsItem.groupNumber - 1
      if (restNum < 9) {
        return chineseNum[restNum - 1]
      } else {
        return restNum
      }
    },
    subsidyPrice() {
      return this.toPrice(this.goodsItem.price - this.goodsItem.actPrice)
    }
  },
  mounted () {
  },
}
</script>

<style scoped>

.gb-card-wrap {
  width: 100%;
  padding-top: 32rpx;
  padding-bottom: 32rpx;
  box-sizing: border-box;
  position: relative;
}

.gb-card-wrap::before {
  content: "";
  width: 100vw;
  height: 2rpx;
  background-color: #f5f5f5;
  position: absolute;
  top: 0;
  left: -48rpx;
}

.gb-card-wrap:first-child::before {
  display: none;
}

.gb-card-content {
  width: 100%;
  min-height: 180rpx;
  position: relative;
  border-radius: 8px;
  font-size: 0;
  flex-shrink: 0;
  box-sizing: border-box;
  display: flex;
}

.gb-card-content__img__wrap {
  width: 248rpx;
  height: 248rpx;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  flex-shrink: 0;
}

.gb-card-label {
  display: inline-flex;
  padding: 4rpx 8rpx;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  border: 1px solid #F7DCC7;
  background: linear-gradient(180deg, #FFEEE1 0%, #FADFCC 100%);
  color: #C76332;
  font-size: 16rpx;
  font-weight: 400;
}

.gb-card-grouped-num {
  position: absolute;
  top: 18rpx;
  left: 16rpx;
}

.gb-card-content__img {
  width: 100%;
  height: 100%;
  display: block;
}

.gb-card-content__info__wrap {
  margin-left: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}

.gb-card-content__info__name {
  font-size: 24rpx;
  display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	word-break: break-word;
	overflow: hidden;
	text-overflow: ellipsis;
  font-weight: 600;
}

.gb-card-content__info__grouped-num {
  margin-top: 8rpx;
}

.gb-card-content__info__price-box {
  margin-top: 6rpx;
  width: 100%;
}

.gb-card-content__info__price-box__popup {
  position: relative;
  width: 166rpx;
  height: 36rpx;
}

.gb-card-content__info__price-box__popup__subsidy {
  position: absolute;
  left: 50%;
  bottom: 0;
  width: 100%;
  color: #fff;
  font-size: 20rpx;
  font-weight: 400;
  transform: translate(-50%, -50%);
  text-align: center;
  line-height: 24rpx;
}

.gb-card-content__info__price-box__price {
  width: 100%;
  height: 100rpx;
  background-color: #FE4B60;
  display: flex;
  border-radius: 16rpx;
}

.gb-card-content__info__price-box__price__left {
  flex: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 0 16rpx;
  box-sizing: border-box;
}

.gb-card-content__info__price-box__price__left__group-price {
  color: #fff;

}

.price-box__price__left__group-price__text {
  font-size: 20rpx;
  font-weight: 400;
}

.price-box__price__left__group-price__symbol {
  font-size: 24rpx;
  font-weight: 600;
}

.price-box__price__left__group-price__price {
  font-size: 32rpx;
  font-weight: 600;
}

.gb-card-content__info__price-box__price__left__ori-price {
  color: rgba(255, 255, 255, 0.60);
  font-size: 20rpx;
  font-weight: 600;
}

.price-box__price__left__ori-price__text {
  font-weight: 400;
}



</style>
<template>
  <view class="sk-card-wrap">
    <view class="sk-card-content" @tap.stop="handleTapGoods">
      <view class="sk-card-content__img__wrap">
        <image lazy-load class="sk-card-content__img" :src="goodsItem.pic" mode="scaleToFill"></image>
        <LabelsOverImage :labels="goodsItem.labels"/>
        <view class="sk-card-content__countdown">
          <view class="sk-card-content__countdown__tip">
            {{ 
              status === 0 ? '距开始:' : 
              status === 1 ? '' : 
              '已结束'
            }}
          </view>
          <text v-if="times.day && times.day !== '00'">{{ times.day }}天</text>
          <text v-if="times.hou && times.min && times.sec">
            {{ times.hou }}:{{ times.min }}:{{ times.sec }}
          </text>
        </view>
      </view>

      <view class="sk-card-content__info__wrap">
        <view>
          <view class="sk-card-content__info__name" v-if="showProductName">
            {{ goodsItem.prodName }}
          </view> 
          <LabelsBelowHeadline v-if="showLabels && goodsItem.labels && goodsItem.labels.length" :labels="goodsItem.labels"/>
          <view v-else class="sk-card-content__info__default__label">
            立减{{ subsidyPrice }}元秒杀
          </view>

        </view>
        <view class="sk-card-content__info__price-box" v-if="showPrice">
          <view class="sk-card-content__info__price-box__ori-price-wrap">
            <view class="sk-car-content__info-ori-price">
              原价¥ {{ toPrice(goodsItem.price, 2) }}
            </view>
            <view class="sk-car-content__info-subsidy">
              立省¥ {{ subsidyPrice }}
            </view>
          </view>
          <view class="sk-card-content__info__price-box__price">
            <view class="sk-card-content__info__price-box__price__left">
              <view class="sk-card-content__info__price-box__price__left__group-price">
                <text class="price-box__price__left__group-price__symbol">￥</text>
                <text class="price-box__price__left__group-price__price">{{ toPrice(goodsItem.activityPrice, 2) }}</text>
                <text class="price-box__price__left__group-price__text">限时价</text>
              </view>
            </view>
            <view class="sk-card-content__info__price-box__price__right">
              <n-i icon="sk-buy-btn" width="170rpx" height="64rpx" />
              <text class="sk-card-content__info__price-box__price__right__text">去抢购</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import NI from '@/components/n-i'
import LabelsBelowHeadline from '@/components/labels-below-headline'
import LabelsOverImage from '@/components/labels-over-image'
import util from '@/utils/util.js'


export default {
  name: 'sk-card',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
      times: {},
      status: 0, //活动状态 0未开始 1进行中 2已结束
    }
  },
  props: {
    goodsItem: {
      type: Object,
      default: () => ({})
    },
    showProductName: {
      type: Boolean,
      default: true
    },
    showLabels: {
      type: Boolean,
      default: true
    },
    showPrice: {
      type: Boolean,
      default: true
    },
    showOriPrice: {
      type: Boolean,
      default: true
    },
  },
  components: {
    LabelsBelowHeadline,
    LabelsOverImage,
    NI
  },
  methods: {
    handleTapGoods() {
      this.$emit('tap-goods', this.goodsItem)
    },
    countdown() {
      // 获取当前时间，同时得到活动结束时间数组
      const startTime = this.goodsItem?.seckillSearchVO.startTime.replace(/-/g, '/');
      const endTime = this.goodsItem?.seckillSearchVO.endTime.replace(/-/g, '/');
      const nowTimeStamps = new Date().getTime();
      const startTimeStamps = new Date(startTime).getTime();
      const endTimeStamps = new Date(endTime).getTime();
      if (nowTimeStamps < startTimeStamps) {
        //活动未开始，倒计时
        const times = util.endOfStartTime(nowTimeStamps, startTimeStamps);
        this.setData({
          status: 0, //活动未开始
          times
        });
        setTimeout(this.countdown, 1000);
      } else if (nowTimeStamps < endTimeStamps) {
        //活动正在进行，倒计时
        const times = util.endOfStartTime(nowTimeStamps, endTimeStamps);
        this.setData({
          status: 1, //活动正在进行
          times
        });
        setTimeout(this.countdown, 1000);
      } else {
        //活动已结束，清空倒计时
        this.setData({
          status: 2 //活动已结束
        });
        return;
      }
    },
  },
  computed: {
    subsidyPrice() {
      return this.toPrice(this.goodsItem.price - this.goodsItem.activityPrice)
    }
  },
  mounted () {
    this.countdown()
  },
}
</script>

<style scoped>

.sk-card-wrap {
  width: 100%;
  padding-top: 32rpx;
  padding-bottom: 32rpx;
  box-sizing: border-box;
  position: relative;
}

.sk-card-wrap::before {
  content: "";
  width: 100vw;
  height: 2rpx;
  background-color: #f5f5f5;
  position: absolute;
  top: 0;
  left: -48rpx;
}

.sk-card-wrap:first-child::before {
  display: none;
}

.sk-card-content {
  width: 100%;
  min-height: 180rpx;
  position: relative;
  border-radius: 8px;
  font-size: 0;
  flex-shrink: 0;
  box-sizing: border-box;
  display: flex;
}

.sk-card-content__img__wrap {
  width: 248rpx;
  height: 248rpx;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  flex-shrink: 0;
}

.sk-card-content__countdown {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 64rpx;
  background-color: rgba(0,0,0,.5);
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  color: #fff;
  font-size: 20rpx;
}

.sk-card-content__info__default__label {
  display: inline-flex;
  padding: 4rpx 8rpx;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  border: 1px solid #999;
  color: #999;
  font-size: 16rpx;
  font-weight: 400;
  margin-top: 8rpx;
}


.sk-card-content__img {
  width: 100%;
  height: 100%;
  display: block;
}

.sk-card-content__info__wrap {
  margin-left: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}

.sk-card-content__info__name {
  font-size: 24rpx;
  display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	word-break: break-word;
	overflow: hidden;
	text-overflow: ellipsis;
  font-weight: 600;
}


.sk-card-content__info__price-box {
  margin-top: 6rpx;
  width: 100%;
}
.sk-card-content__info__price-box__ori-price-wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sk-car-content__info-ori-price {
  color: #999;
  font-size: 20rpx;
  font-weight: 400;
}

.sk-car-content__info-subsidy {
  color: var(--primary-color);
  text-align: center;
  font-size: 20rpx;
  font-weight: 400;
}


.sk-card-content__info__price-box__price {
  width: 100%;
  height: 64rpx;
  background-color: #FFECEE;;
  display: flex;
  border-radius: 16rpx;
  margin-top: 18rpx;
}

.sk-card-content__info__price-box__price__left {
  flex: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 4rpx;
  box-sizing: border-box;
}

.sk-card-content__info__price-box__price__left__group-price {
  color: var(--primary-color);
}

.price-box__price__left__group-price__text {
  font-size: 20rpx;
  font-weight: 400;
  margin-left: 4rpx;
}

.price-box__price__left__group-price__symbol {
  font-size: 24rpx;
  font-weight: 600;
}

.price-box__price__left__group-price__price {
  font-size: 32rpx;
  font-weight: 600;
  text-wrap: nowrap;
}

.sk-card-content__info__price-box__price__right {
  position: relative;
}

.sk-card-content__info__price-box__price__right__text {
  position: absolute;
  top: 50%;
  right: 24rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: 600;
  box-sizing: border-box;
  transform: translateY(-50%);
}


</style>
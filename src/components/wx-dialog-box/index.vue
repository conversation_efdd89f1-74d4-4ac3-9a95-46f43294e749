<template>
  <view :class="arrowDirection">
    <text>{{ text }}</text>
  </view>
</template>

<script>
export default {
  name: 'wx-dialog-box',
  props: {
    arrowDirection: {
      type: String,
      default: 'left',
    },
    text: {
      type: String,
      default: '',
    }
  },
}
</script>

<style scoped>
.left, .right{
  position: relative;
  display: inline-block;
  min-height: 60rpx;
  line-height: 60rpx;
  text-align: left;
  background-color: #36B626;
  color: white;
  border-radius: 14rpx;
  padding: 0rpx 24rpx;
}

.left::before, .right::after{
  position: absolute;
  display: inline-block;
  content: "";
  width: 0px;
  height: 0px;
  border: 12rpx solid transparent;
  top: 20rpx;		/*移到中间*/
}

.left::before{
  border-right-color: #36B626;
  left: -20rpx;
}
.right::after{
  border-left-color: #36B626;
  right: -20rpx;
}

</style>
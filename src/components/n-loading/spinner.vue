<template>
<view class="wenet-spinner" :class="size">
  <view class="wenet-spinner__dot"></view>
  <view class="wenet-spinner__dot"></view>
  <view class="wenet-spinner__dot"></view>
  <view class="wenet-spinner__dot"></view>
  <view class="wenet-spinner__dot"></view>
  <view class="wenet-spinner__dot"></view>
  <view class="wenet-spinner__dot"></view>
  <view class="wenet-spinner__dot"></view>
</view>
</template>

<script>
export default {
  name: 'WenetSpinner',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  props: {
    size: {
      type: String,
      default: 'small', // 取值：normal, small, large
    },
  }
}
</script>

<style lang="scss" scoped>
.wenet-spinner {
  --uib-speed: .9s;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: var(--uib-size);
  width: var(--uib-size);
}

.wenet-spinner.small {
  --uib-size: 28rpx;
}

.wenet-spinner.normal {
  --uib-size: 48rpx;
}

.wenet-spinner.large {
  --uib-size: 64rpx;
}

.wenet-spinner__dot {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  width: 100%;
}

.wenet-spinner__dot::before {
  content: '';
  height: 20%;
  width: 20%;
  border-radius: 50%;
  background-color: $wenet-color-brand;
  transform: scale(0);
  opacity: 0.5;
  animation: pulse0112 calc(var(--uib-speed) * 1.111) ease-in-out infinite;
  box-shadow: 0 0 20px rgba(18, 31, 53, 0.3);
}

.wenet-spinner__dot:nth-child(2) {
  transform: rotate(45deg);
}

.wenet-spinner__dot:nth-child(2)::before {
  animation-delay: calc(var(--uib-speed) * -0.875);
}

.wenet-spinner__dot:nth-child(3) {
  transform: rotate(90deg);
}

.wenet-spinner__dot:nth-child(3)::before {
  animation-delay: calc(var(--uib-speed) * -0.75);
}

.wenet-spinner__dot:nth-child(4) {
  transform: rotate(135deg);
}

.wenet-spinner__dot:nth-child(4)::before {
  animation-delay: calc(var(--uib-speed) * -0.625);
}

.wenet-spinner__dot:nth-child(5) {
  transform: rotate(180deg);
}

.wenet-spinner__dot:nth-child(5)::before {
  animation-delay: calc(var(--uib-speed) * -0.5);
}

.wenet-spinner__dot:nth-child(6) {
  transform: rotate(225deg);
}

.wenet-spinner__dot:nth-child(6)::before {
  animation-delay: calc(var(--uib-speed) * -0.375);
}

.wenet-spinner__dot:nth-child(7) {
  transform: rotate(270deg);
}

.wenet-spinner__dot:nth-child(7)::before {
  animation-delay: calc(var(--uib-speed) * -0.25);
}

.wenet-spinner__dot:nth-child(8) {
  transform: rotate(315deg);
}

.wenet-spinner__dot:nth-child(8)::before {
  animation-delay: calc(var(--uib-speed) * -0.125);
}

@keyframes pulse0112 {
  0%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
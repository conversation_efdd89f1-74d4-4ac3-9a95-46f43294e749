<template>
  <view >
    <view v-if="loading" class="loading-wrap">
      <WenetSpinner size="large" />
    </view>
  </view>
</template>

<script>
import WenetSpinner from './spinner.vue'
export default {
  name: 'n-loading',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  components: {
    WenetSpinner
  },
  data () {
    return {
    }
  },
  methods: {

  },
  computed: {

  },
  mounted () {

  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    requestNum() {
      return this.$store.state.requestNum
    },
    loading() {
      return this.requestNum > 0 || this.show
    }
  }
}
</script>

<style scoped>
.loading-wrap {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 48rpx;
  height: 48rpx;
  transform: translate(-50%, -50%);
  z-index: 99999;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
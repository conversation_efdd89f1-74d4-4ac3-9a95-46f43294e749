<template>
  <view>
    <view class="title">选择开卡方式</view>
    <view class="custom-message">
      <Tabs :use-legacy-primary-color="true" :tabs="tabList" @change="changeTab" :active-tab="activeTab" style="margin-bottom: 20rpx; font-size: 24rpx;" />
      <view class="message-list">
        <view class="type-description" v-if="typeDescription">
          <text>{{ typeDescription }}</text>
        </view>
        <view 
          v-for="(item, index) in localRemarkList" 
          :key="item.key" 
          class="message-item"
        >
          <view v-if="activeTab === item.type">
            <view class="message-row">
              <label 
                class="message-label" 
                :for="'input-' + index"
              >
                <text class="required" v-if="item.isRequired">*</text>
                <text>{{ item.name }}</text>
              </label>
              <input 
                v-if="item.fieldType === 'text'"
                class="message-input"
                @input="(value) => handleInput(value, index)"
                :value="item.value" 
                :placeholder="item.description || `请输入${item.name}`"
                maxlength="20"
                :id="'input-' + index"
              />
              <view 
                v-if="item.fieldType === 'appointmentTime'" 
                class="appointment-trigger"
                @click="showAppointment(index)"
              >
                <text v-if="item.value" class="selected-time">{{ item.value }}</text>
                <text v-else class="placeholder">{{ item.description || '请选择预约时间' }}</text>
              </view>
              <uni-data-select
                v-if="item.fieldType === 'select'"
                :placeholder="item.description || `请选择${item.name}`"
                :localdata="item.options"
                :value="item.value"
                @change="(value) => handleSelectChange(value, index)"
                placeholder-style="color: #999;font-size: 26rpx;"
                style="padding-left: 0;"
                input-text-style="color: #333;font-size: 26rpx;"
                height="44rpx"
                class="select-form-item"
                dropdown-style="z-index:20;"
                placement="top"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 预约时间选择弹窗 -->
    <uni-popup ref="appointmentPopup" type="bottom" :safe-area="false">
      <appointment-time
        v-if="currentAppointmentItem"
        v-model="currentAppointmentItem.value"
        @confirm="handleAppointmentConfirm"
        @cancel="handleAppointmentCancel"
        :timeSlotData="timeSlotData"
      />
    </uni-popup>
  </view>
</template>

<script>
import Tabs from '@/components/tabs/tabs'
import AppointmentTime from '@/components/form/appointment-time-silly'
import uniPopup from '@/components/uni-popup/uni-popup'
import NInput from '@/components/form/n-input'
import UniDataSelect from '@/components/uni-data-select/uni-data-select';
import http from '@/utils/http'
export default {
  name: 'custom-user-message',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
      activeTab: '',
      tabList: [],
      localRemarkList: null, // 本地数据,用于管理输入值
      isInitialized: false, // 标记是否已经初始化
      currentAppointmentItem: null, // 当前正在选择预约时间的项
      timeSlotData: {
        "2025-05-13": [
          {
            "date": "2025-05-13",
            "unavailableReason": "该时间段已过期",
            "timeSlot": "09:00-10:00",
            "currentCount": 0,
            "available": false,
            "maxCount": 3,
            "status": "EXPIRED"
          },
          {
            "date": "2025-05-13",
            "unavailableReason": "该时间段已过期",
            "timeSlot": "10:00-11:00",
            "currentCount": 0,
            "available": false,
            "maxCount": 3,
            "status": "EXPIRED"
          },
          {
            "date": "2025-05-13",
            "unavailableReason": "该时间段已过期",
            "timeSlot": "11:00-12:00",
            "currentCount": 0,
            "available": false,
            "maxCount": 3,
            "status": "EXPIRED"
          },
          {
            "date": "2025-05-13",
            "unavailableReason": "该时间段已过期",
            "timeSlot": "12:00-13:00",
            "currentCount": 0,
            "available": false,
            "maxCount": 3,
            "status": "EXPIRED"
          }
        ],
        "2025-05-14": [
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "09:00-10:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "10:00-11:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "11:00-12:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "12:00-13:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "13:00-14:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "14:00-15:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "15:00-16:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "16:00-17:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "17:00-18:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "18:00-19:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          },
          {
            "date": "2025-05-14",
            "unavailableReason": null,
            "timeSlot": "19:00-20:00",
            "currentCount": 0,
            "available": true,
            "maxCount": 3,
            "status": "AVAILABLE"
          }
        ]
      }
    }
  },
  components: {
    Tabs,
    AppointmentTime,
    uniPopup,
    NInput,
    UniDataSelect
  },
  props: {
    virtualRemarkList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    virtualRemarkList: {
      handler(newVal) {
        // 只在第一次有值时初始化
        if (newVal.length && !this.isInitialized) {
          this.initializeData()
          this.isInitialized = true
        }
      },
      immediate: true
    }
  },
  computed: {
    typeDescription() {
      return this.virtualRemarkList.find(item => item.type === this.activeTab)?.typeDescription
    },
    userInfo() {
      return this.$store.state.userInfo
    },
    currentShop() {
      return this.$store.state?.currentShop
    },
  },
  methods: {
    initializeData() {
      // 初始化本地数据
      // 预处理数据，确保每个项都有value属性
      const localRemarkList = this.virtualRemarkList.map((item, idx) => {
        try {
          const res = {
            ...item,
            value: '',
            key: item.name + idx
          }
          if (item.fieldType === 'select' && item.options) {
            const originOptions = item.options ? JSON.parse(item.options) : [];
            const options = originOptions.map(entity => ({ text: entity.label, value: entity.label }))
            res.options = options
          }
          return res
        } catch (error) {
          return  {}
        }
      })
      
      // 设置标签页数据
      const allTypes = [...new Set(this.virtualRemarkList.map(item => item.type))]
      this.localRemarkList = localRemarkList
      this.activeTab = allTypes[0]
      this.tabList = allTypes.map((item) => ({
        label: item,
        key: item
      }))
    },
    changeTab(k) {
      this.activeTab = k
    },
    handleInput(e, index) {
      this.localRemarkList[index].value = e.target.value
    },
    handleSelectChange(value, index) {
      this.localRemarkList[index].value = value
      const isAppointmentTime = !!this.localRemarkList.find(item => item.type === this.activeTab && item.fieldType === 'appointmentTime').value
      if (isAppointmentTime) {
        uni.showToast({
          title: '信息变更，请重新选择预约时间',
          icon: 'none'
        })
        // 清空预约时间
        this.localRemarkList.find(item => item.type === this.activeTab && item.fieldType === 'appointmentTime').value = ''
      }
    },
    // 校验除了预约时间外的其他字段是否都已填写
    checkOtherFieldsAndGetSomeValues() {
      const values = this.getValues()
      const result = {
        isAllFieldsFilled: true,
        // 硬编码上门激活，无解
        processTypeCode: this.activeTab === '上门激活' ? 1 : 2,
        school: this.userInfo.ouName
      }
      const orderItem = JSON.parse(uni.getStorageSync("orderItem"))
      if (orderItem) {
        result.skuId = orderItem.skuId
      }
      for (const item of values) {
        if (item.fieldType !== 'appointmentTime' && item.isRequired && !item.value) {
          result.isAllFieldsFilled = false
        }
        // 硬编码校区字段，无解
        if (item.name === '校区') {
          result.campus = item.value
        }
      }
      return result
    },
    getTimeSlotData(params) {
      return new Promise((resolve, reject) => {
        http.request({
          url: '/reservation/available-slots',
          method: 'GET',
          data: params,
          callBack: (res) => {
            resolve(res)
          },
          errCallBack: (err) => {
            reject(err)
          }
        })
      })
    },
    // 提供方法给父组件获取填写的值
    getValues() {
      // 只返回当前标签页下的留言
      return this.localRemarkList.filter(item => {
        // 必须是当前标签页下的留言
        if (item.type !== this.activeTab) {
          return false
        }
        return true
      }).map(item => {
        const { key, ...rest } = item
        const res = {
          ...rest,
          value: item.value?.trim() || '' // 确保值不包含首尾空格
        }
        if (item.fieldType === 'select' && item.options) {
          res.options = JSON.stringify(item.options)
        }
        return res
      })
    },
    async showAppointment(itemIndex) {
      const { isAllFieldsFilled, ...otherValues } = this.checkOtherFieldsAndGetSomeValues()
      if (!isAllFieldsFilled) {
        uni.showToast({
          title: '请先填写其他必填信息',
          icon: 'none'
        })
        return
      }
      const timeSlotData = await this.getTimeSlotData(otherValues)
      this.timeSlotData = timeSlotData
      this.currentAppointmentItem = this.localRemarkList[itemIndex],
      this.$refs.appointmentPopup.open()
    },
    handleAppointmentConfirm() {
      this.$refs.appointmentPopup.close()
    },
    handleAppointmentCancel() {
      if (this.currentAppointmentItem) {
        this.currentAppointmentItem.value = ''
      }
      this.$refs.appointmentPopup.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  margin-top: 20rpx;
  font-weight: bold;
  font-size: 24rpx;
}

.custom-message {
  background: #fff;
  padding: 16rpx;
  margin-top: 10rpx;
  border-radius: 10rpx;
}

.message-list {
  padding: 12rpx 0;
}

.type-description {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.message-item {
  margin-bottom: 16rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.message-row {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.message-label {
  font-size: 26rpx;
  color: #333;
  min-width: 140rpx;
  padding-right: 20rpx;
  cursor: pointer;
  
  .required {
    color: #e4393c;
    margin-right: 4rpx;
  }
}

.message-input {
  flex: 1;
  height: 44rpx;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
  
  &::placeholder {
    color: #999;
  }
}

.appointment-trigger {
  flex: 1;
  height: 44rpx;
  line-height: 44rpx;
  font-size: 26rpx;
  color: #333;
}

.selected-time {
  color: #333;
}

.placeholder {
  color: #999;

}
.select-form-item {
  width: calc(100% - 32rpx);
}
</style>
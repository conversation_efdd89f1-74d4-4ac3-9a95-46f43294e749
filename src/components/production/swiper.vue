<template>
  <swiper
    :circular="true"
    indicator-dots
    autoplay
    indicator-color="#f2f2f2"
    :interval="3000"
    :duration="1000"
    indicator-active-color="#eb2444"
  >
    <swiper-item v-for="(item, imgIndex) in imgs" :key="imgIndex">
      <image show-menu-by-longpress mode="widthFix" style="display: unset;" @tap="handleTapImage(imgIndex)" :src="item"></image>
    </swiper-item>
  </swiper>

</template>

<script>
export default {
  data() {
    return {

    }
  },
  props: {
    imgs: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleTapImage(index) {
      uni.previewImage({
        urls: this.imgs,
        current: this.imgs[index]
      })
    }
  }
}

</script>
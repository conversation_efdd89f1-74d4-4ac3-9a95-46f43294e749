<template>
	<view class="prod-items" @tap="toProdPage">
		<view class="hot-imagecont">
			<image lazy-load :src="item.pic" class="hotsaleimg"></image>
		</view>
		<view class="hot-text">
			<view class="hotprod-text">{{item.prodName}}</view>
			<view class="prod-text-info" :class="{'collection-row':showCancelCollect}">
				<view class="price">
					<text v-if="sts==2" class="deadline-price">{{i18n.timeLimitedPrice}}</text>
					<text class="symbol">￥</text>
					<text class="big-num">{{parsePrice(item.price)[0]}}</text>
					<text class="small-num">.{{parsePrice(item.price)[1]}}</text>
				</view>
				<view class="price small-num" v-if="showCancelCollect" @tap.stop="handleCancelCollection">
					{{i18n.cancleCollection}} </view>
			</view>
			<!-- <view class="evaluate-box">
				<view class="self-operate" v-if="item.shopId==1">自营</view>
				<text class="num">{{item.commentNum}}{{i18n.evaluation}}</text>
				<text class="percent">{{item.positiveRating}}%{{i18n.praise}}</text>
			</view> -->
		</view>
	</view>
</template>


<script>
	var http = require("../../utils/http.js");

	export default {
		data() {
			return {};
		},

		components: {},
		props: {
			item: Object,
			sts: String,
			showCancelCollect: Boolean,
		},

		computed:{
			i18n() {
				return this.$t('index')
			}
		},

		methods: {
			gotostore(item) {
				let url = ''
				if (item.renovationId) {
					url = '/pages/shop-feature-index/shop-feature-index0?shopId=' + item.shopId + '&renovationId=' + item.renovationId
				} else {
					url = '/packageShop/pages/shopPage/shopPage?shopId=' + item.shopId
				}
				uni.navigateTo({
					url
				})
			},
			toProdPage() {
				this.$Router.push({ path: '/pages/prod/prod', query: { prodid: this.item.prodId }})
			},
			handleCancelCollection() {
				uni.showLoading();
				var params = {
					url: "/p/user/collection/addOrCancel",
					method: "POST",
					data: this.item.prodId,
					callBack: (res) => {
						uni.showToast({
							title: res ? this.i18n.collectionAdded : this.i18n.collectionCancelled,
							duration: 1200,
							icon: 'none',
						})
						this.$emit('getCollectionProd')
						uni.hideLoading()					}
				};
				http.request(params);
			}
		}
	};
</script>
<style>
	@import "./production.css";
</style>

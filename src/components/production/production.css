page {
  background: #f4f4f4;
}
.prod-items {
  width: 345rpx;
  display: inline-block;
  background: #fff;
  box-sizing: border-box;
  margin-left: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
}

.hot-imagecont .hotsaleimg {
  width: 100%;
  height: 100%;
  border-radius: 20rpx 20rpx 0 0;
}

.hot-text .hotprod-text {
  height: 76rpx;
  line-height: 38rpx;
  font-size: 28rpx;
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #000;
}

.prod-items .hot-imagecont {
  text-align: center;
  font-size: 0;
  width: 345rpx;
  height: 345rpx;
}

.prod-items .hot-text {
  margin-top: 20rpx;
  padding: 20rpx;
}

.prod-items .hot-text .prod-info {
  font-size: 20rpx;
  color: #777;
  margin-top: 8rpx;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.prod-items .hot-text .prod-text-info {
  position: relative;
  height: 40rpx;
  line-height: 40rpx;
  font-family: Arial;
  margin-top: 20rpx;
}
.collection-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.prod-items .hot-text .prod-text-info .price {
  color: var(--primary-color);
}
.deadline-price{
  font-size: 22rpx;
  margin-right: 5rpx;
}

.evaluate-box{
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: 20rpx;
}
.self-operate {
  background: var(--primary-color);
  color: #fff;
  font-size: 20rpx;
  padding: 6rpx 8rpx;
border-radius: 10rpx;
	margin-right: 20rpx;
}
.num{
	font-size: 24rpx;
	color: #888;
	margin-right: 20rpx;
}
.percent{
	font-size: 24rpx;
	color: #888;
}

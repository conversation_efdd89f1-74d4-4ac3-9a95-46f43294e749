<template>
  <view class="wrap" :style="{height: height}" :class="{nomargin: nomargin}">
    <slot name="addonBefore"></slot>
    <input
      class="n-input"
      :password="isPassword"
      :value="value"
      :data-type="type"
      placeholder-class="inp-palcehoder"
      :placeholder="placeholder"
      @input="handleInput"
      :disabled="disabled"
    />
    <view v-if="type === 'password'" @click="changePasswordVisible" class="eyes">
      <n-i v-if="!!isPassword" icon="eyes-hide" width="32rpx" height="32rpx"/>
      <n-i v-else icon="eyes-show" width="32rpx" height="32rpx"/>
    </view>
    <slot name="addonAfter"></slot>
  </view>
</template>

<script>
import NI from '@/components/n-i'

export default {
  name: 'n-input',
  data () {
    return {
      isPassword: false,
    }
  },
  model: {
    prop: 'value',
    event: 'input', // uniapp自定义model，必须使用value和input，否则不生效
  },
  props: {
    value: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: 'text'
    },
    height: {
      type: String,
      default: '84rpx'
    },
    nomargin: {
      type: Boolean,
      default: false
    }
  },
  options: {
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  components: {
    NI,
  },
  mounted(){
    this.isPassword = this.type === 'password'
  },
  methods: {
    handleInput(event) {
      this.$emit('input', event.target.value);
    },
    changePasswordVisible() {
      this.isPassword = !this.isPassword
    },
  },
}
</script>

<style scoped>

.wrap {
  display: flex;
  box-sizing: border-box;
  width: 100%;
  height: 84rpx;
  padding: 0rpx 32rpx;
  align-items: center;
  border-radius: 16rpx;
  background: #F5F5F5;
  margin-bottom: 32rpx;
}

.wrap.nomargin {
  margin-bottom: 0;
}

.n-input {
  width: calc(100% - 32rpx);
  border: none;
  background: transparent;
}
.n-input:focus {
  outline: none;
}

.eyes {
  width: 84rpx;
  height: 84rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: -32rpx;
}

</style>
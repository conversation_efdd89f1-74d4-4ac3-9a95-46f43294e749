<template>
  <view class="appointment-time">
    <!-- 日期选择 -->
    <scroll-view scroll-x class="date-scroll" :scroll-into-view="'date-' + activeDate">
      <view class="date-list">
        <view 
          v-for="item in dateList" 
          :key="item.date"
          :id="'date-' + item.date"
          :class="['date-item', {
            'active': activeDate === item.date,
            'disabled': !hasAvailableTime(item.date)
          }]"
          v-if="hasAvailableTime(item.date)"
          @tap="!hasAvailableTime(item.date) || selectDate(item.date)"
        >
          <text class="date-text">{{ item.label }}</text>
          <text class="week-text">{{ item.week }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 时间段选择 -->
    <view class="time-grid">
      <view 
        v-for="(item, index) in currentTimeSlots" 
        :key="index"
        :class="[
          'time-item',
          !item.available ? 'disabled' : '',
          isTimeSelected(item) ? 'active' : '',
        ]"
        v-if="item.available"
        @tap="item.available && selectTime(item)"
      >
        <text class="time-text">{{ item.timeSlot }}</text>
        <text class="status-text">{{ getStatusText(item) }}</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="cancel-btn" @tap="cancel">暂不预约</view>
      <view class="confirm-btn" @tap="confirm">确认预约时间</view>
    </view>
    <Dialog ref="dialogRef" @ok="handleDialogOk" ok-text="确认预约" cancel-text="更换时间" mask-style="border-radius:20rpx 20rpx 0 0">
      <view class="">该预约时段可能没有营业员为您办理业务（具体以实际情况为准），确认预约该时段吗？</view>
    </Dialog>
  </view>
</template>

<script>
import Dialog from '@/components/popup/dialog'
export default {
  name: 'appointment-time-silly',
  components: {
    Dialog
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    // 后端返回的时间段数据
    timeSlotData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeDate: '',
      activeTimeSlot: null,
      dateList: [],
      weekMap: ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    }
  },
  computed: {
    // 当前选中日期的时间段列表
    currentTimeSlots() {
      if (!this.activeDate || !this.timeSlotData[this.activeDate]) {
        return [];
      }
      
      return this.timeSlotData[this.activeDate] || [];
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val) {
          // 解析已选时间，格式如: "2024-02-08 18:00-19:00"
          const [date, timeRange] = val.split(' ')
          
          this.activeDate = date
          // 查找匹配的时间段
          if (this.timeSlotData[date]) {
            this.activeTimeSlot = this.timeSlotData[date].find(item => item.timeSlot === timeRange)
          }
        }
      }
    },
    timeSlotData: {
      immediate: true,
      handler() {
        this.initDateList()
      }
    }
  },
  created() {
    this.initDateList()
  },
  methods: {
    initDateList() {
      const list = []
      const dates = Object.keys(this.timeSlotData)
      
      // 对日期进行排序
      dates.sort((a, b) => new Date(a) - new Date(b))
      
      // 生成日期列表
      for (const dateStr of dates) {
        const date = new Date(dateStr)
        const month = date.getMonth() + 1
        const day = date.getDate()
        const weekDay = date.getDay()
        const week = this.weekMap[weekDay]
        
        // 判断是否是今天
        const today = new Date()
        const isToday = today.getFullYear() === date.getFullYear() &&
                       today.getMonth() === date.getMonth() &&
                       today.getDate() === date.getDate()
        
        list.push({
          date: dateStr,
          label: isToday ? '今天' : `${month}月${day}日`,
          week
        })
      }
      
      this.dateList = list
      
      // 如果日期列表为空，显示提示
      if (list.length === 0) {
        uni.showToast({
          title: '暂无可预约日期',
          icon: 'none'
        })
        return
      }
      
      // 如果没有选中值，默认选中第一个可用日期
      if (!this.activeDate && list.length > 0) {
        // 找到第一个有可用时间段的日期
        const firstAvailableDate = list.find(item => this.hasAvailableTime(item.date))
        if (firstAvailableDate) {
          this.activeDate = firstAvailableDate.date
        } else {
          this.activeDate = list[0].date
        }
      }
    },
    hasAvailableTime(date) {
      if (!this.timeSlotData[date]) {
        return false
      }
      
      // 检查是否有可用的时间段
      return this.timeSlotData[date].some(item => item.available)
    },
    selectDate(date) {
      this.activeDate = date
      this.activeTimeSlot = null // 切换日期时清空已选时间
    },
    selectTime(timeSlot) {
      this.activeTimeSlot = timeSlot
    },
    isTimeSelected(timeSlot) {
      return this.activeTimeSlot && this.activeTimeSlot.timeSlot === timeSlot.timeSlot
    },
    getStatusText(timeSlot) {
      if (!timeSlot.available) {
        return timeSlot.unavailableReason || '不可预约'
      }
      if (timeSlot.maxCount) {
        // 显示剩余可预约人数
        const remainingCount = timeSlot.maxCount - timeSlot.currentCount
        return `可预约(${remainingCount})`
      }
      return '可预约'
      
    },
    cancel() {
      this.$emit('cancel')
    },
    _processAppointmentConfirmation() {
      this.$emit('input', `${this.activeDate} ${this.activeTimeSlot.timeSlot}`)
      this.$emit('confirm')
    },
    confirm() {
      if (!this.activeDate || !this.activeTimeSlot) {
        uni.showToast({
          title: '请选择预约时间',
          icon: 'none'
        })
        return
      }
      const hour = +this.activeTimeSlot.timeSlot.substring(0, 2)
      if (hour >= 18) {
        this.$refs.dialogRef.show()
        return
      }
      this._processAppointmentConfirmation()
    },
    handleDialogOk() {
      this.$refs.dialogRef.close()
      this._processAppointmentConfirmation()
    }
  }
}
</script>

<style lang="scss" scoped>
.appointment-time {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 32rpx;
}

.date-scroll {
  margin: 0 -32rpx;
  padding: 0 32rpx;
}

.date-list {
  display: flex;
  padding-bottom: 20rpx;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  min-width: 160rpx;
  
  &.active {
    background: var(--primary-color);
    color: #fff;
  }
  
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.date-text {
  font-size: 28rpx;
  margin-bottom: 4rpx;
}

.week-text {
  font-size: 24rpx;
  opacity: 0.8;
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 32rpx;
  min-height: 40vh;
  align-content: flex-start;
}

.time-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  
  &.active {
    background: var(--primary-color);
    color: #fff;
    
    .status-text {
      color: rgba(255,255,255,0.8);
    }
  }
  
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
    
    .time-text, .status-text {
      color: #999;
    }
  }
}

.time-text {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #999;
}

.footer {
  margin-top: 40rpx;
  display: flex;
  gap: 20rpx;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: var(--primary-color);
  color: #fff;
}
</style>

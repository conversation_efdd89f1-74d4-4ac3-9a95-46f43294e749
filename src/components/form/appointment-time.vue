<template>
  <view class="appointment-time">
    <!-- 日期选择 -->
    <scroll-view scroll-x class="date-scroll" :scroll-into-view="'date-' + activeDate">
      <view class="date-list">
        <view 
          v-for="item in dateList" 
          :key="item.date"
          :id="'date-' + item.date"
          :class="['date-item', {
            'active': activeDate === item.date,
            'disabled': isDateToday(item.date) && !hasAvailableTime(item.date)
          }]"
          @tap="selectDate(item.date)"
        >
          <text class="date-text">{{ item.label }}</text>
          <text class="week-text">{{ item.week }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 时间段选择 -->
    <view class="time-grid">
      <view 
        v-for="(item, index) in timeList" 
        :key="index"
        :class="[
          'time-item',
          isTimeDisabled(item, activeDate) ? 'disabled' : '',
          isTimeSelected(item) ? 'active' : '',
        ]"
        @tap="!isTimeDisabled(item, activeDate) && selectTime(item)"
      >
        <text class="time-text">{{ item.label }}</text>
        <text class="status-text">{{ getStatusText(item) }}</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="cancel-btn" @tap="cancel">暂不预约</view>
      <view class="confirm-btn" @tap="confirm">确认预约时间</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'appointment-time',
  props: {
    value: {
      type: String,
      default: ''
    },
    // 距离当前时间多少小时后可预约
    minHoursAhead: {
      type: Number,
      default: 2.5
    },
    // 预约时间段长度（小时）
    timeSlotDuration: {
      type: Number,
      default: 1,
      validator: value => [1, 2, 3].includes(value)
    },
    // 每个时间段最大预约人数
    maxAppointments: {
      type: Number,
      default: 3
    },
    // 已预约人数数据，格式：{ '2024-02-08 09:00': 2, '2024-02-08 10:00': 3 }
    appointmentCounts: {
      type: Object,
      default: () => ({})
    },
    // 开始营业时间
    startTime: {
      type: String,
      default: '09:00'
    },
    // 结束营业时间
    endTime: {
      type: String,
      default: '20:00'
    },
    // 营业日
    weekDays: {
      type: Array,
      default: () => [1, 2, 3, 4, 5, 6, 7]
    }
  },
  data() {
    return {
      activeDate: '',
      activeTime: '',
      dateList: [],
      timeList: []
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val) {
          // 解析已选时间，格式如: "2024-02-08 18:00-19:00"
          const [date, timeRange] = val.split(' ')
          // 从时间范围中提取开始时间
          const startTime = timeRange.split('-')[0]
          
          this.activeDate = date
          this.activeTime = startTime
        }
      }
    }
  },
  created() {
    this.initDateList()
    this.initTimeList()
  },
  methods: {
    initDateList() {
      const list = []
      const weekMap = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      
      // 从今天开始，生成未来7天的日期
      for (let i = 0; i < 7; i++) {
        const date = new Date()
        date.setDate(date.getDate() + i)
        
        const month = date.getMonth() + 1
        const day = date.getDate()
        const weekDay = date.getDay() // 0-6，0表示周日
        const week = weekMap[weekDay]
        
        // 检查这一天是否在营业日内
        if (!this.weekDays.includes(weekDay)) {
          continue
        }
        
        // 格式化月份和日期，确保是两位数
        const formattedMonth = month.toString().padStart(2, '0')
        const formattedDay = day.toString().padStart(2, '0')
        
        list.push({
          date: `${date.getFullYear()}-${formattedMonth}-${formattedDay}`,
          label: i === 0 ? '今天' : `${month}月${day}日`,
          week
        })
      }
      
      this.dateList = list
      
      // 如果日期列表为空，或者第一个日期不可用，就显示一个提示
      if (list.length === 0) {
        uni.showToast({
          title: '暂无可预约日期',
          icon: 'none'
        })
        return
      }
      
      // 如果没有选中值，默认选中第一个可用日期
      if (!this.activeDate && list.length > 0) {
        this.activeDate = list[0].date
      }
    },
    initTimeList() {
      const list = []
      const startTime = this.startTime.split(':')
      const endTime = this.endTime.split(':')
      // 生成时间段
      for (let i = parseInt(startTime[0]); i < parseInt(endTime[0]); i+=this.timeSlotDuration) {
        // 格式化小时，确保是两位数
        const formattedHour = i.toString().padStart(2, '0')
        const endHour = (i + this.timeSlotDuration).toString().padStart(2, '0')
        
        // 检查是否超出营业时间
        if (i + this.timeSlotDuration > this.endTime) continue
        
        list.push({
          value: `${formattedHour}:00`,
          displayValue: `${formattedHour}:00-${endHour}:00`,
          label: `${formattedHour}:00-${endHour}:00`,
          status: 'available'
        })
      }
      
      this.timeList = list
    },
    isDateToday(date) {
      const today = new Date()
      const [year, month, day] = date.split('-')
      return today.getFullYear() === Number(year) &&
             today.getMonth() + 1 === Number(month) &&
             today.getDate() === Number(day)
    },
    hasAvailableTime(date) {
      if (!this.isDateToday(date)) return true
      const now = new Date()
      const currentHour = now.getHours()
      return this.timeList.some(time => {
        const hour = parseInt(time.value)
        return hour > currentHour
      })
    },
    selectDate(date) {
      if (this.isDateToday(date) && !this.hasAvailableTime(date)) return
      this.activeDate = date
      this.activeTime = '' // 切换日期时清空已选时间
    },
    selectTime(time) {
      this.activeTime = time.value
    },
    isTimeDisabled(time, date) {
      // 检查是否已约满
      const timeKey = `${date} ${time.value}`
      if (this.appointmentCounts[timeKey] >= this.maxAppointments) {
        time.status = 'full'
        return true
      }
      
      if (!this.isDateToday(date)) return false
      
      const now = new Date()
      const currentHour = now.getHours()
      const currentMinutes = now.getMinutes()
      
      // 计算当前时间加上最小提前时间
      const minTime = new Date(now.getTime() + this.minHoursAhead * 60 * 60 * 1000)
      const minHour = minTime.getHours()
      const minMinutes = minTime.getMinutes()
      
      const timeHour = parseInt(time.value)
      const timeMinutes = 0
      
      // 比较时间
      if (timeHour < minHour || (timeHour === minHour && timeMinutes < minMinutes)) {
        return true
      }
      
      return false
    },
    isTimeSelected(time) {
      return this.activeTime === time.value
    },
    getStatusText(time) {
      const timeKey = `${this.activeDate} ${time.value}`
      const count = this.appointmentCounts[timeKey] || 0
      
      if (time.status === 'full' || count >= this.maxAppointments) {
        return '约满'
      }
      if (this.isTimeDisabled(time, this.activeDate)) {
        return '不可预约'
      }
      return `可预约(${this.maxAppointments - count})`
    },
    cancel() {
      this.$emit('cancel')
    },
    confirm() {
      if (!this.activeDate || !this.activeTime) {
        uni.showToast({
          title: '请选择预约时间',
          icon: 'none'
        })
        return
      }
      const selectedTime = this.timeList.find(t => t.value === this.activeTime)
      this.$emit('input', `${this.activeDate} ${selectedTime.displayValue}`)
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="scss" scoped>
.appointment-time {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 32rpx;
}

.date-scroll {
  margin: 0 -32rpx;
  padding: 0 32rpx;
}

.date-list {
  display: flex;
  padding-bottom: 20rpx;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  min-width: 160rpx;
  
  &.active {
    background: var(--primary-color);
    color: #fff;
  }
  
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.date-text {
  font-size: 28rpx;
  margin-bottom: 4rpx;
}

.week-text {
  font-size: 24rpx;
  opacity: 0.8;
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 32rpx;
}

.time-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  
  &.active {
    background: var(--primary-color);
    color: #fff;
    
    .status-text {
      color: rgba(255,255,255,0.8);
    }
  }
  
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
    
    .time-text, .status-text {
      color: #999;
    }
  }
}

.time-text {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #999;
}

.footer {
  margin-top: 40rpx;
  display: flex;
  gap: 20rpx;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: var(--primary-color);
  color: #fff;
}
</style>

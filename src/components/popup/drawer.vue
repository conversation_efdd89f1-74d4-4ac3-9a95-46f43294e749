<template>
  <view class="drawer-container" :class="{ active: show }" :style="{ zIndex: zIndex }">
    <view class="drawer-overlay" @click="maskCloseable ? close : undefined"></view>
    <view class="drawer-content" :style="contentStyle">
      <!-- 右上角关闭按钮 -->
      <view class="drawer-close" @click="close">×</view>
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    height: {
      type: String,
      default: 'auto', // 抽屉高度
    },
    maskCloseable: {
      type: Boolean,
      default: true,
    },
    paddingBottom: {
      type: String,
      default: '0',
    },
    zIndex: {
      type: Number,
      default: 10,
    },
  },
  computed: {
    contentStyle() {
      return {
        height: this.height,
        transform: this.show ? 'translateY(0)' : `translateY(100%)`,
        paddingBottom: this.paddingBottom,
      };
    },
  },
  methods: {
    close() {
      this.$emit('update:show', false);
    },
  },
};
</script>

<style>
.drawer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  z-index: 10;
  transition: opacity 0.3s ease-in-out;
  opacity: 0;
  pointer-events: none;
}
.drawer-container.active {
  opacity: 1;
  pointer-events: auto;
}
.drawer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}
.drawer-content {
  position: relative;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  z-index: 2;
  transition: transform 0.3s ease-in-out;
}
/* 右上角关闭按钮样式 */
.drawer-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #888;
  cursor: pointer;
}
.drawer-close:hover {
  color: #000;
}
</style>
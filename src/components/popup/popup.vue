<template>
  <view class="modal" v-show="isShow">
    <view class="modal-cancel" @tap="hideModal" :style="maskStyle"></view>
    <view class="bottom-dialog-body" :style="contentStyle">
      <slot></slot>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {}
  },
  props: ["isShow", "contentStyle", "maskStyle"],
  methods: {
    showModal: function () {
      this.isShow = false;
    },
    hideModal: function () {
      this.isShow = true;
    },
  }
}
</script>
<style scoped>
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
}

.modal-cancel {
  position: absolute;
  z-index: 2000;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.3);
}

.bottom-dialog-body {
  padding: 20px;
  position: absolute;
  z-index: 3000;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 230px;
  background-color: #fff;
  border-radius: 30rpx;
}
</style>
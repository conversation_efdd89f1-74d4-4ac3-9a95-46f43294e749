<template>
  <view class="modal" v-if="open">
    <view class="modal-mask" :style="maskStyle"></view>
    <view class="modal-body" :style="contentStyle">
      <view class="modal-content">
        <slot></slot>
      </view>
      <view class="btns">
        <button v-if="showCancelButton" class="cancel-btn" @click="handleCancel">{{ cancelText }}</button>
        <button v-if="showOkButton" class="cfm-btn" @click="handleConfirm">{{ okText }}</button>
      </view>
    </view>
  </view>
</template>
<script>
import config from '../../utils/config.js'
export default {
  data() {
    return {
      open: false,
      popupAdInfo: {},
      resourcesUrl: config.picDomain,
      context: null,
    }
  },
  options: {
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  // props: ["contentStyle", "maskStyle",],
  props: {
    contentStyle: {
      type: String,
      default: ""
    },
    maskStyle: {
      type: String,
      default: ""
    },
    okText: {
      type: String,
      default: "确认"
    },
    cancelText: {
      type: String,
      default: "取消"
    },
    showOkButton: {
      type: Boolean,
      default: true
    },
    showCancelButton: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    close: function () {
      this.open = false
    },
    show: function (context) {
      this.open = true
      if (context) {
        this.context = context
      }
    },
    handleCancel() {
      this.$emit('cancel', this.context)
      this.close();
      this.context = null;
    },
    handleConfirm() {
      this.$emit('ok', this.context)
    },
  }
}
</script>
<style scoped>
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
}

.modal-mask {
  position: absolute;
  z-index: 2000;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-body {
  position: absolute;
  z-index: 3000;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75%;
  max-height: 70%;
  overflow: auto;
  background-color: #fff;
  border-radius: 32rpx;
  padding: 48rpx 32rpx 0;
}
@media (min-width: 768px) {
  .modal-body {
    width: 50%;
  }
}

.modal-content {
  margin-bottom: 40rpx;
}

.btns {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  position: sticky;
  bottom: 0;
  background-color: #fff;
  padding: 32rpx 0;
}

.btns button {
  width: 40%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cancel-btn {
  background-color: #999;
}

.cfm-btn {
  background-color: var(--primary-color);
}


</style>
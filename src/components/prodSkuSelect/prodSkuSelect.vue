<template>
	<!-- 套装商品sku弹窗 -->
	<view class="pup-sku">
		<view class="pup-sku-main" @tap.stop>
			<view class="pup-sku-header">
				<view class="close" @tap="closePopup"></view>
				<view class="pup-sku-img">
					<image :src="defaultSku.pic || pic"></image>
				</view>
				<view class="pup-sku-prod">
					<!-- <view class="prod-title">{{prodName}}</view> -->
					<view v-if="findSku" class="pup-sku-price">￥
						<text class="pup-sku-price-int">{{parsePrice(defaultSku.matchingPrice || defaultSku.price)[0]}}</text>
						.{{parsePrice(defaultSku.matchingPrice || defaultSku.price)[1]}}
					</view>
					<view v-else class="pup-sku-price">{{i18n.outOfStock}}</view>
					<view class="pup-sku-prop">
						<view>
							<text>{{i18n.selected}}</text>
							<text decode="true">
								{{'&nbsp;' + defaultSku.skuName}}<text v-if="leastNum">{{'&nbsp;'+leastNum+'&nbsp;'}}{{i18n.piece}}</text>
							</text>
						</view>
						<view v-if="findSku">
							<text>{{i18n.inventory}}</text>
							<text decode="true">{{'&nbsp;'+defaultSku.stocks}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="pup-sku-body">
				<!-- 规格 -->
				<view class="pup-sku-area">
					<view class="sku-box" v-if="skuList.length">
						<view class="items sku-text" v-for="(skuLine, key) in skuGroup" :key="key">
							<text class="sku-kind">{{key}}</text>
							<view class="con">
								<text
									v-for="skuLineItem in skuLine"
									:key="skuLineItem"
									class="sku-choose-item"
									:class="
										[selectedProp.find(el => el.key === key && el.value === skuLineItem) ? 'active':'',
										isSkuLineItemNotOptional(allProperties,selectedPropObj,key,skuLineItem,propKeys) ? 'dashed' : '']
									"
									@click="toChooseItem(skuLineItem, key)"
								>{{skuLineItem}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="pup-sku-footer" :class="{gray: !findSku}">
				<view class="btn buy" @tap="submit">{{i18n.confirm}}</view>
			</view>
		</view>
	</view>
	<!-- 规格弹窗 end -->
</template>

<script>
export default {
	data() {
		return {
			groupSkuList: {},
			defaultSku: '',

			propKeys: [],
			selectedPropObj: {},
			skuGroup: {},
			allProperties: [],
			findSku: false
		};
	},
	props: {
		pic: {
			type: String,
			default: ''
		},
		isMain: {
			type: Boolean,
			default: false
		},
		leastNum: {
			type: Number,
			default: 0
		},
		skuName: {
			type: String,
			default: ''
		},
		skuList: {
			type: Array,
			default() {
				return []
			}
		},
		skuId: {
			type: Number,
			default: 0
		},
		comboId: {
			type: Number,
			default: 0
		}
	},
	computed: {
		i18n() {
			return this.$t('index')
		}
	},
	mounted() {
		this.groupSku()
	},
	methods: {
		closePopup() {
			this.$emit('closeSkuPop')
		},
		groupSku() {
			const skuList = this.skuList
			var skuGroup = {};
			var allProperties = [];
			var propKeys = [];
			var selectedPropObj = {}
			var defaultSku = null;
			for (var i = 0; i < skuList.length; i++) {
				var isDefault = false;
				if (!defaultSku && (skuList[i].skuId == this.skuId)) {
					defaultSku = skuList[i];
					isDefault = true;
				}
				var properties = skuList[i].properties; //版本:公开版;颜色:金色;内存:64GB
				allProperties.push(properties);
				var propList = properties.split(";"); // ["版本:公开版","颜色:金色","内存:64GB"]

				for (var j = 0; j < propList.length; j++) {

					var propval = propList[j].split(":"); //["版本","公开版"]
					var props = skuGroup[propval[0]]; //先取出 版本对应的值数组

					//如果当前是默认选中的sku，把对应的属性值 组装到selectedProp
					if (isDefault) {
						propKeys.push(propval[0]);
						selectedPropObj[propval[0]] = propval[1];
					}

					if (props == undefined) {
						props = []; //假设还没有版本，新建个新的空数组
						props.push(propval[1]); //把 "公开版" 放进空数组
					} else {
						if (props.indexOf(propval[1]) === -1) { //如果数组里面没有"公开版"
							props.push(propval[1]); //把 "公开版" 放进数组
						}
					}
					skuGroup[propval[0]] = props; //最后把数据 放回版本对应的值
				}
			}
			this.defaultSku = defaultSku
			this.propKeys = propKeys
			this.selectedPropObj = selectedPropObj
			this.skuGroup = skuGroup
			this.allProperties = allProperties
			this.parseSelectedObjToVals(skuList);
			this.$forceUpdate()
		},

		/**
		 * 将已选的 {key:val,key2:val2}转换成 [val,val2]
		 */
		parseSelectedObjToVals: function(skuList) {
			var selectedPropObj = this.selectedPropObj
			var selectedProperties = "";
			var selectedProp = [];
			for (var key in selectedPropObj) {
				// selectedProp.push(selectedPropObj[key]);
				selectedProp.push({key: key, value: selectedPropObj[key] });
				selectedProperties += key + ":" + selectedPropObj[key] + ";";
			}
			selectedProperties = selectedProperties.substring(0, selectedProperties.length - 1);
			this.selectedProp = selectedProp
			this.selectedProperties = selectedProperties
			this.selectedPropObj = selectedPropObj

			var findSku = false;
			for (var i = 0; i < skuList.length; i++) {
				// 解决排序问题导致无法匹配
				if (
					this.compareArray(selectedProperties.split(';').sort(),
						skuList[i].properties.split(';').sort())
				) {
					findSku = true;
					this.defaultSku = skuList[i]
					break;
				}
			}
			this.findSku = findSku
		},

		/**
		 * 比较两个数组中的元素是否相等
		 * @param a1 第一个数组
		 * @param a2 第二个数组
		 * @return boolean 两个数组中的元素都相等则返回 true，反之返回 false
		 */
		compareArray(a1, a2) {
			if (!a1 || !a2) {
				return false;
			}
			if (a1.length !== a2.length) {
				return false;
			}
			for (var i = 0, n = a1.length; i < n; i++) {
				if (a1[i] !== a2[i]) {
					return false;
				}
			}
			return true;
		},

		/**
		 * 判断当前的规格值 是否可以选
		 */
		isSkuLineItemNotOptional(allProperties, selectedPropObj, key, item, propKeys) {
			var selectedPropObj = Object.assign({}, selectedPropObj)
			var properties = "";
			selectedPropObj[key] = item;
			for (var j = 0; j < propKeys.length; j++) {
				properties += propKeys[j] + ":" + selectedPropObj[propKeys[j]] + ";";
			}
			properties = properties.substring(0, properties.length - 1);
			for (var i = 0; i < allProperties.length; i++) {
				if (properties == allProperties[i]) {
					return false;
				}
			}
			for (var i = 0; i < allProperties.length; i++) {
				if (allProperties[i].indexOf(item) >= 0) {
					return true;
				}
			}
			return false;
		},

		/**
		 * 规格点击事件
		 */
		toChooseItem(skuLineItem, key) {
			this.selectedPropObj[key] = skuLineItem;
			this.parseSelectedObjToVals(this.skuList);
		},

		submit() {
			if (this.findSku) {
				console.log(this.defaultSku, this.isMain);
				this.$emit('setSku', this.defaultSku, this.isMain)
			}
		}
	}
}
</script>

<style scoped>

/** 规格弹窗**/

.pup-sku {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.pup-sku-main {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 375px;
  max-height: 500px;
  background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
}

.close {
  color: #aaa;
  border-radius: 12px;
  line-height: 20px;
  text-align: center;
  height: 20px;
  width: 20px;
  font-size: 18px;
  padding: 1px;
  top: 10px;
  right: 10px;
  position: absolute;
}

.close::before {
  content: '\2716';
}

.pup-sku-header {
  position: relative;
  font-size: 16px;
  color: #333;
  padding: 20rpx 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f2f2f2;
  padding-top: 35rpx;
	border-radius: 20rpx 20rpx 0 0;
}

.pup-sku-img {
  display: inline-block;
  width: 27%;
  height: 180rpx;
  vertical-align: middle;
  border-radius: 10rpx;
}
.pup-sku-img image {
  display: block;
  width: 180rpx;
  height: 100%;
}

.pup-sku-prod {
  display: inline-block;
  width: 70%;
  vertical-align: middle;
  padding-left: 10rpx;
}

.prod-title {
  font-size: 28rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin: 15rpx 0;
}

.pup-sku-price {
  display: inline-block;
  height: 1.5em;
  line-height: 1.5em;
  color: #e43130;
  font-size: 26rpx;
}
.group-sku-pri {
  display: flex;
  align-items: center;
}

.pup-sku-price-int {
  font-size: 34rpx;
}

.pup-sku-prop {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  font-size: 26rpx;
  color: #333;
  line-height: 1.4em;
  padding-right: 10px;
  margin-top: 16rpx;
	overflow: unset;
}

.sel-sku {
  display: inline-block;
  max-width: 340rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 5px;
}

.pup-sku-prop text:first-child {
  color: #999;
}

.pup-sku-body {
  padding-bottom: calc(110rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
  /* overflow: auto; */
}

.pup-sku-main {
  max-height: 750px;
}

.pup-sku-area {
  max-height: 245px;
  overflow-y: scroll;
}
.pup-sku-area .sku-box{
  max-height: 245px;
  overflow-y: scroll;
}

.pup-sku-area .sku-kind {
  font-size: 12px;
  color: #999;
  margin: 0 10px;
  height: 40px;
  line-height: 40px;
}
.pup-sku-area .con {
	padding-right: 20rpx;
}

.pup-sku-area .sku-choose {
  overflow: hidden;
  margin-bottom: 3px;
}

.sku-choose-item {
  display: inline-block;
  min-width: 100rpx;
  max-width: 540rpx;
  padding: 0 10rpx;
  overflow: hidden;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  margin-left: 10px;
  margin-bottom: 10px;
  border-radius: 30rpx;
  color: #333;
  background-color: #fff;
  font-size: 14px;
  border: 1px solid #E9EBED;
}

.sku-choose-item.active {
  background-color: #fff;
  color: var(--primary-color);
  border: 1px solid var(--primary-color) !important;
}

.sku-choose-item.gray {
  background-color: #f9f9f9;
  color: #ddd;
}
.sku-choose-item.dashed {
  border: 1px dashed #ccc;
}


.pup-sku-footer {
  position: fixed;
  bottom: 0;
  bottom: constant(safe-area-inset-bottom);
  bottom: env(safe-area-inset-bottom);
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  align-items: center;
  height: 110rpx;
  z-index: 999;
  /* box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05); */
}

.pup-sku-footer .btn {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  font-size: 30rpx;
  flex-flow: column;
  border-radius: 60rpx;
  height: 2.8em;
  width: 45%;
  margin: 0 15rpx;
}

.pup-sku-footer .btn.cart {
  background: #584e61;
  color: #fff;
}

.pup-sku-footer .btn.buy {
  background: var(--primary-color);
  color: #fff;
}
.pup-sku-footer .btn.pre-sale-buy {
  background: #e43130;
  color: #fff;
}
.pup-sku-footer.gray .btn {
  background: #eee;
  color: #999;
}

</style>

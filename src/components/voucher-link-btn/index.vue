<template>
  <view
    class="voucher-btn"
    :class="{unavalible: isUsed || isExpired}"
    @tap="handleClickVocherBtn"
  >
    核销码
  </view>
</template>

<script>
export default {
  name: 'voucher-link-btn',
  data () {
    return {
    }
  },
  props: {
    orderItem: {
      type: Object,
      default: () => ({})
    }
  },
  options: {
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  methods: {
    handleClickVocherBtn() {
      uni.navigateTo({
        url: `/pages/voucherCode/voucherCode?orderNum=${this.orderItem.orderNumber}`,
      })
    }
  },
  computed: {
    isUsed() {
      return !!this.orderItem.writeOffStatus
    },
    isExpired() {
      if (this.orderItem.writeOffEnd) {
        const nowTime = new Date().getTime();
        const writeOffEndTime = new Date(this.orderItem.writeOffEnd).getTime();
        return nowTime > writeOffEndTime;
      }
      return false
    }
  },
  mounted () {

  },
}
</script>

<style scoped>
.voucher-btn {
  border-radius: 32rpx;
  background-color: var(--primary-color);
  width: 180rpx;
	height: 56rpx;
  line-height: 56rpx;
  font-size: 24rpx;
  text-align: center;
  color: white;
}

.voucher-btn.unavalible {
  background-color:  #cdcdcd;
}
</style>
<template>
	<view class="uni-stat__select">
		<span v-if="label" class="uni-label-text hide-on-phone">{{label + '：'}}</span>
		<view class="uni-stat-box" :class="{'uni-stat__actived': current}">
			<view class="uni-select" :class="{'uni-select--disabled':disabled}" :style="uniSelectStyle">
				<view class="uni-select__input-box" @click="toggleSelector">
					<view v-if="current" class="uni-select__input-text" :style="textStyle">{{textShow}}</view>
					<view v-else class="uni-select__input-text uni-select__input-placeholder" :style="placeholderStyle">{{typePlaceholder}}</view>
					<view v-if="current && clear && !disabled" @click.stop="clearVal" >
            <n-i icon="close" ext="png" width="24rpx" height="24rpx"/>
					</view>
					<view v-else>
            <n-i icon="chevron-down" width="24rpx" height="24rpx"/>
					</view>
				</view>
				<view class="uni-select--mask" v-if="showSelector" @click="toggleSelector" />
				<view class="uni-select__selector" v-if="showSelector" :style="selectorStyle">
					<view class="uni-popper__arrow" :style="arrowStyle"></view>
					<scroll-view scroll-y="true" class="uni-select__selector-scroll">
						<view class="uni-select__search-box" v-if="showSearch">
							<input type="text" v-model="searchText" placeholder="点击搜索" class="uni-select__search-input" />
						</view>
						<view class="uni-select__selector-empty" v-if="filteredData.length === 0">
							<text>{{emptyTips}}</text>
						</view>
						<view v-else class="uni-select__selector-item" v-for="(item,index) in filteredData" :key="index" @click="change(item)" :style="optionStyle">
							<text :class="{'uni-select__selector__disabled': item.disable}">{{formatItemName(item)}}</text>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import NI from '@/components/n-i'
	/**
	 * DataChecklist 数据选择器
	 * @description 通过数据渲染的下拉框组件
	 * @tutorial https://uniapp.dcloud.io/component/uniui/uni-data-select
	 * @property {String} value 默认值
	 * @property {Array} localdata 本地数据 ，格式 [{text:'',value:''}]
	 * @property {Boolean} clear 是否可以清空已选项
	 * @property {Boolean} emptyText 没有数据时显示的文字 ，本地数据无效
	 * @property {String} label 左侧标题
	 * @property {String} placeholder 输入框的提示文字
	 * @property {Boolean} disabled 是否禁用
	 * @event {Function} change  选中发生变化触发
	 */

	export default {
		name: "uni-data-select",
    options: {
      virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
    },
    components: {
      NI
    },
		mixins: [uniCloud.mixinDatacom || {}],
		props: {
			localdata: {
				type: Array,
				default () {
					return []
				}
			},
			value: {
				type: [String, Number],
				default: ''
			},
			modelValue: {
				type: [String, Number],
				default: ''
			},
			label: {
				type: String,
				default: ''
			},
			placeholder: {
				type: String,
				default: '请选择'
			},
			emptyTips: {
				type: String,
				default: '无选项'
			},
			clear: {
				type: Boolean,
				default: true
			},
			defItem: {
				type: Number,
				default: 0
			},
			disabled: {
				type: Boolean,
				default: false
			},
			// 格式化输出 用法 field="_id as value, version as text, uni_platform as label" format="{label} - {text}"
			format: {
				type: String,
				default: ''
			},
			showSearch: {
				type: Boolean,
				default: false
			},
      placeholderStyle: {
        type: String,
        default: ''
      },
      inputTextStyle: {
        type: String,
        default: ''
      },
      height: {
        type: [String, Number],
        default: '35px'
      },
      fontSize: {
        type: [String, Number],
        default: '14px'
      },
      textColor: {
        type: String,
        default: '#222'
      },
      dropdownStyle: {
        type: String,
        default: ''
      },
      optionStyle: {
        type: String,
        default: ''
      },
      placement: {
        type: String,
        default: 'bottom',
        validator: (value) => ['bottom', 'top'].includes(value)
      }
		},
		data() {
			return {
				showSelector: false,
				current: '',
        searchText: '', // 新增搜索文本
				mixinDatacomResData: [],
				apps: [],
				channels: [],
				cacheKey: "uni-data-select-lastSelectedValue",
			};
		},
		created() {
			this.debounceGet = this.debounce(() => {
				this.query();
			}, 300);
			if (this.collection && !this.localdata.length) {
				this.debounceGet();
			}
		},
		computed: {
			typePlaceholder() {
				const text = {
					'opendb-stat-app-versions': '版本',
					'opendb-app-channels': '渠道',
					'opendb-app-list': '应用'
				}
				const common = this.placeholder
				const placeholder = text[this.collection]
				return placeholder ?
					common + placeholder :
					common
			},
			valueCom(){
				// #ifdef VUE3
				return this.modelValue;
				// #endif
				// #ifndef VUE3
				return this.value;
				// #endif
			},
			textShow(){
				// 长文本显示
				let text = this.current;
				if (text.length > 10) {
					return text.slice(0, 25) + '...';
				}
				return text;
			},
      filteredData() {
				if (!this.searchText) {
					return this.mixinDatacomResData;
				}
				return this.mixinDatacomResData.filter(item => {
					return this.formatItemName(item).toLowerCase().includes(this.searchText.toLowerCase());
				});
			},
      uniSelectStyle() {
        return `height: ${typeof this.height === 'number' ? this.height + 'px' : this.height}; font-size: ${typeof this.fontSize === 'number' ? this.fontSize + 'px' : this.fontSize};`
      },
      selectorStyle() {
        const position = this.placement === 'top' 
          ? 'bottom: calc(100% + 12px); top: auto;' 
          : 'top: calc(100% + 12px); bottom: auto;'
        
        return `${position} ${this.dropdownStyle}`
      },
      arrowStyle() {
        const position = this.placement === 'top' ? 'bottom: -6px;' : 'top: -6px;'
        const transform = this.placement === 'top' ? 'transform: rotate(180deg);' : ''
        
        return `${position} ${transform}`
      },
      textStyle() {
        return `color: ${this.textColor}; ${this.inputTextStyle}`
      }
		},

		watch: {
			localdata: {
				immediate: true,
				handler(val, old) {
					if (Array.isArray(val) && old !== val) {
						this.mixinDatacomResData = val
					}
				}
			},
			valueCom(val, old) {
				this.initDefVal()
			},
			mixinDatacomResData: {
				immediate: true,
				handler(val) {
					if (val.length) {
						this.initDefVal()
					}
				}
			},

		},
		methods: {
			debounce(fn, time = 100){
				let timer = null
				return function(...args) {
					if (timer) clearTimeout(timer)
					timer = setTimeout(() => {
						fn.apply(this, args)
					}, time)
				}
			},
			// 执行数据库查询
			query(){
				this.mixinDatacomEasyGet();
			},
			// 监听查询条件变更事件
			onMixinDatacomPropsChange(){
				if (this.collection) {
					this.debounceGet();
				}
			},
			initDefVal() {
				let defValue = ''
				if ((this.valueCom || this.valueCom === 0) && !this.isDisabled(this.valueCom)) {
					defValue = this.valueCom
				} else {
					let strogeValue
					if (this.collection) {
						strogeValue = this.getCache()
					}
					if (strogeValue || strogeValue === 0) {
						defValue = strogeValue
					} else {
						let defItem = ''
						if (this.defItem > 0 && this.defItem <= this.mixinDatacomResData.length) {
							defItem = this.mixinDatacomResData[this.defItem - 1].value
						}
						defValue = defItem
					}
          if (defValue || defValue === 0) {
					  this.emit(defValue)
          }
				}
				const def = this.mixinDatacomResData.find(item => item.value === defValue)
				this.current = def ? this.formatItemName(def) : ''
			},

			/**
			 * @param {[String, Number]} value
			 * 判断用户给的 value 是否同时为禁用状态
			 */
			isDisabled(value) {
				let isDisabled = false;

				this.mixinDatacomResData.forEach(item => {
					if (item.value === value) {
						isDisabled = item.disable
					}
				})

				return isDisabled;
			},

			clearVal() {
				this.emit('')
				if (this.collection) {
					this.removeCache()
				}
			},
			change(item) {
				if (!item.disable) {
					this.showSelector = false
					this.current = this.formatItemName(item)
          this.searchText = '';
					this.emit(item.value)
				}
			},
			emit(val) {
				this.$emit('input', val)
				this.$emit('update:modelValue', val)
				this.$emit('change', val)
				if (this.collection) {
					this.setCache(val);
				}
			},
			toggleSelector() {
				if (this.disabled) {
					return
				}

				this.showSelector = !this.showSelector
			},
			formatItemName(item) {
				let {
					text,
					value,
					channel_code
				} = item
				channel_code = channel_code ? `(${channel_code})` : ''

				if (this.format) {
					// 格式化输出
					let str = "";
					str = this.format;
					for (let key in item) {
						str = str.replace(new RegExp(`{${key}}`,"g"),item[key]);
					}
					return str;
				} else {
					return this.collection.indexOf('app-list') > 0 ?
						`${text}(${value})` :
						(
							text ?
							text :
							`未命名${channel_code}`
						)
				}
			},
			// 获取当前加载的数据
			getLoadData(){
				return this.mixinDatacomResData;
			},
			// 获取当前缓存key
			getCurrentCacheKey(){
				return this.collection;
			},
			// 获取缓存
			getCache(name=this.getCurrentCacheKey()){
				let cacheData = uni.getStorageSync(this.cacheKey) || {};
				return cacheData[name];
			},
			// 设置缓存
			setCache(value, name=this.getCurrentCacheKey()){
				let cacheData = uni.getStorageSync(this.cacheKey) || {};
				cacheData[name] = value;
				uni.setStorageSync(this.cacheKey, cacheData);
			},
			// 删除缓存
			removeCache(name=this.getCurrentCacheKey()){
				let cacheData = uni.getStorageSync(this.cacheKey) || {};
				delete cacheData[name];
				uni.setStorageSync(this.cacheKey, cacheData);
			},
		}
	}
</script>

<style lang="scss" scoped>
	$uni-base-color: #6a6a6a !default;
	$uni-main-color: #222;
	$uni-secondary-color: #909399 !default;
	$uni-border-3: #e5e5e5;


	/* #ifndef APP-NVUE */
	@media screen and (max-width: 500px) {
		.hide-on-phone {
			display: none;
		}
	}

	/* #endif */
	.uni-stat__select {
		display: flex;
		align-items: center;
		justify-content: center;
		// padding: 15px;
		/* #ifdef H5 */
		cursor: pointer;
		/* #endif */
		width: 100%;
		height: 100%;
		flex: 1;
		box-sizing: border-box;
	}

	.uni-stat-box {
		width: 100%;
		flex: 1;
	}

	.uni-stat__actived {
		width: 100%;
		flex: 1;
		// outline: 1px solid #2979ff;
	}

	.uni-label-text {
		font-size: 14px;
		font-weight: bold;
		color: $uni-base-color;
		margin: auto 0;
		margin-right: 5px;
	}

	.uni-select {
		font-size: 14px;
		box-sizing: border-box;
		border-radius: 4px;
		// padding: 0 5px;
		// padding-left: 10px;
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		user-select: none;
		/* #endif */
		flex-direction: row;
		align-items: center;
		width: 100%;
		flex: 1;
		height: 35px;

		&--disabled {
			/* background-color: #f5f7fa; */
			cursor: not-allowed;
		}
	}

	.uni-select__label {
		font-size: 16px;
		// line-height: 22px;
		height: 35px;
		padding-right: 10px;
		color: $uni-secondary-color;
	}

	.uni-select__input-box {
		height: 35px;
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex: 1;
		flex-direction: row;
		align-items: center;
	}

	.uni-select__input {
		flex: 1;
		font-size: 14px;
		height: 22px;
		line-height: 22px;
	}

	.uni-select__input-plac {
		font-size: 14px;
		color: $uni-secondary-color;
	}

	.uni-select__selector {
		/* #ifndef APP-NVUE */
		box-sizing: border-box;
		/* #endif */
		position: absolute;
		left: 0;
		width: 100%;
		background-color: #FFFFFF;
		border: 1px solid #EBEEF5;
		border-radius: 6px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		z-index: 3;
		padding: 4px 0;
	}

	.uni-select__selector-scroll {
		/* #ifndef APP-NVUE */
		max-height: 200px;
		box-sizing: border-box;
		/* #endif */
	}

	/* #ifdef H5 */
	@media (min-width: 768px) {
		.uni-select__selector-scroll {
			max-height: 600px;
		}
	}
	/* #endif */

	.uni-select__selector-empty,
	.uni-select__selector-item {
		/* #ifndef APP-NVUE */
		display: flex;
		cursor: pointer;
		/* #endif */
		line-height: 35px;
		font-size: 14px;
		text-align: center;
		/* border-bottom: solid 1px $uni-border-3; */
		padding: 0px 10px;
	}

	.uni-select__selector-item:hover {
		background-color: #f9f9f9;
	}

	.uni-select__selector-empty:last-child,
	.uni-select__selector-item:last-child {
		/* #ifndef APP-NVUE */
		border-bottom: none;
		/* #endif */
	}

	.uni-select__selector__disabled {
		opacity: 0.4;
		cursor: default;
	}

	/* picker 弹出层通用的指示小三角 */
	.uni-popper__arrow,
	.uni-popper__arrow::after {
		position: absolute;
		display: block;
		width: 0;
		height: 0;
		border-color: transparent;
		border-style: solid;
		border-width: 6px;
	}

	.uni-popper__arrow {
		filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
		left: 10%;
		margin-right: 3px;
		border-top-width: 6px;
		border-bottom-width: 6px;
		border-top-color: #EBEEF5;
		border-bottom-color: #EBEEF5;

		&::after {
			content: " ";
			margin-left: -6px;
			border-width: 6px;
			border-top-color: #fff;
			border-bottom-color: #fff;
			position: absolute;
		}
	}

	.uni-select__input-text {
		// width: 280px;
		width: 100%;
		color: $uni-main-color;
		white-space: nowrap;
		text-overflow: ellipsis;
		-o-text-overflow: ellipsis;
		overflow: hidden;
		font-size: 16px;
	}

	.uni-select__input-placeholder {
		color: $uni-base-color;
		font-size: 16px;
	}

	.uni-select--mask {
		position: fixed;
		top: 0;
		bottom: 0;
		right: 0;
		left: 0;
		z-index: 2;
	}

  .uni-select__search-box {
    display: flex;
    align-items: center;
    padding: 0 10px;
    height: 35px;
    width: 100%;
    background-color: #fff;
  }
</style>

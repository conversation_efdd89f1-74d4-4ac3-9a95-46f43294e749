.labels {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.font-label {
  /* 限制高度，多余内容隐藏，以实现多余标签不显示效果 */
  height: 34rpx; 
  overflow: hidden;
}
.corner-mark-label {
  height: 34rpx;
  overflow: hidden;
}

.font-label-item {
  display: inline-block;
  font-size: 24rpx;
  height: 34rpx; 
  vertical-align: top;
}
.corner-mark-label-item {
  position: relative;
  display: inline-block;
  margin-right: 20rpx;
  height: 34rpx;
  vertical-align: top;
  margin-bottom: 6rpx;
}
.corner-mark-style {
  display: inline-block;
  font-size: 20rpx;
  padding: 0px 8rpx;
  border: 2rpx solid transparent;
  border-radius: 4rpx;
  vertical-align: top;
  white-space: nowrap;
  height: 30.5rpx;
}
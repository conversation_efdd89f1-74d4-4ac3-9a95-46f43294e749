<template>
<view class="labels">
  <!-- 纯文字标签 -->
  <view class="font-label" v-if="!!fontLabels.length">
    <view 
      v-for="(item, index) in fontLabels" 
      :key="item.labelId" 
      :style="{color: item.textColor}" 
      class="font-label-item"
    >
      <text v-if="index !== 0" style="color: #888; margin: 0rpx 8rpx">|</text>
      {{ item.labelName }}
    </view>
  </view>
  <!-- 角标标签 -->
  <view class="corner-mark-label" v-if="!!cornerMarkLabels.length">
    <view 
      v-for="(item) in cornerMarkLabels" 
      :key="item.labelId" 
      class="corner-mark-label-item"
    > 
      <text
        v-if="item.style === 'background-only'" 
        class="corner-mark-style"
        :style="{color: '#fff', backgroundColor: item.bgColor}"
      >
        {{ item.labelName }}
      </text>
      <view 
        v-if="item.style === 'font-only'"  
        class="corner-mark-style"
        :style="{color: item.bgColor, borderColor: item.bgColor}"
      >
        {{ item.labelName }}
      </view>
      <view 
        v-if="item.style === 'both-colors'" 
        class="corner-mark-style"
        :style="{color: item.textColor, backgroundColor: item.bgColor}"
      >
        {{ item.labelName }}
      </view>
    </view>	
  </view>
</view>
</template>
<script>
export default {
  props: {
    labels: {
      type: [Array, null, undefined],
      default: () => [],
    }
  },
  computed: {
    fontLabels() {
      return this.labels?.filter(item => item.shown == true && item.type == '1') || []
    },
    cornerMarkLabels() {
      return this.labels?.filter(item => item.shown == true && item.type == '2' && item.position === 'below-headline') || []
    },
  },
}

</script>
<style scoped>
@import url('./index.css');
</style>
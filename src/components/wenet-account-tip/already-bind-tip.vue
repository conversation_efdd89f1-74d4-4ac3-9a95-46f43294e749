<template>
  <popup :is-show="popupVisible">
    <view class="popup-content" v-if="!loading">
      <view class="popup-title">
        <view>
          <text>您的账号已与下方wenet账号绑定</text>
        </view>
        <text class="popup-wenet-account"></text>
        <view>
          <text>如需更换，请联系客服</text>
        </view>
      </view>
      <view class="popup-desc">
        可在微信公众号，wenet服务中心-网络客服-在线客服，与客服联系  
      </view>
      <view class="popup-btns">
        <view class="popup-btn" @tap="handleConfirm">我知道了</view>
      </view>
    </view>
  </popup>
</template>

<script>
import popup from '@/components/popup/popup'
import http from '@/utils/http';

export default {
  data() {
    return {
      popupVisible: false,
      loading: false,
      bindedWenetAccount: '',
      bindedSchoolName: '',
    }
  },
  
  methods: {
    handleConfirm() {
      this.$emit('confirm')
      this.popupVisible = false
    },
    showPopup() {
      this.popupVisible = true
    },
  }

}

</script>

<style scoped>
.popup-content {
  padding: 0 30upx;
  text-align: center;
}
.popup-title {
  font-size: 32upx;
  color: #333;
  margin-bottom: 20upx;
}

.popup-wenet-school {
  color: var(--primary-color);
}
.popup-wenet-account {
  color: var(--primary-color);
}

.popup-desc {
  font-size: 24upx;
  color: #999;
  margin-bottom: 40upx;
}

.popup-btns {
  display: flex;
  justify-content: center;
}

.popup-btn {
  width: 300upx;
  height: 80upx;
  line-height: 80upx;
  text-align: center;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 40upx;
}

</style>
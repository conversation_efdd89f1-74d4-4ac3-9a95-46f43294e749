<template>
  <view class="bottom-login-nav" :class="isMini ? 'mini' : 'h5'" v-if="showTip">
    <view class="bottom-login-nav__logo">
      <image :src="uniLoginLogoImg" class="bottom-login-nav__logo__img" mode="heightFix"></image>
    </view>
    <view class="bottom-login-nav__text">
      <text class="bottom-login-nav__text__top">亲爱的同学</text>
      <text class="bottom-login-nav__text__bottom">添加学校信息，获取学校专属商品</text>
    </view>
    <button :loading="loading" class="bottom-login-nav__btn" @tap="handleTapBtn">前往添加</button>
  </view>
</template>

<script>
import http from '@/utils/http';
import { AppType } from '@/utils/constant';

export default {
  data() {
    return {
      uniLoginLogoImg: '',
      isMini: false,
      loading:false,
    }
  },
  options: {
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  computed: {
    showTip() {
      if (!this.isLogin) {
        return false
      }
      if (this.userInfo && this.userInfo.bind) {
        return false
      }
      return true
    },
    isLogin() {
      return this.$store.state.isLogin
    },
    userInfo() {
      return this.$store.state.userInfo
    }
  },
  mounted() {
    var uniLoginLogoImg = uni.getStorageSync("uniLoginLogoImg");
    if (uniLoginLogoImg) {
      this.uniLoginLogoImg = uniLoginLogoImg
    } else {
      // 获取uni-app相关配置
      this.getUniWebConfig()
    }
    const appType = uni.getStorageSync("appType");
    this.isMini = appType === AppType.MINI
  },
  methods: {
    handleTapBtn() {
      uni.navigateTo({
        url: '/packageWenet/pages/bind-wenet/bind-wenet'
      });
    },
    getUniWebConfig: function() {
      const params = {
        url: "/webConfig/getUniWebConfig",
        method: "GET",
        data: {},
        callBack: res => {
          this.setData({
            uniLoginLogoImg: res.uniLoginLogoImg
          });
          uni.setStorageSync("uniLoginLogoImg",this.uniLoginLogoImg)
        }
      };
      http.request(params);
    },
  }
}

</script>

<style scoped>
.bottom-login-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  position: fixed;
  width: 100vw;
  padding: 8px 16px;
  box-sizing: border-box;
  height: 64px;
  column-gap: 4px;
}

.mini {
  bottom: 0;
}

.h5 {
  bottom: 50px;
  bottom: calc(constant(safe-area-inset-bottom) + 50px);
  bottom: calc(env(safe-area-inset-bottom) + 50px);
  transition: all .3s;
}

.bottom-login-nav__logo {
  width: 50px;
  height: 50px;
}

.bottom-login-nav__logo__img {
  width: 100%;
  height: 100%;
  display: block;
}

.bottom-login-nav__text {
  flex: 1;
  display: flex;
  flex-direction: column;

}

.bottom-login-nav__text__top {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.bottom-login-nav__text__bottom {
  font-size: 12px;
  color: #999;
}

.bottom-login-nav__btn {
  width: fit-content;
  height: 32px;
  background-color: var(--primary-color);
  border-radius: 16px;
  color: #fff;
  font-size: 12px;
  line-height: 32px;
}

</style>
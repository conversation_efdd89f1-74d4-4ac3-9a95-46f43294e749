<template>
	<!--pages/shop-tabbar/shop-tabbar.wxml-->
	<view class="shop-tabbar-container">
		<block v-for="(item, index) in list" :key="index">
			<view class="tabBar" @tap="tabChange" :data-index="index">
				<image class="tabIcon" :src="item.selectedIconPath" v-if="currentTab==index"></image>
				<image class="tabIcon" :src="item.iconPath" v-else></image>
				<text :class="'tabTitle ' + (activeTab==index?'on':'')">{{item.text}}</text>
			</view>
		</block>
		<!-- #ifndef MP-WEIXIN -->
		<!-- <block key="3">
			<view class="tabBar" @tap="tabChange" data-index="3">
				<image class="tabIcon" src="../../static/images/tabbar/shop-search-sel.png" v-if="currentTab==3"></image>
				<image class="tabIcon" src="../../static/images/tabbar/shop-search.png" v-else></image>
				<text :class="'tabTitle ' + (activeTab==3?'on':'')">店铺客服</text>
			</view>
		</block> -->
		<!-- #endif -->
	</view>
</template>


<script>

  const defaultList = [
    {
      text: '首页',
      iconPath: "/static/images/tabbar/homepage.png",
      selectedIconPath: "/static/images/tabbar/homepage.png"
    },{
      text: '分类',
      iconPath: "/static/images/tabbar/category.png",
      selectedIconPath: "/static/images/tabbar/category.png"
    }
  ]
	export default {
		data() {
			return {
				activeTab: this.currentTab,
				shopInfo: null
			};
		},

		components: {},
		props: {
			currentTab: {
				type: String,
				default: '0'
			},
			// renovationId: {
			// 	type: [Number,String],
			// 	default: ''
			// },
			// shopId: {
			// 	type: [Number,String],
			// 	default: ''
			// }
		},

		computed: {
			i18n() {
				return this.$t('index')
			},
      shopCustomerSwitch() {
        return this.$store.state.shopCustomerSwitch
      },
      list() {
        return this.shopCustomerSwitch
          ?
          [
            ...defaultList,
            {
              text: '店铺客服',
              iconPath: "../../static/images/tabbar/shop-search.png",
              selectedIconPath: "../../static/images/tabbar/shop-search-sel.png"
            }
          ] : defaultList
      }
		},

		mounted(){
			// 店铺信息
			this.shopInfo = uni.getStorageSync('shopInfo')
		},
		destroyed: function() { // 在组件实例被从页面节点树移除时执行
		},
		methods: {
			toProdPage: function(e) {
				var prodid = e.currentTarget.dataset.prodid;
				this.$Router.push({
					path: '/pages/prod/prod',
					query: {
						prodid: prodid
					}
				})
			},

			// tabbar切换
			tabChange(e) {
				const {
					index
				} = e.currentTarget.dataset;
				this.activeTab = index
				if (this.activeTab == 0) {
					uni.switchTab({
            url: '/pages/index/index'
          })
				} else if (this.activeTab == 1) {
					uni.switchTab({
            url: '/pages/category/category'
          })
				} else if (this.activeTab == 2) {
					// uni.redirectTo({
					// 	url: '/packageShop/pages/shopSearch/shopSearch'
					// });
					uni.redirectTo({
						url: '/packageUser/pages/chat/chatIm?shopid=' + this.shopInfo.shopId
					});
				}
			}

		}
	};
</script>
<style>
	@import "./shop-tabbar.css";
</style>

<template>
  <view @tap="handleClick" class="copy-wrap">
    <slot name="default"></slot>  
    <i class="iconfont icon-fuzhi copy-ico"></i>
  </view>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  props: {
    copyText: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleClick() {
      uni.setClipboardData({
        data: this.copyText,
        showToast: false,
        success: () => {
          // 为了覆盖微信自带的toast
          uni.showToast({
            title: '',
            icon: 'none',
            duration: 1,
          });
          this.$emit('success', this.copyText);
        }
      });
    }
  }
}

</script>

<style scoped>
.copy-wrap {
  display: flex;
  align-items: center;
}
.copy-ico {
  margin: 0 0 0 4px;
  color: var(--primary-color);
}
</style>
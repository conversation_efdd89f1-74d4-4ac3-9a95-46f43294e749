<template>
  <popup :is-show="popupVisible">
    <view class="popup-content">
      <view class="popup-title">
        <view>
          <text>{{ title }}</text>
        </view>
      </view>
      <view class="popup-desc">
        {{ desc }}
      </view>
      <view class="popup-btns">
        <view class="popup-btn" @tap="handleConfirm">我知道了</view>
      </view>
    </view>
  </popup>
</template>

<script>
export default {
  data() {
    return {
      popupVisible: false,
      bindedWenetAccount: '',
      bindedSchoolName: '',
      title: '',
      desc: ''
    }
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  
  methods: {
    handleConfirm() {
      this.$emit('confirm')
      this.popupVisible = false
    },
    showPopup({ title = '', desc = ''} = {}) {
      this.title = title
      this.desc = desc
      this.popupVisible = true
    },
  }

}

</script>

<style scoped>
.popup-content {
  padding: 0 30upx;
  text-align: center;
}
.popup-title {
  font-size: 32upx;
  color: #333;
  margin-bottom: 20upx;
}

.popup-desc {
  font-size: 24upx;
  color: #999;
  margin-bottom: 40upx;
}

.popup-btns {
  display: flex;
  justify-content: center;
}

.popup-btn {
  width: 300upx;
  height: 60upx;
  line-height: 60upx;
  text-align: center;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 40upx;
}

</style>
<template>
  <view class="privacy-tip">
    <checkbox color="#2F54D4" :checked="checked" class="privacy-tip__checkbox" @click="handleClickCheckbox" />
    <label class="privacy-tip__label">
      <text @click="handleClickCheckbox">我已阅读并同意</text>
      <text class="privacy-tip__text__link" @click="handleClickLink('servicePolicy')">《隐私策略》</text>
      <text @click="handleClickCheckbox">和</text>
      <text class="privacy-tip__text__link" @click="handleClickLink('serviceTerms')">《服务条款》</text>
    </label>
  </view>
</template>

<script>
export default {

  data() {
    return {

    }
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  props: {
    checked: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClickLink(sts) {
      uni.navigateTo({
        url: `/packageUser/pages/termsPage/termsPage?sts=${sts}`,
      })
    },
    handleClickCheckbox() {
      this.$emit('change', !this.checked)
    }
  }

}

</script>

<style lang="scss" scoped>

.privacy-tip {
  text-align: center;
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  background-color: #fff;
  position: fixed;
  bottom: calc(32rpx + constant(safe-area-inset-bottom));
  bottom: calc(32rpx + env(safe-area-inset-bottom));
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 48rpx);
}

.privacy-tip__checkbox {
  transform: scale(0.7);
}

.privacy-tip__label {
  display: flex;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
  line-height: 48rpx;
  flex-wrap: nowrap;
}

.privacy-tip__text__link {
  color: $wenet-color-brand;
}
</style>
<template>
  <view class="payment-selector-container">
    <radio-group class="ways" @change="changePayType">
			<!-- 支付宝支付 -->
      <label v-if="appType!=1 && appType!=2 && paySwitchInfo.aliPaySwitch &&  actualTotal > 0" class="item">
        <view class="pay-name">
          <view class="img">
						<image :src="`${staticPicDomain}images/icon/alipay.png`" mode=""></image>
          </view>
          <view class="name">{{ i18n.PayWithAli }}</view>
        </view>
        <radio color="var(--legacy-primary-color)" value="aliPay"  :checked="payTypeStr==='aliPay'" />
      </label>
			<!-- 微信支付 -->
      <label v-if="paySwitchInfo.wxPaySwitch && actualTotal > 0" class="item">
        <view class="pay-name">
          <view class="img">
						<image :src="`${staticPicDomain}images/icon/wxpay.png`"></image>
          </view>
          <view class="name">{{ i18n.payWithWeChat }}</view>
        </view>
        <radio color="var(--legacy-primary-color)" value="wechatPay"  :checked="payTypeStr==='wechatPay'" />
      </label>
      <!-- #ifdef H5 -->
      <!-- PayPal支付 -->
      <label v-if="paySwitchInfo.payPalSwitch  && actualTotal > 0">
      	<view class="item">
      		<view class="pay-name">
      			<view class="img">
      				<image :src="`${staticPicDomain}images/icon/paypal-logo.png`" mode=""></image>
      			</view>
      			<view class="name">{{i18n.paypalPay}}</view>
      		</view>
      		<view class="check">
      			<radio color="var(--legacy-primary-color)" value="payPal"  :checked="payTypeStr==='payPal'"></radio>
      		</view>
      	</view>
      </label>
      <!-- #endif -->
			<!-- 余额支付 -->
			<label v-if="!hideBalancePay && paySwitchInfo.balancePaySwitch">
        <view class="item">
          <view class="pay-name">
            <view class="img">
							<image :src="`${staticPicDomain}images/icon/balancePay.png`"></image>
            </view>
            <view class="name">{{ i18n.balancePay + `（${i18n.currentBalance}：${totalBalance}）` }}</view>
          </view>
          <view class="check">
            <radio color="var(--legacy-primary-color)" value="balancePay"  :checked="payTypeStr==='balancePay'" />
          </view>
        </view>
      </label>

  		<!-- #ifdef MP-WEIXIN -->
			<!-- 微信好友代付 -->
      <label v-if="showFriendPay && paySwitchInfo.friendPaySwitch && actualTotal > 0" :class="['item',!showFriendPay?'recharge-item':'']">
        <view class="pay-name">
          <view class="img">
						<image :src="`${staticPicDomain}images/icon/external_wechat.svg`"></image>
          </view>
          <view class="name">{{ i18n.payWithWeChatFriend }}</view>
        </view>
        <radio color="var(--legacy-primary-color)" value="wechatFriendPay"  :checked="payTypeStr==='wechatFriendPay'" />
      </label>
			<!-- #endif -->
    </radio-group>
	</view>
</template>

<script>
	import { PayType, AppType } from '../../utils/constant'
	const util = require('../../utils/util.js')
	export default {
		name:"paymentMethodSelector",
		props: {
			// 支付类型字符串
			payTypeStr: {
				default: 'aliPay',
				type: String / Number
			},
			// 是否隐藏余额支付，部分商品（如分销订单）不显示余额支付
			hideBalancePay: {
				default: false,
				type: Boolean
			},
			// 当前用户余额
      totalBalance: {
        default: 0,
        type: Number / String
      },
      actualTotal: {
        default: 0,
        type: Number / String
      },
      // 是否显示微信好友代付
      showFriendPay: {
        default: true,
        type: Boolean
      }
		},
		computed: {
			i18n() {
				return this.$t('index')
			}
		},
		data() {
			return {
				// 支付类型常量
				currentPayTypeStr: 'aliPay',
				payType: PayType.ALIPAY_H5,
				appType: uni.getStorageSync('appType'),
				// 系统支付设置
				paySwitchInfo: uni.getStorageSync('paySwitchInfo').switchInfo
			}
		},
		methods: {

			/**
			 * 修改支付类型
			 */
			changePayType(val) {
				console.log('支付开关', this.paySwitchInfo);
				util.tapLog()
				// 支付类型(字符串格式) payTypeStr: aliPay 支付宝   wechatPay 微信支付   balancePay 余额支付
				this.currentPayTypeStr = val.detail.value

				// 设置支付类型 payType
				let payType
				if (val.detail.value === 'balancePay') {
					payType = PayType.BALANCEPAY
				} else if (val.detail.value === 'payPal') {
					payType = PayType.PAYPAL
				} else {
					// #ifdef H5
					payType = val.detail.value === 'aliPay' ? PayType.ALIPAY_H5 : this.appType === AppType.MP ? PayType.WECHATPAY_MP : PayType.WECHATPAY_H5
					// #endif
					// #ifdef APP-PLUS
					payType = val.detail.value === 'aliPay' ? PayType.ALIPAY_APP : PayType.WECHATPAY_APP
					// #endif
					// #ifdef MP-WEIXIN
					payType = val.detail.value === 'wechatPay' ? PayType.WECHATPAY : PayType.WECHATPAY_FRIEND
					// #endif
				}
				// this.payType = payType
				const paymentInfo = {
					payTypeStr: val.detail.value,
					payType: payType
				}
			this.$emit('setPayType', paymentInfo)
			}
		}
	}
</script>
<style lang="scss" scoped>
.ways {
	padding: 0 20rpx;
	font-size: 24rpx;
  --legacy-primary-color: #FF2442;
}
.ways .item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 2rpx solid #eee;
	padding: 20rpx 0;
}
.ways .item.recharge-item {
	border: none;
}
.ways .item .pay-name {
	display: flex;
	align-items: center;
}

.ways .item .pay-name .img {
	width: 44rpx;
	height: 44rpx;
	font-size: 0;
}
.ways .item .pay-name .img image{
	width: 100%;
	height: 100%;
}

.ways .item .pay-name .name {
	margin-left: 20rpx;
}
/*radio 选项框大小  */
radio .wx-radio-input {
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
}
/*radio选中后图标样式  */
radio .wx-radio-input.wx-radio-input-checked::before {
  text-align: center;
  font-size: 20rpx;
  color: #fff;
  background: transparent;
  transform: translate(-50%, -50%) scale(1);
  -webkit-transform: translate(-50%, -50%) scale(1);
}

</style>


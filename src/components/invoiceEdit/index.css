.invoice-popup {
  /* background: #f2f3f7; */
  font-size: 24rpx;
}
.invoice-popup input {
  font-size: 24rpx;
}
.invoice-popup .uni-input-input {
  font-size: 24rpx;
}

/* 发票信息弹窗 */
.invoice-popup .popup .invoice {
  height: 80%;
}

.invoice-popup .popup .invoice .con-box {
	bottom: calc(110rpx + env(safe-area-inset-bottom));
}

.invoice-popup .popup .invoice .invoice-msg .invoice-item {
	margin-top: 30rpx;
}

.invoice-popup .popup .invoice .invoice-msg .invoice-item .item-tit {
	font-weight: 600;
	max-width: 300rpx;
	display: flex;
}

.invoice-popup .popup .invoice .invoice-msg .invoice-item .item-tit .text-red {
	margin-left: 6rpx;
	color: var(--primary-color);
}

.invoice-popup .popup .invoice .invoice-msg .invoice-item .item-con {
	display: flex;
	align-items: center;
	margin-top: 20rpx;
}

.invoice-popup .popup .invoice .invoice-msg .invoice-item .item-con .item-btn {
	background: #f5f5f5;
	height: 56rpx;
	line-height: 56rpx;
	border-radius: 56rpx;
	padding: 0 40rpx;
	margin-right: 20rpx;
	border: 2rpx solid #f5f5f5;
	text-align: center;
}

.invoice-popup .popup .invoice .invoice-msg .invoice-item .item-con .item-btn.pd60 {
	padding: 0 60rpx;
}

.invoice-popup .popup .invoice .invoice-msg .invoice-item .item-con .item-btn.active {
	border-color: var(--primary-color);
	color: var(--primary-color);
	background: #fff;
}

.invoice-popup .popup .invoice .invoice-msg .invoice-item .item-con .item-input {
	height: 40rpx;
	line-height: 40rpx;
	padding-bottom: 20rpx;
	flex: 1;
	border-bottom: 2rpx solid #f9f9f9;
}

.invoice-popup .popup .invoice .invoice-msg .invoice-item .item-con .text-box {
	color: #999;
	line-height: 40rpx;
	padding: 20rpx;
	background: #f9f9f9;
	border-radius: 6rpx;
}

.invoice-popup .popup .invoice .btn-box {
	background: #fff;
}

/* 弹窗背景 */
.invoice-popup .popup {
  position: fixed;
  top: 100%;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 750rpx;
  margin: auto;
  z-index: 210;
}

.invoice-popup .popup.show {
  top: 0;
}

.invoice-popup .popup .popup-mask {
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,.5);
}

/* 底部弹窗样式 */
.invoice-popup .popup .invoice-popup-con-bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  transform: translateY(100%);
  transition: all .3s;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80%;
  min-height: 40%;
}

.invoice-popup .popup.show .invoice-popup-con-bottom {
  transform: translateY(0);
}

.invoice-popup .popup .invoice-popup-tit {
  position: relative;
  padding: 30rpx;
  text-align: center;
  height: 38rpx;
  line-height: 38rpx;
  z-index: 1;
  width: 750rpx;
  box-sizing: border-box;
}

.invoice-popup .popup .invoice-popup-tit .tit-text {
  font-size: 28rpx;
  font-weight: 600;
}

.invoice-popup .popup .invoice-popup-tit .invoice-close {
  width: 34rpx;
  height: 34rpx;
  position: absolute;
  top: 32rpx;
  right: 30rpx;
  border-radius: 50%;
  background: #f5f5f5;
  font-size: 0;
}

.invoice-popup .popup .invoice-popup-tit .invoice-close image {
  width: 14rpx;
  height: 14rpx;
  margin: 10rpx auto;
}

.invoice-popup .popup .con-box,
.invoice-popup .popup .con-box-min {
  position: absolute;
  top: 98rpx;
  bottom: env(safe-area-inset-bottom);
  left: 30rpx;
  right: 30rpx;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.invoice-popup .popup .con-box-min {
  bottom: calc(110rpx + env(safe-area-inset-bottom));
}

.invoice-popup .popup .con-box::after,
.invoice-popup .popup .con-box-min::after {
  min-height: calc(100% + 2rpx);
}

.invoice-popup .popup .btn-box {
  position: absolute;
  bottom: env(safe-area-inset-bottom);
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  justify-content: space-between;
  display: none;
}

.invoice-popup .popup.show .btn-box {
  display: flex;
  z-index: 9999;
  background-color: #ffffff;
}

.invoice-popup .popup .btn-box .sumbit-btn {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  background: var(--primary-color);
  color: #fff;
  text-align: center;
  border-radius: 70rpx;
  font-size: 26rpx;
  font-weight: 600;
}

.invoice-popup .popup.show .btn-box .add-cart-btn {
  background-color: #ffbb0d;
  margin-right: 24rpx;
}


.invoice-popup .popup .btn-box .gray {
  background: #dddddd;
}

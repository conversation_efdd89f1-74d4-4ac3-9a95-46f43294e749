<template>
  <view>
    <GetUserMobile
      :auto-verify="false"
      @getWxPhoneCode="getWxPhoneCode"
      @getSmsPhoneCode="getSmsPhoneCode"
      page-name="登录页"
      :privacy-checked="privacyChecked"
    />
    <view v-if="showRegister" class="to-register" @click="goToRegister" >没有账号？前往注册</view>
  </view>
</template>

<script>
import http from '@/utils/http'
import utils from '@/utils/util'
import GetUserMobile from '@/components/get-user-mobile'
import { getWenetAccountInfo, cacheWenetAccountInfo, getExtendInfoListConfig, checkWenetAccountInfo} from '../../utils/login-processor'

export default {
  name: 'login-with-mobile',
  data () {
    return {
    }
  },
  components: {
    GetUserMobile,
  },
  props: {
    basOrgList: {
      type: Array,
      default: () => []
    },
    privacyChecked: {
      type: <PERSON>olean,
      default: false,
    },
    selectedOu: {
      type: String,
      default: ''
    },
    selectedOuPrefix: {
      type: String,
      default: ''
    },
    studentIdPlaceholder: {
      type: String,
      default: ''
    },
    passwordPlaceholder: {
      type: String,
      default: ''
    },
    showRegister: {
      type: Boolean,
      default: false,
    },
  },
  mounted(){
  },
  methods: {
    goToRegister() {
      uni.navigateTo({
        url: `/pages/login/register?ou=${this.selectedOu}`
      })
    },
    getWxPhoneCode(phoneCodeEvent) {
      this.$umaTracker.ModularClick({
        Um_Key_ButtonName: '微信授权手机号登录',
        Um_Key_SourcePage: '登录页',
        Um_Key_SourceLocation: '手机号登录',
      })
      wx.login({
        success: (wxloginRes) => {
          // 用code 小程序注册登录
          const params = {
            url: '/self/code/login',
            method: 'POST',
            data: {
              code: wxloginRes.code,
              wxCode: phoneCodeEvent.detail.code,
              ou: this.selectedOu
            },
            callBack: loginRes => {
              uni.setStorageSync('loginActionData', { way: 'login', type: '微信登录'}) 
              this.redirectHandler(loginRes, '微信登录')
            },
            errCallBack: async errRes => {
              if (errRes.statusCode === 476) {
                if (this.showRegister) {
                  uni.showToast({
                    title: '用户不存在，请先注册账号',
                    icon: 'none'
                  })
                }else {
                  uni.showToast({
                    title: '手机号未绑定学号，请先前往学号登录并绑定手机号',
                    icon: 'none',
                    duration: 5000
                  })
                }
              } else if (errRes.statusCode === 478) {
                uni.showToast({
                  title: '账号不存在',
                  icon: 'none'
                })
              }else {
                uni.showToast({
                  title: errRes.data,
                  icon: 'none'
                })
              }
            }
          }
          http.request(params)
        },
      })
    },
    getSmsPhoneCode(res) {
      if (!this.privacyChecked) {
        uni.showToast({
          title: '请先同意隐私策略和服务条款',
          icon: 'none'
        })
        return
      }
      this.$umaTracker.ModularClick({
        Um_Key_ButtonName: '短信登录',
        Um_Key_SourcePage: '登录页',
        Um_Key_SourceLocation: '手机号登录',
      })
      const params = {
        url: '/self/code/login',
        method: 'POST',
        data: {
          smsCode: res.verifyCode,
          mobile: res.mobile,
          ou: this.selectedOu
        },
        callBack: loginRes => {
          uni.setStorageSync('loginActionData', { way: 'login', type: '短信登录'}) // 手机号可正常登录
          this.redirectHandler(loginRes, '短信登录')
        },
        errCallBack: errRes => {
          if (errRes.statusCode === 476) {
            if (this.showRegister) {
              uni.showToast({
                title: '用户不存在，请先注册账号',
                icon: 'none'
              })
            }else {
              uni.showToast({
                title: '手机号未绑定学号，请先前往学号登录并绑定手机号',
                icon: 'none',
                duration: 5000
              })
            }
            this.$umaTracker.LoginFailed({
              Wenet_Key_Reason: '手机号未注册',
            })
          } else if (errRes.statusCode === 478) {
            uni.showToast({
              title: '账号不存在',
              icon: 'none'
            })
          } else {
            this.$umaTracker.LoginFailed({
              Wenet_Key_Reason: errRes.data,
            })
            uni.showToast({
              title: errRes.data,
              icon: 'none'
            })
          }
        }
      }
      // #ifdef MP-WEIXIN
      wx.login({
        success: ({code}) => {
          params.data.code = code
          http.request(params)
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
        http.request(params);
      // #endif
    },
    // 检查wenet账号信息，如果不完整，跳转到完善信息页面
    async redirectHandler(loginRes, loginType) {
      try {
        utils.saveLoginSuccessData(loginRes)
        const wenetToken = loginRes.basToken
        if (!wenetToken) {
          utils.routeAfterLogin(false)
          return
        }
        uni.setStorageSync('wenetToken', wenetToken);
        const wenetAccountInfo = await getWenetAccountInfo(wenetToken)
        cacheWenetAccountInfo(wenetAccountInfo)

        // 获取扩展信息
        const orgExtendedInfo = getExtendInfoListConfig(wenetAccountInfo.org.ou, this.basOrgList)
        this.$umaTracker.setUserBasicInfo({
          userId: wenetAccountInfo.person.uid,
          schoolName: wenetAccountInfo.org.localityName,
        })
        // 检查用户信息
        const checkRes = checkWenetAccountInfo(wenetAccountInfo, orgExtendedInfo);

        if (!checkRes.extendInfo) {
          // 保存登录方式, 用于完善信息页面判断
          // 跳转到填写信息页面
          this.toImproveInfo()
          return
        }
        this.$umaTracker.LoginSuc({
          Wenet_Key_LoginType: loginType,
          Wenet_Key_IsImproveInfo: false,
          Wenet_Key_IsImproveMobile: false,
        })
        utils.routeAfterLogin(false)
      } catch (error) {
        console.dir(error)
      } finally {
      }
    },

    // 跳转到填写信息页面
    toImproveInfo() {
      uni.reLaunch({
        url: '/packageuser/pages/improve-info/improve-info'
      })
    },
  },
  computed: {
    
  },
}
</script>

<style lang="scss" scoped>
.to-register {
  padding: 24rpx;
  text-align: center;
  color: $wenet-color-brand;
  font-size: 26rpx;
}
</style>
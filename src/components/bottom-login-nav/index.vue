<template>
  <view class="bottom-login-nav" :class="isMini ? 'mini' : 'h5'">
    <view class="bottom-login-nav__text">
      <text class="bottom-login-nav__text__top">亲爱的同学</text>
      <text class="bottom-login-nav__text__bottom">请登录账号，获取您的专属服务</text>
    </view>
    <NButton class="bottom-login-nav__btn" type="primary" @click="toLogin" :disabled="loading">去登录</NButton>
  </view>
</template>

<script>
import http from '@/utils/http';
import { AppType } from '@/utils/constant';
import NButton from '@/components/n-button'

export default {
  components: {
    NButton
  },
  data() {
    return {
      uniLoginLogoImg: '',
      isMini: false,
      loading:false,
    }
  },
  mounted() {
    var uniLoginLogoImg = uni.getStorageSync("uniLoginLogoImg");
    if (uniLoginLogoImg) {
      this.uniLoginLogoImg = uniLoginLogoImg
    } else {
      // 获取uni-app相关配置
      this.getUniWebConfig()
    }
    const appType = uni.getStorageSync("appType");
    this.isMini = appType === AppType.MINI
  },
  methods: {
    getUniWebConfig: function() {
      const params = {
        url: "/webConfig/getUniWebConfig",
        method: "GET",
        data: {},
        callBack: res => {
          this.setData({
            uniLoginLogoImg: res.uniLoginLogoImg
          });
          uni.setStorageSync("uniLoginLogoImg",this.uniLoginLogoImg)
        }
      };
      http.request(params);
    },
    toLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },

  }
}

</script>

<style lang="scss" scoped>
.bottom-login-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  position: fixed;
  width: 100vw;
  padding: 8px 16px;
  box-sizing: border-box;
  height: 64px;
  column-gap: 4px;
}

.mini {
  bottom: 0;
}

.h5 {
  bottom: 50px;
  bottom: calc(constant(safe-area-inset-bottom) + 50px);
  bottom: calc(env(safe-area-inset-bottom) + 50px);
  transition: all .3s;
}

.bottom-login-nav__logo {
  width: 50px;
  height: 50px;
}

.bottom-login-nav__logo__img {
  width: 100%;
  height: 100%;
  display: block;
}

.bottom-login-nav__text {
  flex: 1;
  display: flex;
  flex-direction: column;

}

.bottom-login-nav__text__top {
  font-size: 32rpx;
  color: $wenet-color-text-primary;
  font-weight: bold;
}

.bottom-login-nav__text__bottom {
  font-size: 24rpx;
  color: $wenet-color-text-secondary;
}

.bottom-login-nav__btn {
  height: 64rpx;
}

</style>
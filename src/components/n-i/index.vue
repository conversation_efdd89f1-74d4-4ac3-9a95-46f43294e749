<template>
  <image
    :mode="mode"
    :src="`${staticPicDomain}/images/icon/${icon}.${ext}`"
    :style="{width: width, height: height}"
    @click="handleClick"
  >
  </image>
</template>

<!-- 组件使用方法 -->
<!-- 

  <n-i icon="filename" />

  fliename不用带扩展名，只需要写文件名即可，如：bell-02
  可通过width和height来设置图片的宽高
 -->

<script>
export default {
  name: 'n-i',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
    }
  },
  props: {
    icon: {
      type: String,
      default: 'bell-02'
    },
    ext: {
      type: String,
      default: 'svg'
    },
    width: {
      type: String,
      default: '24rpx'
    },
    height: {
      type: String,
      default: '24rpx'
    },
    mode: {
      type: String,
      default: 'aspectFill',
    }
  },
  methods: {
    handleClick() {
      this.$emit('click')
    }
  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
</style>
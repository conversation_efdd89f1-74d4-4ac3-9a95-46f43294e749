<template>

  <view class="earn-button">
    
    <view class="earn-button-image-content" @tap="processTap">
      <view class="earn-button-title-content">
        <view class="earn-button-title-text">最高可赚</view>
        <view class="earn-button-price">¥{{ price }}</view>
      </view>
    </view>
    <view class="earn-button-text-content" @tap="processTap">
      <view class="earn-button-text-content__text">点我赚钱</view>
    </view>
    <distribution-agreement-drawer 
      :show="showDrawer" 
      @agreed="handleAgreementAgreed" 
      @cancel="handleAgreementCancel"
      :z_index="100"
    />
  </view>
</template>

<script>
import http from '@/utils/http.js'
import util from '@/utils/util.js'
import DistributionAgreementDrawer from '@/components/distribution-agreement-drawer/distribution-agreement-drawer.vue'

export default {
  components: {
    DistributionAgreementDrawer
  },
  props: {
    price: {
      type: String,
      default: 0,
    },
  },
  data() {
    return {
      canApplyDist: false,
      isDist: false, // 是否为分销员
      // 用户是否同意过分销协议
      isAgreeDistProtocol: false,
      showDrawer: false
    }
  },
  mounted() {
    this.init()
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo
    }
  },
  methods: {
    // 处理协议弹窗取消
    handleAgreementCancel() {
      this.showDrawer = false
    },
    // 用户同意了协议
    async handleAgreementAgreed() {
      this.isAgreeDistProtocol = true
      this.showDrawer = false
      const registerRes = await this.registerDist()
      if (!registerRes) {
        uni.showToast({
          title: '您已成功提交审核',
          icon: 'none'
        })
        return
      }
      const res = await this.getIsDistInfo()
      const isDist = res?.state == 1
      if (isDist) {
        wx.setStorageSync("distCardNo", res.cardNo);
        this.isDist = isDist;
        this.$emit('click-tap')
      }
    },
    async processTap() {
      if (this.isDist) { // 用户已经是分销员，如果用户没有同意协议，则弹出协议
        if (!this.isAgreeDistProtocol) {
          this.showDrawer = true
          return
        }
        this.$emit('click-tap') // 用户已经是分销员，且同意了协议，则直接进行点击逻辑
      } else { // 用户不是分销员
        if (!this.canApplyDist) { // 不可以无门槛成为分销员，则跳转到申请分销员页面
          uni.navigateTo({
            url: 'packageDistribution/pages/applyDist/applyDist'
          })
          return
        }
        if (!this.isAgreeDistProtocol) { // 可以无门槛成为分销员，且用户没有同意协议，则直接弹出协议
          this.showDrawer = true
          return 
        } else if (this.isAgreeDistProtocol) { // 且用户同意了协议，但是用户不是分销员，表示当前在审核中
          uni.showToast({
            title: '您已提交审核，请耐心等待审核结果',
            icon: 'none'
          })
          return
        }
      }
      util.tapLog(3, { shopId: this.shopId })
    },
    async init () {
      const canApplyDist = await this.getCondition()

      const res = await this.getIsDistInfo()
      const isDist = res?.state == 1
      const isAgreeDistProtocol = res?.isAgreeDistProtocol
      if (isDist) {
        wx.setStorageSync("distCardNo", res.cardNo);
      }
      this.isDist = isDist;
      this.canApplyDist = canApplyDist;
      this.isAgreeDistProtocol = isAgreeDistProtocol
    },
    /**
     * 查询用户 是否为分销员
     */
    getIsDistInfo() {
      return new Promise((resolve, reject) => {
        http.request({
          url: "/p/distribution/user/distributionUserInfo",
          method: "GET",
          callBack: res => {
            resolve(res)
          },
          errCallBack: (err) => reject(err)
        });
      })
    },
    getCondition() {

      return new Promise((resolve, reject) => {
        http.request({
          url: "/p/distribution/register/condition",
          method: "GET",
          callBack: res => {
            const needIdCardNo = res.identityCardNumber;
            const neddIdCardPic = res.identityCardPic;
            const needRealName = res.realName;
            resolve(!needIdCardNo && !neddIdCardPic && !needRealName)
          },
          errCallBack: (err) => reject(err)
        });

      })
    },

    registerDist() {

      return new Promise((resolve, reject) => {
        uni.showLoading();
        var params = {
          url: "/p/distribution/register/addDistributionUser",
          method: "post",
          data: {
            sharerCardNo: this.scene,
            userMobile: this.userInfo.userMobile,
            isAgreeDistProtocol: true
          },
          callBack: res => {
            uni.hideLoading()
            resolve(res)
          },
          errCallBack: (err) => {
            uni.hideLoading()
            uni.showToast({
              title: err.data,
              icon: 'none'
            })
            reject(err)
          }
        };
        http.request(params);
      })
    },
  }
}
</script>

<style scoped>
.earn-button {
  position: absolute;
  top: 8rpx;
  right: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.earn-button-image-content {
  width: 128rpx;
  height: 128rpx;
  background: url('https://m.wenet.com.cn/resources/shop-static/images/other/earn.png') no-repeat center/contain;
  transform-origin: center;
  animation: shake 0.5s linear infinite alternate;
  /* 可根据实际需求调整宽高 */
}
.earn-button-title-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 24rpx;
  left: 0;
  width: 100%;
}
.earn-button-title-text {
  font-size: 18rpx;
  font-weight: 500;
  color: #fdd45b;
  /* animation: shake 0.5s linear infinite alternate; */
}
.earn-button-price {
  font-size: 24rpx;
  font-weight: 500;
  color: #fff;
  animation: scaling 0.5s linear infinite alternate;
  margin-top: 0rpx;
  vertical-align: middle;
  line-height: 32rpx;
}
.earn-button-text-content {
  background-color: #fdd45b;
  color: #983a13;
  border-radius: 32rpx;
  padding: 8rpx 16rpx;
  margin-top: -16rpx;
  position: relative;
  z-index: 10;
}
.earn-button-text-content__text {
  font-size: 28rpx;
  font-weight: 500;
  animation: scaling 0.5s linear infinite alternate;
}

/* 晃动动画 */
@keyframes shake {
  from { transform: rotate(5deg);}
  to { transform: rotate(-5deg);}
}

@keyframes scaling {
  0% { transform: scale(1);}
  100% { transform: scale(0.9);}
}

@keyframes scaling-and-shake {
  0% { transform: scale(1) rotate(5deg);}
  100% { transform: scale(0.9) rotate(-5deg);}
}
</style>
/* components/SwiperSlider/SwiperSlider.wxss */
/* 幻灯片 */
.swiper{
  width: 100%;
  height: 460rpx;
  /* display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
  margin-top: 34rpx; */
}
.swiper-item{
  width: 460rpx!important;
  height:100%!important;
  /* padding: 0 10rpx; */
}
/* .swiper-item .swiper-img-item{
  width: 100%; height: 100%; position: relative;
  background-size: cover;
} */
.slide-image{ 
  width: 100%;
  height: 100%;
  border-radius: 4rpx;
  border-radius: 14rpx;
}
.quiet { 
  transform: scale(0.8333333);
  transition: all 0.2s ease-in 0s;
}
.active { 
  transform: none;
  transition: all 0.2s ease-in 0s;
}
.swiper-slide-active{
  height: 460rpx!important;
  transition: all .1s;
}
.swiper-slide-scaleY{
  height: 420rpx!important;
  margin-top: 20rpx;
}

.swiper-item .living-icon {
  position: absolute;
  top: 60rpx;
  left: 60rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
}
.swiper-item .active-living-icon {
  top: 30rpx;
  left: 30rpx;
  transition: all 0.2s ease-in 0s;
}
.item-content {
  position: absolute;
  bottom: 22rpx;
  left: 0;
  right: 0;
  height: 100rpx;
  border-radius: 0 0 10rpx 10rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
  transform: scale(0.8333333);
  transition: all 0.2s ease-in 0s;
  /* background-image: linear-gradient(180deg, rgba(238,238,238,0.00) 0%, rgba(6,6,6,0.44) 100%); */
}
.active-content {
  bottom: 0;
  transform: none;
  transition: all 0.2s ease-in 0s;
  background: rgba(255,3,46,.4);
}
/* .swiper-slide-active .active-bg-color {
  transition: all 0.2s ease-in 0s;
} */
.item-content .rt {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
}
.item-content .lt .anchor {
  line-height: 1.5em;
  font-size: 26rpx;
}
.item-content .lt .live-tit {
  line-height: 2em;
  font-size: 30rpx;
  font-weight: bold;
}

.dots{
  display: flex;
  justify-content: center;
  padding-bottom: 20rpx;
  padding-top: 30rpx;
}
.dots .dot{
  width: 8rpx;
  height: 8rpx;
  margin-left: 10rpx;
  background: #D2D5DA;
  transition: all .3s;
  border-radius: 50%;
}
.dots .dot.active{
  width: 28rpx;
  background-image: linear-gradient(90deg, #fe1b5e 58%, #fe1b5e 100%);
  border-radius: 50rpx;
}
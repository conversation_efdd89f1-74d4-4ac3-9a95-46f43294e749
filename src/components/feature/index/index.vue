<template>
  <view class="micro-box">
    <view v-for="(item, index) in list" :key="index">
      <ProfileCard :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'profileCard'" />
      <searchBar :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'search'" :pageScorllTop="pageScorllTop" />
      <image-ad :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'imageAd' || item.type === 'hotArea'" />
			<goods :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'goods'" />
      <navigationClassification :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'tabNav'" />
      <titleText :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'titleText'" />
      <promotionalActivities :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'promotionalActivities'" />
      <coupon :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'coupon'" />
			<weChatCode :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'weChatCode'" />
			<immediateNetworking :dataField="item.dataField" :shopId="shopId" v-if="item.type === 'immediateNetworking'" />
    </view>
  </view>
</template>

<script>
	import http from '../../../utils/http.js'

	import imageAd from "../all-feature/image-ad/image-ad";
	import goods from "../all-feature/goods/goods";
	import navigationClassification from '../all-feature/navigation-classification/navigation-classification'
	import titleText from '../all-feature/title-text/title-text'
	import promotionalActivities from '../all-feature/promotional-activities/promotional-activities'
	import searchBar from '../all-feature/search-bar/search-bar'
	import coupon from '../all-feature/coupon/coupon'
	import weChatCode from '../all-feature/weChatCode/weChatCode'
	import immediateNetworking from '../all-feature/immediateNetworking/immediateNetworking'
	import ProfileCard from '../all-feature/profile-card/profile-card'
	export default {
		data() {
			return {
				list: [],
				locking: false,
				backgroundColor: '',
			};
		},
		components: {
			imageAd,
			goods,
			navigationClassification,
			titleText,
			promotionalActivities,
			searchBar,
			coupon,
			weChatCode,
			immediateNetworking,
      ProfileCard
		},
		props: {
			isShowMicro: {
				type: Boolean,
				default: true
			},
			wid: {
				// ID
				type: String,
				default: ''
			},
			pageLoad: {
				type: Boolean,
				default: false
			},
			pageId: {
				type: [Number, String],
				default: ''
			},
			shopId: {
				type: [Number, String],
				default: ''
			},
			pageScorllTop: {
				type: Number,
				default: 0
			}
		},
		mounted() {
			this.$nextTick(()=> {
				this.getPageInfoById()
			})
		},
		computed: {
			i18n() {
				return this.$t('index')
			}
		},
		methods: {
			/**
			 * 请求页面数据
			 */
			getPageInfoById() {
				this.list = []
				// shopId、pageId  在进入店铺主页时获取
				let params = {
					url: '/shopRenovation/info',
					method: 'GET',
					data: {
						renovationId: this.pageId,
						shopId: this.shopId
					},
					callBack: (res) => {
						this.locking = false
						if (!res && !res.content) {
							// 没获取到数据，可能是对应id的页面被删了
							uni.showToast({
								title: this.i18n.pageGone,
								icon: 'none',
								duration: 1200
							})
							// setTimeout(() => {
							// 	uni.navigateBack({
							// 		delta: 1
							// 	})
							// }, 1500)
							return
						}

						let pageData = JSON.parse(res.content);
						this.list = pageData
						this.backgroundColor = pageData[0].dataField.backgroundColor
						this.$emit('pageLoaded', {
							detail: {
								code: 0,
								data: '已获取数据',
								title: pageData[0].dataField.title,
								backgroundColor: pageData[0].dataField.backgroundColor
							}
						})
					}
				}
				if (this.pageId && this.shopId>=0 && !this.locking) {
					this.locking = true
					http.request(params)
				}
			},
		}
	};
</script>
<style>
.micro-box {
	font-size: 0;
	width: 100vw;
  padding: 0px 0 16rpx 0;
	box-sizing: border-box;
  background: #fff;
}
</style>

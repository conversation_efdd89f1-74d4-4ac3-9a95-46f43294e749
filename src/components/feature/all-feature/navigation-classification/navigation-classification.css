.fature-cate-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0 30rpx;
	flex-wrap: wrap;
  margin: 0 48rpx;
}
.fature-cate-item .item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.fature-cate-item .item image {
  width: 86rpx;
  height: 86rpx;
}
.fature-cate-item .item text {
  font-size: 28rpx;
  margin-top: 20rpx;
}
.column-four {
	width: 25%;
}
.column-five {
	width: 20%;
}
.nav-title {
	word-break: break-word;
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	-webkit-text-overflow: ellipsis;
	-moz-text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
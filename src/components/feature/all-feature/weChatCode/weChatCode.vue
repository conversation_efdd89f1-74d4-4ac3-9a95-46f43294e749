<template>
  <view class="wechat-content" v-if="!!wechatCode">
    <view class="wechat-item" @tap="showModal">
      <view class="profile" v-show="dataField.pic">
        <image
          mode='aspectFit' 
          :src="resourcesUrl + dataField.pic"
        ></image>
    </view>
    <view class="info">
        <h3 v-show="dataField.title">{{ dataField.title }}</h3>
        <text v-show="dataField.desc">{{ dataField.desc }}</text>
      </view>
      <view class="button" v-show="dataField.btnName">
        <text>{{ dataField.btnName }}</text>
      </view>
    </view>

    <view class="modal" v-show="popupVisible">
      <view class="modal-cancel" @click="hideModal"></view>
      <view class="modal-body">
        <div class="close-icon" @click="hideModal">
          <n-i icon="video-xx" ext="png" width="24rpx" height="24rpx"/>
        </div>
        <image
          class="modal-body-image"
          mode='aspectFit' 
          :src="resourcesUrl + wechatCode"
          show-menu-by-longpress
        ></image>

			  <!-- #ifndef H5 -->
        <view class="modal-desc">
          <p>长按识别二维码</p>
          <p>添加微信获取优惠信息</p>
        </view>
        <!-- #endif -->
			  <!-- #ifdef H5 -->
        <view class="code-txt">
          <a :href="resourcesUrl + wechatCode">下载图片</a>
        </view>
				<!-- #endif -->
      </view>
    </view>
  </view>
</template>
<script>
import config from '@/utils/config.js'
import NI from '@/components/n-i'

export default {
  data () {
    return {
      resourcesUrl: config.picDomain,
      popupVisible: false,
      wechatCode: '',
    }
  },
  props: {
    dataField: {
      type: Object,
      default: () => ({})
    },
    shopId: {
      type: [Number, String],
      default: 0
    },
    will: {
      type: String,
      default: 'home' 
    }
  },
  components: {
    NI,
  },
  computed:{
    userInfo() {
      return this.$store.state.userInfo
    },
    i18n() {
      return this.$t('index')
    }
  },
  mounted () {
    this.isElementVisible()
  },
  methods: {
    showModal() {
      this.popupVisible = true
    },
    hideModal() {
      this.popupVisible = false
    },
    isElementVisible() {
      const configList = this.dataField.configList
      const currentUserOu = this.userInfo?.ou 
      configList.forEach((item) => {
        if(item.ouList.includes(currentUserOu)) {
          this.wechatCode = item.pic
        }
      })
    },
  }
}
</script>

<style scoped>
.wechat-content {
  padding: 10rpx 0;
  font-size: 24rpx;
  margin: 0 48rpx;
}
.wechat-show {
  box-sizing: border-box;
  padding: 20rpx;
  width: 100%;
  height: auto;
  overflow-y: hidden;
  overflow-x: auto;
  white-space: nowrap;
  border-radius: 10rpx;
}
.wechat-item {
  display: flex;
  align-items: center;
}

.profile {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.profile image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info {
  flex: 1;
  margin: 0rpx 20rpx;
}
.info h3 {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 12rpx;
  font-weight: bold;

}
.info text {
  color: #8c8c8c;
}

.button {
  min-width: 60rpx;
  background-color: #ff0000;
  padding: 10rpx 16rpx;
  color: #fff;
  text-align: center;
}

.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
}

.modal-cancel {
  position: absolute;
  z-index: 2000;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-body {
  position: absolute;
  z-index: 3000;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  max-height: 60%;
  overflow: auto;
  background-color: #fff;
  padding: 40rpx;
  border-radius: 20rpx;
}
.modal-body .modal-body-image {
  width: 100%;
  display: block;
  object-fit: cover;
  margin: 20rpx auto;
}

@media (min-width: 768px) {
  .modal-body {
    width: 50%;
  }
}

.modal-desc {
  line-height: 1.5;
  color: #8c8c8c;
}

.close-icon {
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  filter: brightness(40%); 
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-txt {
  text-align: center;
}

.code-txt a{
  display: inline-block;
  text-decoration: none;
  padding: 16rpx 32rpx;
  border-radius: 90rpx;
  color: var(--primary-color);
 border: 2rpx solid var(--primary-color);
}


</style>

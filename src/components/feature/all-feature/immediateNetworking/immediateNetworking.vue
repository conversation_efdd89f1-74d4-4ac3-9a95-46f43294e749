<template>
	<!-- #ifdef MP-WEIXIN -->
  <view v-if="isShow" class="immediate-networking-wrap">
    <view @tap="handleNetworking(dataField.ssid)">
      <image
        mode='widthFix' 
        style="width:100%;"
        :src="resourcesUrl + dataField.imgUrl"
      ></image>
    </view>
  </view>
  <!-- #endif -->
</template>

<script>
import config from '@/utils/config.js'

export default {
  data() {
    return {
      resourcesUrl: config.picDomain,
      isShow: false,
      deviceList: ['android', 'devtools'],
    };
  },
  props: {
    dataField: {
      type: Object,
      default: () => ({})
    },
    shopId: {
      type: [Number, String],
      default: 0
    },
  },
  mounted() {
    const device = wx.getDeviceInfo()?.platform

    if(this.deviceList.includes(device)) {
      this.isShow = true
    }
  },
  methods: {
    async handleNetworking(SSID = '', password = '') {
      try {
        await this.startWifiModule();
        await this.connectToWifi(SSID, password);
      } catch (error) {
        uni.showToast({
          title: error.errMsg,
          icon: 'none',
        })
      }
    },
    // 启动Wi-Fi模块
    startWifiModule() {
      return new Promise((resolve, reject) => {
        wx.startWifi({
          success: resolve,
          fail: reject
        });
      });
    },
    // 连接到指定的Wi-Fi
    connectToWifi(SSID, password) {
      return new Promise((resolve, reject) => {
        wx.connectWifi({
          SSID,
          password,
          success: resolve,
          fail: reject,
          maunal: true, // 目前没办法自动连接wifi，因此直接跳转至系统的wifi设置
        });
      });
    },
  }
}
</script>

<style>
	@import url("./immediateNetworking.css");
</style>

<template>
  <view class="promotional-activities">
    <view class="snap-up-head" v-if="goods.length">
      <text class="snap-up-tit">{{ activityTitle }}</text>
      <view class="more-goods" @tap="toActiveList">
        <text class="snap-up-countdown">{{ activityTime }}</text>
        <n-i icon="icon-right" />
      </view>
    </view>

    <!-- 一行滑动 -->
    <view v-if="dataField.size == 3">
      <ScrollXContainer only-bottom-radius >
        <VerticalCard
          @tap-goods="toActiveDetails"
          v-for="item in goods"
          :activityType="dataField.activityType"
          :goods-item="item"
          :key="item.prodId"
          :show-product-name="state.productName"
          :show-labels="state.labels"
          :show-price="state.price"
          :card-size="['medium', 'small','large'][dataField.scrollCardSize]"
        />
      </ScrollXContainer>
    </view>

    <!-- 一行两个 -->
    <GoodsContainer v-else-if="dataField.size == 2" only-bottom-radius>
      <VerticalCard
        @tap-goods="toActiveDetails"
        v-for="item in goods"
        :activityType="dataField.activityType"
        :goods-item="item"
        :key="item.prodId"
        :show-product-name="state.productName"
        :show-labels="state.labels"
        :show-price="state.price"
      />
    </GoodsContainer>

    <!-- 一行一个 -->
    <GoodsContainer v-else only-bottom-radius>
      <HorizontalCard
        @tap-goods="toActiveDetails"
        v-for="item in goods"
        :activityType="dataField.activityType"
        cardSize="large"
        :goods-item="item"
        :key="item.prodId"
        :show-product-name="state.productName"
        :show-labels="state.labels"
        :show-price="state.price"
      />
    </GoodsContainer>
  </view>
</template>

<script>
	import http from '@/utils/http.js'
  import utils from '@/utils/util'
  import GoodsContainer from '@/components/goods-card/goods-container'
  import HorizontalCard from '@/components/goods-card/horizontal-card'
  import VerticalCard from '@/components/goods-card/vertical-card'
  import ScrollXContainer from '@/components/goods-card/scroll-x-container'
  import NI from '@/components/n-i'
	export default {
		data() {
			return {
				goods: [],
				show: false,
        state: {
					//显示状态
					productName: false,//1
					labels: false,//2
					price: false, //3
					buyBtn: false //4
				},
				mapActivityTypeToName: {
					1: '团购',
					2: '秒杀',
					6: '优惠活动'
				},
				mapActivityTypeToLink: {
					1: `/packageActivities/pages/aBulkList/aBulkList?shopId=${this.shopId}`,
					2: `/packageActivities/pages/snapUpList/snapUpList?shopId=${this.shopId}`,
					6: `/packageActivities/pages/specialOfferList/specialOfferList?shopId=${this.shopId}`,
				},
        locking: false,
        activityTime: ''
			};
		},
		components: {
      GoodsContainer,
      HorizontalCard,
      VerticalCard,
      NI,
      ScrollXContainer
		},
		mounted() {
      this.countDownActivityTime()
			this.getGoodsList()
		},
		props: {
			dataField: {
				type: Object,
				default: () => ({})
			},
			shopId: {
				type: [Number, String],
				default: 0
			},
			will: {
				//需要获取的类型 默认首页
				type: String,
				default: 'home' //home 首页 ，feature 微页面  传 化名ID , goods 商品详情页，传商品ID ,ad 公共广告
			},
		},
		methods:{
      // 计算活动的开始时间、剩余时间
      countDownActivityTime () {
        if (this.dataField.activityType === 1 || this.dataField.activityType === 6) {
          const { needCountdown, str } = utils.calcActivityTime(this.dataField.startTime, this.dataField.endTime)
          this.activityTime = str
          if (needCountdown) {
            setTimeout(() => {
              this.countDownActivityTime()
            }, 1000)
          }
        }
      },
			/**
			 * 跳转活动商品
			 */
			toActiveDetails(item) {
				if (item.prodType == 2) {
          uni.navigateTo({
            url: '/packageActivities/pages/snapUpDetail/snapUpDetail?seckillId=' + item.activityId
          })
					return
				}
        uni.navigateTo({
          url: '/pages/prod/prod?prodid=' + item.prodId
        })
			},
			/**
			 * 跳转活动列表
			 */
			toActiveList() {
				// 1团购  2秒杀 6优惠活动
        let url = this.mapActivityTypeToLink[this.dataField.activityType];
        if (this.dataField.activityId) {
          if (this.dataField.activityType === 1 || this.dataField.activityType === 6) {
            url += `&activityId=${this.dataField.activityId}&activityName=${this.dataField.activityName}&startTime=${this.dataField.startTime}&endTime=${this.dataField.endTime}`
          }
        }
				uni.navigateTo({
					url,
				})
			},
			/**
			 * 获取活动商品列表
			 */
			getGoodsList() {
				let data = this.dataField;
        this.setData({
          state: {
            //显示状态
            productName: !!data.showContent?.find(x => x * 1 === 1),//1
            labels: !!data.showContent?.find(x => x * 1 === 2),//2
            price: !!data.showContent?.find(x => x * 1 === 3),//3
            buyBtn: !!data.showContent?.find(x => x * 1 === 4) //4
          }
        });
        if (this.dataField.prodIds && !this.locking) {
          this.locking = true
          http.request({
            url: '/prod/listProdByIdsAndType',
            method: 'GET',
            data: {
              prodIds: this.dataField.prodIds.toString(),
              prodType: data.activityType
            },
            callBack: res => {
              this.locking = false
              const sorted = this.sortList(res)
              this.goods = sorted
            }
          })
        }
			},
      /**
			 * 按照装修的id排序
			 */
			sortList(goodsList) {
				let showArr = this.dataField.prodIds
				goodsList.forEach(item=>{
					item.sortId = showArr.indexOf(item.prodId)
				})
				return goodsList.sort((a,b) => { return a.sortId - b.sortId })
			},
		},
    computed: {
      activityTitle() {
        if (this.dataField.activityType !== 2) {
          return this.dataField.activityName || this.mapActivityTypeToName[this.dataField.activityType]
        }
        return this.mapActivityTypeToName[this.dataField.activityType]
      }
    }
	}
</script>

<style scoped>
	@import url("./promotional-activities.css");
</style>

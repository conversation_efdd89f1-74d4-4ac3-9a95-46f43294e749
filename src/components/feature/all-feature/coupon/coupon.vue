<template>
  <view class="coupon">
    <view class="coupon-header">
      <text class="coupon-header-title">优惠券</text>
      <view class="coupon-header-more" @click="handleClick">
        <text>查看更多</text>
        <n-i icon="line-chevron-right" width="28rpx" height="28rpx" />
      </view>
    </view>
    <ScrollXContainer>
      <view class="coupon-container">
        <view 
          v-for="(item, index) in couponList.slice(0,5)"
          :key="index" 
          class="coupon-item-wrap"
          :class="{fill: couponList.length <= 1}"
        >
          <SimpleCouponCard 
            :coupon="item" 
            @setCouponCanGoUseFlag="setCouponCanGoUseFlag(index)"
          />
        </view>
      </view> 
    </ScrollXContainer>
  </view>
</template>

<script>
import http from '@/utils/http.js'
import NI from '@/components/n-i'
import ScrollXContainer from '@/components/goods-card/scroll-x-container'
import SimpleCouponCard from '@/components/coupon-card/simple-coupon-card'

export default {
  data() {
    return {
      selectedCouponId: 0,
      couponList: [],
      stsType: 4,
    };
  },
  props: {
    dataField: {
      type: Object,
      default: () => ({})
    },
    shopId: {
      type: [Number, String],
      default: 0
    },
    will: {
      //需要获取的类型 默认首页
      type: String,
      default: 'home' 
    }
  },
  computed:{
  	i18n() {
  		return this.$t('index')
  	},

  },
  components: {
    NI,
    ScrollXContainer,
    SimpleCouponCard,
  },
  mounted() {
    this.getCouponList()
  },
  methods: {
    getCouponList() {
      http.request({
        url: `/coupon/couponByIds`,
        method: 'GET',
        data: {
          couponIds: this.dataField.couponIds.join(','),
        },
        callBack: res => {
          this.initCouponCanGoUseFlag(res);
          this.couponList = res
        }
      })
    },
    // 初始化优惠券去可以使用的标记
    initCouponCanGoUseFlag(couponList) {
      couponList.forEach(coupon => {
        coupon.canGoUse = coupon.curUserReceiveCount >= coupon.limitNum;
      });
    },
    // 设置优惠券去使用的标记
    setCouponCanGoUseFlag(index) {
      var tempCouponList = this.couponList;
      tempCouponList[index].canGoUse = true;
      tempCouponList[index].stocks -= 1;
      this.setData({
        couponList: tempCouponList
      });
    },
    handleClick() {
      uni.navigateTo({
        url: '/packageActivities/pages/couponList/couponList?couponIds=' + this.dataField.couponIds.join(','),
      })
    }
  }
};
</script>

<style scoped>
.coupon {
  padding: 10rpx 0;
}
.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin: 0 48rpx 36rpx;
}
.coupon-header-title {
  color: #000;
  font-size: 32rpx;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.coupon-header-more {
  color: #999;
  text-align: right;
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.coupon-header-more image {
  vertical-align: bottom;
  width: 28rpx;
  height: 28rpx;
}
.coupon-container {
  display: inline-flex;
  height: 206rpx;
  gap: 16rpx;
  width: 100%;
}

.coupon-item-wrap {
  position: relative;
  flex-shrink: 0;
  width: 459rpx;
  height: 206rpx;
}

.coupon-item-wrap.fill {
  width: calc(100vw - 64rpx)
}

</style>

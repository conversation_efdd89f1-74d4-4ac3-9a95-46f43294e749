<template>
  <view>
    <view v-if="!locking && isEmpty">
    </view>

    <ScrollXContainer v-if="dataField.size == 3">
      <VerticalCard
        @tap-goods="goodsFun"
        v-for="item in goods"
        :goods-item="item"
        :key="item.prodId"
        :show-product-name="state.productName"
        :show-labels="state.labels"
        :show-price="state.price"
        :card-size="['medium', 'small','large'][dataField.scrollCardSize]"
        :show-ori-price="false"
      />
    </ScrollXContainer>

    <GoodsContainer v-else-if="dataField.size == 2">
      <VerticalCard
        @tap-goods="goodsFun"
        v-for="item in goods"
        :goods-item="item"
        :key="item.prodId"
        :show-product-name="state.productName"
        :show-labels="state.labels"
        :show-price="state.price"
        :show-ori-price="false"
      />
    </GoodsContainer>
    <GoodsContainer v-else>
      <HorizontalCard
        @tap-goods="goodsFun"
        v-for="item in goods"
        :goods-item="item"
        :key="item.prodId"
        :show-product-name="state.productName"
        :show-labels="state.labels"
        :show-price="state.price"
        :show-ori-price="false"
      />
    </GoodsContainer>
  </view>

</template>

<script>
	import http from '../../../../utils/http.js'
  import GoodsContainer from '@/components/goods-card/goods-container'
  import HorizontalCard from '@/components/goods-card/horizontal-card'
  import VerticalCard from '@/components/goods-card/vertical-card'
  import ScrollXContainer from '@/components/goods-card/scroll-x-container'

	export default {
		data() {
			return {
				goods: [],
				state: {
					productName: false,//1
					labels: false,//2
					price: false, //3
					buyBtn: false //4
				},
				locking: false
			};
		},
		components: {
      GoodsContainer,
      HorizontalCard,
      VerticalCard,
      ScrollXContainer
		},
		props: {
			dataField: {
				type: Object,
				default: () => ({})
			},
			shopId: {
				type: [Number, String],
				default: 0
			},
			will: {
				//需要获取的类型 默认首页
				type: String,
				default: 'home' //home 首页 ，feature 微页面  传 化名ID , goods 商品详情页，传商品ID ,ad 公共广告
			}
		},

		mounted() {
			let data = this.dataField;
			this.setData({
				state: {
					productName: !!data.showContent.find(x => x * 1 === 1), // 1
					labels: !!data.showContent.find(x => x * 1 === 2), // 2
					price: !!data.showContent.find(x => x * 1 === 3), // 3
					buyBtn: !!data.showContent.find(x => x * 1 === 4) //4
				}
			});
			if (this.dataField.goods && !this.locking) {
				this.locking = true
				http.request({
					url: '/prod/listProdByIdsAndType',
					method: 'GET',
					data: {
						prodIds: this.dataField.goods.toString(),
						prodType: ''
					},
					callBack: res => {
						this.locking = false
						const sorted = this.sortList(res)
            this.goods = sorted
					}
				})
			}
		},

		methods: {
			/**
			 * 点击商品事件
			 * */
			goodsFun(prod) {
        if (prod.prodId) {
          uni.navigateTo({
            url: '/pages/prod/prod?prodid=' + prod.prodId
          })
        }
			},

			/**
			 * 按照装修的id排序
			 */
			sortList(goodsList) {
				let showArr = this.dataField.goods
				goodsList.forEach(item=>{
					item.sortId = showArr.indexOf(item.prodId)
				})
				return goodsList.sort((a,b) => { return a.sortId - b.sortId })
			}
		},
    computed: {
      isEmpty() {
        return this.goods.length === 0
      }
    }
	};
</script>

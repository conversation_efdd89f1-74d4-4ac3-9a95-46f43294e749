<template>
  <view class="notice-box-wrap">
    <view class="notice-box" @tap="handleTapNotice">
      <view class="notice-box__left">
        <n-i icon="bell-02" width="26rpx" height="26rpx" />
      </view>
      <view class="notice-box__content">
        <view class="notice-box__text-wrap">
          <view v-if="!!newsItem" class="notice-box__content__text" :class="{wordsloop: needScroll(newsItem.title)}">
            {{newsItem.title}}
          </view>
        </view>
      </view>
      <view class="notice-box__right">
        <n-i icon="chevron-right" width="26rpx" height="26rpx" />
      </view>
    </view>
  </view>
</template>

<script>
	import http from '../../../../utils/http.js'
  import NI from "@/components/n-i"

  let currentNoticeId = ''

	export default {
		data() {
			return {
				newsItem: null
			};
		},

		components: {
      NI,
		},
		props: {
			dataField: {
				type: Object,
				default: () => ({})
			},
			shopId: {
				type: [Number, String],
				default: 0
			},
			will: {
				//需要获取的类型 默认首页
				type: String,
				default: 'home' //home 首页 ，feature 微页面  传 化名ID , goods 商品详情页，传商品ID ,ad 公共广告
			}
		},

		mounted() {
			this.getNoticeList()
		},

		methods: {
      // 是否需要滚动字幕
      needScroll(str = '') {
        return str.length > 25
      },
      handleSwiperChange({ detail }) {
        currentNoticeId = detail.currentItemId
      },
			getNoticeList() {
				// 加载公告
				var params = {
					url: "/shop/notice/topNoticeList/" + this.shopId,
					method: "GET",
					callBack: res => {
            if (res && res.length > 0) {
              const target = res.filter((item) => item.isTop)

              if (target && target.length) {
                currentNoticeId = target[0].id.toString()
                this.setData({
                  newsItem: target[0]
                });
              }
							else {
								currentNoticeId = res[0].id.toString()
								this.setData({
									newsItem: res[0]
								});
							}
            }
						uni.hideLoading();
					}
				};
				http.request(params);
			},
			//跳转公告列表页面
			handleTapNotice() {
				uni.navigateTo({
					url: `/pages/news-detail/news-detail?id=${currentNoticeId}`
				});
			},
		},
    computed: {
    }
	};
</script>
<style>
	@import "./notice.css";
</style>

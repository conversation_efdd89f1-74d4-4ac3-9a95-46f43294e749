
@keyframes wordsLoop {
  0% {
      transform: translateX(20%);
  }
  100% {
      transform: translateX(-100%);
  }
}

.notice-box-wrap {
  margin: 2rpx 48rpx;
}

.notice-box {
  width: 100%;
  height: 44rpx;
  display: flex;
  border-radius: 8rpx;
  border: 1px solid #F2B436;
  background-color: #FFF7EA;
  overflow-y: hidden;
  box-sizing: border-box;
}

.notice-box__left {
  width: 44rpx;
  height: 44rpx;
  background-color: #F2B436;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.notice-box__content {
  flex: 1;
  color: #000;
  height: 44rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding-left: 16rpx;
  overflow: hidden;
}

.notice-box__text-wrap {
  width: 100%;
  overflow: hidden;

}


.notice-box__content__text {
  font-size: 20rpx;
  display: inline-block;
  white-space: nowrap;
  box-sizing: border-box;
}

.wordsloop {
  animation: wordsLoop 10s linear infinite;
}

.notice-box__right {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}
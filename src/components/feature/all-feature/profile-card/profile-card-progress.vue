<template>
  <view class="profile-card-userinfo-big-circle">
    <view class="profile-card-userinfo-big-circle-progress">
      <view class="circle-progress-canvas-wrap">
        <canvas canvas-id="circleProgress" class="circle-progress-canvas"></canvas>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ProfileCardProgress',
  options: {
    virtualHost: true
  },
  data() {
    return {
      canvasId: 'circleProgress'
    }
  },
  props: {
    simplePlanInfo: {
      type: Object,
      default: () => null
    }
  },
  watch: {
    simplePlanInfo: {
      handler() {
        if (this.simplePlanInfo) {
          this.$nextTick(() => {
            this.drawCircleProgress()
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    getCanvasParentSize() {
      return new Promise((resolve) => {
        uni.createSelectorQuery().in(this).select('.profile-card-userinfo-big-circle-progress').boundingClientRect(data => {
          resolve(data)
        }).exec()
      })
    },
    // 绘制圆角矩形的方法
    drawRoundRect(ctx, x, y, width, height, r, color) {
      // 开始绘制
      ctx.beginPath()
      ctx.setFillStyle(color)
      // 左上角
      ctx.arc(x + r, y + r, r, Math.PI, Math.PI * 1.5)
      // 上边
      ctx.moveTo(x + r, y)
      ctx.lineTo(x + width - r, y)
      // 右上角
      ctx.arc(x + width - r, y + r, r, Math.PI * 1.5, Math.PI * 2)
      // 右边
      ctx.lineTo(x + width, y + height - r)
      // 右下角
      ctx.arc(x + width - r, y + height - r, r, 0, Math.PI * 0.5)
      // 下边
      ctx.lineTo(x + r, y + height)
      // 左下角
      ctx.arc(x + r, y + height - r, r, Math.PI * 0.5, Math.PI)
      // 左边
      ctx.lineTo(x, y + r)
      ctx.closePath()
      ctx.fill()
    },
    
    async drawCircleProgress() {
      const progress = this.simplePlanInfo.progress
      const { width, height } = await this.getCanvasParentSize()
      const ctx = uni.createCanvasContext(this.canvasId, this)
      const centerX = width / 2
      const centerY = height / 2
      const circleRadius = centerX - 8 // 留出边距
      const lineWidth = 16
      
      // 绘制背景圆环
      ctx.beginPath()
      ctx.arc(centerX, centerY, circleRadius, 0, 2 * Math.PI)
      ctx.setLineWidth(lineWidth)
      ctx.setLineCap('round')
      ctx.setStrokeStyle('#EAEEFC')
      ctx.stroke()
      
      // 绘制进度圆环
      ctx.beginPath()
      const startAngle = Math.PI
      const endAngle = startAngle + (progress / 100) * 2 * Math.PI;
      ctx.arc(centerX, centerY, circleRadius, startAngle, endAngle)
      ctx.setLineWidth(lineWidth)
      ctx.setLineCap('round')
      ctx.setStrokeStyle('#2F54D4') // 使用品牌色
      ctx.stroke()
      
      // 绘制虚线圆
      ctx.beginPath()
      ctx.arc(centerX, centerY, circleRadius - lineWidth - 4, 0, 2 * Math.PI)
      ctx.setLineWidth(1)
      ctx.setLineCap('round')
      ctx.setLineDash([2, 2])
      // #ifdef H5
      ctx.setLineDash([5, 5])
      // #endif
      ctx.setStrokeStyle('#2F54D4')
      ctx.stroke()

      // 绘制simplePlanInfo.value 和 simplePlanInfo.unit
      ctx.setTextBaseline('middle')
      ctx.setFontSize(20)
      const valueWidth = ctx.measureText(this.simplePlanInfo.value).width
      ctx.setFontSize(14)
      const unitWidth = ctx.measureText(this.simplePlanInfo.unit).width
      const totalWidth = valueWidth + unitWidth
      const x = centerX - totalWidth / 2
      ctx.setFontSize(20)
      ctx.setFillStyle('#2F54D4')
      ctx.fillText(this.simplePlanInfo.value, x, centerY - 10)
      ctx.setFontSize(14)
      ctx.setFillStyle('#373737')
      ctx.fillText(this.simplePlanInfo.unit, x + valueWidth + 4, centerY - 10)

      // 绘制simplePlanInfo.description 带圆角矩形背景
      ctx.setFontSize(12)
      const descriptionWidth = ctx.measureText(this.simplePlanInfo.description).width
      const descriptionX = centerX - descriptionWidth / 2
      const padding = 2 // 文字与背景边缘的间距
      const bgWidth = descriptionWidth + padding * 2
      const bgHeight = 20 // 背景高度
      const bgY = centerY + 2 // 背景Y坐标
      const cornerRadius = 12 // 圆角半径
      
      // 绘制圆角矩形背景
      this.drawRoundRect(ctx, centerX - bgWidth / 2, bgY, bgWidth, bgHeight, cornerRadius, '#E2E9FF')
      
      // 绘制文字
      ctx.setFillStyle('#2F54D4')
      ctx.fillText(this.simplePlanInfo.description, descriptionX, bgY + 10)

      ctx.draw()
    },
  }
}
</script>

<style lang="scss" scoped>
.profile-card-userinfo-big-circle {
  --circle-size: 284rpx;
  width: var(--circle-size);
  height: var(--circle-size);
  background-image: linear-gradient(140deg, rgba(188,240,255,0.75) 1%, #FFFFFF 21%);
  border-radius: 50%;
  margin: 0 auto;
  position: absolute;
  right: 20rpx;
  top: calc(var(--top-space) * -1);
  display: flex;
  align-items: center;
  justify-content: center;
}
.profile-card-userinfo-big-circle-progress {
  width: calc(var(--circle-size) - 32rpx);
  height: calc(var(--circle-size) - 32rpx);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-progress-canvas-wrap {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
}

.circle-progress-canvas {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
<script>
import NButton from '@/components/n-button'
import NI from '@/components/n-i'
import callWenetApi from '@/utils/call-wenet-api'
import http from '@/utils/http'
import { featureRoute } from '@/utils/util'
import ContactButton from '@/components/contact-button'
import ProfileCardProgress from './profile-card-progress'
export default {
  name: 'ProfileCard',
  components: {
    NButton,
    NI,
    ContactButton,
    ProfileCardProgress
  },
  options: {
    virtualHost: true
  },
  props: {
    dataField: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      canvasId: 'circleProgress',
      currentPlan: null,
      packageNameContainerWidth: 0,
      packageNameContentWidth: 0,
      simplePlanInfo: {
        value: '',
        unit: '',
        description: '',
        progress: 0
      },
      notice: {
        title: '',
        id: '',
      },
      extraList: [
        {
          icon: 'home-icon-customer',
          title: '在线客服',
          desc: '咨询、订购、报修',
          key: 'customer'
        },
        {
          icon: 'home-icon-service',
          title: '自助服务',
          desc: '日常问题个人排查',
          key: 'service'
        }
      ],
      renewProdId: '',
    }
  },
  watch: {
    currentShop: {
      handler() {
        this.getNoticeList()
      },
      immediate: true
    }
  },
  mounted() {
    this.getCurrentPlans()
    this.$nextTick(() => {
      this.getPackageNameContainerWidth()
    })
  },
  methods: {
    async getCurrentPlans() {
      const res = await callWenetApi({url: '/api/v2/plan/plans', method: 'GET', data: {page: 0, size: 100, useStatus: 'IS_USING'}})
      const plans = res.content || []
      const currentPlan = plans.find(plan => plan.default_plan)
      const simplePlanInfo = this.calculateSimplePlanInfo(currentPlan)
      this.currentPlan = currentPlan
      this.simplePlanInfo = simplePlanInfo
      this.getRenewProd(currentPlan.productId)
      this.$nextTick(() => {
        this.getPackageNameContentWidth()
      })
    },
    async getPackageNameContainerWidth() {
      const query = uni.createSelectorQuery().in(this)
      query.select('.profile-card-userinfo-package-content-wrap').boundingClientRect(data => {
        const containerWidth = data.width
        this.packageNameContainerWidth = containerWidth
      }).exec()
    },
    async getPackageNameContentWidth() {
      const query = uni.createSelectorQuery().in(this)
      query.select('.profile-card-userinfo-package-content').boundingClientRect(data => {
          const contentWidth = data.width
          this.packageNameContentWidth = contentWidth
        }).exec()
    },
    
    calculateSimplePlanInfo(planData) {
      const result = {
        value: '',
        unit: '',
        description: '',
        progress: 0
      }
      // 如果不是生效套餐，直接返回空结果
      if (!planData?.default_plan) {
        return result
      }
      const { flowQuota, timeQuota, timeRemaining, flowRemaining } = planData
      if (timeQuota !== 0) {
        result.description = '剩余时长'
        result.progress = 100 * (timeRemaining / timeQuota)
        result.value = (timeRemaining / 60 / 60 / 24).toFixed(1)
        result.unit = '天'
      } else if (flowQuota !== 0) {
        result.description = '剩余流量'
        result.progress = 100 * (flowRemaining / flowQuota)
        result.value = (flowRemaining / 1000 / 1000).toFixed(1)
        result.unit = 'G'
      }
      return result
    },
    getNoticeList() {
      if (!this.currentShop?.shopId) {
        return
      }
      http.request({
        url: "/shop/notice/topNoticeList/" + this.currentShop.shopId,
        method: "GET",
        callBack: res => {
          if (res && res.length > 0) {
            const target = res.filter((item) => item.isTop)
            if (target && target.length) {
              this.notice = target[0]
            }
          }
        }
      });
    },
    //跳转公告列表页面
    handleTapNotice() {
      if (this.notice?.id) {
        uni.navigateTo({
          url: `/pages/news-detail/news-detail?id=${this.notice.id}`
        });
      }
    },
    handleTapBuy() {
      const buyType = this.dataField?.buyType
      const buyPath = this.dataField?.buyPath
      featureRoute({
        type: buyType,
        link: buyPath
      })
    },
    handleTapRenew() {
      if (this.renewProdId) {
        uni.navigateTo({
          url: `/pages/prod/prod?prodid=${this.renewProdId}`
        })
        return
      }
      const renewType = this.dataField?.renewType
      const renewPath = this.dataField?.renewPath
      featureRoute({
        type: renewType,
        link: renewPath
      })
    },
    handleTapExtra(key) {
      if (key === 'customer') {
        this.$refs.contactButtonRef.handleShowServiceImage()
      } else if (key === 'service') {
        uni.switchTab({
          url: '/pages/user/user'
        })
      }
    },
    getRenewProd(basPid) {
      http.request({
        url: '/prod/prodIdByOuterId',
        method: 'GET',
        data: {
          outerId: basPid
        },
        callBack: res => {
          const renewProd = res?.[0]
          if (renewProd) {
            this.renewProdId = renewProd.prodId
          }
        }
      })
    }
  },
  computed: {
    isLogin() {
      return this.$store.state.isLogin;
    },
    currentShop() {
      return this.$store.state?.currentShop;
    },
    userInfo() {
      return this.$store.state?.userInfo;
    },
    simpleWenetAccountInfo() {
      return this.$store.state?.simpleWenetAccountInfo;
    },
    currentPlanName() {
      return this.currentPlan?.productName || '-'
    },
    packageNameOverflowWidth() {
      return (this.packageNameContainerWidth - this.packageNameContentWidth)
    },
    showNotice() {
      return this.dataField?.showNotice
    }
  }
}

</script>

<template>
  <view class="profile-card-wrap" :style="{ '--package-name-overflow-width': (packageNameOverflowWidth-16) + 'px' }">
    <view class="profile-card-top-space"></view>
    <view class="profile-card">
      <view class="profile-card-content" :class="{ 'allradius': !showNotice }">
        <view class="profile-card-userinfo text-primary">
          <view class="profile-card-userinfo-name">
            <text class="text-size-20 font-weight-600 profile-card-userinfo-name-hi">Hi </text>
            <text class="text-size-14 font-weight-700" v-if="userInfo">{{ userInfo.basUsername }}</text>
          </view>
          <view class="profile-card-userinfo-package">
            <view class="profile-card-userinfo-package-title text-size-14 font-weight-400">我的套餐</view>
            <view class="profile-card-userinfo-package-content-wrap">
              <view class="profile-card-userinfo-package-content text-size-14 font-weight-600" :class="{ 'animate': packageNameOverflowWidth < 0 }">{{ currentPlanName }}</view>
            </view>
          </view>
          <NButton type="ghost" @click="handleTapRenew">我要续费</NButton>
        </view>
        <view class="profile-card-buy-nav">
          <NButton type="primary" @click="handleTapBuy">购买套餐</NButton>
        </view>
      </view>
      
      <ProfileCardProgress :simplePlanInfo="simplePlanInfo" />
      <view class="profile-card-footer" v-if="showNotice">
        <view class="profile-card-footer-content" @click="handleTapNotice">
          <view class="profile-card-footer-content-icon">
            <NI icon="home-icon-notice" width="44rpx" height="44rpx" />
          </view>
          <view class="profile-card-footer-content-text text-size-14 font-weight-400 text-primary">
            {{ notice.title }}
          </view>
        </view>
      </view>
    </view>

    <view class="profile-card-extra">
      <view class="profile-card-extra-item" v-for="item in extraList" :key="item.key" @click="handleTapExtra(item.key)">
        <view class="profile-card-extra-item-icon">
          <NI :icon="item.icon" width="64rpx" height="64rpx" />
        </view>
        <view class="profile-card-extra-item-text text-primary">
          <view class="text-size-16 font-weight-600">{{ item.title }}</view>
          <view class="text-size-10 font-weight-400">{{ item.desc }}</view>
        </view>
      </view>
    </view>
    <view style="display: none;">
      <ContactButton type="normal" ref="contactButtonRef" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.profile-card-wrap {
  margin: 0 48rpx 32rpx;
  box-sizing: border-box;
  --top-space: 48rpx;
  --package-name-overflow-width: 0px;
}
.profile-card-top-space {
  height: var(--top-space);
  background: transparent;
}
.profile-card {
  font-size: 28rpx;
  box-shadow: 0 3px 6px -4px rgba(0,0,0,0.12), 0 6px 16px 0 rgba(0,0,0,0.08);
  border-radius: 16rpx;
  position: relative;
  margin-bottom: 36rpx;
}
.profile-card-content {
  padding: 32rpx;
  background-image: linear-gradient(157deg, rgba(188,240,255,0.75) 1%, #FFFFFF 31%);
  box-sizing: border-box;
  min-height: 200rpx;
  border-radius: 16rpx 16rpx 0 0;
  position: relative;
}
.profile-card-content.allradius {
  border-radius: 16rpx;
}
.profile-card-userinfo-name {
  margin-bottom: 38rpx;
  font-family: "HelveticaNeueBold";
}
.profile-card-userinfo-name-hi {
  margin-right: 18rpx;
}
.profile-card-userinfo-package {
  display: flex;
  flex-direction: column;
  row-gap: 20rpx;
  margin-bottom: 48rpx;
}
.profile-card-userinfo-package-content-wrap {
  width: 50%;
  overflow: hidden;
}

.profile-card-userinfo-package-content {
  @keyframes wordsLoop {
    0% {
      transform: translateX(0);
    }
    50% {
      transform: translateX(var(--package-name-overflow-width));
    }
    100% {
      transform: translateX(0);
    }
  }
  width: fit-content;
  white-space: nowrap;
}

.profile-card-userinfo-package-content.animate {
  animation: 8s wordsLoop 3s linear infinite normal;
}


.profile-card-buy-nav {
  position: absolute;
  right: 60rpx;
  bottom: 32rpx;
}

.profile-card-footer {
  background: #EAEEFC;
  height: 80rpx;
  box-shadow: 0 3px 6px -4px rgba(0,0,0,0.12), 0 6px 16px 0 rgba(0,0,0,0.08);
  border-radius: 0 0 16rpx 16rpx;
  overflow: hidden;
}
.profile-card-footer-content {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding-left: 22rpx;
  display: flex;
  align-items: center;
}
.profile-card-footer-content-icon {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.profile-card-footer-content-text {
  margin-left: 12rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.profile-card-extra {
  display: flex;
  column-gap: 22rpx;
}

.profile-card-extra-item {
  width: 50%;
  height: 124rpx;
  background-image: linear-gradient(179deg, #FFFFFF 0%, #D6EFFF 100%);
  border: 1px solid #9BD8FF;
  box-shadow: 0 3px 4px 0 rgba(0,0,0,0.10);
  border-radius: 8px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  column-gap: 18rpx;
  padding-left: 34rpx;
}
</style>
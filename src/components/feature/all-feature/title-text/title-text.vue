<template>
  <view class="text-title-wrap">
    <view class="text-title">
      <view class="title-row">
        <view class="title" :style="{textAlign: dataField.align, fontSize: dataField.titleSize +'px', fontWeight: dataField.titleWeight }">
          {{dataField.title}}
        </view>
        <view class="title-more" v-if="dataField.isLink" @tap="viewMore">
          <view class="more-text" v-if="dataField.linkStyle!=3">{{dataField.linkText}}</view>
          <view class="more-icon" v-if="dataField.linkStyle!=1">
            <image :src="`${staticPicDomain}images/icon/more.png`" mode=""></image>
          </view>
        </view>
      </view>
      <view class="decs-row" v-if="dataField.desc" :style="{width: '100%', textAlign: dataField.descAlign || dataField.align, fontSize: dataField.descSize +'px', fontWeight: dataField.descWeight }">
        {{dataField.desc}}
      </view>
    </view>
  </view>
</template>

<script>
import util from '../../../../utils/util'
	export default {
		data() {
			return {

			};
		},
		props: {
			dataField: {
				type: Object,
				default: () => ({})
			},
			shopId: {
				type: [Number, String],
				default: 0
			},
		},
		mounted() {
			// console.log(this.dataField)
		},
		methods: {
			/**
			 * 点击查看更多
			 */
			viewMore() {
				util.featureRoute(this.dataField.link)
			}
		}
	}
</script>

<style>
	@import url("./title-text.css");
</style>

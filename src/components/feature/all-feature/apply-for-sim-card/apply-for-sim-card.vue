<template>
  <view class="apply-for-sim-card">
    <view class="apply-for-sim-card-title">
      <view class="title-tag">
        <text class="title-tag-text">步骤一</text>
        <NI class="title-tag-icon" icon="step-bg01" width="104rpx" height="38rpx" />
      </view>
      <view class="title-text">请选择您想要办理的运营商</view>
    </view>
    <view class="isp-list">
      <view :class="['isp-item', activeIsp === item.key ? 'active' : '']" v-for="(item) in ispList" :key="item.key" @tap="handleIspTap(item.key)">
        <view class="isp-item-logo">
          <image class="isp-item-logo-img" v-show="activeIsp !== item.key" :src="`${staticPicDomain}/images/icon/${item.logo}`" mode="widthFix" />
          <image class="isp-item-logo-img" v-show="activeIsp === item.key" :src="`${staticPicDomain}/images/icon/${item.activeLogo}`" mode="widthFix" />
        </view>
        <view :class="['isp-item-name', activeIsp === item.key ? 'active' : '']">{{ item.name }}</view>
      </view>
    </view>
    <view class="apply-for-sim-card-title">
      <view class="title-tag">
        <text class="title-tag-text">步骤二</text>
        <NI class="title-tag-icon" icon="step-bg01" width="104rpx" height="38rpx" />
      </view>
      <view class="title-text">请选择激活方式</view>
    </view>
    <view class="method-list">
      <view 
        v-for="(item) in activeMethods" 
        :key="item.key" 
        class="method-item"
        @tap="handleMethodTap(item.key)"
      >
        <view class="method-content">
          <view class="method-title">
            <text class="method-name">{{ item.name }}</text>
          </view>
          <view class="method-description">{{ item.description }}</view>
        </view>
        <view class="radio-button">
          <view :class="['radio-inner', activeMethod === item.key ? 'active' : '']"></view>
        </view>
      </view>
    </view>
    
    <view class="tip-box">
      <view class="tip-icon">i</view>
      <view class="tip-text">选择完毕后将前往对应的号卡套餐选择页面，购买成功后即可按照所选方式办理号卡。</view>
    </view>

    <view class="bottom-button" @tap="handleConfirm">确认办理</view>
  </view>
</template>

<script>
import utils from '@/utils/util'
import NI from '@/components/n-i'
const ispNameMap = {
  cmcc: '中国移动',
  cucc: '中国联通',
  ctcc: '中国电信',
}
export default {
  name: 'applyForSimCard',
  data () {
    return {
      activeIsp: null,
      activeMethod: 'selfActivationUrl',
      activeMethods: []
    }
  },
  props: {
    dataField: {
      type: Object,
      default: () => ({})
    },
    shopId: {
      type: [Number, String],
      default: 0
    },
  },
  components: {
    NI
  },
  methods: {
    handleIspTap(key) {
      this.activeIsp = key;
      this.updateActiveMethods(key)
      this.$emit('update:activeIsp', key);
    },
    handleMethodTap(key) {
      this.activeMethod = key
    },
    handleConfirm() {
      if (!this.activeIsp) {
        uni.showToast({
          title: '请选择运营商',
          icon: 'none'
        })
        return
      }
      if (!this.activeMethod) {
        uni.showToast({
          title: '暂无可用的激活方式',
          icon: 'none'
        })
        return
      }
      const url = this.dataField[this.activeIsp][this.activeMethod];
      console.log(url)
      if (url) {
        this.$nextTick(() => {
          utils.featureRoute({
            type: 4, // 直接复用图片广告的方法，支持跳转 http开头的链接，type = 4，表示自定义链接
            link: url
          })
        })
      }
    },
    updateActiveMethods(isp) {
      if (!isp) {
        this.activeMethods = []
        this.activeMethod = null
        return 
      }
      const res = []
      const previousMethod = this.activeMethod // 保存之前选中的方式
      const showSelfActivation = !!this.dataField[isp].selfActivationUrl
      const showAssistedActivation = !!this.dataField[isp].assistedActivationUrl
      if (showSelfActivation) {
        res.push({
          key: 'selfActivationUrl',
          name: '自行激活',
          description: '号卡通过快递寄送，按照附带的激活说明书自行激活号卡',
        })
      }
      if (showAssistedActivation) {
        res.push({
          key: 'assistedActivationUrl',
          name: '专人激活',
          description: '可自行前往服务点或由工作人员上门帮助您激活号卡',
        })
      }
      if (res.length === 0) {
        this.activeMethods = []
        this.activeMethod = null
        return 
      }
      this.activeMethods = res
      // 如果新的激活方式列表包含之前选中的方式，则保持选中状态
      this.activeMethod = res.some(item => item.key === previousMethod) 
        ? previousMethod 
        : res[0].key
    }
  },
  computed: {
    ispList() {
      const res = [];
      Object.keys(this.dataField).forEach(key => {
        if (this.dataField[key].enable) {
          res.push({
            ...this.dataField[key],
            logo: `${key}.png`,
            activeLogo: `${key}-white.png`,
            name: ispNameMap[key],
            key,
          })
        }
      })
      return res;
    },
  },
  watch: {
  },

  mounted () {
    const allKeys = Object.keys(this.dataField);
    const activeKey = allKeys.find(key => this.dataField[key].enable);
    this.activeIsp = activeKey;
    this.updateActiveMethods(activeKey)
  },
}
</script>

<style scoped>
.apply-for-sim-card {
  width: 100%;
  font-size: 28rpx;
  background-color: #f5f7fa;
  padding: 20rpx 32rpx;
  box-sizing: border-box;
}
.apply-for-sim-card-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.title-tag {
  position: relative;
  font-size: 28rpx;
  height: 40rpx;
  position: relative;
  width: 104rpx;
  height: 38rpx;
}
.title-tag-text {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 24rpx;
  color: #fff;
  width: 104rpx;
  height: 38rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.title-text {
  font-size: 24rpx;
  color: #333;
  margin-left: 20rpx;
}
.isp-list {
  display: flex;
  flex-wrap: nowrap;
  column-gap: 32rpx;
  margin-bottom: 64rpx;
}

.isp-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  background: #EDEDED;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #333;
}

.isp-item.active {
  color: #fff;
  background: var(--primary-color);
  position: relative;
}

.isp-item.active::after {
  /* 气泡三角形  */
  content: '';
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 20rpx solid var(--primary-color);
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-bottom: 20rpx solid transparent;
  position: absolute;
  bottom: -30rpx;
  right: 50%;
  transform: translateX(50%);
}

.isp-item-logo {
  width: 96rpx;
  height: 96rpx;
  margin-bottom: 12rpx;
}

.isp-item-logo-img {
  width: 100%;
  height: 100%;
}

.isp-item-name {
  font-size: 24rpx;
}

.method-list {
  margin-bottom: 32rpx;
}

.method-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;

}
.method-item:last-child {
  margin-bottom: 0;
}

.method-content {
  flex: 1;
}

.method-title {
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
}

.method-name {
  color: #333;
  font-size: 28rpx;
  font-weight: 600;
}

.method-description {
  color: rgba(51,51,51,0.60);;
  font-size: 24rpx;
}

.radio-button {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 24rpx;

}

.radio-inner {
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    background: transparent;
    transition: all 0.2s;

  }

.radio-inner.active {
  background: var(--primary-color);
}

.tip-box {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.tip-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid var(--primary-color);
  box-sizing: border-box;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.tip-text {
  color: #666;
  font-size: 24rpx;
  line-height: 1.5;
}

.bottom-button {
  height: 88rpx;
  background: var(--primary-color);
  color: #fff;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
}
</style>
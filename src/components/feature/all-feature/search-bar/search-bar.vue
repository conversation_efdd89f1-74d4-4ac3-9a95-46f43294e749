<template>
	<view class="con" id="featureSearchBar" :style="{'margin-bottom':dataField.position===2&&fixSearchBar?(dataField.boxHeight+16) + 'px': '0',}">
		<view class="search-bar-container" :style="{background:dataField.bgColor, 'position': dataField.position===2&&fixSearchBar?'fixed': 'unset',
							 'top':dataField.position===2&&fixSearchBar?'0': 'unset','z-index':dataField.position===2&&fixSearchBar?'999': 'unset', }">
			<view class="search-bar" @tap="toSearchPage" :style="{height: dataField.boxHeight + 'px', color: dataField.textColor, justifyContent: dataField.textAlgin, }">
				<image :src="`${staticPicDomain}images/icon/search.png`" mode=""></image>
				<text>{{i18n.search}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				fixSearchBar: false,
				searchBarTop: 0,
			}
		},
		props: {
			dataField: {
				type: Object,
				default: () => ({})
			},
			pageScorllTop: {
				type: Number,
				default: 0
			},
			shopId: {
				type: [Number, String],
				default: 0
			},
		},
		computed: {
			i18n() {
				return this.$t('index')
			}
		},
		watch: {
			pageScorllTop(nv) {
				let extraHeight = 0
				// #ifdef H5
				extraHeight = 40
				// #endif
				if (nv >= this.searchBarTop + extraHeight) {
					this.fixSearchBar = true
				} else {
					this.fixSearchBar = false
				}
			}
		},
		mounted() {
			this.getSearchBarScrollTop()
		},
		methods: {
			/**
			 * 获取搜索框所在位置
			 */
			getSearchBarScrollTop() {
				this.$nextTick(() => {
					setTimeout(() => {
						const query = this.createSelectorQuery()
						query.select('#featureSearchBar').boundingClientRect()
						query.selectViewport().scrollOffset()
						query.exec((res) => {
							if (res[0].top) {
								this.searchBarTop = res[0].top
							}
						})
					}, 200)
				})
			},
			/**
			 * 跳转搜索页面
			 */
			toSearchPage() {
				// 平台首页，跳转到平台搜索页面
				uni.navigateTo({
					url: this.shopId === 0 ? '/pages/search-page/search-page' : '/packageShop/pages/shopSearch/shopSearch?shopId=' + this.shopId
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.search-bar-container {
	padding: 16rpx 48rpx;
	width: 100%;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	align-items: center;
}
.search-bar {
	flex: 1;
	display: flex;
	align-items: center;
	font-size: 28rpx;
	height: 60rpx;
	line-height: 48rpx; 
	padding-left: 6rpx;
	border-bottom: 1px solid $wenet-color-brand;
	color: #999;
	justify-content: flex-start;
	position: relative;
}
.search-bar > image {
	width: 30rpx;
	height: 30rpx;
	margin-right: 10rpx;
}
.search-bar > text {
	font-size: 26rpx;
}
.search{
	width: 48px;
	height: 28px;
	border-radius: 28rpx;
	background-color: $wenet-color-brand;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	color: #fff;
	position: absolute;
	right: 2rpx;
}
</style>

<template>
  <view class="school-selector">
    <UniDataSelect
      class="input-style" 
      :localdata="selectLocaldata"
      v-model="selectedOu"
      @change="handleChange"
      show-search
    />
  </view>
</template>

<script>
  import http from '@/utils/http';
  import NI from '@/components/n-i'
  import UniDataSelect from '@/components/uni-data-select/uni-data-select';
  import config from '@/utils/config';
  export default {
    data() {
      return {
        orgList: [],
        innerBasOrgList: [],
        selectedOu: '',
      }
    },
    model: {
      prop: 'value',
      event: 'input', // uniapp自定义model，必须使用value和input，否则不生效
    },
    props: {
      value: {
        type: Object,
        default: () => ({})
      },
      disabled: {
        type: Boolean,
        default: false
      },
      basOrgList: {
        type: Array,
        default: null,
      }
    },
    components: {
      NI,
      UniDataSelect
    },
    created() {
      this.getOrgList()
      if (this.basOrgList) {
        this.innerBasOrgList = this.basOrgList
      } else {
        this.getBasOrgList()
      }
    },
    methods: {
      /**
       * 获取学校列表
       */
      getOrgList() {
        const params = {
          url: '/sys/org/list',
          method: 'GET',
          data: {},
          callBack: res => {
            if (res && res.length > 0) {
              this.orgList.unshift(...res)
            }
          }
        }
        http.request(params);
      },
      getBasOrgList() {
        var params = {
          url: "/v1/projection/org/visible",
          domain: config.wenetUrl, 
          method: "GET",
          callBack: (res) => {
            this.innerBasOrgList = res
          }
        };
        http.request(params);
      },
      handleChange(value) {
        const originItem = this.usedBasOrgList.find(item => item.ou === value)
        this.$emit('input', originItem)
        this.$emit('change', originItem)
      }
    },
    computed: {
      usedBasOrgList() {
        if (this.basOrgList && this.basOrgList.length) {
          return this.basOrgList
        }
        return this.innerBasOrgList
      },
      selectLocaldata() {
        return this.usedBasOrgList?.map((item) => ({ text: item.localityName, value: item.ou}))
      }
    }
  }
</script>

<style scoped>

.school-selector {
  display: flex;
  box-sizing: border-box;
  width: 100%;
  height: 84rpx;
  padding: 0rpx 32rpx;
  align-items: center;
  border-radius: 16rpx;
  background: #F5F5F5;
  margin-bottom: 48rpx;
}
</style>

<template>
  <view class="login-with-wenet">
    <view class="login-with-wenet__input-area"  :class="{loading: loading}">
      <!-- 没有缓存wenet信息，需要输入wenet账号，随后再获取手机号，登录商城，再关联wenet账号 -->
      <template v-if="!haveWenetAccount">
        
        <template>
          <view>
            <NInput v-model="wenetAccount.username" :placeholder="studentIdPlaceholder" />
            <view style="height: 32rpx;"></view>
            <NInput type="password" v-model="wenetAccount.password" :placeholder="passwordPlaceholder" />
            <view  class="back">
              <text @click="toForgetPassword">
                忘记密码
              </text>
            </view>
            <NButton :loading="loading" @click="handleWenetAccountConfirm" type="primary" block>确认</NButton>
          </view>
        </template>
      </template>
  
      <template v-else>
        <view class="login-phone__tip" v-if="selectedOu !== '0'">
          为了方便您的购物，需要获取您的手机号码
        </view>

        <view v-if="mode === 'wx'">
          <!-- #ifdef MP-WEIXIN -->
           <NButton
            type="primary"
            block
            :loading="loading"
            :open-type="privacyChecked ? 'getPhoneNumber': ''"
            @click="handleClickGetPhoneNumber"
            @getphonenumber="handleGetPhoneNumber"
          >
            一键获取手机号码
           </NButton>
           <view style="height: 32rpx;"></view>
          <!-- #endif -->

          <view class="switch-mode-btn-wrap">
            <NButton @click="switchMode" type="ghost" block>
              使用短信验证码登录
            </NButton>
          </view>
        </view>

        <view v-if="mode === 'phone'">
          <VerifyCode :privacy-checked="privacyChecked" @beforeSendVerifyCode="handleBeforeSendVerifyCode" @clickLogin="handleClickVerifyCodeConfirm" @verifyCodeSuccess="handleVerifyCodeSuccess" @verifyCodeError="handleVarifyCodeError" />
            <!-- #ifdef MP-WEIXIN -->
            <view style="height: 32rpx;"></view>
            <NButton
              type="ghost"
              block
              :loading="loading"
              :open-type="privacyChecked ? 'getPhoneNumber': ''"
              @click="handleClickGetPhoneNumber"
              @getphonenumber="handleGetPhoneNumber"
            >
            一键获取手机号码
           </NButton>
            <!-- #endif -->
        </view>
      </template>

    </view>

    <view v-if="showRegister && !haveWenetAccount" class="to-register" @click="goToRegister" >没有账号？前往注册</view>

  </view>
</template>

<script>
import config from '@/utils/config'
import http from '@/utils/http'
import VerifyCode from './VerifyCode'
import NI from '@/components/n-i'
import NButton from '@/components/n-button'
import NInput from '@/components/form/n-input'
import { AppType } from '@/utils/constant.js'
import { cacheWenetAccountInfo } from '@/utils/login-processor'

export default {
  data() {
    const apptype = uni.getStorageSync('appType')
    return {
      wenetAccount: {
        username: '',
        password: '',
      },
      loading: false,
      haveWenetAccount: false,
      mode: apptype === AppType.MINI ? 'wx' : 'phone',
    }
  },
  props: {
    basOrgList: {
      type: Array,
      default: () => []
    },
    privacyChecked: {
      type: Boolean,
      default: false
    },
    selectedOu: {
      type: String,
      default: ''
    },
    selectedOuPrefix: {
      type: String,
      default: ''
    },
    studentIdPlaceholder: {
      type: String,
      default: ''
    },
    passwordPlaceholder: {
      type: String,
      default: ''
    },
    showRegister: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    VerifyCode,
    NI,
    NButton,
    NInput
  },
  mounted() {
    this.mode = this.appType === AppType.MINI ? 'wx' : 'phone'
  },
  methods: {
    toForgetPassword() {
      uni.navigateTo({
        url: `/packageUser/pages/forget-password/forget-password?ou=${this.selectedOu}&prefix=${this.selectedOuPrefix}`
      })
    },
    goToRegister() {
      uni.navigateTo({
        url: `/pages/login/register?ou=${this.selectedOu}`
      })
    },
    switchMode() {
      this.mode = this.mode === 'wx' ? 'phone' : 'wx'
    },
    handleClickGetPhoneNumber() {
      if (!this.privacyChecked) {
        uni.showToast({
          title: '请先同意隐私策略和服务条款',
          icon: 'none'
        })
        return
      }
    },
    handleGetPhoneNumber(e) {
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
          // 请求微信接口获取 code
          const params = {
            url: '/verify/moblie',
            method: 'post',
            data: {
              code: e.detail.code,
            },
            callBack: res => {
              const cachedWenetAccount = uni.getStorageSync("simpleWenetAccount");
              this.$emit('getUserMobileSuccess', {
                ...cachedWenetAccount,
                basMobile: res
              })
            },
            errCallBack: errRes => {
            }
          }
          http.request(params)
				} else {
					uni.showToast({
						title: this.i18n.privilegeGrantFailed,
						icon: 'none'
					})
				}
    },

    // 点击确定按钮，检查wenet账号是否正确
    handleWenetAccountConfirm() {
      // 需要同意隐私协议
      if (!this.privacyChecked) {
        uni.showToast({
          title: '请先同意隐私策略和服务条款',
          icon: 'none'
        })
        return
      }
      const tips = {
        ou: '请选择学校',
        username: '请输入账号',
        password: '请输入密码',
      }
      const values = {
        ...this.wenetAccount,
        ou: this.selectedOu,
      }
      let isFullInfo = true
      Object.keys(tips).forEach(key => {
        if (!values[key]) {
          uni.showToast({
            title: tips[key],
            icon: 'none'
          })
          isFullInfo = false
          return
        }
      })
      if (!isFullInfo) return
      this.checkWenetAccount()
    },
    /**
     * 检查wenet账号
     */
    checkWenetAccount() {
      const username = this.wenetAccount.username;
      const password = this.wenetAccount.password;
      const prefix = this.selectedOuPrefix;

      const fullUsername = `${prefix}${username}`
      http.request({
        domain: config.wenetUrl,
        url: `/api/v1/auth?username=${fullUsername}&password=${password}`,
        method: 'POST',
        callBack: (res) => {
          const accessToken = res.access_token;
          const tokenType = 'Bearer'
          uni.setStorageSync('wenetToken', `${tokenType} ${accessToken}`)
          this.getRequiredWenetInfo(`${tokenType} ${accessToken}`)
        },
        errCallBack: (err) => {
          if (err.data.code?.toString() === '1022') {
            uni.showToast({
              title: '账号或密码错误',
              icon: 'none'
            })
          }
        }
      });
    },
    /**
     * 根据检查wenet账号获得的token 查询uid
     */
    getRequiredWenetInfo(token) {
      var params = {
        domain: config.wenetUrl,
        url: '/api/v1/account/me',
        method: "GET",
        data: {},
        dontTrunLogin: true,
        overWriteToken: token,
        callBack: (res) => {
          const simpleWenetAccount = cacheWenetAccountInfo(res)
          this.haveWenetAccount = true
          this.$emit('getBasUserinfoSuccess', simpleWenetAccount)
        },
        errCallBack: () => {
        }
      };
      http.request(params);
    },

    handleClickVerifyCodeConfirm() {
    },
    handleVerifyCodeSuccess(res) {
      const simpleWenetAccount = uni.getStorageSync("simpleWenetAccount");
      this.$emit('getUserMobileSuccess', {
        ...simpleWenetAccount,
        basMobile: res
      })
    },
    handleVarifyCodeError() {
    },
    handleBeforeSendVerifyCode(mobile, next) {
      next()
    }
  },
  computed: {
    appType() {
      return uni.getStorageSync('appType')
    },
    showWxSwitch() {
      return this.appType === AppType.MINI
    },
  }
}
</script>

<style lang="scss" scoped>
.login-with-wenet {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  column-gap: 8rpx;
  margin: auto;
  position: relative;
}

.login-with-wenet__input-area.loading {
  opacity: 0;
}
uni-button[loading][type=primary] {
  background-color: $wenet-color-brand;
}

.confirm-btn {
  display: flex;
  width: 100%;
  padding: 32rpx 174rpx;
  justify-content: center;
  align-items: center;
  line-height: 1;
  border-radius: 170rpx;
  background: $wenet-color-brand;
  font-size: 28rpx;
}

.login-phone__tip {
  font-size: 24rpx;
  margin-top: 20rpx;
  margin-bottom: 64rpx;
  text-align: center;
}

.back{
	padding: 0 50rpx;
	text-align: right;
	color: #999;
	font-size: 26rpx;
  margin-bottom: 32rpx;
}

.to-register {
  padding: 24rpx;
  text-align: center;
  color: $wenet-color-brand;
  font-size: 26rpx;
}

.authorized-btn {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 170rpx;
  background: $wenet-color-brand;
	border: 0rpx;
	line-height: 1;
	margin-top: 16rpx;
  margin-bottom: 64rpx;
  font-size: 28rpx;
}
.authorized-btn.ghost {
  background: #fff;
  border: none;
  color: $wenet-color-brand;
  margin-top: 32px;
}
button[type=primary] {
  background-color: $wenet-color-brand;
}

.update-wenet-btn-wrap {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
	margin-top: 64rpx;
}
.update-wenet-btn {
  color: $wenet-color-brand;
  font-size: 24rpx;
  margin: 0;
  line-height: 24rpx;
}

.input-style {
  width: calc(100% - 32rpx);
  border: none;
  background: transparent;
}
.input-style:focus {
  outline: none;
}

.eyes {
  width: 104rpx;
  height: 84rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: -32rpx;
}

.switch-mode-btn-wrap {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
</style>

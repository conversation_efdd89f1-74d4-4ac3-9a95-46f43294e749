<template>
	<view>
		<view class="con">
      <view class="login-form">
        <view :class="['item',errorTips==1 || MobileEmpty? 'error':'']">
          <!-- 手机号 -->
          <view class="account">
            <text class="input-item">+86</text>
            <input @focus="handleInpFocus" type="number" v-model="mobile" placeholder-class="inp-palcehoder" :placeholder="i18n.enterMobileNumber"
              maxlength="11"/>
          </view>
          <view class="error-text" v-if="errorTips==1"><text class="warning-icon">!</text>{{i18n.enterValidPhone}}</view>
          <view class="error-text" v-if="MobileEmpty"><text class="warning-icon">!</text>{{i18n.enterMobileNumber}}</view>
        </view>
        <view :class="['item',errorTips==2 || errorTips==6? 'error':'']">
          <!-- 验证码 -->
          <view class="account">
            <input @focus="handleInpFocus" type="text" class="int-yzm" v-model="validCode" placeholder-class="inp-palcehoder" :placeholder="i18n.enterCode" maxlength="6"
            />
            <text class="input-item" @click="getCode" v-if="show">{{i18n.getCode}}</text>
            <text class="input-item getcode" v-if="!show">{{count}} s</text>
          </view>
          <view class="error-text" v-if="errorTips==2"><text class="warning-icon">!</text>{{i18n.enterCode}}</view>
          <view class="error-text" v-if="errorTips==6"><text class="warning-icon">!</text>{{i18n.enterCodeFirst}}</view>
        </view>
      </view>
      <!-- 按钮 -->
      <NButton block type="primary" @click="handleLogin">{{ submitName }}</NButton>

		</view>
	</view>
</template>

<script>
	var http = require("../../utils/http");
	var util = require('../../utils/util.js');
  import NButton from '@/components/n-button'
	export default {
		data() {
			return {
				errorTips: 0, // 输入错误提示:  1手机号输入错误  2验证码输入错误  3账号输入错误  4密码输入错误  5验证密码输入错误
				mobile: '',
				confirmPwd: '',

				// 验证码相关
				validCode: '',
				show: true,
				count: '',
				timer: null,
				MobileEmpty: false, // 手机号是否为空
			};
		},
    components: {
      NButton
    },
		props: {
      privacyChecked: {
        type: Boolean,
        default: false
      },
			submitName: {
				type: String,
				default: '登录'
			},
      autoVerify: {
        type: Boolean,
        default: true
      },
      pageName: {
        type: String,
        default: ''
      },
    },

		computed: {
			i18n() {
				return this.$t('index')
			},
			appType() {
				return uni.getStorageSync('appType')
			}
		},
		
		methods: {
			/**
			 * 输入框聚焦
			 */
			handleInpFocus() {
				this.errorTips = 0
			},

      startCountDown() {
        const timeCount = 60;
        if (!this.timer) {
          this.count = timeCount
          this.show = false;
          this.timer = setInterval(() => {
            if (this.count > 0 && this.count <= timeCount) {
              this.count--;
            } else {
              clearInterval(this.timer);
              this.timer = null
              this.show = true
            }
          }, 1000)
        }
      },

			/**
			 * 获取验证码
			 */
			async getCode() {
				if (!this.mobile) {
					this.MobileEmpty = true
					this.errorTips = 0
					return
				}
				if (!util.checkPhoneNumber(this.mobile)) {
					this.MobileEmpty = false
					this.errorTips = 1
					return
				}
				this.MobileEmpty = false
				this.errorTips = 0
        this.$emit('beforeSendVerifyCode', this.mobile, () => {
          this.$umaTracker.ModularClick({
            Um_Key_ButtonName: '获取验证码',
            Um_Key_SourcePage: this.pageName,
            Um_Key_SourceLocation: this.pageName,
          })
          var params = {
            url: "/sms/sendLoginCode",
            method: "post",
            data: {
              mobile: this.mobile,
            },
            callBack: res => {
              this.startCountDown()
            }
          };
          http.request(params);
        })

			},


			handleLogin() {
				if (!this.privacyChecked) {
					uni.showToast({
						title: '请先同意隐私策略和服务条款',
						icon: 'none'
					})
					return
				}
        if (!this.validCode || !this.mobile) {
          uni.showToast({
            title: '请输入手机号和验证码',
            icon: 'none'
          })
          return
        }

        this.$emit('getSmsPhoneCode', {
            type: 'sms',
            mobile: this.mobile,
            verifyCode: this.validCode,
          });

        if (this.autoVerify) {
          const params = {
            url: '/verify/moblie',
            method: 'POST',
            data: {
              verifyCode: this.validCode,
              mobile: this.mobile
            },
            callBack: res => {
              this.$emit('verifyCodeSuccess', res)
            },
            errCallBack: errRes => {
              uni.showToast({
                title: errRes.data,
                icon: 'none'
              })
              this.$emit('verifyCodeError', errRes)
            }
          }
          http.request(params);
        }

			},
		},

}
</script>

<style lang="scss" scoped>
.account{
  display: flex;
  box-sizing: border-box;
  width: 100%;
  height: 84rpx;
  padding-left: 32rpx;
  align-items: center;
  border-radius: 16rpx;
	font-size: 30rpx;
  background: #F5F5F5;
  margin-bottom: 48rpx;
}
.account input{
  padding-left: 20rpx;
  width:70%;
}
.inp-palcehoder{
  font-size: 26rpx;
}
.account input.int-yzm {
  width: 380rpx;
}
.input-item {
  font-size: 28rpx;
	color: $wenet-color-brand;
}
.input-item.getcode {
  width: 120rpx;
  text-align: right;
}

.item {
	display: block;
	margin-bottom: 40rpx;
}
.error .error-text {
	display: block;
	width: 100%;
	font-size: 28rpx;
	color: $wenet-color-error;
  text-align: left;
  margin-top: 10rpx;
}
.error .error-text .warning-icon {
  display: inline-block;;
  color: #fff;
  width: 26rpx;
  height: 26rpx;
  line-height: 26rpx;
  background: $wenet-color-error;
  border-radius: 50%;
  text-align: center;
  margin-right: 12rpx;
  font-size: 22rpx;
}

</style>

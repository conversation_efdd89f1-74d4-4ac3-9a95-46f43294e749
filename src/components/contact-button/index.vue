<template>
  <view>
    <!-- #ifdef MP-WEIXIN -->
    <draggable-view :right="64" :bottom="48" v-if="type === 'flow'">
      <button 
        :class="btnClassName" 
        :style="{bottom: bottom}"
        v-if="isMobile"
        :open-type="useImageService ? undefined : 'contact'"
        :session-from="useImageService ? undefined : `7moor|${nickName}||${JSON.stringify(customField)}`"
      :send-message-title="useImageService ? undefined : title"
      :send-message-img="useImageService ? undefined : img"
      :send-message-path="useImageService ? undefined : $Route.fullPath"
      :show-message-card="useImageService ? undefined : Boolean(title)"
      @contact="useImageService ? undefined : handleContact"
      @tap="handleShowServiceImage"
    >
      <image v-if="type === 'flow'" :src="`${staticPicDomain}images/icon/icon11.png`"></image>
      <slot>
        <text>客服</text>
        </slot>
      </button>
    </draggable-view>
    <view v-else>
      <button 
        :class="btnClassName" 
        :style="{bottom: bottom}"
        v-if="isMobile"
        :open-type="useImageService ? undefined : 'contact'"
        :session-from="useImageService ? undefined : `7moor|${nickName}||${JSON.stringify(customField)}`"
        :send-message-title="useImageService ? undefined : title"
        :send-message-img="useImageService ? undefined : img"
        :send-message-path="useImageService ? undefined : $Route.fullPath"
        :show-message-card="useImageService ? undefined : Boolean(title)"
        @contact="useImageService ? undefined : handleContact"
        @tap="handleShowServiceImage"
    >
      <image v-if="type === 'flow'" :src="`${staticPicDomain}images/icon/icon11.png`"></image>
      <slot>
        <text>客服</text>
        </slot>
      </button>
    </view>
    <uni-popup ref="servicePopup" type="center" v-if="useImageService" @maskClick="handleClose" :safe-area="false">
      <view class="popup-content" @tap.stop>
        <image 
          class="service-image" 
          :src="serviceImageUrl" 
          mode="aspectFit"
          @tap="handleClose"
          @longpress="downloadImg"
        ></image>
        <view class="close-button" @tap="handleClose">
          <text class="close-icon">×</text>
        </view>
      </view>
    </uni-popup>
    <!-- #endif -->
  
  </view>
</template>

<script>
import config from "@/utils/config.js";
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import DraggableView from '@/components/draggable-view'


export default {
  name: 'contact-button',
  components: {
    uniPopup,
    DraggableView,
  },
  data () {
    return {
      nickName: '',
      customField: {},
      bottom: '',
      prodPath: [
        '/packageActivities/pages/snapUpDetail/snapUpDetail',
        '/pages/prod/prod',
      ],
      isMobile: true, // 暂不设置哪些设备不显示，需要测试验证
      serviceImageUrl: 'https://m.wenet.com.cn/resources/shop-static/images/other/wenet-officalaccount-ui2.png', // 替换为实际的图片URL
      useImageService: true, // 控制是否使用图片客服
    }
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    img: {
      type: String,
      default: '',
    },
    type: {
      type: String, // 'flow'悬浮 or 'normal'按钮
      default: 'flow',
    }
  },
  watch: {
    // 实时监听props变化，否则props不更新
    title: function(newVal,oldVal){
      this.title = newVal;  
    },
    img: function(newVal,oldVal){
      this.img = newVal;
    },
  },
  mounted () {
    this.checkDevice()
    this.initData()
  },
  methods: {
    handleContact(e) {
      const path = e.detail.path;
      const query = e.detail.query;
      const pathWithQuery = path + '?' + Object.keys(query).map(key => `${key}=${query[key]}`).join('&');
      uni.navigateTo({
        url: pathWithQuery,
      })
    },
    handleShowServiceImage() {
      const userInfo = this.$store.state.userInfo
      const school = userInfo?.ou || ''
      // 使用 ics 客服机器人的学校
      const icsSchoolList = ['10000', '111111', '14222', '8144', '10704', '11600', '12712']
      if (icsSchoolList.includes(school)) {
            uni.navigateTo({
            url: '/pages/ics/ics',
          })
        } else {
          this.$refs.servicePopup.open()
        }
    },
    handleImageClick() {
      uni.previewImage({
        urls: [this.serviceImageUrl]
      })
    },
    handleClose() {
      this.$refs.servicePopup.close()
    },
    initData() {
      const userInfo = wx.getStorageSync('userInfo')
      const originalWenetAccount = wx.getStorageSync('originalWenetAccount')

      const studentId = originalWenetAccount?.users?.find((userItem) => userItem.type === 'STUDENT_ID_ASSOCIATED_USER')?.username || '';
      const telephone = originalWenetAccount?.users?.find((userItem) => userItem.type === 'TELEPHONE_ASSOCIATED_USER')?.username || '';
      let initials = '';
      let carLicense = '';
      let roomNumber = '';
      let uid = '';
      if (originalWenetAccount?.person) {
        initials = originalWenetAccount.person?.initials || '';
        carLicense = originalWenetAccount.person?.carLicense || '';
        roomNumber = originalWenetAccount.person?.roomNumber || '';
        uid = originalWenetAccount.person?.uid || '';
      }
      const address = initials + carLicense + roomNumber;
      const realName = originalWenetAccount?.identification?.realName;
      const localityName = originalWenetAccount?.org?.localityName;

      const numb = studentId || telephone || '';
      let theNickName = '';
      if (realName) {
        theNickName = realName;
        const ou = originalWenetAccount?.org?.ou;
        if (numb && (ou === '10080' || ou === '10000')) {
          theNickName += `(${numb})`;
        } else {
          theNickName += '同学';
        }
      } else if (!!numb) {
        theNickName = numb;
      } else {
        theNickName = userInfo?.nickName
      }

      this.nickName = theNickName
      this.customField = {
        用户上网账号: studentId || telephone || '',
        所在学校: localityName || userInfo?.ouName,
        真实姓名: realName,
        学号: studentId,
        手机号: telephone || userInfo?.userMobile,
        地址信息: address,
        user_labels: {
          school: localityName || userInfo?.ouName,
          name: realName,
        },
        商品链接: this.prodPath.includes(this.$Route.path) ? `${config.domainAddress}${this.$Route.fullPath}` : ''
      }
      this.bottom = this.prodPath.includes(this.$Route.path) ? '180rpx' : '24rpx'
    },
    checkDevice() {
      const platform = uni.getSystemInfoSync()?.osName;
      const mobileDeviceList = ['android', 'ios', 'windows']

      if (mobileDeviceList.includes(platform)) {
        this.isMobile = true;
      } else {
        this.isMobile = false;
      }
    },
    downloadImg() {
      // #ifdef MP-WEIXIN
      uni.showModal({
        title: '提示',
        content: '长按可保存图片到相册',
        confirmText: '确定保存',
        success: (res) => {
          if (res.confirm) {
            wx.getImageInfo({
              src: this.serviceImageUrl,
              success: (res) => {
                let path = res.path;
                wx.saveImageToPhotosAlbum({
                  filePath: path,
                  success: (res) => {
                    console.log('保存图片成功res:', res);
                    uni.showToast({
                      title: '保存成功',
                      icon: 'success',
                      duration: 2000
                    })
                  },
                  fail: (err) => {
                    console.log('保存图片失败err:', err);
                    if (err.errMsg.includes('auth deny')) {
                      uni.showModal({
                        title: '提示',
                        content: '需要您授权保存相册',
                        showCancel: false,
                        success: () => {
                          uni.openSetting()
                        }
                      })
                    } else {
                      // uni.showToast({
                      //   title: '保存失败',
                      //   icon: 'error',
                      //   duration: 2000
                      // })
                    }
                  }
                })
              },
              fail: (err) => {
                console.log('获取图片信息失败err:', err);
                uni.showToast({
                  title: '图片加载失败',
                  icon: 'error',
                  duration: 2000
                })
              }
            })
          }
        }
      })
      // #endif
    }
  },
  computed: {
    btnClassName() {
      return (this.type === 'flow') ? 'contact-button-flow' : 'contact-button'
    }
  },
}
</script>

<style scoped>
.contact-button-flow {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 28rpx;
  padding: 0;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  background-color: #fff;
}
.contact-button {
  border: none;
  background-color: transparent;
  font-size: 28rpx;
  padding: 0;
}
.contact-button::after, uni-button:after, button::after {
  border-width: 0px !important;
}

image {
  width: 52rpx;
  height: 52rpx;
}
text {
  font-size: 20rpx;
  line-height: 1;
  color: rgba(0, 0, 0, 0.85);
}

.popup-content {
  padding: 40rpx 20rpx;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.service-image {
  width: 600rpx;
  height: 1130rpx;
  max-width: 90vw;
  max-height: 90vh;
  display: block;
}

.close-button {
  position: absolute;
  top: -20rpx;
  right: 10rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.close-icon {
  color: #fff;
  font-size: 40rpx;
  line-height: 1;
}
</style>
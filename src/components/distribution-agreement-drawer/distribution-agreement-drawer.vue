<template>
  <Drawer :z-index="z_index" :show="show" @update:show="handleUpdateShow" :mask-closeable="false">
    <view class="agreement-container">
      <view class="agreement-title">成为分销员</view>
      <view class="agreement-desc">成为分销员后，您可以赚取商品分销佣金</view>
      <view class="agreement-content">
        <view class="agreement-checkbox-container">
          <label class="agreement-checkbox">
            <checkbox :checked="checked" color="#e4393c" @tap.stop="handleAgreementChange" />
          </label>
          <text class="agreement-text" @tap.stop="handleAgreementChange">我已阅读并同意</text>
          <text class="agreement-link" @tap.stop="navigateToAgreement">《分销协议》</text>
        </view>
      </view>
      <view class="agreement-buttons">
        <view class="agreement-cancel" @tap="handleCancel">取消</view>
        <view :class="['agreement-confirm', !checked ? 'agreement-confirm-disabled' : '']" @tap="handleConfirm">确认</view>
      </view>
    </view>
  </Drawer>
</template>

<script>
import Drawer from '@/components/popup/drawer.vue'
import http from '@/utils/http.js'


export default {
  components: {
    Drawer
  },
  props: {
    // 是否显示抽屉
    show: {
      type: Boolean,
      default: false
    },
    // z-index层级
    z_index: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      checked: false
    }
  },
  methods: {
    // 处理协议选中状态变化
    handleAgreementChange() {
      this.checked = !this.checked
    },
    // 跳转到协议页面
    navigateToAgreement() {
      uni.navigateTo({
        url: `/packageUser/pages/termsPage/termsPage?sts=profitPolicy`,
      })
    },
    // 取消按钮
    handleCancel() {
      this.$emit('cancel')
    },
    // 确认按钮
    async handleConfirm() {
      if (!this.checked) return
      this.$emit('agreed')
    },
    handleUpdateShow(show) {
      if (!show) {
        this.$emit('cancel')
      }
    }
  }
}
</script>

<style scoped>
/* 分销协议弹窗样式 */
.agreement-container {
  padding: 40rpx;
}

.agreement-checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.agreement-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 24rpx;
}

.agreement-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.agreement-content {
  margin-bottom: 40rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.agreement-text {
  margin-left: 8rpx;
  color: #666;
}

.agreement-link {
  color: #e4393c;
}

.agreement-buttons {
  display: flex;
  justify-content: space-between;
}

.agreement-cancel, .agreement-confirm {
  width: 45%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.agreement-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.agreement-confirm {
  background-color: #e4393c;
  color: #fff;
}

.agreement-confirm-disabled {
  background-color: #fcc;
  color: #fff;
}
</style>

<template>
  <view class="menu-item-wrap">
    <view>{{ text }}</view>
    <view>
      <n-i icon="right" />
    </view>
  </view>
</template>

<script>
import NI from '@/components/n-i'
export default {
  name: 'user-center-menu-item',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
    }
  },
  props: {
    text: {
      type: String,
      default: ''
    },
    path: {
      type: String,
      default: ''
    },
  },
  methods: {

  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
.menu-item-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 42rpx;
}

</style>
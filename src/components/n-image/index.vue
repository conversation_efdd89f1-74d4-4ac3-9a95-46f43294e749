<template>
  <image 
    :src="currentSrc" 
    :mode="mode" 
    @error="handleError"
    :id="uniqueId"
    class="n-image"
  />
</template>

<script>
export default {
  name: 'n-image',
  options: {
    multipleSlots: true,
  },
  props: {
    src: {
      type: [String, Object],
      default: '',
    },
    lazyLoad: {
      type: Boolean,
      default: true,
    },
    mode: {
      type: String,
      default: 'aspectFill',
    },
    className: {
      type: String,
      default: '',
    },
    fallbackSrc: {
      type: String,
      default: 'https://m.wenet.com.cn/resources/shop-static/images/icon/lazyloading.svg', // 默认备用图片
    },
  },
  data() {
    return {
      lazyloadingSrc: 'https://m.wenet.com.cn/resources/shop-static/images/icon/lazyloading.svg',
      isError: false,
      isLoaded: false,
      observer: null,
      uniqueId: `n-image-${Math.random().toString(36).substr(2, 9)}`, // 唯一类名
    };
  },
  computed: {
    currentSrc() {
      if (this.isError) {
        return this.fallbackSrc;
      }
      if (this.lazyLoad && !this.isLoaded) {
        return this.lazyloadingSrc;
      }
      return this.src;
    },
  },
  methods: {
    handleError() {
      this.isError = true;
      this.$emit('error');
    },
    observeImage() {
      // #ifdef MP-WEIXIN
      console.log('observeImage')
      if (this.observer) {
        this.observer.disconnect();
        this.observer = null;
      }
      if (!this.lazyLoad) {
        this.isLoaded = true;
        return;
      }
      const observer = uni.createIntersectionObserver(this);
      observer.relativeToViewport().observe(`#${this.uniqueId}`, (res) => {
        if (res.intersectionRatio > 0) {
          this.isLoaded = true;
          observer.disconnect();
          this.observer = null;
        }
      });
      this.observer = observer;
      // #endif
      // #ifndef MP-WEIXIN
      // TODO h5 使用createIntersectionObserver会报错：找不到元素，待优化
      this.isLoaded = true;
      // #endif
    },
  },
  mounted() {
    console.log('mounted')
    this.observeImage();
  },
  beforeDestroy() {
    console.log('beforeDestroy')
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  },
  watch: {
    src: {
      handler() {
        this.isError = false;
        this.isLoaded = !this.lazyLoad;
      },
      immediate: true,
    },
  },
};
</script>

<style>
.n-image {
  width: 100%;
  height: 100%;
}
</style>
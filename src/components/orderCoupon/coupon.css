.coupon-item{
  margin: 15px 0;
  position: relative;
  box-shadow: 1px 1px 3px rgba(0,0,0,0.15);
  height: 95px;
  background: #fff;
}
.coupon-item .left{
  float: left;
  color: #fff;
  text-align: center;
  border-left: 1px dashed #fff;
  padding: 20px 0;
  background: -webkit-gradient(linear,left top,right top,from(#F45C43),to(#e43130));
  background: -o-linear-gradient(to right,#F45C43,#e43130);
  background: linear-gradient(to right,#F45C43,#e43130);
  background: -webkit-linear-gradient(to right,#F45C43,#e43130);
  width: 260rpx;
  height: 55px;
}
.coupon-item .left .num{
  font-weight:600;
  font-size:30rpx;
  height:70rpx;
  line-height:70rpx;
  font-family:arial;
}
.coupon-item .left .num .coupon-price{
  font-size: 70rpx;
  line-height: 1em;
  display: inline-block;
  font-family: arial;
}
.coupon-item .left .coupon-condition{
  font-size: 28rpx;
  line-height: 28rpx;
  padding: 0 2px;
  display: block;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.coupon-item .right{
  margin-left: 270rpx;
  padding: 20rpx 10rpx;
  height: 95px;
  box-sizing: border-box;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.coupon-item .right .c-des{
  height: 30px;
  font-size: 26rpx;
  line-height: 40rpx;
  font-weight: 600;
}
.coupon-item .right .c-des .c-type{
  font-size: 24rpx;
  background: #fdf0f0;
  color: #e43130;
  border-radius: 8px;
  padding:3px 10px;
  margin-right: 6rpx;
}
.coupon-item .right .c-date{
  font-size: 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.coupon-item .right .c-date .c-data-info{
  font-family: arial;    
	word-break: break-word;
	max-width: 70%;
}
.coupon-item .right .c-date .c-btn{
	display: inline-block;
  color: #fff;
  font-size: 24rpx;
  font-family: arial;
  border-radius: 14px;
  padding:3px 7px;
	background-color: #E43130;
	border: 1px solid #e43130;
}

.coupon-item .right .c-date .c-btn.get-btn{
  background: #fff;
  border: 1px solid #e43130;
  color:#e43130;
}

.coupon-item .right .c-date .no-use-btn{
	color: #fff;
	font-size: 24rpx;
	font-family: arial;
	border-radius: 14px;
	padding:3px 7px;
	background: #bbbbbb;
	border: 1px solid #bbbbbb;
}

.coupon-item.gray .left{
  background: #bbb;
}

.coupon-item.gray .right .c-des .c-type{
  background: #bbb;
    color: #fff;
}

.coupon-item.gray .right .c-date .c-btn{
  display: none;
}

.coupon-item .tag-img{
  position: absolute;
  top:0;
  right:0;
  width:120rpx;
  height:120rpx;
}

.coupon-item .sel-btn{
  position:absolute;
  right:10px;
  top:35px;
}

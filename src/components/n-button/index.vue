<template>
  <button class="n-button" :class="[type, { block, disabled, loading }]" @click="handleTap" :open-type="openType" @getphonenumber="handleGetPhoneNumber">
    <view class="n-button-loader" v-if="loading">
      <wenet-spinner />
    </view>
    <slot></slot>
  </button>
</template>

<script>
import WenetSpinner from '@/components/n-loading/spinner.vue'
export default {
  name: 'NButton',
  components: {
    WenetSpinner
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  props: {
    block: {
      type: Boolean,
      default: false
    },
    type: {
      type: String, // primary | secondary | ghost
      default: 'primary',
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    openType: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleTap(e) {
      if (this.disabled || this.loading) return;
      this.$emit('click', e);
    },
    handleGetPhoneNumber(e) {
      this.$emit('getphonenumber', e);
    }
  }
}
</script>

<style lang="scss" scoped>
.n-button {
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  width: fit-content;
  padding: 8rpx 32rpx;
  transition: all 0.1s ease-in-out;
  box-sizing: border-box;
  line-height: normal;
  margin-left: unset;
  margin-right: unset;
}

.n-button::after {
  content: none;
}

.n-button:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.n-button.disabled:active {
  transform: none;
}

.n-button.primary {
  background-image: linear-gradient(0deg, $wenet-color-gradient-end 0%, $wenet-color-brand 100%);
  box-shadow: 0 0 4px 0 rgba(235,235,235,0.50);
  color: #FFFFFF;
}

.n-button.secondary {
  background-color: wenet-color-brand-opacity(0.1);
  color: $wenet-color-brand;
}

.n-button.block {
  width: 100%;
  background-image: linear-gradient(270deg, $wenet-color-gradient-end 0%, $wenet-color-brand 100%);
  box-shadow: 0 0 4px 0 rgba(235,235,235,0.50);
  border-radius: 16rpx;
  color: #FFFFFF;
  height: 84rpx;
}

.n-button.ghost {
  background-image: none;
  background-color: transparent;
  color: $wenet-color-brand;
  border: 1px solid wenet-color-brand-opacity(0.8);
  border-radius: 8rpx;
  font-weight: 400;
}

.n-button.disabled, .n-button.loading {
  background-image: none;
  background-color: $wenet-color-disabled;
  color: #ffffff;
  border: none;
}

.n-button-loader {
  margin-right: 8rpx;
}
</style>
<template>
<view>
  <view 
    v-for="(item) in positionLabels" 
    :key="item.labelId" 
    :class="'corner-mark-position ' + item.position"
  > 
    <view v-show="item.type == '2'">
      <text
        v-if="item.style === 'background-only'" 
        class="corner-mark-style"
        :style="{color: '#fff', backgroundColor: item.bgColor}"
      >
        {{ item.labelName }}
      </text>
      <text 
        v-if="item.style === 'font-only'"  
        class="corner-mark-style"
        :style="{color: item.bgColor, borderColor: item.bgColor, backgroundColor: '#fff'}"
      >
        {{ item.labelName }}
      </text>
      <text 
        v-if="item.style === 'both-colors'" 
        class="corner-mark-style"
        :style="{color: item.textColor, backgroundColor: item.bgColor}"
      >
        {{ item.labelName }}
      </text>
    </view>
    <view v-show="item.type == '3'">
      <image mode="heightFix" :src="`${resourcesUrl}${item.pic}`"></image>
    </view>
  </view>
</view>
</template>

<script>
import config from '@/utils/config.js'
export default {
  data(){
    return {
      resourcesUrl: config.picDomain,
    }
  },
  props: {
    labels: {
      type: [Array, null, undefined],
      default: () => [],
    }
  },
  computed: {
    positionLabels() {
      const topLeftLabel = this.labels?.findLast(item => item.position == 'top-left')
      const topRightLabel = this.labels?.findLast(item => item.position == 'top-right')
      const bottomLeftLabel = this.labels?.findLast(item => item.position == 'bottom-left')
      const bottomRightLabel = this.labels?.findLast(item => item.position == 'bottom-right')
      return [
        topLeftLabel,
        topRightLabel,
        bottomLeftLabel,
        bottomRightLabel,
      ].filter(item => !!item && item.shown == true)
    }
  },
}
</script>
<style scoped>
@import url('./index.css');

</style>
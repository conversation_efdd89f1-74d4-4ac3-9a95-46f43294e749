<template>
<view class="coupon-item" >
  <view class="top">
    <view 
      class="coupon-left"
      :style="{ backgroundImage:  `url(${staticPicDomain}/images/icon/subtract-coupon-left.svg)`}"
      @tap="changeShowDetail(coupon)"
    >
      <view class="coupon-info">
        <view class="coupon-name">{{ coupon.couponName }}</view>
        <view class="coupon-desc" v-if="coupon.shopId == 0">{{coupon.suitableProdType == 0 ? i18n.universalAll : i18n.universalOnly}}</view>
        <view class="coupon-desc" v-if="!!coupon.shopName">{{`仅限${coupon.shopName}可用`}}</view>
                
        <view
          class="coupon-time"
          v-if="coupon.validTimeType == 2"
        >
          有效期：{{i18n.getCoupons}}{{coupon.validDays}}{{i18n.invalid}}
          <text class="top-arrow" v-if="coupon.isShowDetail"></text>
          <text class="bottom-arrow" v-else></text>
        </view>
        <view 
          class="coupon-time" 
          v-if="!!coupon.startTime && coupon.validTimeType != 2"
        >
          有效期：{{ formatDate(coupon.startTime) }} - {{ formatDate(coupon.endTime) }}
          <text class="top-arrow" v-if="coupon.isShowDetail"></text>
          <text class="bottom-arrow" v-else></text>
        </view>
      </view>
    </view>
    <view 
      class="coupon-right"
      :style="{ backgroundImage: isBtnGray ? `url(${staticPicDomain}/images/icon/subtract-coupon-right-gray.svg)` :`url(${staticPicDomain}/images/icon/subtract-coupon-right.svg)` }"
    >
      <view :class="['coupon-price' , isBtnGray ? 'gray-price' : '']" v-if="coupon.couponType == 1">
        <text class="coupon-unit">¥</text>{{coupon.reduceAmount}}
      </view>
      <view :class="['coupon-price' , isBtnGray ? 'gray-price' : '']" v-if="coupon.couponType == 2">
        {{coupon.couponDiscount}}<text class="coupon-unit">折</text>
      </view>

      <view class="gray-price coupon-tips">{{i18n.available}}{{coupon.cashCondition}}</view>
      <view v-show="isShowBtn">
        <view class="coupon-btn" v-if="!coupon.canGoUse && coupon.stocks" :data-couponindex="index" @tap="receiveCoupon">{{i18n.getItByCoupon}}</view>
        <view class="coupon-btn use-btn" v-if="coupon.canGoUse" :data-couponid="coupon.couponId" @tap="useCoupon">{{i18n.useItNow}}</view>
        <view class="coupon-btn gray-btn" v-if="isBtnGray"  @tap="receiveCoupon">{{i18n.nocoupons1}}</view>
      </view>
    </view>
  </view>
  <view class="bottom" v-if="coupon.isShowDetail">
    <view v-if="coupon.shopId == 0">
      使用范围：{{coupon.suitableProdType == 0 ? i18n.universalAll : i18n.universalOnly}}
    </view>
    <view v-else>
      使用范围：<text v-if="!!coupon.shopName">{{`仅限${coupon.shopName}可用`}}，</text>{{coupon.suitableProdType == 0 ? '全店商品通用(特殊商品除外)' : '部分商品可用'}}
    </view>
    <view :class="[coupon.validTimeType == 2 ? '' : 'hideHeight']">
      使用时间：{{i18n.getCoupons}}{{coupon.validDays}}{{i18n.invalid}}
    </view>
    <view :class="[(!!coupon.startTime && coupon.validTimeType != 2) ? '' : 'hideHeight']">使用时间：{{ coupon.startTime }} - {{ coupon.endTime }}</view>
  </view>
</view>
</template>

<script>
import config from '@/utils/config.js'
import util from '@/utils/util.js'
import http from '@/utils/http.js'
import { requestCouponArrivalNotification } from '@/utils/wx-subscribe-message'

export default {
  data() {
    return {
      resourcesUrl: config.picDomain,
      selectedCouponId: 0,
    }
  },
  props: {
    coupon: {
      type: Object,
      default: () => ({}),
    },
    isShowBtn: {
      type: Boolean,
      default: true,
    }
  },
  computed:{
  	i18n() {
  		return this.$t('index')
  	},
    isBtnGray() {
      return (!this.coupon.canGoUse && !this.coupon.stocks) || !this.isShowBtn
    }
  },
  methods: {
    formatDate(dateStr) {
      if (!dateStr || dateStr.toLowerCase() === 'null') {
          return '';
      }
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const formattedDate = `${year}.${month}.${day}`;
      return formattedDate;
    },    
    /**
     * 立即领取
     */
    receiveCoupon(e) {
      this.setData({
        selectedCouponId: this.coupon.couponId
      });
      util.checkAuthInfo(() => {
        var ths = this;

        if (ths.selectedCouponId) {
          uni.showLoading();
          http.request({
            url: "/p/myCoupon/receive",
            method: "POST",
            data: ths.selectedCouponId,
            callBack: data => {
              uni.hideLoading()
              uni.showToast({
                title: ths.i18n.successfullyReceived,
                icon: 'success',
                duration: 2000
              });
              this.$emit('setCouponCanGoUseFlag', this.coupon)
              this.$emit("refreshDeductPrice")
              requestCouponArrivalNotification({})
            }
          });
        }
      });
    },
    /**
     * 立即使用
     */
    useCoupon(e) {
      let url = '/pages/prod-classify/prod-classify?sts=4';
      let couponId = e.currentTarget.dataset.couponid;
      var title = this.i18n.couponEventGoods;
      var prodList = e.currentTarget.dataset.prodlist;

      if (prodList && prodList.length == 1) {
        this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodList[0].prodId }})
      } else {
        if (couponId) {
          url += "&tagid=" + couponId + "&title=" + title;
        }

        uni.navigateTo({
          url: url
        });
      }
    },
    changeShowDetail() {
      this.$emit('changeShowDetail', this.coupon)
    }
  }
}
</script>
<style scoped>
.coupon-item {
  position: relative;
  flex-shrink: 0;
  width: auto;
  filter: drop-shadow(0px 0px 20px rgba(0, 0, 0, 0.08));
}
.top {
  width: 100%;
  height: 190rpx;
  display: flex;
}
.coupon-left {
  flex: 1;
  height: 190rpx;
  background-repeat: no-repeat;
  background-position: right center;
  border-radius: 16rpx 0rpx 0rpx 16rpx;
  background-size: auto 100%;
  box-sizing: border-box;
  display: flex;
  padding-right: 24rpx;
}

.coupon-right {
  width: 176rpx;
  height: 190rpx;
  flex-shrink: 0;
  padding: 26rpx 0rpx 16rpx;
  box-sizing: border-box;
  text-align: center;
  position: relative;
  background-size: 176rpx 190rpx;
}
.coupon-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 16rpx 0rpx 0rpx 16rpx;
  background-color: #fff;
  padding: 32rpx ;
  position: relative;
}
.coupon-name {
  color: #000;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
}
.coupon-desc, .coupon-time {
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.coupon-time {
  color: #959595;
  display: flex;
  align-items: center;
  justify-self: flex-end;
  width: 100%;
}
.coupon-price, .coupon-unit  {
  color: var(--primary-color);
  font-size: 52rpx;
  font-weight: 600;
  line-height: 52rpx;
}
.coupon-unit {
  text-align: center;
  font-size: 24rpx;
}
.coupon-tips {
  color: #999;
  font-size: 20rpx;
  font-weight: 400;
  line-height: 28rpx;
}
.coupon-btn {
  display: inline-block;
  padding: 8rpx 16rpx;
  gap: 16rpx;
  border-radius: 8rpx;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
  font-size: 20rpx;
  font-weight: 400;
  line-height: 28rpx;
  margin-top: 9rpx;
  text-align: center;
  background-color: transparent;
}
.gray-btn {
  color: #999;
  border-color: #999;
}
.gray-price, .gray-price text {
  color: #999;
}
.bottom {
  color: #959595;
  font-size: 20rpx;
  border-top: solid 1px #dfdede;
  padding: 20rpx ;
  background-color: #fff;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}

.top-arrow, .bottom-arrow {
  display: inline-block;
  width: 10rpx;
  height: 10rpx;
  transform: rotate(45deg);
  margin-left: 10rpx;
}
.top-arrow {
  border-top: 2rpx solid #AAAAAA;
  border-left: 2rpx solid #AAAAAA;
}

.bottom-arrow {
  border-bottom: 2rpx solid #AAAAAA;
  border-right: 2rpx solid #AAAAAA;
}
.hideHeight {
  height: 0rpx;  
  overflow: hidden;
}
</style>
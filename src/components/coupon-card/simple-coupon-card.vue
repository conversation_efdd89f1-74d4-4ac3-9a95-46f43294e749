<template>
<view class="coupon-item">
  <view class="coupon-info">
    <n-i icon="ChargeUp" width="64rpx" height="66rpx" ext="png"/>
    <view class="coupon-detail">
      <text class="coupon-name">{{ coupon.couponName }}</text>
      <text class="coupon-time" v-show="!!formatDate(coupon.startTime)">{{ formatDate(coupon.startTime) }} - {{ formatDate(coupon.endTime) }}</text>
      <view class="coupon-getCoupon" v-if="!coupon.canGoUse && coupon.stocks" :data-couponid="coupon.couponId" @tap="receiveCoupon">立即领取</view>
      <view class="coupon-getCoupon use-btn" v-if="coupon.canGoUse" :data-couponid="coupon.couponId" @tap="goCoupon(coupon.couponId)">立即使用</view>
      <view class="coupon-getCoupon gray-btn" v-if="!coupon.canGoUse && !coupon.stocks">已抢光</view>	 
    </view>
  </view>
  <n-i icon="Subtract" width="90rpx" height="206rpx" />
</view>
</template>
<script>
import http from '@/utils/http.js'
import util from '@/utils/util'
import NI from '@/components/n-i'
import { requestCouponArrivalNotification } from '@/utils/wx-subscribe-message'

export default {
  data() {
    return {
      selectedCouponId: 0,
      stsType: 4,
    }
  },
  props: {
    coupon: {
      type: Object,
      default: () => ({}),
    }
  },
  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },
  components: {
    NI,
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  mounted() {
    this.initCouponCanGoUseFlag()
  },
  methods: {
    // 初始化优惠券去可以使用的标记
    initCouponCanGoUseFlag() {
      this.coupon.canGoUse = this.coupon.curUserReceiveCount >= this.coupon.limitNum;
    },
    // 领取优惠券
    receiveCoupon(e){
      this.setData({
        selectedCouponId: e.currentTarget.dataset.couponid
      })
      util.checkAuthInfo(() => {
        var ths = this;
        if (ths.selectedCouponId) {
          uni.showLoading();
          http.request({
            url: "/p/myCoupon/receive",
            method: "POST",
            data: ths.selectedCouponId,
            callBack: data => {
              uni.hideLoading()
							uni.showToast({
                title: ths.i18n.successfullyReceived,
                icon: 'success',
                duration: 2000
              });
              this.$emit('setCouponCanGoUseFlag')
              requestCouponArrivalNotification({})
            }
          });
        }
      });
    },
    // 跳转至优惠券详情
    goCoupon(couponId) {
      util.checkAuthInfo(() => {
        let url = '/pages/prod-classify/prod-classify?sts=' + this.stsType;
        const id = couponId;
        const title = this.i18n.couponEventGoods;

        if (id) {
          url += "&tagid=" + id + "&title=" + title;
        }
        uni.navigateTo({
          url: url
        });
      });
    },
    formatDate(dateStr) {
      // 如果日期为null，返回空字符串或其他默认值
      if (!dateStr || dateStr.toLowerCase() === 'null') {
          return '';
      }
      // 将日期字符串转换为日期对象
      const date = new Date(dateStr);
      // 获取日期的年、月和日
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      // 拼接年月日，用点号分隔
      const formattedDate = `${year}.${month}.${day}`;
      return formattedDate;
    },
  }
}

</script>
<style scoped>

.coupon-item {
  position: relative;
  display: flex;
  flex-shrink: 0;
  width: auto;
  height: 206rpx;
  background: transparent;
  box-shadow: 0rpx 0rpx 40rpx 0rpx rgba(0, 0, 0, 0.05);
  border-radius: 16rpx 0px 0px 16rpx;
  
}
.coupon-info {
  display: flex;
  flex: 1;
  padding: 32rpx 0px 32rpx 32rpx;
  justify-content: flex-end;
  align-items: flex-start;
  border-radius: 16rpx 0px 0px 16rpx;
  height: 100%;
  box-sizing: border-box;
  background: linear-gradient(180deg, #FFF3E9 0%, rgba(255, 255, 255, 0.00) 100%);
}
.coupon-detail {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4rpx;
  flex: 1;
  height: 142rpx;
  position: relative;
  margin-left: 28rpx
}
.coupon-name {
  color: #242422;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
}
.coupon-time {
  color: #959595;
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.coupon-getCoupon {
  color: #FF5E18;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  position: absolute;
  bottom: 0rpx;
}
.gray-btn {
  color: #959595;
}

</style>
<template>
  <view class="modal" v-show="isShow">
    <view class="modal-cancel" :style="maskStyle"></view>
    <view class="modal-body" :style="contentStyle">
		  <image
        class="modal-image" 
        mode='widthFix' 
        @error="handleError"  
        @click="navigateToDetail" 
        v-if="!!popupAdInfo.popPic"
        :src="resourcesUrl + popupAdInfo.popPic"
      ></image>
		  <i class="close-icon" @click="hideModal">X</i>
    </view>
  </view>
</template>
<script>
import config from '../../utils/config.js'
export default {
  data() {
    return {
      popupAdInfo: {},
      resourcesUrl: config.picDomain,
    }
  },
  props: ["isShow", "contentStyle", "maskStyle",],
  methods: {
    hideModal: function () {
      this.$umaTracker.TriggerShowTabbar({
        reason: '关闭弹窗广告'
      })
      uni.showTabBar()
      this.$emit('hidePopupAd');
    },
    init: function (popupAdInfo) {
      this.$umaTracker.TriggerHideTabbar({
        reason: '出现弹窗广告'
      })
      uni.hideTabBar() 
      this.popupAdInfo = popupAdInfo
    },
    navigateToDetail() {
      this.hideModal()
      uni.navigateTo({
        url: this.popupAdInfo.targetUrl,
      })
    },
    handleError() {
      this.hideModal()
    }
  }
}
</script>
<style scoped>
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
}

.modal-cancel {
  position: absolute;
  z-index: 2000;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-body {
  position: absolute;
  z-index: 3000;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70%;
  max-height: 70%;
  overflow: auto;
}
@media (min-width: 768px) {
  .modal-body {
    width: 50%;
  }
}


.modal-image{
  width: 100%;
}

.close-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: #fff;
  font-size: 12px;
  border: 1px solid;
  border-radius: 50%;
  margin-top: 16rpx;
  font-weight: 600;
  font-style: normal;
}

</style>
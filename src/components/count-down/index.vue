<template>
  <view class="counter">
    <view class="counter__item" v-if="times.day !== '00'">
      <view class="counter__item__num">{{ times.day }}</view>
    </view>
    <view class="counter__spliter" v-if="times.day !== '00'">天</view>
    <view class="counter__item">
      <view class="counter__item__num">{{ times.hou }}</view>
    </view>
    <view class="counter__spliter">:</view>
    <view class="counter__item">
      <view class="counter__item__num">{{ times.min }}</view>
    </view>
    <view class="counter__spliter">:</view>
    <view class="counter__item">
      <view class="counter__item__num">{{ times.sec }}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'count-down',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
    }
  },
  props: {
    times: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {

  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
.counter {
  flex: 1;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.counter__item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rpx 8rpx;
  border-radius: 2px;
  background: #313131;
}

.counter__item__num {
  color: #FFF;
  font-size: 24rpx;
  font-weight: 400;
}
.counter__spliter {
  margin: 0 8rpx;
  font-size: 28rpx;
}
</style>
<template>
  <view>
    <!-- 店铺信息 -->
    <view class="shop">
      <view class="shopInfo" :data-shopid="shopId">
        <view class="sl">
          <view class="shopLogo">
            <image :src="shopInfo.shopLogo" alt="shopLogo"></image>
          </view>
          <view class="shopTitle">
            <view class="shopname">{{shopInfo.shopName}}</view>
            <view :class="[isEn ? 'shopIntro-en' : 'shopIntro']">
              <view class="self-operate" v-if="shopId==1">{{i18n.selfShop}}</view>
              <view class="fol" v-if="shopInfo.shopStatus == 1">{{shopInfo.fansCount < 10000 ? shopInfo.fansCount + i18n.haveSpacePeople:shopInfo.fansCount/10000 + i18n.tenThousandPeople}}{{i18n.collection}}</view>
            </view>
          </view>
        </view>
        <view :class="['follow-btn',isFollowed?'followed':'']" @tap="collectShop">
          <view class="fol">
            <image class="col-icon" :src="isFollowed ? `${staticPicDomain}images/icon/collected.png` : `${staticPicDomain}images/icon/not-collected.png`"></image>
            <view>{{isFollowed?i18n.collected:i18n.collection}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
var http = require("@/utils/http.js");
var util = require("@/utils/util.js");
var big = require("@/utils/big.min.js");
export default {
  data() {
    return {
      isFollowed: false,
      shopInfo: {}, // 店铺信息
      fansCountStr: '',
      isEn: uni.getStorageSync("lang") == 'en' ? true : false,
    }
  },
  props: {
    shopId: Number / String,
  },
  computed: {
    i18n() {
      return this.$t('index')
    }
  },

  // 生命周期函数，可以为函数，或一个在methods段中定义的方法名
  mounted: function () {
    this.getShopInfo() // 获取店铺信息
    this.queryShopCollection() // 查询店铺是否已收藏
  },

  methods: {
    // 获取店铺信息
    getShopInfo() {
      var params = {
        url: "/shop/headInfo",
        method: "GET",
        data: {
          shopId: this.shopId || uni.getStorageSync('shopInfo').shopId
        },
        callBack: (res) => {
          this.setData({
            shopInfo: res
          });
					uni.setStorageSync("shopInfo", res);
					// 进入页面时判断店铺状态
          this.handleShopStatus(res)
        }
      };
      http.request(params);
    },

    /**
     * 店铺状态处理
     */
    handleShopStatus(res) {
      const shopStatus = res.shopStatus
      // shopStatus店铺状态(-1:未开通 0: 停业中 1:营业中 2:平台下线 3:平台下线待审核)，可修改
      if (shopStatus === -1) {
        this.handleTipsModal(this.i18n.storeStatusTips2)
        return
      }
      if (shopStatus === 0) {
        const contractStartTime = new Date(data.contractStartTime).getTime()
        const contractEndTime = new Date(data.contractEndTime).getTime()
        const today = new Date().getTime()
        // 1、店铺状态为0(停业中)时，当店铺未到签约开始时间，用户进入店铺提示：商家尚未营业
        if (today < contractStartTime) {
          this.handleTipsModal(this.i18n.storeStatusTips4)
          return
        }
        // 2、店铺状态为0(停业中)时，当店铺超过签约有效期，用户进入店铺提示：商家已暂停未营业
        if (today > contractEndTime) {
          this.handleTipsModal(this.i18n.storeStatusTips5)
          return
        }
      }
      if (shopStatus === 2 || shopStatus === 3) {
        this.handleTipsModal(this.i18n.storeStatusTips3)
        return
      }
    },
    handleTipsModal(tips) {
      uni.showModal({
        title: this.i18n.tips,
        content: tips,
        showCancel: false,//是否显示取消按钮
        cancelText: this.i18n.cancel,
        confirmText: this.i18n.confirm,
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack({
              delta: 1
            })
          }
        }
      })
    },

    // 查询店铺是否已收藏
    queryShopCollection() {
      var shopId = this.shopId; //加载轮播图
      var params = {
        url: `/p/shop/collection/isCollection`,
        method: "GET",
        data: {
          shopId: shopId
        },
				dontTrunLogin: true,
        callBack: res => {
          uni.hideLoading()
          if (res == true) {
            this.isFollowed = true
          } else {
            this.isFollowed = false
          }
        }
      };
      http.request(params);
    },

    // 添加/取消店铺
    collectShop() {
      var shopId = this.shopId; //加载轮播图
      util.checkAuthInfo(() => {
        var params = {
          url: `/p/shop/collection/addOrCancel`,
          method: "POST",
          data: shopId,
          callBack: res => {
            uni.hideLoading()
            if (res == true) {
              this.isFollowed = true
            } else {
              this.isFollowed = false
            }
            console.log('添加/取消收藏操作后isFollowed：', this.isFollowed)
            this.getShopInfo()
            // this.$emit('queryShoInfo')
          }
        };
        http.request(params);
      })
    },
  }
}
</script>

<style scoped>
@import "./shopHeader.css";
</style>
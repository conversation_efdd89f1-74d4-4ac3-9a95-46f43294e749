<template>
  <view>
    <view @tap="handleShow">
      <slot>
        <text>点击查看订单二维码</text>
      </slot>
    </view>
    <view class="qrcode-container" v-if="qrcodeVisible">
      <view class="qrcode-box">
        <tki-qrcode
          ref="qrcode"
          onval
          :val="qrcodeValue"
          :size="400"
          :load-make="true"
          :show-loading="false"
          @result="qrcodeResult"
          @longpress="downloadImg"
        />

      </view>
      <view class="qrcode-mask" @tap="handleClose"></view>
    </view>
  </view>
</template>

<script>
import TkiQrcode from '@/components/tki-qrcode/tki-qrcode'

export default {
  name: 'click-to-show-qrcode',
  data () {
    return {
      qrcodeVisible: false,
      qrcodeImg: '',
    }
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  comments: {
    TkiQrcode,
  },
  props: {
    qrcodeValue: {
      type: String,
      default: '',
    }
  },
  methods: {
    handleShow() {
      this.qrcodeVisible = true
    },
    handleClose() {
      this.qrcodeVisible = false
    },
    qrcodeResult (v) {
      this.qrcodeImg = v
      // console.log('qrcodeImg:', this.qrcodeImg)
    },
    /**
     * 保存图片至相册
     */
     downloadImg() {
      // #ifdef MP-WEIXIN
      wx.getImageInfo({
        src: this.qrcodeImg,
        success: (res) => {
          let path = res.path;
          wx.saveImageToPhotosAlbum({
            filePath: path,
            success: (res) => {
                console.log('保存图片成功res:',res);
                wx.showToast({
                    title: this.i18n.downloadComplete,
                })
            },
            fail: (err) => {
                console.log('保存图片失败err:', err);
            }
          })
        },
        fail: (err) => {
          console.log('获取图片信息失败err:', err);
        }
      })
      // #endif
    }
  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
.qrcode-container {
  height: 100vh;
  width: 100vw;
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0;
  left: 0;
  z-index: 100;
}

.qrcode-box {
  width: fit-content;
  height: fit-content;
  padding: 48rpx;
  position: relative;
  z-index: 10000;
  background: #fff;
}

.qrcode-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
}
</style>
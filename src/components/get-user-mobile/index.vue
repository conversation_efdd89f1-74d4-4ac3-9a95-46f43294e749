<template>
  <view>
    <!-- <view class="login-phone__tip">
      为了方便您的购物，需要获取您的手机号码
    </view> -->
    <VerifyCode
      v-if="showSmsAccess"
      :privacy-checked="privacyChecked"
      @getSmsPhoneCode="handleClickVerifyCodeConfirm"
      @beforeSendVerifyCode="handleBeforeSendVerifyCode"
      @verifyCodeSuccess="handleVerifyCodeSuccess"
      @verifyCodeError="handleVarifyCodeError"
      :submit-name="submitName"
      :auto-verify="autoVerify"
      />
    <!-- #ifdef MP-WEIXIN -->
      <view style="height: 32rpx;"></view>
      <NButton
        :disabled="loading"
        type="ghost"
        block
        :open-type="privacyChecked ? 'getPhoneNumber': ''"
        @click="handleClickGetPhoneNumber"
        @getphonenumber="handleGetPhoneNumber"
      >
        {{ wxBtnText }}
      </NButton>
    <!-- #endif -->
  </view>
</template>

<script>
import http from '@/utils/http'
import VerifyCode from '@/components/login-with-wenet/VerifyCode'
import PrivacyTip from '@/components/privacy-tip'
import NI from '@/components/n-i'
import { AppType } from '@/utils/constant.js'
import NButton from '@/components/n-button'
export default {
  name: 'get-user-mobile',
  data () {
    return {
      loading: false,
    }
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  components: {
    VerifyCode,
    PrivacyTip,
    NI,
    NButton,
  },
  props: {
    privacyChecked: {
      type: Boolean,
      default: false
    },
    submitName: {
      type: String,
      default: '登录',
    },
    autoVerify: { // 是否自动验证手机号
      type: Boolean,
      default: true
    },
    pageName: {
      type: String,
      default: ''
    },
    means: {
      type: Array,
      default: () => ['sms', 'wx']
    },
    needCheckBeforSend: {
      type: Boolean,
      default: false
    },
    wxBtnText: {
      type: String,
      default: "手机号快捷登录"
    }
  },
  methods: {

    handleGetPhoneNumber(e) {
      this.$umaTracker.ModularClick({
        Um_Key_ButtonName: '微信授权获取手机号',
        Um_Key_SourcePage: this.pageName,
        Um_Key_SourceLocation: this.pageName,
      })
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        this.$umaTracker.ModularClick({
          Um_Key_ButtonName: '同意微信授权获取手机号',
          Um_Key_SourcePage: this.pageName,
          Um_Key_SourceLocation: this.pageName,
        })
        // 请求微信接口获取 code
        if (this.autoVerify) {
          this.startLoading()
          const params = {
            url: '/verify/moblie',
            method: 'post',
            data: {
              code: e.detail.code,
            },
            callBack: res => {
              this.$emit('verifyMobileSuccess', res)
            },
            errCallBack: errRes => {
              this.stopLoading()
            }
          }
          http.request(params)
        } else {
          this.$emit('getWxPhoneCode', e)
        }
      } else {
        this.stopLoading()
        this.$umaTracker.ModularClick({
          Um_Key_ButtonName: '拒绝微信授权获取手机号',
          Um_Key_SourcePage: this.pageName,
          Um_Key_SourceLocation: this.pageName,
        })
        uni.showToast({
          title: this.i18n.privilegeGrantFailed,
          icon: 'none'
        })
      }
    },
    handleClickGetPhoneNumber() {
      if (!this.privacyChecked) {
        uni.showToast({
          title: '请先同意隐私策略和服务条款',
          icon: 'none'
        })
        return
      }
    },
    startLoading() {
      this.loading = true;
    },
    stopLoading() {
      this.loading = false;
    },
    handlePrivacyTipChange(newValue) {
      this.privacyChecked = newValue
    },
    handleClickVerifyCodeConfirm(res) {
      this.$emit('getSmsPhoneCode', res)
    },
    handleVerifyCodeSuccess(res) {
      this.$emit('verifyMobileSuccess', res)
    },
    handleBeforeSendVerifyCode(mobile, next) {
      // 小程序不能读取$listeners，需要额外的判断
      if(this.$listeners['beforeSendVerifyCode'] || this.needCheckBeforSend) {
        this.$emit('beforeSendVerifyCode', mobile, next)
      } else {
        next()
      }
    },
    handleVarifyCodeError() {
      this.stopLoading()
    },
  },
  computed: {
    appType() {
      return uni.getStorageSync('appType')
    },
    showWxSwitch() {
      return this.means.includes('wx') && (this.appType === AppType.MINI)
    },
    showWxAccess() {
      return this.means.includes('wx')
    },
    showSmsAccess() {
      return this.means.includes('sms')
    },
    i18n() {
  		return this.$t('index')
  	}
  },
}
</script>

<style lang="scss" scoped>
.login-phone__tip {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 10rpx;
  text-align: center;
}
.back{
	width: 100%;
	padding: 0 50rpx;
	text-align: right;
	color: #999;
	font-size: 26rpx;
  box-sizing: border-box;
}

.wx-login-wrap {
  margin-top: 128rpx;
}

.wx-btn-text {
  margin-left: 8rpx;
}
</style>
<template>
  <view class="">
    <view class="title">修改密码</view>
    <view>
      <NInput type="password" v-model="password" placeholder="密码" />
      <NInput type="password" v-model="cfmPassword" placeholder="确认密码" />
      <NButton>确认</NButton>
    </view>
  </view>
</template>

<script>
import { restPassword } from '@/utils/login-processor'
import NInput from '@/components/form/n-input'
import NButton from '@/components/n-button'

export default {
  data() {
    return {
      password: "",
      cfmPassword: "",
    };
  },
  components: {
    NInput,
    NButton
  },
  props: {},
  mounted() {
  },
  methods: {
    checkForm() {
      // 检查password 和 cfmpassowrd
      if (!this.password || !this.cfmPassword) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
      }
      if (this.password !== this.cfmPassword) {
        uni.showToast({
          title: '两次密码不一致',
          icon: 'none'
        })
      }
      restPassword.then(() => {
        uni.showToast({
          title: '修改成功',
          icon: 'success'
        })
        this.$emit('resetSuccess')
      })
    }
  }
};
</script>
<style scoped>
</style>

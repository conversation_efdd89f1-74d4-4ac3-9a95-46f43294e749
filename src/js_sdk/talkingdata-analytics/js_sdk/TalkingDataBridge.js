// #ifdef H5
	console.log("这是H5平台")
	import TDH5App from './sdk_release.js'
// #endif

// #ifdef MP-WEIXIN
console.log("这是微信平台");
import tdweapp from './tdweapp.js'
// #endif

// #ifdef APP
var TalkingDataSDK = uni.requireNativePlugin("TalkingData-SDK")
// #endif


const AppHandler = {
	curPage: "",
	init: function(config) {
		let appId = config.appKey;
		let channelId = config.channelId;
		let custom = config.custom;
		TalkingDataSDK.init(appId, channelId, custom);
	},
	onPageShow: function(name) {
		let routes = getCurrentPages();
		let route = routes[routes.length - 1];
		let url = route.route || "";
		let opt = route.options;
		let param = "";
		let keys = Object.keys(opt);
		if (keys.length > 0) {
			for (let i = 0; i < keys.length; i++) {
				if (i == 0) {
					url += '?' + keys[i] + '=' + opt[keys[i]];
				} else {
					url += '&' + keys[i] + '=' + opt[keys[i]];
				}
			}
		}
		this.curPage = url;

		TalkingDataSDK.onPageBegin(url);
	},
	onPageEnd: function() {
		TalkingDataSDK.onPageEnd(this.curPage);
	},
	onEvent: function(eventInfo) {
		TalkingDataSDK.onEvent(eventInfo.id, eventInfo.value, eventInfo.params);
	},
	_wrappProfile2App: function(opt) {
		let obj = JSON.parse(JSON.stringify(opt));
		obj.type = obj.profileType;
		delete obj.profileType;
		delete obj.invitationCode;
		return obj;
	},
	onRegister: function(opt) {
		let obj = this._wrappProfile2App(opt);

		TalkingDataSDK.onRegister(opt.profileId, obj, opt.invitationCode);
	},
	onLogin: function(opt) {
		let info = this._wrappProfile2App(opt);
		TalkingDataSDK.onLogin(opt.profileId, opt);
	},
	onUpdateProfile: function(opt) {
		let info = this._wrappProfile2App(opt);
		TalkingDataSDK.onProfileUpdate(info);
	},
	_wrappOrder2App: function(opt) {
		let obj = JSON.parse(JSON.stringify(opt));
		obj.total = obj.amount;
		delete obj.amount;
		delete obj.profileId;
		return obj;
	},
	onPlaceOrder: function(opt) {
		let obj = this._wrappOrder2App(opt);
		TalkingDataSDK.onPlaceOrder(obj, opt.profileId);
	},
	onCancelOrder: function(opt) {
		let obj = this._wrappOrder2App(opt);
		TalkingDataSDK.onCancelOrder(obj);
	},
	onOrderPaySucc: function(opt) {
		let obj = this._wrappOrder2App(opt);
		delete obj.paymentType;
		TalkingDataSDK.onOrderPaySucc(obj, opt.paymentType, opt.profileId);
	},
	onViewItem: function(opt) {
		TalkingDataSDK.onViewItem(opt.itemId, opt.category, opt.name, opt.unitPrice);
	},
	onAddItemToShoppingCart: function(opt) {
		TalkingDataSDK.onAddItemToShoppingCart(opt.itemId, opt.category, opt.name, opt.unitPrice, opt.amount);
	},
	onViewShoppingCart: function(opt) {
		TalkingDataSDK.onViewShoppingCart(opt);
	},
	onCreateRole: function(name) {
		TalkingDataSDK.onCreateRole(name);
	},
	onLevelPass: function(opt = {}) {
		let profileId = opt.profileId;
		let levelId = opt.levelId;
		TalkingDataSDK.onLevelPass(profileId, levelId);
	},
	onGuideFinished: function(opt = {}) {
		let profileId = opt.profileId;
		let content = opt.content;
		TalkingDataSDK.onGuideFinished(profileId, content);
	},
	onAchievementUnlock: function(opt = {}) {
		let profileId = opt.profileId;
		let achievementId = opt.achievementId;
		TalkingDataSDK.onAchievementUnlock(profileId, achievementId);
	},
	getOAID: function() {
		return TalkingDataSDK.getOAID();
	},
	getDeviceId: function() {
		return TalkingDataSDK.getDeviceId();
	},
	backgroundSessionEnabled: function() {
		TalkingDataSDK.backgroundSessionEnabled();
	},
	setLocation: function(latitude, longitude) {
		TalkingDataSDK.setLocation(latitude, longitude);
	}
}

const WxAppHandler = {
	_wrappConfig2Wx: function(conf) {
		let wxConfig = {
			appkey: conf.appKey,
			appName: conf.appName,
			wxAppid: conf.wxAppId,
			versionName: conf.versionName,
			versionCode: conf.versionCode,
			showLog: conf.showLog,
			autoOnReachBottom: conf.autoOnReachBottom,
			autoOnPullDownRefresh: conf.autoOnPullDownRefresh
		};
		return  wxConfig;
		
	},
	_wrappProfile2WX: function(opt) {
		if (!opt) {
			console.log("profile信息不能为空!");
			return;
		}
		let info = JSON.parse(JSON.stringify(opt));
		if (info.profileId || /0{1}/.test(info.profileId)) {
			info.accountId = info.profileId;
			delete info.profileId;
		}
		if (info.profileType || /0{1}/.test(info.profileType)) {
			info.accountType = info.profileType;
			delete info.profileType;
		}
		return info;
	},
	onLaunch: function(config, option) {
		let conf = this._wrappConfig2Wx(config);
		tdweapp.init(conf);
		tdweapp.App.onLaunch(option);
	},
	onAppShow: function(opt) {
		tdweapp.App.onShow(opt);
	},
	onAppHide: function() {
		tdweapp.App.onHide();
	},
	onPageShow: function() {
		tdweapp.Page.onShow()
	},
	onPageHide: function() {
		tdweapp.Page.onHide();
	},
	onEvent: function(info) {
		tdweapp.Event.event(info);
	},
	onRegister: function(opt) {
		let info = this._wrappProfile2WX(opt);
		tdweapp.Profile.register(info);
	},
	onLogin: function(opt) {
		let info = this._wrappProfile2WX(opt);
		tdweapp.Profile.login(info);
	},
	onUpdateProfile: function(opt) {
		let info = this._wrappProfile2WX(opt);
		tdweapp.Profile.update(info);
	},
	onPlaceOrder: function(opt) {
		tdweapp.iap.placeOrder(opt);
	},
	onCancelOrder: function(opt) {
		tdweapp.iap.cancelOrder(opt);
	},
	onOrderPaySucc: function(opt) {
		tdweapp.iap.orderPaySucc(opt);
	},
	onShare: function(opt) {
		tdweapp.Event.share(opt);
	}
}
const H5Handler = {
	onPage: function() {
		let routes = getCurrentPages();
		let route = routes[routes.length - 1];
		let url = route.route || "";
		let opt = route.options;
		let param = "";
		let keys = Object.keys(opt);
		if (keys.length > 0) {
			for (let i = 0; i < keys.length; i++) {
				if (i == 0) {
					url += '?' + keys[i] + '=' + opt[keys[i]];
				} else {
					url += '&' + keys[i] + '=' + opt[keys[i]];
				}
			}
		}
		TDAPP.onPage(url);
	},
	onEvent: function(eventInfo) {
		TDAPP.onEvent(eventInfo.id, eventInfo.label, eventInfo.params, eventInfo.value);
	},
	onRegister: function(opt) {
		TDAPP.register(opt);
	},
	onLogin: function(opt) {
		TDAPP.login(opt);
	},
	onUpdateProfile: function(opt) {
		TDAPP.updateProfile(opt);
	},
	onPlaceOrder: function(opt) {
		TDAPP.onPlaceOrder(opt);
	},
	onCancelOrder: function(opt) {
		TDAPP.onCancelOrder(opt);
	},
	onOrderPaySucc: function(opt) {
		TDAPP.onOrderPaySucc(opt);
	}

}

const TDBridge = {
	debug: true,
	curPage: '',
	log: function(...msg) {
		if (this.debug) {
			if (msg && msg.length > 0) {
				let param = "TDBridge:";
				for (let m in msg) {
					let temp = msg[m];
					if (typeof temp == 'object') {
						temp = JSON.stringify(temp);
					}
					param = param + temp + ",";
				}
				console.log(param);
			}
		}
	},
	_checkNull: function(key, val) {
		if (!val && /0{1}/.test(val)) {
			return `{key} 为必填项目`;
		}

	},


	onAppLaunch: function(config = {}, options = {}) {
		this.log("appLaunch", options);
		let isNull = this._checkNull("appKey", config.appKey);
		if (isNull) {
			console.log(isNull);
			return;
		}
		if(config.showLog){
			this.debug=true;
		}else{
			this.debug = false;
		}
		// #ifdef H5
		TDH5App.init(config);
		// #endif

		// #ifdef MP-WEIXIN
		WxAppHandler.onLaunch(config);
		// #endif

		// #ifdef APP
		AppHandler.init(config);
		// #endif
	},
	onAppShow: function(options) {
		this.log("appShow", options);
		// #ifdef MP-WEIXIN
		WxAppHandler.onAppShow(options);
		// #endif
	},
	onAppHide: function() {
		this.log("appHide")
		// #ifdef MP-WEIXIN
		WxAppHandler.onAppHide();
		// #endif
	},
	onPageShow: function() {
		this.log("pageShow")
		// #ifdef MP-WEIXIN
		WxAppHandler.onPageShow();
		// #endif
		// #ifdef H5
		H5Handler.onPage();
		// #endif

		// #ifdef APP
		AppHandler.onPageShow();
		// #endif
	},
	onPageHide: function() {
		this.log("pageHide");
		// #ifdef MP-WEIXIN
		tdweapp.Page.onHide();
		// #endif
		// #ifdef APP
		AppHandler.onPageEnd();
		// #endif

	},
	onPageUnload: function() {
		this.log("pageUnload");
		// #ifdef MP-WEIXIN
		WxAppHandler.onPageHide();
		// #endif

	},
	onEvent: function(eventInfo) {
		// #ifdef MP-WEIXIN
		WxAppHandler.onEvent(eventInfo);
		// #endif

		// #ifdef H5
		H5Handler.onEvent(eventInfo);
		// #endif

		// #ifdef APP
		AppHandler.onEvent(eventInfo);
		// #endif
	},
	onRegister: function(opt) {
		// #ifdef H5
		H5Handler.onRegister(opt);
		// #endif
		// #ifdef MP-WEIXIN
		WxAppHandler.onRegister(opt);
		// #endif
		// #ifdef APP
		AppHandler.onRegister(opt);
		// #endif

	},
	onLogin: function(opt) {
		// #ifdef H5
		H5Handler.onLogin(opt);
		// #endif
		// #ifdef MP-WEIXIN
		WxAppHandler.onLogin(opt);
		// #endif
		// #ifdef APP
		AppHandler.onLogin(opt);
		// #endif
	},
	onUpdateProfile: function(opt) {
		// #ifdef H5
		H5Handler.onUpdateProfile(opt);
		// #endif
		// #ifdef MP-WEIXIN
		WxAppHandler.onUpdateProfile(opt);
		// #endif
		// #ifdef APP
		AppHandler.onUpdateProfile(opt);
		// #endif
	},
	onPlaceOrder: function(opt) {
		// #ifdef H5
		H5Handler.onPlaceOrder(opt);
		// #endif
		// #ifdef MP-WEIXIN
		WxAppHandler.onPlaceOrder(opt);
		// #endif
		// #ifdef APP
		AppHandler.onPlaceOrder(opt);
		// #endif
	},
	onOrderPaySucc: function(opt) {
		// #ifdef H5
		H5Handler.onOrderPaySucc(opt);
		// #endif
		// #ifdef MP-WEIXIN
		WxAppHandler.onOrderPaySucc(opt);
		// #endif
		// #ifdef APP
		AppHandler.onOrderPaySucc(opt);
		// #endif
	},
	onCancelOrder: function(opt) {
		// #ifdef H5
		H5Handler.onCancelOrder(opt);
		// #endif
		// #ifdef MP-WEIXIN
		WxAppHandler.onCancelOrder(opt);
		// #endif
		// #ifdef APP
		AppHandler.onCancelOrder(opt);
		// #endif
	},
	onShare: function(opt) {
		// #ifdef MP-WEIXIN
		WxAppHandler.onShare(opt);
		// #endif

	},
	mpPullDownRefresh: function() {
		// #ifdef MP-WEIXIN
		tdweapp.Event.pullDownRefresh();
		// #endif
	},
	mpReachBottom: function() {
		// #ifdef MP-WEIXIN
		tdweapp.Event.reachBottom();
		// #endif
	},
	onViewItem: function(opt) {
		// #ifdef APP
		AppHandler.onViewItem(opt);
		// #endif

	},
	onAddItemToShoppingCart: function(opt) {
		// #ifdef APP
		AppHandler.onAddItemToShoppingCart(opt);
		// #endif
	},
	onViewShoppingCart: function(opt) {
		// #ifdef APP
		AppHandler.onViewShoppingCart(opt);
		// #endif
	},
	onCreateRole: function(name) {
		// #ifdef APP
		AppHandler.onCreateRole(name);
		// #endif

	},
	onLevelPass: function(opt = {}) {
		// #ifdef APP
		AppHandler.onLevelPass(opt);
		// #endif

	},
	onGuideFinished: function(opt = {}) {
		// #ifdef APP
		AppHandler.onGuideFinished(opt);
		// #endif

	},
	onAchievementUnlock: function(opt = {}) {
		// #ifdef APP
		AppHandler.onAchievementUnlock(opt);
		// #endif
	},
	getOAID: function() {
		// #ifdef APP
		return AppHandler.getOAID();
		// #endif
	},
	getDeviceId: function() {
		// #ifdef APP
		return AppHandler.getDeviceId();
		// #endif
	},
	backgroundSessionEnabled: function() {
		// #ifdef APP
		AppHandler.backgroundSessionEnabled();
		// #endif
	},
	setLocation: function(latitude, longitude) {
		// #ifdef APP
		AppHandler.setLocation(latitude, longitude);
		// #endif
	}

};
export default TDBridge;

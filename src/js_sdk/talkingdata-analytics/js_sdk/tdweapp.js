"use strict";

function iapJudge(e, t) {
	return e.orderId && "string" == typeof e.orderId ? "number" != typeof e.amount || e.amount !== e.amount ? (console
		.warn("请填写正确的订单金额"), !1) : "string" != typeof e.currencyType || 3 !== e.currencyType.length ? (console.warn(
		"请填写正确的货币类型"), !1) : !t || "string" == typeof e.paymentType || void 0 === e.paymentType || (console.warn(
		"请填写正确的支付方式"), !1) : (console.warn("请填写正确的订单ID"), !1)
}
var _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
		return typeof e
	} : function(e) {
		return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" :
			typeof e
	},
	customConf = {config:{}},
	_uidUrl = "https://api.talkingdata.com/mpopenid",
	_requestUrl = "https://h5.udrig.com/app/wx/v1",
	_uidKey = "openId",
	_version = ["3", "0", "15"],
	waitFlag = {
		device: !0,
		network: !0,
		uid: !0
	},
	appInfo = {
		sdk: {
			version: _version[0],
			minorVersion: _version[1],
			build: _version[2],
			platform: "Weapp",
			partner: ""
		},
		app: {
			versionCode: customConf.config.versionCode || "1",
			versionName: customConf.config.versionName || "1.0.0",
			installTime: 0,
			displayName: customConf.config.appName,
			appKey: customConf.config.appkey,
			uniqueId: customConf.config.wxAppid,
			channel: ""
		},
		device: {
			type: "mobile",
			softwareConfig: {},
			hardwareConfig: {},
			deviceId: {}
		},
		networks: [{
			type: "wifi",
			available: !1,
			connected: !1
		}, {
			type: "cellular",
			available: !1,
			connected: !1,
			current: []
		}, {
			type: "unknown",
			available: !1,
			connected: !1
		}],
		locations: [],
		appContext: {}
	},
	regeneratorRuntime = function(e) {
		function t(e, t, n, o) {
			var i = t && t.prototype instanceof r ? t : r,
				a = Object.create(i.prototype),
				c = new d(o || []);
			return a._invoke = u(e, n, c), a
		}

		function n(e, t, n) {
			try {
				return {
					type: "normal",
					arg: e.call(t, n)
				}
			} catch (e) {
				return {
					type: "throw",
					arg: e
				}
			}
		}

		function r() {}

		function o() {}

		function i() {}

		function a(e) {
			["next", "throw", "return"].forEach(function(t) {
				e[t] = function(e) {
					return this._invoke(t, e)
				}
			})
		}

		function c(e) {
			function t(r, o, i, a) {
				var c = n(e[r], e, o);
				if ("throw" !== c.type) {
					var u = c.arg,
						s = u.value;
					return s && "object" === (void 0 === s ? "undefined" : _typeof(s)) && m.call(s, "__await") ? Promise
						.resolve(s.__await).then(function(e) {
							t("next", e, i, a)
						}, function(e) {
							t("throw", e, i, a)
						}) : Promise.resolve(s).then(function(e) {
							u.value = e, i(u)
						}, function(e) {
							return t("throw", e, i, a)
						})
				}
				a(c.arg)
			}

			function r(e, n) {
				function r() {
					return new Promise(function(r, o) {
						t(e, n, r, o)
					})
				}
				return o = o ? o.then(r, r) : r()
			}
			var o;
			this._invoke = r
		}

		function u(e, t, r) {
			var o = I;
			return function(i, a) {
				if (o === D) throw new Error("Generator is already running");
				if (o === U) {
					if ("throw" === i) throw a;
					return h()
				}
				for (r.method = i, r.arg = a;;) {
					var c = r.delegate;
					if (c) {
						var u = s(c, r);
						if (u) {
							if (u === _) continue;
							return u
						}
					}
					if ("next" === r.method) r.sent = r._sent = r.arg;
					else if ("throw" === r.method) {
						if (o === I) throw o = U, r.arg;
						r.dispatchException(r.arg)
					} else "return" === r.method && r.abrupt("return", r.arg);
					o = D;
					var l = n(e, t, r);
					if ("normal" === l.type) {
						if (o = r.done ? U : L, l.arg === _) continue;
						return {
							value: l.arg,
							done: r.done
						}
					}
					"throw" === l.type && (o = U, r.method = "throw", r.arg = l.arg)
				}
			}
		}

		function s(e, t) {
			var r = e.iterator[t.method];
			if (r === v) {
				if (t.delegate = null, "throw" === t.method) {
					if (e.iterator.return && (t.method = "return", t.arg = v, s(e, t), "throw" === t.method)) return _;
					t.method = "throw", t.arg = new TypeError("The iterator does not provide a 'throw' method")
				}
				return _
			}
			var o = n(r, e.iterator, t.arg);
			if ("throw" === o.type) return t.method = "throw", t.arg = o.arg, t.delegate = null, _;
			var i = o.arg;
			return i ? i.done ? (t[e.resultName] = i.value, t.next = e.nextLoc, "return" !== t.method && (t.method =
				"next", t.arg = v), t.delegate = null, _) : i : (t.method = "throw", t.arg = new TypeError(
				"iterator result is not an object"), t.delegate = null, _)
		}

		function l(e) {
			var t = {
				tryLoc: e[0]
			};
			1 in e && (t.catchLoc = e[1]), 2 in e && (t.finallyLoc = e[2], t.afterLoc = e[3]), this.tryEntries.push(t)
		}

		function p(e) {
			var t = e.completion || {};
			t.type = "normal", delete t.arg, e.completion = t
		}

		function d(e) {
			this.tryEntries = [{
				tryLoc: "root"
			}], e.forEach(l, this), this.reset(!0)
		}

		function f(e) {
			if (e) {
				var t = e[w];
				if (t) return t.call(e);
				if ("function" == typeof e.next) return e;
				if (!isNaN(e.length)) {
					var n = -1,
						r = function t() {
							for (; ++n < e.length;)
								if (m.call(e, n)) return t.value = e[n], t.done = !1, t;
							return t.value = v, t.done = !0, t
						};
					return r.next = r
				}
			}
			return {
				next: h
			}
		}

		function h() {
			return {
				value: v,
				done: !0
			}
		}
		var v, g = Object.prototype,
			m = g.hasOwnProperty,
			y = "function" == typeof Symbol ? Symbol : {},
			w = y.iterator || "@@iterator",
			S = y.asyncIterator || "@@asyncIterator",
			T = y.toStringTag || "@@toStringTag";
		e.wrap = t;
		var I = "suspendedStart",
			L = "suspendedYield",
			D = "executing",
			U = "completed",
			_ = {},
			b = {};
		b[w] = function() {
			return this
		};
		var x = Object.getPrototypeOf,
			E = x && x(x(f([])));
		E && E !== g && m.call(E, w) && (b = E);
		var N = i.prototype = r.prototype = Object.create(b);
		return o.prototype = N.constructor = i, i.constructor = o, i[T] = o.displayName = "GeneratorFunction", e
			.isGeneratorFunction = function(e) {
				var t = "function" == typeof e && e.constructor;
				return !!t && (t === o || "GeneratorFunction" === (t.displayName || t.name))
			}, e.mark = function(e) {
				return Object.setPrototypeOf ? Object.setPrototypeOf(e, i) : (e.__proto__ = i, T in e || (e[T] =
					"GeneratorFunction")), e.prototype = Object.create(N), e
			}, e.awrap = function(e) {
				return {
					__await: e
				}
			}, a(c.prototype), c.prototype[S] = function() {
				return this
			}, e.AsyncIterator = c, e.async = function(n, r, o, i) {
				var a = new c(t(n, r, o, i));
				return e.isGeneratorFunction(r) ? a : a.next().then(function(e) {
					return e.done ? e.value : a.next()
				})
			}, a(N), N[T] = "Generator", N[w] = function() {
				return this
			}, N.toString = function() {
				return "[object Generator]"
			}, e.keys = function(e) {
				var t = [];
				for (var n in e) t.push(n);
				return t.reverse(),
					function n() {
						for (; t.length;) {
							var r = t.pop();
							if (r in e) return n.value = r, n.done = !1, n
						}
						return n.done = !0, n
					}
			}, e.values = f, d.prototype = {
				constructor: d,
				reset: function(e) {
					if (this.prev = 0, this.next = 0, this.sent = this._sent = v, this.done = !1, this.delegate =
						null, this.method = "next", this.arg = v, this.tryEntries.forEach(p), !e)
						for (var t in this) "t" === t.charAt(0) && m.call(this, t) && !isNaN(+t.slice(1)) && (this[
							t] = v)
				},
				stop: function() {
					this.done = !0;
					var e = this.tryEntries[0],
						t = e.completion;
					if ("throw" === t.type) throw t.arg;
					return this.rval
				},
				dispatchException: function(e) {
					function t(t, r) {
						return i.type = "throw", i.arg = e, n.next = t, r && (n.method = "next", n.arg = v), !!r
					}
					if (this.done) throw e;
					for (var n = this, r = this.tryEntries.length - 1; r >= 0; --r) {
						var o = this.tryEntries[r],
							i = o.completion;
						if ("root" === o.tryLoc) return t("end");
						if (o.tryLoc <= this.prev) {
							var a = m.call(o, "catchLoc"),
								c = m.call(o, "finallyLoc");
							if (a && c) {
								if (this.prev < o.catchLoc) return t(o.catchLoc, !0);
								if (this.prev < o.finallyLoc) return t(o.finallyLoc)
							} else if (a) {
								if (this.prev < o.catchLoc) return t(o.catchLoc, !0)
							} else {
								if (!c) throw new Error("try statement without catch or finally");
								if (this.prev < o.finallyLoc) return t(o.finallyLoc)
							}
						}
					}
				},
				abrupt: function(e, t) {
					for (var n = this.tryEntries.length - 1; n >= 0; --n) {
						var r = this.tryEntries[n];
						if (r.tryLoc <= this.prev && m.call(r, "finallyLoc") && this.prev < r.finallyLoc) {
							var o = r;
							break
						}
					}
					o && ("break" === e || "continue" === e) && o.tryLoc <= t && t <= o.finallyLoc && (o = null);
					var i = o ? o.completion : {};
					return i.type = e, i.arg = t, o ? (this.method = "next", this.next = o.finallyLoc, _) : this
						.complete(i)
				},
				complete: function(e, t) {
					if ("throw" === e.type) throw e.arg;
					return "break" === e.type || "continue" === e.type ? this.next = e.arg : "return" === e.type ? (
							this.rval = this.arg = e.arg, this.method = "return", this.next = "end") : "normal" ===
						e.type && t && (this.next = t), _
				},
				finish: function(e) {
					for (var t = this.tryEntries.length - 1; t >= 0; --t) {
						var n = this.tryEntries[t];
						if (n.finallyLoc === e) return this.complete(n.completion, n.afterLoc), p(n), _
					}
				},
				catch: function(e) {
					for (var t = this.tryEntries.length - 1; t >= 0; --t) {
						var n = this.tryEntries[t];
						if (n.tryLoc === e) {
							var r = n.completion;
							if ("throw" === r.type) {
								var o = r.arg;
								p(n)
							}
							return o
						}
					}
					throw new Error("illegal catch attempt")
				},
				delegateYield: function(e, t, n) {
					return this.delegate = {
						iterator: f(e),
						resultName: t,
						nextLoc: n
					}, "next" === this.method && (this.arg = v), _
				}
			}, e
	}("object" === ("undefined" == typeof module ? "undefined" : _typeof(module)) ? module.exports : {}),
	RC4 = function() {
		return function e(t, n, r) {
			function o(a, c) {
				if (!n[a]) {
					if (!t[a]) {
						var u = "function" == typeof require && require;
						if (!c && u) return u(a, !0);
						if (i) return i(a, !0);
						var s = new Error("Cannot find module '" + a + "'");
						throw s.code = "MODULE_NOT_FOUND", s
					}
					var l = n[a] = {
						exports: {}
					};
					t[a][0].call(l.exports, function(e) {
						var n = t[a][1][e];
						return o(n || e)
					}, l, l.exports, e, t, n, r)
				}
				return n[a].exports
			}
			for (var i = "function" == typeof require && require, a = 0; a < r.length; a++) o(r[a]);
			return o
		}({
			1: [function(e, t, n) {
				function r() {
					l = !1, c.length ? s = c.concat(s) : p = -1, s.length && o()
				}

				function o() {
					if (!l) {
						var e = setTimeout(r);
						l = !0;
						for (var t = s.length; t;) {
							for (c = s, s = []; ++p < t;) c && c[p].run();
							p = -1, t = s.length
						}
						c = null, l = !1, clearTimeout(e)
					}
				}

				function i(e, t) {
					this.fun = e, this.array = t
				}

				function a() {}
				var c, u = t.exports = {},
					s = [],
					l = !1,
					p = -1;
				u.nextTick = function(e) {
						var t = new Array(arguments.length - 1);
						if (arguments.length > 1)
							for (var n = 1; n < arguments.length; n++) t[n - 1] = arguments[n];
						s.push(new i(e, t)), 1 !== s.length || l || setTimeout(o, 0)
					}, i.prototype.run = function() {
						this.fun.apply(null, this.array)
					}, u.title = "browser", u.browser = !0, u.env = {}, u.argv = [], u.version = "", u
					.versions = {}, u.on = a, u.addListener = a, u.once = a, u.off = a, u
					.removeListener = a, u.removeAllListeners = a, u.emit = a, u.binding = function(e) {
						throw new Error("process.binding is not supported")
					}, u.cwd = function() {
						return "/"
					}, u.chdir = function(e) {
						throw new Error("process.chdir is not supported")
					}, u.umask = function() {
						return 0
					}
			}, {}],
			2: [function(e, t, n) {
				function r(e) {
					if (Array.isArray(e)) {
						for (var t = 0, n = Array(e.length); t < e.length; t++) n[t] = e[t];
						return n
					}
					return Array.from(e)
				}

				function o(e) {
					u = a(e)
				}

				function i(e) {
					for (var t = [], n = 0, r = e.length; r > n; n++) t.push(e.charCodeAt(n));
					return t
				}

				function a(e) {
					for (var t = [].concat(r(Array(256).keys())), n = 0, e = i(e), o = 0, a = t
						.length; a > o; o++) {
						n = (n + t[o] + e[o % e.length]) % 256;
						var c = [t[n], t[o]];
						t[o] = c[0], t[n] = c[1]
					}
					return t
				}

				function c(e) {
					var t, n, r = new Array;
					t = e.length;
					for (var o = 0; o < t; o++) n = e.charCodeAt(o), n >= 65536 && n <= 1114111 ? (r
							.push(n >> 18 & 7 | 240), r.push(n >> 12 & 63 | 128), r.push(n >> 6 & 63 |
								128), r.push(63 & n | 128)) : n >= 2048 && n <= 65535 ? (r.push(n >>
							12 & 15 | 224), r.push(n >> 6 & 63 | 128), r.push(63 & n | 128)) : n >=
						128 && n <= 2047 ? (r.push(n >> 6 & 31 | 192), r.push(63 & n | 128)) : r.push(
							255 & n);
					return r
				}
				var u = "",
					s = regeneratorRuntime.mark(function e(t) {
						var n, r, o;
						return regeneratorRuntime.wrap(function(e) {
							for (;;) switch (e.prev = e.next) {
								case 0:
									n = 0, r = 0;
								case 1:
									return n = (n + 1) % 256, r = (r + t[n]) % 256,
										o = [t[r], t[n]], t[n] = o[0], t[r] = o[1], e
										.next = 9, t[(t[n] + t[r]) % 256];
								case 9:
									e.next = 1;
									break;
								case 11:
								case "end":
									return e.stop()
							}
						}, e, this)
					});
				o.prototype.encrypt = function(e) {
					for (var t = c(e), n = t.length, r = new ArrayBuffer(n), o = s(u.slice(0)), i =
							new Int8Array(r), a = 0; n > a; a++) i[a] = t[a] ^ o.next().value;
					return i
				}, o.prototype.decrypt = function(e) {
					var t = "",
						n = s(u.slice(0));
					e = e.match(/[a-z0-9]{2}/gi);
					for (var r = 0, o = e.length; o > r; r++) t += String.fromCharCode(parseInt(e[
						r], 16) ^ n.next().value);
					return t
				}, t.exports = o
			}, {}],
			3: [function(e, t, n) {
				(function(e, n) {
					function r(e) {
						return e && "undefined" != typeof Symbol && e.constructor === Symbol ?
							"symbol" : void 0 === e ? "undefined" : _typeof(e)
					}! function(n) {
						function o(e, t, n, r) {
							var o = Object.create((t || a).prototype),
								i = new v(r || []);
							return o._invoke = d(e, n, i), o
						}

						function i(e, t, n) {
							try {
								return {
									type: "normal",
									arg: e.call(t, n)
								}
							} catch (e) {
								return {
									type: "throw",
									arg: e
								}
							}
						}

						function a() {}

						function c() {}

						function u() {}

						function s(e) {
							["next", "throw", "return"].forEach(function(t) {
								e[t] = function(e) {
									return this._invoke(t, e)
								}
							})
						}

						function l(e) {
							this.arg = e
						}

						function p(t) {
							function n(e, n) {
								var r = t[e](n),
									o = r.value;
								return o instanceof l ? Promise.resolve(o.arg).then(a, c) : Promise
									.resolve(o).then(function(e) {
										return r.value = e, r
									})
							}

							function o(e, t) {
								function r() {
									return n(e, t)
								}
								return i = i ? i.then(r, r) : new Promise(function(e) {
									e(r())
								})
							}
							"object" === (void 0 === e ? "undefined" : r(e)) && e.domain && (n = e
								.domain.bind(n));
							var i, a = n.bind(t, "next"),
								c = n.bind(t, "throw");
							n.bind(t, "return"), this._invoke = o
						}

						function d(e, t, n) {
							var r = L;
							return function(o, a) {
								if (r === U) throw new Error("Generator is already running");
								if (r === _) {
									if ("throw" === o) throw a;
									return m()
								}
								for (;;) {
									var c = n.delegate;
									if (c) {
										if ("return" === o || "throw" === o && c.iterator[o] ===
											y) {
											n.delegate = null;
											var u = c.iterator.return;
											if (u) {
												var s = i(u, c.iterator, a);
												if ("throw" === s.type) {
													o = "throw", a = s.arg;
													continue
												}
											}
											if ("return" === o) continue
										}
										var s = i(c.iterator[o], c.iterator, a);
										if ("throw" === s.type) {
											n.delegate = null, o = "throw", a = s.arg;
											continue
										}
										o = "next", a = y;
										var l = s.arg;
										if (!l.done) return r = D, l;
										n[c.resultName] = l.value, n.next = c.nextLoc, n
											.delegate = null
									}
									if ("next" === o) n._sent = a, n.sent = r === D ? a : y;
									else if ("throw" === o) {
										if (r === L) throw r = _, a;
										n.dispatchException(a) && (o = "next", a = y)
									} else "return" === o && n.abrupt("return", a);
									r = U;
									var s = i(e, t, n);
									if ("normal" === s.type) {
										r = n.done ? _ : D;
										var l = {
											value: s.arg,
											done: n.done
										};
										if (s.arg !== b) return l;
										n.delegate && "next" === o && (a = y)
									} else "throw" === s.type && (r = _, o = "throw", a = s.arg)
								}
							}
						}

						function f(e) {
							var t = {
								tryLoc: e[0]
							};
							1 in e && (t.catchLoc = e[1]), 2 in e && (t.finallyLoc = e[2], t
								.afterLoc = e[3]), this.tryEntries.push(t)
						}

						function h(e) {
							var t = e.completion || {};
							t.type = "normal", delete t.arg, e.completion = t
						}

						function v(e) {
							this.tryEntries = [{
								tryLoc: "root"
							}], e.forEach(f, this), this.reset(!0)
						}

						function g(e) {
							if (e) {
								var t = e[S];
								if (t) return t.call(e);
								if ("function" == typeof e.next) return e;
								if (!isNaN(e.length)) {
									var n = -1,
										r = function t() {
											for (; ++n < e.length;)
												if (w.call(e, n)) return t.value = e[n], t.done = !
													1, t;
											return t.value = y, t.done = !0, t
										};
									return r.next = r
								}
							}
							return {
								next: m
							}
						}

						function m() {
							return {
								value: y,
								done: !0
							}
						}
						var y, w = Object.prototype.hasOwnProperty,
							S = "function" == typeof Symbol && Symbol.iterator || "@@iterator",
							T = "object" === (void 0 === t ? "undefined" : r(t)),
							I = n.regeneratorRuntime;
						if (I) return void(T && (t.exports = I));
						I = n.regeneratorRuntime = T ? t.exports : {}, I.wrap = o;
						var L = "suspendedStart",
							D = "suspendedYield",
							U = "executing",
							_ = "completed",
							b = {},
							x = u.prototype = a.prototype;
						c.prototype = x.constructor = u, u.constructor = c, c.displayName =
							"GeneratorFunction", I.isGeneratorFunction = function(e) {
								var t = "function" == typeof e && e.constructor;
								return !!t && (t === c || "GeneratorFunction" === (t.displayName ||
									t.name))
							}, I.mark = function(e) {
								return Object.setPrototypeOf ? Object.setPrototypeOf(e, u) : e
									.__proto__ = u, e.prototype = Object.create(x), e
							}, I.awrap = function(e) {
								return new l(e)
							}, s(p.prototype), I.async = function(e, t, n, r) {
								var i = new p(o(e, t, n, r));
								return I.isGeneratorFunction(t) ? i : i.next().then(function(e) {
									return e.done ? e.value : i.next()
								})
							}, s(x), x[S] = function() {
								return this
							}, x.toString = function() {
								return "[object Generator]"
							}, I.keys = function(e) {
								var t = [];
								for (var n in e) t.push(n);
								return t.reverse(),
									function n() {
										for (; t.length;) {
											var r = t.pop();
											if (r in e) return n.value = r, n.done = !1, n
										}
										return n.done = !0, n
									}
							}, I.values = g, v.prototype = {
								constructor: v,
								reset: function(e) {
									if (this.prev = 0, this.next = 0, this.sent = y, this
										.done = !1, this.delegate = null, this.tryEntries
										.forEach(h), !e)
										for (var t in this) "t" === t.charAt(0) && w.call(this,
											t) && !isNaN(+t.slice(1)) && (this[t] = y)
								},
								stop: function() {
									this.done = !0;
									var e = this.tryEntries[0],
										t = e.completion;
									if ("throw" === t.type) throw t.arg;
									return this.rval
								},
								dispatchException: function(e) {
									function t(t, r) {
										return i.type = "throw", i.arg = e, n.next = t, !!r
									}
									if (this.done) throw e;
									for (var n = this, r = this.tryEntries.length - 1; r >=
										0; --r) {
										var o = this.tryEntries[r],
											i = o.completion;
										if ("root" === o.tryLoc) return t("end");
										if (o.tryLoc <= this.prev) {
											var a = w.call(o, "catchLoc"),
												c = w.call(o, "finallyLoc");
											if (a && c) {
												if (this.prev < o.catchLoc) return t(o.catchLoc,
													!0);
												if (this.prev < o.finallyLoc) return t(o
													.finallyLoc)
											} else if (a) {
												if (this.prev < o.catchLoc) return t(o.catchLoc,
													!0)
											} else {
												if (!c) throw new Error(
													"try statement without catch or finally"
													);
												if (this.prev < o.finallyLoc) return t(o
													.finallyLoc)
											}
										}
									}
								},
								abrupt: function(e, t) {
									for (var n = this.tryEntries.length - 1; n >= 0; --n) {
										var r = this.tryEntries[n];
										if (r.tryLoc <= this.prev && w.call(r, "finallyLoc") &&
											this.prev < r.finallyLoc) {
											var o = r;
											break
										}
									}
									o && ("break" === e || "continue" === e) && o.tryLoc <= t &&
										t <= o.finallyLoc && (o = null);
									var i = o ? o.completion : {};
									return i.type = e, i.arg = t, o ? this.next = o.finallyLoc :
										this.complete(i), b
								},
								complete: function(e, t) {
									if ("throw" === e.type) throw e.arg;
									"break" === e.type || "continue" === e.type ? this.next = e
										.arg : "return" === e.type ? (this.rval = e.arg, this
											.next = "end") : "normal" === e.type && t && (this
											.next = t)
								},
								finish: function(e) {
									for (var t = this.tryEntries.length - 1; t >= 0; --t) {
										var n = this.tryEntries[t];
										if (n.finallyLoc === e) return this.complete(n
											.completion, n.afterLoc), h(n), b
									}
								},
								catch: function(e) {
									for (var t = this.tryEntries.length - 1; t >= 0; --t) {
										var n = this.tryEntries[t];
										if (n.tryLoc === e) {
											var r = n.completion;
											if ("throw" === r.type) {
												var o = r.arg;
												h(n)
											}
											return o
										}
									}
									throw new Error("illegal catch attempt")
								},
								delegateYield: function(e, t, n) {
									return this.delegate = {
										iterator: g(e),
										resultName: t,
										nextLoc: n
									}, b
								}
							}
					}("object" === (void 0 === n ? "undefined" : r(n)) ? n : "object" === (
							"undefined" == typeof window ? "undefined" : r(window)) ? window :
						"object" === ("undefined" == typeof self ? "undefined" : r(self)) ? self :
						void 0)
				}).call(this, e("_process"), "undefined" != typeof global ? global : "undefined" !=
					typeof self ? self : "undefined" != typeof window ? window : {})
			}, {
				_process: 1
			}],
			4: [function(e, t, n) {
				t.exports = {
					full: "1.2.0",
					major: "1",
					minor: "2",
					dot: "0"
				}
			}, {}]
		}, {}, [2])(2)
	}(),
	Util = {
		firstInit: !1,
		initTime: 0,
		sessionId: "",
		sessionStartTime: 0,
		appLaunchInfo: {},
		sendFailTimes: 0,
		bakData: {},
		Store: {
			set: function(e, t) {
				try {
					wx.setStorageSync("TDSDK_" + e, t)
				} catch (e) {}
				Util.bakData["TDSDK_" + e] = t
			},
			get: function(e) {
				var t = null;
				try {
					t = wx.getStorageSync("TDSDK_" + e)
				} catch (e) {}
				return t || (t = Util.bakData["TDSDK_" + e] || null), t
			},
			remove: function(e) {
				try {
					wx.removeStorageSync("TDSDK_" + e)
				} catch (e) {}
				delete Util.bakData["TDSDK_" + e]
			}
		},
		random: function() {
			for (var e = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890", t = e.length, n = "", r =
					0; r < 12; r++) n += e.charAt(Math.floor(Math.random() * t));
			return n
		},
		timestamp: function() {
			return (new Date).getTime()
		},
		deviceId: function() {
			return "weapp-" + this.timestamp() + "-" + this.random()
		},
		isNumber: function(e) {
			return "number" == typeof e && !isNaN(e)
		},
		getEventId: function(e) {
			if (!e && !/0{1}/.test(e)) return "";
			var t = "";
			try {
				t = e.toString()
			} catch (n) {
				try {
					t = JSON.stringify(e)
				} catch (e) {}
			}
			return t.split(" ")[0].slice(0, 64)
		},
		addStoreData: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [],
				t = "EVENT_" + Util.sessionId,
				n = Util.Store.get(t);
			n = n && n.length ? n.concat(e) : e, Util.Store.set(t, n), n.length >= 30 && (onLaunchFn
				.sessionContinue(), onLaunchFn.startLoop())
		},
		eventHandle: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "",
				t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
			if (e) {
				var n = getCurrentPages(),
					r = n[n.length - 1],
					o = {
						eventId: e,
						label: r.__route__,
						count: 1,
						startTime: Util.timestamp()
					};
				if ("WeappShare" === e) {
					o.shareTickets = t.shareTickets;
					var i = JSON.parse(JSON.stringify(r.options || {}));
					i.user = Util.deviceId, i.title = t.title, i.desc = t.desc, i.path = t.path, o.params = i
				}
				Util.addStoreData([o])
			}
		},
		getCacheData: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
				t = Object.keys(e),
				n = [],
				r = [];
			return t.length && t.forEach(function(t) {
				var o = e[t];
				o && o.sendFail && o.data && (n = n.concat(o.data), r.push(t))
			}), {
				data: n,
				keys: r
			}
		},
		sendCacheList: {},
		updateSendTime: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [],
				t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0,
				n = appInfo.device.deviceId,
				r = Util.Store.get("uid"),
				o = Util.Store.get("deviceId");
			return e.forEach(function(i, a) {
				if (!i.device.deviceId.tid && !i.device.deviceId.uid)
					if (n.tid) {
						if (i.device.deviceId.tid = n.tid, n.uid) return i.device.deviceId.uid = n.uid, !0
					} else {
						if (n.uid) return i.device.deviceId.uid = n.uid, i.device.deviceId.tid = n.uid, !0;
						if (TDID.isWaitingForOpenid) {
							if (r) return i.device.deviceId.uid = r, i.device.deviceId.tid = r, n.uid = r, n
								.tid = r, !0;
							if (o) i.device.deviceId.tid = o, i.device.deviceId.uid = "";
							else {
								var c = Util.deviceId();
								n.tid = c, n.uid = "", Util.Store.set("deviceId", c), i.device.deviceId
									.tid = c, i.device.deviceId.uid = "", TDID.shouldOverwriteTid = !1
							}
						} else i.device.deviceId.tid = n.tid, i.device.deviceId.uid = n.uid
					} i.action && i.action.data && (e[a].action.data.start = t)
			}), e
		},
		getRequestData: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [],
				t = JSON.parse(JSON.stringify(e)),
				n = Util.sendCacheList;
			if (Object.keys(n).length) {
				var r = Util.getCacheData(n);
				t = t.concat(r.data), r.keys.forEach(function(e) {
					return delete n[e]
				})
			}
			var o = t.length;
			if (o) {
				var i = [];
				if (o >= 30) {
					JSON.stringify(t).length > 61440 && i.push(t.splice(0, o / 2)), i.push(t)
				} else i.push(t);
				i.forEach(function(e) {
					var t = Util.timestamp();
					n[t] = {
						data: e,
						sendFail: !1
					};
					var r = Util.updateSendTime(e, Util.timestamp());
					Util.request(t, r)
				})
			}
		},
		handleData: function(e) {
			return new RC4(
				"r5czusfu0wjcaz4pp01v2k7qte55xc25fngq4ylby2civ230vdy6uy6goz9w4kgfqjk31l8khfzfvbxj7emcprjyy8nngf0r9dvxzwbhm2uw7ljre52jt95lg0knyp8e5c4go44s3z5ciy58h0tuosmwhupa62rdnkeicgdba6w6f0kenp0xac7so8j1vdbjpqwyprx2ouenv22isustwnpltt9ui5plnijd4bq4013o3mzdkllozn26zwds9x38"
				).encrypt(e)
		},
		request: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0,
				t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [];
			customConf.config.showLog && console.warn("Talkingdata:", t);
			var n = Util.handleData(JSON.stringify(t));
			wx.request({
				url: _requestUrl,
				data: n.buffer,
				header: {
					"content-type": "application/octet-stream",
					"decrypt-version": "v1.0.0"
				},
				method: "POST",
				success: function(t) {
					200 === t.statusCode && (delete Util.sendCacheList[e], Util.sendFailTimes = 0,
						appHandle.appIsHide || (clearTimeout(onLaunchFn.timeout), onLaunchFn
							.timeout = null, onLaunchFn.startLoop()))
				},
				fail: function() {
					appHandle.appIsHide ? (Util.Store.set("RESEND_" + e, t), delete Util.sendCacheList[
						e]) : (Util.sendCacheList[e].sendFail = !0, Util.sendFailTimes < 5 && Util
						.sendFailTimes++)
				}
			})
		}
	},
	TDID = {
		shouldOverwriteTid: !0,
		isWaitingForOpenid: !0,
		isFirst: !0,
		init: function() {
			var e = this,
				t = Util.Store.get("deviceId"),
				n = Util.Store.get("uid");
			if (n) {
				var r = t || n;
				e.setData(r, n)
			} else {
				new Promise(this.getOpenid).then(function(n) {
					var r = void 0;
					t ? r = t : (r = n, Util.Store.set("deviceId", n)), e.setData(r, n), Util.Store.set(
						"uid", n), TDID.isWaitingForOpenid = !1
				}).catch(function(n) {
					var r = void 0;
					r = t || Util.deviceId(), e.setData(r, ""), TDID.shouldOverwriteTid && Util.Store.set(
						"deviceId", r), TDID.isWaitingForOpenid = !1
				})
			}
		},
		setData: function(e, t) {
			TDID.shouldOverwriteTid ? appInfo.device.deviceId = {
				tid: e,
				uid: t
			} : appInfo.device.deviceId.uid = t, waitFlag.uid = !1, onLaunchFn.getAppProfile()
		},
		getOpenid: function(e, t) {
			function n() {
				r.isFirst ? r.reGetOpenid(e, t) : t("error")
			}
			var r = TDID;
			(new Date).getTime();
			wx.login({
				timeout: 3e3,
				success: function(t) {
					if (t.code) {
						var r = _uidUrl;
						wx.request({
							url: r + "/" + customConf.config.appkey + "/" + t.code,
							success: function(t) {
								var r = t.data;
								r && 200 === r.code && r[_uidKey] ? e(r[_uidKey]) : n()
							},
							fail: function(e) {
								n()
							}
						})
					} else n()
				},
				fail: function(e) {
					n()
				}
			})
		},
		reGetOpenid: function(e, t) {
			TDID.isFirst = !1, TDID.getOpenid(e, t)
		}
	},
	DomainName = {
		placeOrder: {
			domain: "iap",
			name: "placeOrder"
		},
		orderPaySucc: {
			domain: "iap",
			name: "pay"
		},
		cancelOrder: {
			domain: "iap",
			name: "cancelOrder"
		},
		register: {
			domain: "account",
			name: "register"
		},
		login: {
			domain: "account",
			name: "login"
		},
		update: {
			domain: "account",
			name: "update"
		}
	},
	request = {
		sendTime: 0,
		statusType: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
				t = [],
				n = JSON.parse(JSON.stringify(appInfo)),
				r = {
					domain: e.domain,
					name: e.name,
					data: e.data
				};
			n.ts = e.data.start || Util.timestamp(), n.action = r, t.push(n), Util.getRequestData(t)
		},
		dataType: function(e, t) {
			var n = this.getStoreList(e, t);
			Util.getRequestData(n)
		},
		getEventType: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
				t = DomainName[e.domainName];
			if (t) return t;
			if (e.pageEvent) return {
				domain: "page",
				name: "leave"
			};
			if (e.eventId) {
				var n = e.eventId,
					r = {};
				switch (n) {
					case "WeappShare":
						r = {
							domain: "user",
							name: "share"
						};
						break;
					case "WeappPullDownRefresh":
						r = {
							domain: "page",
							name: "pullDownRefresh"
						};
						break;
					case "WeappReachBottom":
						r = {
							domain: "page",
							name: "reachBottom"
						};
						break;
					default:
						r = {
							domain: "appEvent",
							name: ""
						}
				}
				return r
			}
		},
		getStoreList: function(e, t) {
			var n = this,
				r = [],
				o = e || Util.sessionId,
				i = JSON.stringify(appInfo),
				a = Util.Store.get("EVENT_" + o);
			return a && a.length && (a.forEach(function(e) {
				var o = n.getEventType(e),
					a = JSON.parse(i);
				t && a.appContext && (a.appContext.sessionStartTime = t);
				var c = JSON.parse(JSON.stringify(e)),
					u = c.pageEvent ? c.leaveTime : c.startTime;
				c.pageEvent && delete c.pageEvent, c.leaveTime && delete c.leaveTime, c.domainName || (c
					.status = 2), c.domainName && delete c.domainName;
				var s = {
					domain: o.domain,
					name: o.name,
					data: c
				};
				a.ts = u || Util.timestamp(), a.action = s, r.push(a)
			}), Util.Store.remove("EVENT_" + o)), r
		}
	},
	createProfile = function(e) {
		var t = {},
			n = Util.Store.get("account") || {};
		return "update" !== e && "setProfile" !== e || (t = n || {}), {
			getInfo: function() {
				return t
			},
			setId: function(e) {
				if ("string" != typeof e || "" === e) return "accountId 类型错误";
				e === n.accountId ? t = n : t.accountId = e
			},
			setName: function(e) {
				if ("string" != typeof e) return "profile name 类型错误";
				t.name = e
			},
			setAge: function(e) {
				if (!Util.isNumber(e)) return "profile age 类型错误";
				t.age = e
			},
			setGender: function(e) {
				var n = Number(e);
				if (0 !== n && 1 !== n && 2 !== n) return "profile gender 类型错误";
				t.gender = n
			},
			setType: function(e) {
				var n = Number(e);
				if (!(Util.isNumber(n) && /^[0-9][0-9]*$/.test(n) && (n >= 0 && n <= 6 || n >= 11 && n <= 20)))
					return "account type 类型错误";
				t.type = n
			},
			setProperty: function(e, n) {
				if (!Util.isNumber(n) && "string" != typeof n) return "profile " + e + " 类型错误";
				t[e] = n
			}
		}
	},
	Account = {
		handleData: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
				t = arguments[1],
				n = createProfile(t),
				r = {
					name: "setName",
					gender: "setGender",
					age: "setAge",
					accountType: "setType"
				},
				o = !1,
				i = new RegExp("^property[1-9]$");
			if (e.accountId && "update" !== t && "setProfile" !== t) {
				var a = n.setId(e.accountId);
				if (a) return o = !0, console.error(a)
			}
			if (Object.keys(e).forEach(function(t) {
					if (!o) {
						var a = e[t],
							c = r[t];
						if (c) {
							var u = n[c](a);
							if (u) return o = !0, console.error(u)
						} else if (i.test(t) || "property10" === t) {
							var s = n.setProperty(t, a);
							if (s) return o = !0, console.error(s)
						}
					}
				}), !o) {
				var c = n.getInfo();
				if (Account.updateAccountInfo("account", c), "register" === t || "login" === t || "update" === t) {
					var u = Object.assign({
						domainName: t
					}, e);
					Util.addStoreData([u])
				}
			}
		},
		setAccountInfo: function(e) {
			var t = Util.Store.get(e);
			t && ("wxUserInfo" === e ? this.assignUserInfo(t) : "account" === e && this.assignAccount(t))
		},
		updateAccountInfo: function(e, t) {
			Util.Store.set(e, t), "wxUserInfo" === e ? this.assignUserInfo(t) : "account" === e && this
				.assignAccount(t)
		},
		assignUserInfo: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
			appInfo.user = {
				accounts: [{
					type: "wechat",
					name: e.nickName || "",
					extra: [e]
				}]
			}
		},
		assignAccount: function(e) {
			appInfo.appContext.account = e
		}
	},
	accountApi = {
		register: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
			return e.accountId ? e.accountType || 0 === e.accountType ? void Account.handleData(e, "register") :
				console.error("Register事件缺少accountType字段") : console.error("Register事件缺少accountId字段")
		},
		login: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
			return e.accountId ? e.accountType || 0 === e.accountType ? void Account.handleData(e, "login") :
				console.error("Login事件缺少accountType字段") : console.error("Login事件缺少accountId字段")
		},
		update: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
			Account.handleData(e, "update")
		}
	},
	iapEvent = {
		placeOrder: function(e) {
			if (!iapJudge(e)) return !1;
			var t = {
				orderId: e.orderId,
				amount: e.amount,
				currencyType: e.currencyType,
				domainName: "placeOrder"
			};
			Util.addStoreData([t])
		},
		orderPaySucc: function(e) {
			if (!iapJudge(e, !0)) return !1;
			var t = {
				orderId: e.orderId,
				amount: e.amount,
				currencyType: e.currencyType,
				payType: e.paymentType,
				domainName: "orderPaySucc"
			};
			Util.addStoreData([t])
		},
		cancelOrder: function(e) {
			if (!iapJudge(e)) return !1;
			var t = {
				orderId: e.orderId,
				amount: e.amount,
				currencyType: e.currencyType,
				domainName: "cancelOrder"
			};
			Util.addStoreData([t])
		}
	},
	hasDataFlag = !1,
	onLaunchFn = {
		timeout: null,
		init: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
			Util.appLaunchInfo = JSON.parse(JSON.stringify(e)), Util.appLaunchInfo.scene = e.scene ? e.scene
				.toString() : "", TDID.init(), onLaunchFn.judgeRequireData(), onLaunchFn.getLocalParams(),
				onLaunchFn.getSystemInfo(), onLaunchFn.getNetwork(), onLaunchFn._inited = !0
		},
		launchRequest: function() {
			var e = {
				first: !0
			};
			request.statusType({
				domain: "app",
				name: "init",
				data: e
			})
		},
		sessionStart: function(e) {
			var t = Util.appLaunchInfo || {},
				n = {
					status: 1,
					duration: 0,
					name: t.path,
					scene: t.scene,
					query: t.query || {},
					shareTicket: t.shareTicket,
					referrerInfo: t.referrerInfo
				};
			e && onLaunchFn.setNewSession(), n.start = Util.Store.get("session_time") || Util.timestamp(), n.url =
				onLaunchFn.getUrl(n.name, n.query), request.statusType({
					domain: "session",
					name: "begin",
					data: n
				})
		},
		getUrl: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "",
				t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
				n = Object.keys(t),
				r = n.sort(function(e, t) {
					return e > t
				}) || [],
				o = r.length ? e + "?" : e;
			return r.forEach(function(e, n) {
				0 !== n && (o += "&"), o += e + "=" + t[e]
			}), o
		},
		sessionContinue: function() {
			request.dataType()
		},
		sessionEnd: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
				t = {
					status: 3,
					start: e.startTime,
					duration: e.duration
				};
			request.statusType({
				domain: "session",
				name: "end",
				data: t
			})
		},
		sendTmpSession: function() {
			onLaunchFn.sessionContinue(), onLaunchFn.startLoop()
		},
		startLoop: function() {
			clearTimeout(onLaunchFn.timeout), onLaunchFn.timeout = null;
			var e = 3e3 * (Util.sendFailTimes + 1);
			onLaunchFn.timeout = setTimeout(function() {
				onLaunchFn.sendTmpSession()
			}, e)
		},
		judgeRequireData: function() {
			appInfo.app.appKey || (appInfo.app.appKey = "", console.error("请填写您在TalkingData申请的App ID")), appInfo.app
				.displayName || (appInfo.app.displayName = "appname", console.error("请填写您的小程序名称"))
		},
		getLocalParams: function() {
			var e = Util.Store.get("initTime");
			e ? Util.initTime = e : (Util.initTime = Util.timestamp(), Util.Store.set("initTime", Util.initTime),
				Util.firstInit = !0), appInfo.app.installTime = Util.initTime;
			var t = Util.appLaunchInfo.query || {},
				n = t.TDChannelId ? t.TDChannelId : "";
			appInfo.app.channel = n, Account && (Account.setAccountInfo("wxUserInfo"), Account.setAccountInfo(
				"account")), onLaunchFn.setNewSession()
		},
		setNewSession: function() {
			Util.sessionId = Util.deviceId(), Util.sessionStartTime = Util.timestamp(), Util.Store.set(
					"session_time", Util.sessionStartTime), appInfo.appContext.sessionId = Util.sessionId, appInfo
				.appContext.sessionStartTime = Util.sessionStartTime
		},
		getLaunchInfo: function() {
			var e = JSON.parse(JSON.stringify(onLaunchFn.launchOptions));
			return e.type = "appLaunch", e
		},
		getAppProfile: function() {
			if (!hasDataFlag) {
				var e = ["device", "network", "uid"],
					t = !0;
				e.forEach(function(e) {
					waitFlag[e] && (t = !1)
				}), t && (hasDataFlag = !0, this.startRequest())
			}
		},
		startRequest: function() {
			Util.firstInit && onLaunchFn.launchRequest(), this.sessionStart(), this.startLoop()
		},
		getNetwork: function() {
			wx.getNetworkType({
				complete: function(e) {
					var t = appInfo.networks,
						n = e.networkType;
					"wifi" === n ? (t[0].available = !0, t[0].connected = !0) : "unknown" === n ? (t[2]
						.available = !0, t[2].connected = !0) : "none" !== n && (t[1].available = !
						0, t[1].connected = !0, t[1].current.push({
							type: n
						})), waitFlag.network = !1, onLaunchFn.getAppProfile()
				}
			})
		},
		getSystemInfo: function() {
			wx.getSystemInfo({
				complete: function(e) {
					if (e.model || e.system || e.SDKVersion) {
						var t = {
								model: e.model,
								pixel: e.screenWidth + "*" + e.screenHeight + "*" + e.pixelRatio,
								densityDpi: e.pixelRatio,
								brand: e.brand
							},
							n = {
								os: e.system,
								local: e.language,
								language: "zh_CN",
								osVersionCode: e.version,
								timezone: -(new Date).getTimezoneOffset() / 60,
								mpVersion: e.SDKVersion
							};
						appInfo.device.hardwareConfig = t, appInfo.device.softwareConfig = n
					}
					waitFlag.device = !1, onLaunchFn.getAppProfile()
				}
			})
		},
		_inited: !1
	},
	eventHandle = {
		event: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			var t = Util.getEventId(e.id);
			if (t) {
				var n = {};
				n.eventId = t, n.label = Util.getEventId(e.label), n.count = e.count || 1, n.params = e.params, n
					.startTime = Util.timestamp(), void 0 !== e.value ? "number" != typeof e.value ? console.error(
						'自定义事件"' + t + '"中value对应的值的类型需为Number类型') : isNaN(e.value) ? console.error('自定义事件"' + t +
						'"中请输入有效的Number类型数值') : (n.value = e.value, Util.addStoreData([n])) : Util.addStoreData([n])
			}
		},
		share: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			Util.eventHandle("WeappShare", e)
		},
		pullDownRefresh: function() {
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			(TDSDK || customConf.config.autoOnPullDownRefresh) && Util.eventHandle("WeappPullDownRefresh")
		},
		reachBottom: function() {
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			(TDSDK || customConf.config.autoOnReachBottom) && Util.eventHandle("WeappReachBottom")
		},
		setProfile: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			Account.handleData(e, "setProfile")
		},
		setWXUserInfo: function(e) {
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			"object" !== (void 0 === e ? "undefined" : _typeof(e)) || e instanceof Array ? console.warn(
				"setUserInfo接口只接受json对象作为参数") : Account.updateAccountInfo("wxUserInfo", e)
		}
	},
	appHandle = {
		isHide2Show: !1,
		appIsHide: !1,
		lastHideTime: 0,
		appCount: 0,
		show: function() {
			var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			if (appHandle.appCount % 2 == 1) return void console.error(
				"TDSDK.App.onShow()和TDSDK.App.onHide()应该成对调用");
			if (appHandle.appCount = appHandle.appCount + 1, appHandle.appIsHide = !1, appHandle.getlastTmpData(),
				appHandle.isHide2Show) {
				var t = Util.Store.get("TMP_time_end_" + Util.sessionId),
					n = e.scene ? e.scene.toString() : "";
				if (e.scene && n === Util.appLaunchInfo.scene) {
					Util.timestamp() - t > 3e4 ? appHandle.sessionRestart(t) : (appHandle.lastHideTime = t, Util
						.Store.remove("TMP_time_end_" + Util.sessionId))
				} else Util.appLaunchInfo = JSON.parse(JSON.stringify(e)), Util.appLaunchInfo.scene = n, appHandle
					.sessionRestart(t);
				appHandle.isHide2Show = !1, onLaunchFn.startLoop()
			}
		},
		sessionRestart: function(e) {
			var t = Util.Store.get("TMP_time_start_" + Util.sessionId),
				n = {
					startTime: t,
					duration: parseInt((e - t) / 1e3)
				};
			onLaunchFn.sessionEnd(n), Util.Store.remove("TMP_time_start_" + Util.sessionId), Util.Store.remove(
				"TMP_time_end_" + Util.sessionId), Util.Store.remove("session_time"), onLaunchFn.sessionStart(!
				0), appHandle.lastHideTime = 0
		},
		hide: function() {
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			if (appHandle.appCount % 2 == 0) return void console.error(
				"TDSDK.App.onShow()和TDSDK.App.onHide()应该成对调用");
			appHandle.appCount = appHandle.appCount + 1, appHandle.appIsHide = !0, clearTimeout(onLaunchFn.timeout),
				onLaunchFn.timeout = null, onLaunchFn.sessionContinue(), appHandle.isHide2Show = !0;
			var e = Util.Store.get("session_time"),
				t = Util.timestamp(),
				n = appHandle.lastHideTime ? t - appHandle.lastHideTime : t - e;
			Util.Store.set("TMP_time_start_" + Util.sessionId, e), Util.Store.set("TMP_time_end_" + Util.sessionId,
				t);
			var r = {
				start: t,
				duration: parseInt((t - e) / 1e3),
				durationHide: parseInt(n / 1e3)
			};
			request.statusType({
				domain: "session",
				name: "hide",
				data: r
			})
		},
		getlastTmpData: function() {
			var e = [],
				t = wx.getStorageInfoSync().keys || [],
				n = void 0,
				r = void 0;
			t && t.length && (n = t.filter(function(e) {
				return e.indexOf("TDSDK_EVENT") > -1
			}), r = t.filter(function(e) {
				return e.indexOf("TDSDK_RESEND") > -1
			})), n && n.length && (n.forEach(function(t) {
				var n = {};
				t.split("_")[2];
				n.id = t.split("_")[2], n.time = n.id.split("-")[1], e.push(n)
			}), appHandle.sendLastTmpData(e)), r && r.length && r.forEach(function(e) {
				wx.getStorage({
					key: e,
					success: function(t) {
						Util.getRequestData(t.data), wx.removeStorage({
							key: e,
							success: function(e) {}
						})
					}
				})
			})
		},
		sendLastTmpData: function() {
			(arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []).forEach(function(e) {
				request.dataType(e.id, e.time)
			})
		}
	},
	pageHandle = {
		curPagePath: "",
		refer: "",
		pageTime: 0,
		pageQuery: {},
		hasShow: !1,
		show: function() {
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			var e = getCurrentPages(),
				t = e[e.length - 1];
			"" !== pageHandle.curPagePath && (pageHandle.refer = pageHandle.curPagePath), pageHandle.curPagePath = t
				.__route__, pageHandle.pageTime = Util.timestamp(), pageHandle.pageQuery = t.options, pageHandle
				.hasShow = !0, onLaunchFn.startLoop()
		},
		hide: function() {
			
			if (!onLaunchFn._inited) return void console.error("请先调用TDSDK.App.onLaunch()完成初始化");
			if (!pageHandle.hasShow) return void console.log("重复进入");
			pageHandle.hasShow = !1;
			var e = getCurrentPages(),
				t = e[e.length - 1];
			if (pageHandle.curPagePath != t.__route__) return void console.error(
				"TDSDK.Page.onShow()和TDSDK.Page.onHide()/TDSDK.Page.onUnload()应该成对调用");
			clearTimeout(onLaunchFn.timeout), onLaunchFn.timeout = null;
			var n = Util.timestamp(),
				r = [{
					name: pageHandle.curPagePath,
					from: pageHandle.refer || "",
					query: pageHandle.pageQuery,
					scene: Util.appLaunchInfo.scene,
					duration: parseInt((n - pageHandle.pageTime) / 1e3),
					startTime: pageHandle.pageTime,
					pageEvent: !0,
					leaveTime: n
				}];
			Util.addStoreData(r)
				
		}
	},
	TDSDK = {
		init:function(conf){
			customConf.config = conf;
			appInfo.app.versionCode = customConf.config.versionCode || "1";
			appInfo.app.versionName = customConf.config.versionName || "1.0.0";
			appInfo.app.displayName = customConf.config.appName;
			appInfo.app.appKey = customConf.config.appkey;
			appInfo.app.uniqueId = customConf.config.wxAppid;
		},
		App: {
			onLaunch: onLaunchFn.init,
			onShow: appHandle.show,
			onHide: appHandle.hide
		},
		Page: {
			onShow: pageHandle.show,
			onHide: pageHandle.hide,
			onUnload: pageHandle.hide
		},
		Event: {
			event: eventHandle.event,
			share: eventHandle.share,
			pullDownRefresh: eventHandle.pullDownRefresh,
			reachBottom: eventHandle.reachBottom,
			setProfile: eventHandle.setProfile,
			setWxInfo: eventHandle.setWXUserInfo
		},
		Profile: {
			register: accountApi.register,
			login: accountApi.login,
			update: accountApi.update
		},
		iap: iapEvent
	};
module.exports = TDSDK;

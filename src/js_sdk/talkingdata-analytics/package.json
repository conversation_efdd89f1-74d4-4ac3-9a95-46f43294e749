{"id": "talkingdata-analytics", "displayName": "talkingdata-analytics", "version": "1.0.1", "description": "本插件适用于使用uni-app开发的面向H5、微信小程序以及Android、iOS应用。", "keywords": ["talkingdata-analytics", "TalkingData", "统计", "分析", "监测"], "repository": "", "engines": {"HBuilderX": "^3.5.1"}, "dcloudext": {"type": "sdk-js", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件本身没有采集，使用的是 TD SDK 的采集服务，SDK 服务内所需采集服务请查看官方文档 ： https://www.talkingdata.com/terms.jsp?languagetype=zh_cn https://www.talkingdata.com/security.jsp?languagetype=zh_cn   \n数据发送地址：\nhttps://tdsdk.cpatrk.net  \nhttps://h5.udrig.com/app/v1  \nhttps://h5.udrig.com  \nhttps://api.talkingdata.com", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "u"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}
# talkingdata-analytics
## 适用范围
本插件适用于使用uni-app开发的面向H5、微信小程序以及Android、iOS应用。

## <font color=#1E90FF>集成准备-创建应用并获取App ID</font>
App ID(又称为appKey) 是 TalkingData 分析平台标识一款独立应用的唯一标识，集成 SDK 前需要在 TalkingData 产品中心创建应用并获取相应的 App ID。

**步骤：**

1. 注册并登录 [https://www.talkingdata.com/](https://www.talkingdata.com/)，进入产品中心。
2. 创建应用并获取 App ID。
3. 如果已创建的应用，请从“产品中心->我的应用->点击应用名称”查看 App ID。

## <font color=#1E90FF>集成准备-统计标准说明</font>

根据各行业场景，TalkingData SDK 提供行业通用事件体系、行业专属事件体系、页面事件体系、自定义事件体系等。

　● **用户：**TalkingData SDK 数据系统中的“用户”，指用户的一台唯一设备。  
　● **激活用户：**指下载、安装并首次启动应用成功的用户。  
　● **行业通用事件：**适用于所有行业类型的移动App，包含唤醒、注册、登录、添加支付信息、收藏、分享、签到打卡、搜索。  
　● **行业专属事件：**针对各行业特性专属定制的事件体系，例如查看商品、通过关卡、课程学习、授信交易等。  
　
## <font color=#1E90FF>快速集成-引入SDK</font>

### 引入SDK
本插件的原生功能依赖于 [TalkingData 原生插件](https://ext.dcloud.net.cn/plugin?id=8985)。

如果不需要开发Android和iOS的应用，那么可以不必引入TalkingData原生插件。
如果需要开发Android和iOS应用，请按照原生插件通用使用流程集成原生插件。

在需要使用SDK的文件中调用如下代码，引入SDK插件：

```
import TDBridge from '@/uni_modules/talkingdata-analytics/js_sdk/TalkingDataBridge.js'
```
**注意：**
* 关于开发Android开发的权限说明和iOS应用的框架依赖 ，请参考[TalkingData 原生插件](https://ext.dcloud.net.cn/plugin?id=8985)中**权限说明**和**依赖框架说明**部分。
* 关于微信小程序开发中的域名配置，请参考[TalkingData 应用统计分析小程序SDK集成说明](https://doc.talkingdata.com/posts/1036)

## <font color=#1E90FF>快速集成-初始化</font>

### 接口说明

只有在SDK正确初始化后，SDK的其他接口才可以使用。
在App.vue中的onLaunch方法中初始化SDK。

### 接口定义

```
TDBridge.onAppLaunch(conf,opt);

```
### 参数说明

|参数|类型|是否必选|描述|
|----|----|------|-----|
|conf|object|是|配置SDK初始化参数|
|opt|object|是|为App的onLaunch接口传入的参数|

其中conf中各字段如下：

|参数           |       类型    |H5是否必填|微信小程序是否必填 |App是否必填    |    描述|
|---------------|--------------|----------|------------------|---------------|------------------------------------------------------------------------|
|appKey         |string        |√         |√                 |√              |appId为应用追踪的唯一标识，在【产品中心】-【我的应用】-【点击应用名称】获取|
|appName        |string        |×         |×                 | ×             |应用名字
|channelId      |string        |×         | ×                |√              | channelId为渠道跟踪ID；<br/>如果在GooglePlay官方市场上架，channelId必须设置为GooglePlay或play.google.com，<br/>避免系统误判为其他第三方市场数据；  <br/>如果在AppStore官方市场上架，channelId必须设置为AppStore避免系统误判为其他第三方市场数据；<br/>  如果在国内第三方应用市场，或者越狱市场上架，channelId最多包含64个字符，支持中文、英文、数字、下划线、“.”，但不能包含空格或其他的转义字符。  此外，针对GooglePlay官方市场，请集成Google专属版SDK。详见：https://www.talkingdata.com/sdk-forgp.jsp|
|versionName    |string        |√         | √                |×              |应用版本名称|
|versionCode    |string        |√         | √                |×              |应用版本号|
|wxAppId        | string       |×         |√                 |×              | 微信小程序appId|


### 示例代码

```
onLaunch: function(opt) {
			let conf = {
					appKey:"aaaaaaa",//所有平台必需
					appName:"测试uniapp应用",//可选
					channelId:"default",
					versionName:"1.0",//仅h5和微信小程序有效
					versionCode:"10",//仅h5和微信小程序有效
					wxAppId:"wxasasdsadasd",//仅微信有效		
			};
			TDBridge.onAppLaunch(conf,opt);
}
```

## <font color=#1E90FF>基础-基础统计</font>

在App.vue中，除在`onLaunch`中初始化SDK外，还需要做如下配置：

```
import TDBridge from '@/uni_modules/talkingdata-analytics/js_sdk/TalkingDataBridge.js'
	export default {
		onLaunch: function(opt) {
			let conf = {
					appKey:"aaaaaaa",//所有平台必需
					appName:"测试uniapp应用",//可选
					channelId:"default",
					custom:"ssss",//仅app平台adt产品线有效
					versionName:"1.0",//仅h5和微信小程序有效
					versionCode:"10",//仅h5和微信小程序有效
					wxAppId:"wxasasdsadasd"//仅微信有效			
			};
			TDBridge.onAppLaunch(conf,opt);
		},
		onShow: function() {
			TDBridge.onAppShow();
		},
		onHide: function() {
			TDBridge.onAppHide();
		}
	}

```

正确完成上述调用后，SDK自动完成应用启动的统计。

基于应用启动，后台自动计算独立设备，并计算新增、活跃、留存、升级等指标。


## <font color=#1E90FF>基础-渠道统计</font>

### 用途和用法
给App应用的安装包打入特殊的渠道标记，用户安装并使用后，就可以在报表中分不同渠道来源单独查询相应的数据。

在您为应用商店、下载站等分发渠道提供应用安装包时，可以加入渠道标记；在进行特别的活动时也可加入特殊渠道标记便于单独分析效果。

### 注意事项：
此用法仅限于App，用户的渠道归属是指每台设备首次安装激活的渠道，同一用户在更替渠道包使用后不会重复计算新增，使用数据归入首次激活渠道。如果未添加渠道标记，或渠道标记是示例代码中的默认值，用户将会归入为“未知渠道”。

### 集成方式说明
在初始化中写入渠道ID即可。

## <font color=#1E90FF>基础-页面统计</font>
### 用途和用法
此功能用于帮助开发者统计应用中各个页面的访问次数和停留时长，并可以追踪用户从某个页面离开后所访问的其他页面，为产品优化提供依据。   
SDK会自动统计页面停留时长及来源页面。  

### 接口定义
在每个page的vue文件中，对page的生命周期回调分别插入下面代码：


```
	TDBridge.onPageShow();
	TDBridge.onPageHide();
	TDBridge.onPageUnload();
```

### 示例代码

```
	export default {
		
		onShow() {
			TDBridge.onPageShow();
		},
		onHide() {
			TDBridge.onPageHide();
		},
		onUnload() {
			TDBridge.onPageUnload();
		}
	}

```
## <font color=#1E90FF>自定义事件</font>

### 接口说明
自定义事件用于统计任何您期望跟踪的数据，如：用户点击某功能按钮、填写某个输入框、触发了某个广告等。 完成这些自定义事件统计后可以在报表对其进行统计分析并进行关键路径转化分析。

### 接口定义

```
TDBridge.onEvent(event);
```
### 参数说明
event为Object类型，具体参数如下：

|参数	|类型	|是否必须	|描述																										|
|----|-----|----|------|
|id		|string	|是			|事件ID, 事件的唯一标识。最多包含64个字符，支持中文、英文、数字、下划线，但不能包含空格或其他的转义字符。	|
|label	|string	|否			|事件描述																							|
|params	|object	|否			|自定义的事件参数																							|
|value  |number |否         |仅限传入数值类型，表明当前事件为数值事件类型，服务端会对此值进行sum/avg等操作    |

### 示例代码

```
var event1 = {
			id:"testEvent4",
			label:"label4",
			value:12
			}
TDBridge.onEvent(event1);


var event2 = {
			id:"testEvent3",
			label:"label3",
			params:{
				a:"111",
				b:222,
				c:"eee"
			}
			}
TDBridge.onEvent(event2);
				
```

## <font color=#1E90FF>账户事件-注册</font>

### 接口说明
注册接口用于记录用户在使用应用过程中的注册行为，在注册成功时调用此接口。

### 接口定义

```
TDBridge.onRegister(profileInfo);

```
profileInfo为object类型，具体参数如下：  

|参数								|类型			|是否必须	|描述																				|
|-----------------------------------|---------------|-----------|--------|
|profileId							|string			|是			|账户的唯一标识，用于区分不同用户																		|
|profileType						|number			|是			|传入账户的类型。支持匿名、自有账户显性注册、第三方账户及其他预留的自定义账户类型，共4大类，具体如下：0：ANONYMOUS，匿名账号；<br>1：REGISTERED，自有帐户显性注册；<br>2：SINA_WEIBO，新浪微博账号；<br>3：QQ，QQ账号；<br>4：TENCENT_WEIBO，腾讯微博账号；<br>5：ND91，91平台账号；<br>6：WEIXIN,微信账号	<br>11：Type1<br>12：Type2；<br>13：Type3；<br>……<br>20：Type10		|																					
|name								|string			|否			|账户名称																								|
|gender								|number			|否			|性别，0：未知；1：男性；2：女性																		|
|age								|number			|否			|年龄，限数字																							|
|property1 ~property10				|number/string	|否			|property1到property10为扩展属性	

### 示例代码

```
let opt ={
			profileId:"qqqq",
			profileType:0,
			name:"testName",
			gender:0,
			age:12,
			property1:"student",
			property5:3500
			}
TDBridge.onRegister(opt);

```


##  <font color=#1E90FF>账户事件-登录</font>

### 接口说明
 用于记录用户在使用应用过程中的登录行为，在登录成功时调用此接口。
 
### 接口定义
```
TDBridge.onLogin(profileInfo);
```

### 参数说明
profileInfo为object类型，具体参数如下：  

|参数								|类型			|是否必须	|描述																									|
|------|----------|--------------|--------|
|profileId							|string			|是			|账户的唯一标识，用于区分不同用户																		|
|profileType						|number			|是			|传入账户的类型。支持匿名、自有账户显性注册、第三方账户及其他预留的自定义账户类型，共4大类，具体如下：0：ANONYMOUS，匿名账号；<br>1：REGISTERED，自有帐户显性注册；<br>2：SINA_WEIBO，新浪微博账号；<br>3：QQ，QQ账号；<br>4：TENCENT_WEIBO，腾讯微博账号；<br>5：ND91，91平台账号；<br>6：WEIXIN,微信账号	<br>11：Type1<br>12：Type2；<br>13：Type3；<br>……<br>20：Type10		|																					
|name								|string			|否			|账户名称																								|
|gender								|number			|否			|性别，0：未知；1：男性；2：女性																		|
|age								|number			|否			|年龄，限数字																							|
|property1 ~property10				|number/string	|否			|property1到property10为扩展属性																		|

### 示例代码

```
let opt ={
			profileId:"qqqq",
			profileType:0,
			name:"testName",
			gender:0,
			age:12,
			property1:"student",
			property2:35001
			}
TDBridge.onLogin(opt);
```

##  <font color=#1E90FF>账户事件-更新账户</font>

### 接口说明
 更新已注册或者登录过的账户信息时调用此接口。
 
### 接口定义
```
TDBridge.onUpdateProfile(profileInfo);
```

### 参数说明
profileInfo为boject类型，具体参数如下：  

|参数								|类型			|是否必须	|描述																									|
|------|----------|--------------|--------|
|profileType						|number			|否			|传入账户的类型。支持匿名、自有账户显性注册、第三方账户及其他预留的自定义账户类型，共4大类，具体如下：0：ANONYMOUS，匿名账号；<br>1：REGISTERED，自有帐户显性注册；<br>2：SINA_WEIBO，新浪微博账号；<br>3：QQ，QQ账号；<br>4：TENCENT_WEIBO，腾讯微博账号；<br>5：ND91，91平台账号；<br>6：WEIXIN,微信账号	<br>11：Type1<br>12：Type2；<br>13：Type3；<br>……<br>20：Type10		|																					
|name								|string			|否			|账户名称																								|
|gender								|number			|否			|性别，0：未知；1：男性；2：女性																		|
|age								|number			|否			|年龄，限数字																							|
|property1 ~property10				|number/string	|否			|property1到property10为扩展属性																		|

### 示例代码

```
let opt ={
			profileType:0,
			name:"testName",
			gender:0,
			age:12,
			property1:"student",
			property2:3500
			}
TDBridge.onUpdateProfile(opt);
```

##  <font color=#1E90FF>订单事件-下单</font>

### 接口说明
用于记录用户的下单情况
### 接口定义

```
TDBridge.onPlaceOrder(order)
```
### 参数说明
其中order为Object类型，其具体参数如下：  

|参数			|类型	|是否必须	|说明			|备注																																																												|
|---------------|-------|-----------|---------------|---------------------------|
|orderId		|string	|是			|订单id			|																																																													|
|amount			|number	|是			|订单金额		|单位为货币类型的最小单位。即如果是人民币，则单位为分																																																|
|currencyType	|string	|是			|货币类型，如CNY|限定于3位长度的字符串。请使用国际标准组织 ISO4217 中规范的 3 位字母代码标记货币类型。<br>例：人民币 CNY；美元 USD；欧元 EUR；如果您使用其他自定义等价物作为现金，亦可使用 ISO4217 中没有的 3 位字母组合传入货币类型，我们会在报表页面中提供汇率设定功能|

### 示例代码
```
let order={
			orderId:"orderId123456",
			amount:1000,
			currencyType:"CNY"
			};
TDBridge.onPlaceOrder(order);
```

##  <font color=#1E90FF>订单事件-支付成功</font>

### 接口说明

用于记录用户订单支付情况，建议在用户完成支付之后调用。  

### 接口定义

```
TDBridge.onOrderPaySucc(order)
```
### 参数说明

其中order为Object类型，其具体参数如下：  

|参数			|类型			|是否必须|说明		|备注							|
|---------------|-------		|-----------|---------------|---------------------------	|
|orderId		|string			|是		|订单id		|								|
|amount			|number			|是		|订单金额	|单位为货币类型的最小单位。即如果是人民币，则单位为分|
|currencyType	|string			|是		|                                               |
|paymentType	|string	|否			|支付类型，如支付宝（alipay）	|

### 示例代码
```
let order={
			orderId:"orderId1222222",
			amount:1002,
			currencyType:"CNY",
			paymentType:"weixin"
			};
TDBridge.onOrderPaySucc(order);
```

##  <font color=#1E90FF>订单事件-取消订单</font>


### 接口说明

记录用户取消订单的情况。 

### 接口定义

```
TDBridge.onCancelOrder(order)
```
### 参数说明

其中order为Object类型，其具体参数如下：  

|参数			|类型	|是否必须	|说明			|备注																																																												|
|---------------|-------|-----------|---------------|---------------------------|
|orderId		|string	|是			|订单id			|																																																													|
|amount			|number	|是			|订单金额		|单位为货币类型的最小单位。即如果是人民币，则单位为分		|
|currencyType	|string	|是			|货币类型，如CNY||

### 示例代码
```
let order={
			orderId:"orderId66666",
			amount:9999,
			currencyType:"CNY",
			};
TDBridge.onCancelOrder(order);

```

##  <font color=#1E90FF>微信专属-分享</font>

## 接口说明

在用户发起分享时，调用此接口，用于记录分享事件。

## 接口定义

```
TDBridge.onShare(param);
```
### 参数说明

param为object类型，具体参数如下：  

|参数	|类型	|是否必须	|描述			|
|-------|-------|-----------|---------------|
|title	|string	|是			|分享内容的标题	|
|path	|string	|是			|分享路径		|
|desc	|string	|否			|分享内容描述	|

### 示例代码

```
TDBridge.onShare({
				title:"测试分享标题A",
				path:"pages/index/index"
			});
```

##  <font color=#1E90FF>微信专属-下拉刷新</font>

### 接口说明

用户对页面进行下拉刷新操作时，可以调用此接口记录下拉刷新事件。

### 接口定义

在需要记录下拉刷新的页面中，回调下面方法即可：

```
TDBridge.mpPullDownRefresh();

```


### 示例代码

```
onPullDownRefresh() {
	TDBridge.mpPullDownRefresh();
	},
		
```


##  <font color=#1E90FF>微信专属-页面触底</font>

### 接口说明

用户滑动页面到达页面底部时，可以调用此接口记录页面触底事件。

### 接口定义
 
在需要记录页面触底事件的页面中，调用下面接口即可：

```
TDBridge.mpReachBottom();

```



### 示例代码

```
onReachBottom() {
	TDBridge.mpReachBottom();
	},

```

##  <font color=#1E90FF>App专属-查看商品</font>

### 接口说明

在用户浏览一个商品详情时调用此接口。

### 接口定义
```
TDBridge.onViewItem(item);

```
### 参数说明

其中item为object类型，具体参数如下：

| 参数      | 类型   | 是否必选 | 描述                                 |
| --------- | ------ | -------- | ------------------------------------ |
| itemId    | string | 否       | 商品ID，支持英文、数字、符号         |
| category  | string | 否       | 商品类别，支持中文、英文、数字、符号 |
| name      | string | 否       | 商品名称，支持中文、英文、数字、符号 |
| unitPrice | number | 否       | 商品单价                             |


### 示例代码

```
let item ={
	 itemId:"item01",
	 category:"item_category", 
	 name: "item_name",
	 unitPrice:1200
}
TDBridge.onViewItem();
```

##  <font color=#1E90FF>App专属-添加到购物车</font>

### 接口说明

在用户将一个商品添加到购物车时调用此接口。

### 接口定义
```
TDBridge.onAddItemToShoppingCart(item);

```
### 参数说明

其中item为object类型，具体参数如下：

| 参数      | 类型   | 是否必选 | 描述                                 |
| --------- | ------ | -------- | ------------------------------------ |
| itemId    | string | 否       | 商品ID，支持英文、数字、符号         |
| category  | string | 否       | 商品类别，支持中文、英文、数字、符号 |
| name      | string | 否       | 商品名称，支持中文、英文、数字、符号 |
| unitPrice | number | 否       | 商品单价                             |
|amount |number |否  |商品数量|



### 示例代码

```
let item ={
	 itemId:"item01",
	 category:"item_category", 
	 name: "item_name",
	 unitPrice:1200,
	 amount:10
}
TDBridge.onAddItemToShoppingCart();
```

##  <font color=#1E90FF>App专属-查看购物车</font>

### 接口说明

在用户浏览购物车内商品时调用此接口。

### 接口定义
```
TDBridge.onViewShoppingCart(shoppingCart);

```
### 参数说明

其中shoppingCart为object类型，其内仅有一个items数组，

即`{items：[]}`,其中items具体参数如下：

| 参数      | 类型   | 是否必选 | 描述                                 |
| --------- | ------ | -------- | ------------------------------------ |
| itemId    | string | 否       | 商品ID，支持英文、数字、符号         |
| category  | string | 否       | 商品类别，支持中文、英文、数字、符号 |
| name      | string | 否       | 商品名称，支持中文、英文、数字、符号 |
| unitPrice | number | 否       | 商品单价                             |
|amount |number |否  |商品数量|



### 示例代码

```
let item ={
	 itemId:"item01",
	 category:"item_category", 
	 name: "item_name",
	 unitPrice:1200,
	 amount:10
}
let shoppingCart={
	items:[item]
} 
TDBridge.onViewShoppingCart(shoppingCart);

```

## <font color=#1E90FF>App专属-创建角色</font>

### 接口说明

在用户创建角色的时候调用此接口。

### 接口定义

```
TDBridge.onCreateRole(name);
```

### 参数说明

| 参数 | 类型   | 是否必选 | 描述                                 |
| ---- | ------ | -------- | ------------------------------------ |
| name | string | 否       | 角色名称，支持中文、英文、数字、符号 |

### 示例代码

```
TDBridge.onCreateRole("role01");
```

## <font color=#1E90FF>App专属-通过关卡</font>

### 接口说明

在用户通过设定的关卡的时候调用此接口。

### 接口定义

```
TDBridge.onLevelPass(opt);
```

### 参数说明

opt为object类型，其中具体参数如下：

| 参数      | 类型   | 是否必选 | 描述                             |
| --------- | ------ | -------- | -------------------------------- |
| profileId | string | 否       | 用户账号ID，支持英文、数字、符号 |
| levelId   | string | 否       | 关卡ID，支持英文、数字、符号     |

### 示例代码

```
let opt = {
	profileId:"user01",
	levelId:"10"
}
TDBridge.onLevelPass(opt);
```

## <font color=#1E90FF>App专属-完成新手教程</font>

### 接口说明

在用户完成了新手教程的时候调用此接口。

### 接口定义

```
TDBridge.onGuideFinished(opt);
```

### 参数说明

其中opt为object类型，其中具体参数如下：

| 参数      | 类型   | 是否必选 | 描述                                 |
| --------- | ------ | -------- | ------------------------------------ |
| profileId | string | 否       | 用户账号ID，支持英文、数字、符号     |
| content   | string | 否       | 教程信息，支持中文、英文、数字、符号 |

### 示例代码

```
let opt = {
	profileId:"user01",
	content:"新手任务完成"
}
TDBridge.onGuideFinished(opt);
```

## <font color=#1E90FF>App专属-解锁成就</font>

### 接口说明

在用户解锁成就的时候调用此接口。

### 接口定义

```
TDBridge.onAchievementUnlock(opt);
```

### 参数说明
opt为object类型，其中具体参数如下：

| 参数          | 类型   | 是否必选 | 描述                             |
| ------------- | ------ | -------- | -------------------------------- |
| profileId     | string | 否       | 用户账号ID，支持英文、数字、符号 |
| achievementId | string | 否       | 成就ID，支持英文、数字、符号     |

### 示例代码

```
let opt = {
	profileId:"user01",
	achievementId:"td_123456"
}
TDBridge.onAchievementUnlock("user01", "td_123456");
```
## <font color=#1E90FF>App专属-获取设备ID</font>

### 接口说明

获取设备的唯一id

### 接口定义

```js
TDBridge.getDeviceId();
```

### 参数说明

无

### 示例代码

```js
var tdid = TDBridge.getDeviceId();
```

## <font color=#1E90FF>App专属-获取OAID（仅Android）</font>

### 接口说明

通过调用 TDBridge 插件的 getOAID 接口获取 Android 10 及以上设备的 OAID。

需要注意的是，这个接口建议您在初始化SDK之后的 3-5 秒再去调用，以避免您获取到 null 值。

### 接口定义

```js
TDBridge.getOAID();
```

### 参数说明

无

### 示例代码

```js
var oaid = TDBridge.getOAID();
```

## <font color=#1E90FF>App专属-后台使用时长（仅iOS）</font>

### 接口说明

默认获取的是前台使用时长。如需获取后台使用时长，可通过调用以下接口设置。

调用该接口后，用户的使用时长指标包含前台使用时长和后台使用时长。

**注意事项：**该接口需要在SDK初始化方法之前调用。

### 接口定义

```js
TDBridge.backgroundSessionEnabled();
```

### 参数说明

无

### 示例代码

```js
TDBridge.backgroundSessionEnabled();

TDBridge.onLaunch({
	.......
});
```

## <font color=#1E90FF>App专属-自定义位置（仅iOS）</font>

### 接口说明

TalkingData 默认使用设备中收取的 MCC（移动国家码）和用户联网 IP 来判定用户的地区，与地区相关的数据会有一定误差。

如果您的应用会使用用户的位置信息，可通过接口将信息提交至 TalkingData 数据中，可使您获得更加精准的数据报表。

### 接口定义

```js
TDBridge.setLocation(latitude, longitude);
```

### 参数说明

| 参数      | 类型   | 是否必选 | 描述 |
| --------- | ------ | -------- | ---- |
| latitude  | number | 是       | 纬度 |
| longitude | number | 是       | 经度 |

### 示例代码

```js
TDBridge.setLocation(39.942, 116.435);
```

class EventEmitter {
  _stores = {};

  on(event, fn, ctx) {
    if (typeof fn !== 'function') {
      throw new Error('Listener must be a function');
    }

    this._stores[event] = this._stores[event] || [];
    this._stores[event].push({ cb: fn, ctx: ctx });
  }

  emit(event, data){
    this._stores[event] = this._stores[event] || [];
    const store = [...this._stores[event]];
    const eventData = { eventCode: event, data };

    for (const handler of store) {
      handler.cb.call(handler.ctx, eventData);
    }
  }

  off(event, fn) {
    if (!event) {
      this._stores = {};
      return;
    }

    const store = this._stores[event];
    if (!store) return;

    if (!fn) {
      delete this._stores[event];
      return;
    }

    for (let i = store.length - 1; i >= 0; i--) {
      if (store[i].cb === fn) {
        store.splice(i, 1);
        break;
      }
    }
  }
}

export default EventEmitter;

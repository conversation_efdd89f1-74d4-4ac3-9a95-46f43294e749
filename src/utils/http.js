var config = require("./config.js"); //统一的网络请求方法
var util = require("./util.js")
import store from '../store'

import {
	AppType
} from './constant.js'

import i18n from '../main.js'

function request (params) {
  try {
    // 全局变量
    var globalData = getApp().globalData; // 如果正在进行登陆，就将非登陆请求放在队列中等待登陆完毕后进行调用
    // 刷新token
    if (!params.login && !globalData.isLanding && !params.isRefreshing) {
      params = util.refreshToken(params)
    }
    const appType = uni.getStorageSync('appType')
    if ((appType === AppType.MP || appType === AppType.MINI)
          && !params.login && globalData.isLanding && !params.isRefreshing) {
      globalData.requestQueue.push(params);
      return
    }
    if (Object.prototype.toString.call(params.data) == '[object Array]') {
      params.data = JSON.stringify(params.data);
    } else if (Object.prototype.toString.call(params.data) == '[object Number]') {
      params.data = params.data + '';
    }
  } catch (error) {
    console.log(error);
  }

  const header = {
    'content-type': params.contentType ? params.contentType : 'application/json;charset=utf-8',
    // 'content-type': params.method == "GET" ? 'application/x-www-form-urlencoded' : 'application/json;charset=utf-8',
    'locale': wx.getStorageSync('lang') || 'zh_CN',
    'AppType': 'h5',
    ...(params.header || {})
  };
  if (!params.notoken) {
    header.Authorization = params.overWriteToken? params.overWriteToken : uni.getStorageSync('token')
  }

  store.dispatch('addRequestNum')

	uni.request({
		url: (params.domain ? params.domain : config.domain) + params.url,
		//接口请求地址
		data: params.data,
		header,
		method: params.method == undefined ? "POST" : params.method,
		dataType: 'json',
		responseType: params.responseType == undefined ? 'text' : params.responseType,
		success: function (res) {
			if (res.statusCode == 200) {
				//如果有定义了params.callBack，则调用 params.callBack(res.data)
				if (params.callBack) {
					params.callBack(res.data);
				}
			} else if (res.statusCode == 500) {
				uni.hideLoading()
				setTimeout(() => {
          const msgFromServer = res.data?.description
					uni.showToast({
						title: msgFromServer || i18n.t('index.serverWrong'),
						icon: "none"
					});
				}, 1);
        if (params.errCallBack) {
					params.errCallBack(res);
				}
			} else if (res.statusCode == 401) {
				uni.removeStorageSync('loginResult');
				uni.removeStorageSync('token');
				uni.removeStorageSync('hadBindUser');
				uni.removeStorageSync('code');
				uni.removeStorageSync('userInfo');
				uni.removeStorageSync('expiresTimeStamp');
				// #ifdef H5
				const ua = navigator.userAgent.toLowerCase();
				if (ua.search(/MicroMessenger/i) > -1) uni.setStorageSync('appType', AppType.MP)
				// #endif
				uni.hideLoading();
				const pages = getCurrentPages()
        const currentPage = pages.length ? pages[pages.length-1].$page.fullPath : '/pages/index/index'
        if (!util.isLoginPage(currentPage)) {
          uni.setStorageSync('routeUrlAfterLogin', currentPage)
        }
				if (!params.dontTrunLogin) {
					// if (uni.getStorageSync('hadLogin')) {
						// #ifdef H5
						uni.showModal({
							title: i18n.t('index.tips'),
							content: i18n.t('index.loginExpired'),
							cancelText: i18n.t('index.cancel'),
							confirmText: i18n.t('index.confirm'),
							success: res => {
								if (res.confirm) {
									uni.navigateTo({
										url: 'pages/login/login'
									})
								} else {
									let router = getCurrentPages()
									if(router[0].route === 'pages/basket/basket'){
										uni.switchTab({
											url: '/pages/index/index'
										})
									} else {
										uni.navigateBack(0)
									}
								}
							}
						})
						// #endif
					// } else {
						// #ifdef MP-WEIXIN
						uni.redirectTo({
							url: '/pages/login/login'
						})
						// #endif
					// }
				}
				// 如果有定义了params.errCallBack，则调用 params.errCallBack(res.data)
				if (params.errCallBack) {
					params.errCallBack(res);
				}

			} else if (res.statusCode == 400 && !params.errCallBack) {
				uni.hideLoading();
				setTimeout(() => {
					uni.showToast({
						title: res.data,
						icon: "none"
					});
				}, 1);
			} else {
				//如果有定义了params.errCallBack，则调用 params.errCallBack(res.data)
        uni.hideLoading();
				if (params.errCallBack) {
					params.errCallBack(res);
				}
			}
		},
		fail: function (err) {
			uni.hideLoading();
			if (err.errMsg == 'request:fail abort') {
				console.log('请求被取消啦~')
				return
			}
		},
    complete: function () {
      store.dispatch('reduceRequestNum')
    }
	});
}


/**
 * 上传文件统一接口
 */
function upload (params) {
	wx.uploadFile({
		url: config.domain + params.url,
		filePath: params.filePath,
		name: params.name,
		header: {
			'Authorization': params.login ? undefined : wx.getStorageSync('token')
		},
		dataType: 'json',
		responseType: params.responseType == undefined ? 'json' : params.responseType,
		success: function (res) {
			if (res.statusCode == 200) {
				//如果有定义了params.callBack，则调用 params.callBack(res.data)
				if (params.callBack) {
					params.callBack(res.data);
				}
			} else {
				uni.showToast({
					title: i18n.t('index.serverWrong'),
					icon: "none"
				});
			}
		},
		fail: function (err) {
			uni.hideLoading();
		}
	});
}

// 更新用户头像昵称
function updateUserInfo () {
	wx.getUserInfo({
		success: res => {
			var userInfo = JSON.parse(res.rawData);
			request({
				url: "/p/user/setUserInfo",
				method: "PUT",
				data: {
					avatarUrl: userInfo.avatarUrl,
					nickName: userInfo.nickName
				}
			});
		}
	});
}

/**
 * 获取购物车商品数量
 */
function getCartCount () {
	if (!wx.getStorageSync('token')) {
		util.removeTabBadge()
		return
	}
	var params = {
		url: "/p/shopCart/prodCount",
		method: "GET",
		dontTrunLogin: true,
		data: {},
		callBack: function (res) {
			if (res > 0) {
				wx.setTabBarBadge({
					index: 2,
					text: res > 99 ? "99+" : res + ""
				});
				var app = getApp().globalData;
				getApp().globalData.totalCartCount = res;
			} else {
				util.removeTabBadge()
				var app = getApp().globalData;
				getApp().globalData.totalCartCount = 0;
			}
		}
	};
	request(params);
}


function mpAuthLogin (page, needCode) {
  // 在微信环境打开,请求公众号网页登陆
  var redirectUrl = null

  if (!page || page === config.domainAddress) {
    redirectUrl = window.location.href
  } else {
    redirectUrl = config.domainAddress + page
	}
	var scope = 'snsapi_userinfo'
	window.location.href = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + config.officalAccountAppId +
		'&redirect_uri=' +
		encodeURIComponent(redirectUrl) + '&response_type=code&scope=' + scope + '&state=' + (needCode ? 'needCode'
    : 'unNeedCode') +
    '#wechat_redirect'
}

/**
 * 用户操作记录
 */
var saveLog = function (flowData, visitType) {
	var flowAnalysisLogDto = Object.assign(flowData)
	flowAnalysisLogDto.visitType = visitType
	var params = {
		url: '/p/flowAnalysisLog',
		method: 'POST',
		data: flowAnalysisLogDto,
    dontTrunLogin: true,
		callBack: () => {
			// console.log(params.data)
		}
	}
  const token = uni.getStorageSync('token')
  if (token) {
    request(params)
  }
}


exports.request = request;
exports.getCartCount = getCartCount;
exports.updateUserInfo = updateUserInfo;
exports.upload = upload;
exports.mpAuthLogin = mpAuthLogin;
exports.saveLog = saveLog;

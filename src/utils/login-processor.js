import http from '@/utils/http'
import utils from './util';
import config from '@/utils/config.js'
import { AppType } from "./constant";
import store from '@/store'
// 获取bas学校列表
export function getBasOrgList() {
  return new Promise((resolve) => {
    http.request({
      domain: config.wenetUrl,
      url: '/v1/projection/org/visible',
      method: "GET",
      dontTrunLogin: true,
      overWriteToken: '',
      callBack: (res) => {
        if (res) {
          resolve(res)
        }
      },
      errCallBack: (err) => {
        reject(err)
      }
    })
  })
}

// 使用bastoken获取bas用户信息
export function getWenetAccountInfo(token) {
  return new Promise((resolve, reject) => {
    var params = {
      domain: config.wenetUrl,
      url: '/api/v1/account/me',
      method: "GET",
      data: {},
      dontTrunLogin: true,
      overWriteToken: token || uni.getStorageSync('wenetToken'),
      callBack: (res) => {
        resolve(res)
      },
      errCallBack: (err) => {
        reject(err)
      }
    };
    http.request(params);
  })
}

// 缓存wenet账号信息
export function cacheWenetAccountInfo(wenetAccountInfo) {
  /* 保存wenet账号的部分信息，给完善手机号页面或完善扩展信息页面使用 */
  const { person, org } = wenetAccountInfo
  const simpleWenetAccount = {
    uid: person.uid,
    ou: org.ou,
    basUsername: utils.getStudentIdFromWenetAccountInfo(wenetAccountInfo),
    basMobile: person.mobile || '',
  }
  uni.setStorageSync('simpleWenetAccount', simpleWenetAccount)
  uni.setStorageSync('originalWenetAccount', wenetAccountInfo)
  return simpleWenetAccount
}

// 获取用户需要填写的信息
export function getExtendInfoListConfig(ou, orgList) {
  const target = orgList?.find((item) => item.ou == ou)
  if (target) {
    return {
      identificationRule: target.identificationRule,
      studentIdRule: target.studentIdRule,
      addressRule: target.addressRule,
      extendedFileds: target.extendedFileds
    }
  }
}

export function isExtendInfoAllFilled(wenetAccountInfo = {}, orgExtendedInfo = {}) {
  const realname = wenetAccountInfo.identification?.realName
  const idNumber = wenetAccountInfo.identification?.idNumber
  const addressInfo = {
    campus: wenetAccountInfo.person?.initials,
    floor: wenetAccountInfo.person?.carLicense,
    dorm: wenetAccountInfo.person?.roomNumber,
  }
  const studentIdInfo = {
    username: utils.getStudentIdFromWenetAccountInfo(wenetAccountInfo),
  }
  const userExtendInfo = wenetAccountInfo.person?.extendInfoList || []
  const orgExtendFields = orgExtendedInfo.extendedFileds || []
  let result = true
  let reason = ''
  if(orgExtendedInfo.identificationRule !== 'NO_DISPLAY') {
    if(!realname || !idNumber) {
      result = false
      reason = '请填写完整的实名制信息'
    }
  }
  if(orgExtendedInfo.studentIdRule !== 'NO_DISPLAY') {
    if(!studentIdInfo.username) {
      result = false
      reason = '请填写完整的学号信息'
    }
  }
  if(orgExtendedInfo.addressRule !== 'NO_DISPLAY') {
    if(!addressInfo.campus || !addressInfo.floor || !addressInfo.dorm) {
      result = false
      reason = '请填写完整的地址信息'
    }
  }
  for (let i = 0; i < orgExtendFields.length; i++) {
    if (orgExtendFields[i].rule !== 'NO_DISPLAY') {
      const extendInfo = userExtendInfo.find(item => item.name === orgExtendFields[i].name)
      if (!extendInfo?.value) {
        result = false
        reason = `请填写${orgExtendFields[i].name}`
        break;
      }
    }
  }
  return {
    result,
    reason
  }
}

// 检查wenet账号信息是否完整（1. 手机号，2. 学号 3. 其他信息）
export function checkWenetAccountInfo(wenetAccountInfo = {}, orgExtendedInfo = {}) {
  const { person } = wenetAccountInfo
  const res = {
    mobile: true,
    extendInfo: true,
  }
  if (!person?.mobile) {
    res.mobile = false
  }
  const isAllFilledRes = isExtendInfoAllFilled(wenetAccountInfo, orgExtendedInfo)
  res.extendInfo = isAllFilledRes.result
  console.log('需要补充信息', isAllFilledRes.reason)
  return res
}

export function isWenetAccountAvaliable(account) {
  return new Promise((resolve) => {
    http.request({
      domain: config.wenetUrl,
      url: `/api/v2/wx/auth/realname?username=${account}`,
      method: 'POST',
      callBack: () => {
        resolve(false)
      },
      errCallBack: (err) => {
        if (err.data?.code === 1024) {
          resolve(true)
        } else {
          resolve(err)
        }
      }
    });
  })
}

/**
 * 通过bas的学号查询其手机号，或通过bas的手机号查询其学号
 * @param {} param0 
 * @returns 
 */
export function getWenetAnotherAccount({ username, mobile}) {
  const p = {}
  if (username) {
    p.username = username
  }
  if (mobile) {
    p.mobile = mobile
  }
  return new Promise((resolve, reject) => {
    http.request({
      domain: config.wenetUrl,
      url: '/v2/account/query/bind',
      method: 'POST',
      overWriteToken: uni.getStorageSync('wenetToken'),
      data: p,
      callBack: (res) => {
        resolve(res)
      },
      errCallBack: (err) => {
        reject(err)
      }
    })
  })
}


export function getWenetToken(fullUsername, password) {
  return new Promise((resolve, reject) => {
    http.request({
      domain: config.wenetUrl,
      url: `/api/v1/auth?username=${fullUsername}&password=${password}`,
      method: 'POST',
      callBack: (res) => {
        const accessToken = res.access_token;
        const tokenType = 'Bearer'
        resolve(`${tokenType} ${accessToken}`)
      },
      errCallBack: (err) => {
        if (err.data.code?.toString() === '1022') {
          uni.showToast({
            title: '账号或密码错误',
            icon: 'none'
          })
        } 
        reject(err)
      }
    });
  })
}

export function registerAccountToWenet({ studentId, mobile, org, password, realname}) {
  return new Promise((resolve, reject) => {
    var params = {
      domain: config.wenetUrl,
      url: "/v1/account/both/registration.json",
      method: "POST",
      data: {
        studentId,
        mobile,
        org,
        password,
        preRealName: realname
      },
      callBack: res => {
        resolve(res)
      },
      errCallBack: errRes => {
        reject(errRes)
      }
    };
    http.request(params);
  })
}

export function sendRegisterCode(mobile) {
  return new Promise((resolve, reject) => {
    var params = {
      domain: config.wenetUrl,
      url: `/binding-service/api/v1/sms/code/${mobile}`,
      method: "POST",
      callBack: res => {
        resolve(res)
      },
      errCallBack: errRes => {
        reject(errRes)
      }
    };
    http.request(params);
  })
}

export function registerAccountToWenet2({ ou, verifyCode, mobile }) {
  return new Promise((resolve, reject) => {
    var params = {
      domain: config.wenetUrl,
      url: "/binding-service/api/v1/account",
      method: "POST",
      data: {
        organization: ou,
        code: verifyCode,
        username: mobile,
      },
      callBack: res => {
        resolve(res)
      },
      errCallBack: errRes => {
        reject(errRes)
      }
    };
    http.request(params);
  })
}

export function appendStudentIdToWenet(studentId) {
  return new Promise((resolve, reject) => {
    const params = {
      domain: config.wenetUrl,
      url: `/v1/account/bindUser/studentId?studentId=${studentId}`,
      method: "POST",
      overWriteToken: uni.getStorageSync('wenetToken'),
      callBack: res => {
        resolve(res)
      },
      errCallBack: errRes => {
        reject(errRes)
      }
    };
    http.request(params);
  })
}

// 给wenet账号添加手机号，username就是手机号
export function appendMobileToWenet({uid, username, force = false}) {
  return new Promise((resolve, reject) => {
    const params = {
      domain: config.wenetUrl,
      url: `/v1/account/bindUser?uid=${uid}&username=${username}&force=${force}`,
      method: "POST",
      overWriteToken: uni.getStorageSync('wenetToken'),
      callBack: res => {
        resolve(res)
      },
      errCallBack: errRes => {
        reject(errRes)
      }
    };
    http.request(params);
  })
}

export function appendRealnameToWenet(realname) {
  return new Promise((resolve, reject) => {
    const params = {
      domain: config.wenetUrl,
      url: `/v2/account/update/info/step/1`,
      method: "POST",
      data: {
        realName: realname,
      },
      overWriteToken: uni.getStorageSync('wenetToken'),
      callBack: res => {
        resolve(res)
      },
      errCallBack: errRes => {
        reject(errRes)
      }
    };
    http.request(params);
  })
}

/**
 * 微信环境统一登录方法 (小程序)
 */
export const weChatLogin  = () => {
  const appType = uni.getStorageSync('appType')
	if (appType === AppType.MINI) {
		// 微信小程序
		// 请求微信接口获取 code
		wx.login({
			success: ({ code }) => {
        const params = {
          login: true,
          url: '/api/v2/quick/login',
          method: 'POST',
          data: {
            code
          },
          callBack: async (res) => {
            if (res) {
              utils.saveLoginSuccessData(res)
              const wenetToken = res.basToken
              const wenetAccountInfo = await getWenetAccountInfo(wenetToken)
              const orgList  = await getBasOrgList();
              const orgExtendedInfo = getExtendInfoListConfig(wenetAccountInfo.org.ou, orgList)
              const checkRes = checkWenetAccountInfo(wenetAccountInfo, orgExtendedInfo)
              if (!checkRes.extendInfo) {
                uni.reLaunch({
                  url: '/packageuser/pages/improve-info/improve-info'
                })
                return
              }
              utils.routeAfterLogin(true)
            } else {

            }
          },
          errCallBack: errRes => {
          }
        }
        http.request(params)
			}
		})
		return
	}
}

export const doWechatOfficalAccountOAuth = () => {
  const wechatSelfUrl = config.wechatSelfUrl
  window.location.href = wechatSelfUrl + `/wechat-oauth?redirect_uri=${encodeURIComponent(window.location.href)}`
}

export const updateWenetPassword = (password, cfmPassword) => {
  return new Promise((resolve, reject) => {
    const wenetToken = uni.getStorageSync('wenetToken')
    http.request({
      domain: config.wenetUrl,
      url: `/api/v1/auth/password/wx?confirm=${cfmPassword}&newPassword=${password}`,
      overWriteToken: wenetToken,
      method: 'POST',
      callBack: async (res) => {
        resolve(res)
      },
      errCallBack: (err) => {
        reject(err)
      }
    });
  })
}


import http from '@/utils/http.js'
import config from '@/utils/config'

const legacyErrorCode = {
  1019: '旧密码错误',
  1020: '实名制信息不存在',
  1023: '学号已存在',
  1036: '身份证号已存在',
  1021: '实名制信息已存在',
  2001: '该手机号不在服务范围内，请联系对应运营商解决',
  2002: '该手机号不在服务范围内，请联系对应运营商解决',
  2003: '无效验证码',
  2004: '短信发送失败',
  2012: '新卡手机号码错误',
  2029: '学号信息有误，请确认后重新提交。',
  2030: '该学号已被绑定，如有疑问请联系客服',
  3009: '超过购买限制数',
  3010: '已购买过同类产品，并且超过了数量限制',
  3013: '绑定的运营商账号密码不能为空',
  5401: '请输入短信验证码',
  6001: '请选择正确的用户类型',
  6002: '该手机号已被其他账号绑定，请联系客服解决',
  6003: '该宽带账号已被其他账号绑定，请联系客服解决',
  7006: '新卡验证不存在',
  7008: '新卡已验证',
  7009: '新卡不能激活',
  8001: '激活码已存在',
  8002: '兑换码不存在',
  8003: '兑换码已经被使用不能重复使用',
  11009: '验证码输入错误',
  14002: '宽带手机不存在',
  14003: '手机号已绑定',
  16001: '宽带账号或密码不正确',
  16002: '与当前绑定账号一致，请更换',
  16003: '宽带账号已被绑定，请更换',
};

/**
 * 调用wenet api
 * 如果调用的是 gateway 的接口，直接写 url 即可
 * 如果调用的是“绑定服务”的接口，即/binding-service 开头的接口，需要将 url 拼接上 /binding-service 前缀
 * @param {*} param0 
 * @returns 
 */
function callWenetApi({url, method, data}) {
  const wenetToken = uni.getStorageSync('wenetToken')
  return new Promise((resolve, reject) => {
    http.request({
      domain: config.wenetUrl,
      url,
      method: method || 'GET',
      data: data || {},
      overWriteToken: wenetToken,
      errCallBack: (err) => {
        const codeTranslate = legacyErrorCode[err.data?.code]
        uni.showToast({
          title: codeTranslate || err.message || err.data?.description,
          icon: 'none'
        })
        reject(err)
      },
      callBack: (res) => {
        resolve(res)
      }
    })
  })
}

export default callWenetApi
import http from './http'
const templeteIds = {
  // COUPON_ARRIVAL: '4ouKwesXrOqliJw4SFfd65dG4dn-OeURqcfkJNO_ewk',
};

export const getSubscribeTemplates = async () => {
  return new Promise((resolve, reject) => {
    http.request({
      url: "/wx/mp/getAllWxMpTemp",
      method: "GET",
      callBack: (res) => {
        if (res && res.length) {
          res.forEach((item) => {
            console.log(item);
            const itemType = item.type?.trim?.()
            if (itemType) {
              templeteIds[itemType] = item.priTmplId
            }
          })
        }
        resolve();
      },
      errCallBack: (err) => {
        reject(err)
      }
    });
  })
}

/**
 * 服务到期提醒 + 优惠券到账通知
 * 触发位置：1. 我的->待支付
 * 触发位置：2. 提交订单
 */
export const requestServiceExpirationMessage = async ({ success, fail, complete}) => {
  // #ifdef MP-WEIXIN
  if (!templeteIds.SERVICE_EXPIRE || !templeteIds.COUPON_ARRIVAL) {
    await getSubscribeTemplates();
  }
  if (templeteIds.SERVICE_EXPIRE && templeteIds.COUPON_ARRIVAL) {
    uni.requestSubscribeMessage({
      tmplIds: [templeteIds.SERVICE_EXPIRE, templeteIds.COUPON_ARRIVAL],
      success,
      fail,
      complete
    })
  } else {
    complete?.()
  }
  // #endif
  // #ifndef MP-WEIXIN
  complete?.()
  // #endif
}

/**
 * 优惠券到账通知
 * 触发位置：1. 领券中心-领取优惠券，首页领取优惠券卡片-领取按钮
 * 触发位置：2. 提交订单
 */
export const requestCouponArrivalNotification = async ({ success, fail, complete}) => {
  // #ifdef MP-WEIXIN
  if (!templeteIds.COUPON_ARRIVAL) {
    await getSubscribeTemplates();
  }
  if (templeteIds.COUPON_ARRIVAL) {
    uni.requestSubscribeMessage({
      tmplIds: [templeteIds.COUPON_ARRIVAL],
      success,
      fail,
      complete
    })
  } else {
    complete?.()
  }
  // #endif
  // #ifndef MP-WEIXIN
  complete?.()
  // #endif
}
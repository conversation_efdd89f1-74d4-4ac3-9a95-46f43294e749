var config = require("./config.js");
var http = require('./http.js')
import store from '../store'
import {
	PayType,
	AppType
} from "../utils/constant.js";
const formatTime = date => {
	const year = date.getFullYear();
	const month = date.getMonth() + 1;
	const day = date.getDate();
	const hour = date.getHours();
	const minute = date.getMinutes();
	const second = date.getSeconds();
	return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':');
};

export const formatTimeExceptSecond = date => {
	const year = date.getFullYear();
	const month = date.getMonth() + 1;
	const day = date.getDate();
	const hour = date.getHours();
	const minute = date.getMinutes();
	return [year, month, day].map(formatNumber).join('.') + ' ' + [hour, minute].map(formatNumber).join(':');
}

const formatNumber = n => {
	n = n.toString();
	return n[1] ? n : '0' + n;
};

const formatHtml = content => {
	if (!content) {
		return
	}
	content = content.replace(/\<p/gi,'<p style="max-width:100% !important;word-wrap:break-word;word-break:break-word;" ');
	content = content.replace(/\<img/gi,'<img style="width:100% !important;height:auto !important;margin:0;display:flex;" ');
	content = content.replace(/style="/gi,'style="max-width:100% !important;table-layout:fixed;word-wrap:break-word;word-break:break-word;');
	content = content.replace(/\<table/gi,'<table style="table-layout:fixed;word-wrap:break-word;word-break:break-word;" ');
	// content = content.replace(/\<td/gi,'<td cellspacing="0" cellpadding="0" border="0" style="display:block;vertical-align:top;margin: 0px; padding: 0px; border: 0px;outline-width:0px;"');
	content = content.replace(/\<td/gi,'<td cellspacing="0" cellpadding="0" style="border-width:1px; border-style:solid; border-color:#666; margin: 0px; padding: 0px;"');
	content = content.replace(/width=/gi, 'sss=');
	content = content.replace(/height=/gi, 'sss=');
	content = content.replace(/ \/\>/gi,' style="max-width:100% !important;height:auto !important;margin:0;display:block;" \/\>');
	return content;
};

const endOfStartTime = (startTime, endTime) => {
	let result = {
		day: '00',
		hou: '00',
		min: '00',
		sec: '00',
		signs: 0
	};

	if (endTime - startTime > 0) {
		let time = (endTime - startTime) / 1000; // 获取天、时、分、秒
		let day = parseInt(time / (60 * 60 * 24));
		let hou = parseInt(time % (60 * 60 * 24) / 3600);
		let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
		let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
		result = {
			day: `${timeFormat(day)}`,
			hou: `${timeFormat(hou)}`,
			min: `${timeFormat(min)}`,
			sec: `${timeFormat(sec)}`,
			signs: 1
		};
	}

	return result;
}; // 小于10的格式化函数


const timeFormat = times => {
	return times < 10 ? '0' + times : times;
};

const dateToTimestamp = dateStr => {
	if (!dateStr) {
		return '';
	}

	let newDataStr = dateStr.replace(/\.|\-/g, '/');
	let date = new Date(newDataStr);
	let timestamp = date.getTime();
	return timestamp;
};

/**
 * 初始化支付类型
 */
const initPayType = () => {
	const appType = uni.getStorageSync('appType')
	let payTypeInfo = {
		payType: PayType.ALIPAY_H5,
		payTypeStr: 'aliPay'
	}
	// #ifdef H5
	if(appType === AppType.MP){
		// 如果是微信环境打开,使用公众号支付
		payTypeInfo.payType  = PayType.WECHATPAY_MP
		payTypeInfo.payTypeStr = 'wechatPay'
	}
	// #endif

	// #ifdef APP-PLUS
	payTypeInfo.payType = PayType.ALIPAY_APP
	payTypeInfo.payTypeStr = 'aliPay'
	// #endif

	// #ifdef MP-WEIXIN
	payTypeInfo.payType = PayType.WECHATPAY
	payTypeInfo.payTypeStr = 'wechatPay'
	// #endif
	return payTypeInfo
}


// 检查是否授权
const checkAuthInfo = fn => {
	uni.hideLoading()
  // eslint-disable-next-line no-undef
  const pages = getCurrentPages()
  const currentPage = pages.length ? pages[pages.length - 1].$page.fullPath : '/pages/index/index';
  let routeUrlAfterLogin = currentPage
  if (currentPage.includes('login')) {
    routeUrlAfterLogin = '/pages/index/index'
  }
  uni.setStorageSync('routeUrlAfterLogin', routeUrlAfterLogin)
  if (uni.getStorageSync('token')) {
    fn()
    return
  }
  if (pages[0] && pages[0].route === 'pages/cart/cart') {
    return
  }
  if (uni.getStorageSync('tempUid')) {
    // uni.navigateTo({
    //   url: '/pages/register/register'
    // })
	  uni.navigateTo({
		  url: '/pages/login/login'
	  })
  } else { // 没有tempUid
	  //#ifdef H5
	  uni.navigateTo({
		  url: '/pages/login/login'
	  })
	  //#endif
	  //#ifdef MP-WEIXIN
	  uni.navigateTo({
		  url: '/pages/login/login'
	  })
	  //#endif
	  //#ifdef APP-PLUS
	  uni.navigateTo({
	  		  url: '/pages/accountLogin/accountLogin'
	  })
	  //#endif
  }
};

// 函数参数必须是字符串，因为二代身份证号码是十八位，而在javascript中，十八位的数值会超出计算范围，造成不精确的结果，导致最后两位和计算的值不一致，从而该函数出现错误。
// 详情查看javascript的数值范围
function checkIDCard(idcode){
  // 加权因子
  var weight_factor = [7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2];
  // 校验码
  var check_code = ['1', '0', 'X' , '9', '8', '7', '6', '5', '4', '3', '2'];

  var code = idcode + "";
  var last = idcode[17];//最后一位

  var seventeen = code.substring(0,17);

  // ISO 7064:1983.MOD 11-2
  // 判断最后一位校验码是否正确
  var arr = seventeen.split("");
  var len = arr.length;
  var num = 0;
  for(var i = 0; i < len; i++){
      num = num + arr[i] * weight_factor[i];
  }

  // 获取余数
  var resisue = num%11;
  var last_no = check_code[resisue];

  // 格式的正则
  // 正则思路
  /*
  第一位不可能是0
  第二位到第六位可以是0-9
  第七位到第十位是年份，所以七八位为19或者20
  十一位和十二位是月份，这两位是01-12之间的数值
  十三位和十四位是日期，是从01-31之间的数值
  十五，十六，十七都是数字0-9
  十八位可能是数字0-9，也可能是X
  */
  var idcard_patter = /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/;

  // 判断格式是否正确
  var format = idcard_patter.test(idcode);

  // 返回验证结果，校验码和格式同时正确才算是合法的身份证号码
  return last === last_no && format ? true : false;
}

/**
 * 手机号正则校验
 */
const checkPhoneNumber = (phoneNumber) => {
	var regexp = /^[1][0-9]{10}$/
	return regexp.test(phoneNumber)
};

/**
 * 用户名正则校验
 */
const checkUserName = (userName) => {
	var regexp = /^([a-zA-Z0-9_]{4,16})$/
	return regexp.test(userName)
}

/**
 * 用户密码校验
 * @returns {Boolean}
 */
const checkPassword = (password) => {
	const regexp = new RegExp("^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{6,20}$")
	return regexp.test(password)
}

/**
 * 用户邮箱校验
 * @returns {Boolean}
 */
const checkEmail = (email) => {
	const regexp = new RegExp("^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$")
	return regexp.test(email)
}

/**
 * 获取链接上的参数
 */
const getUrlKey = (name) => {
	return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || ['', ''])[1]
		.replace(/\+/g, '%20')) || null
}

/**
 * 处理参数
 *
 * @param {obj}  to: 目标页面路由信息
 */
const getPageInfo = (to) => {
	var path = to.path
	var query = to.query
	var pageInfo = {}
	if (path == '/pages/index/index' || path == '' || path == '*') {
		pageInfo.pageId = 1
	} else if (path == '/pages/prod/prod' && !query.seckillId && !query.groupActivityId) {
		pageInfo.pageId = query.bannerEnter == '1' ? 2 : 3
		pageInfo.bizType = 0 // biztype  0为普通商品  1拼团商品 2为秒杀商品
		pageInfo.bizData = query.prodid
	} else if (path == '/pages/snapUpDetail/snapUpDetail' && query.seckillId) {
		pageInfo.pageId = 4
		pageInfo.bizType = 2
		pageInfo.bizData = query.seckillId
	} else if (path == '/pages/prod/prod' && query.groupActivityId) {
		pageInfo.pageId = 5
		pageInfo.bizType = 1
		pageInfo.bizData = query.prodid
	} else if (path == '/pages/category/category') {
		pageInfo.pageId = 6
	} else if (path == '/pages/specialDiscount/specialDiscount') {
		pageInfo.pageId = 7
	} else if (path == '/pages/basket/basket') {
		pageInfo.pageId = 8
	} else if (path == '/pages/order-detail/order-detail') {
		pageInfo.pageId = 9
	} else if (path == '/pages/payWay/payWay') {
		pageInfo.pageId = 10
		pageInfo.bizData = query.orderNumbers
	} else if (path == '/pages/pay-result/pay-result') {
		pageInfo.pageId = 11
		pageInfo.bizData = query.orderNumbers
	}	else if (path == '/pages/user/user') {
		pageInfo.pageId = 12
	} else if (path == '/pages/orderList/orderList') {
		pageInfo.pageId = 13
	} else if (path == '/pages/ics/ics') {
		pageInfo.pageId = 14
	}

	return pageInfo
}

/**
 * 记录页面点击次数
 */
const tapLog = (visitType, {orderNumbers,isPayRes, prodNum, shopId} = {}) => {

	// 更新会话时间
	uni.setStorageSync('sessionTimeStamp', new Date().getTime())
	var flowAnalysisLogDto = uni.getStorageSync('flowAnalysisLogDto')

	flowAnalysisLogDto.visitType = visitType || 3
	flowAnalysisLogDto.nums = prodNum || undefined
  if (shopId) {
    flowAnalysisLogDto.shopId = shopId
  }
	// 支付页面单独处理
	if (orderNumbers) {
		visitType = 1
		flowAnalysisLogDto.orderNumbers = orderNumbers
		flowAnalysisLogDto.step = flowAnalysisLogDto.step + 1
		flowAnalysisLogDto.pageId = !isPayRes ? 10 : 11
	}
	http.saveLog(flowAnalysisLogDto, visitType)
}

/**
 * 获取唯一值
 */
const getUuid = () => {
	var s = [];
	var hexDigits = "0123456789abcdef";
	for (var i = 0; i < 36; i++) {
		s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
	}
	s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
	s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
	s[8] = s[13] = s[18] = s[23] = "-";

	var uuid = s.join("");
	return uuid;
}

/**
 *	时间戳与当前时间比较，是否为同一天
 */
const checkSameDay = (timestamp) => {
	var nt = new Date (new Date().getTime())
	var ot = new Date(timestamp)
	return ot.getFullYear() + ot.getMonth() + ot.getDate() == nt.getFullYear() + nt.getMonth() + nt.getDate()

}

/**
 * 函数节流
 * @param fn
 * @param wait
 * @returns {Function}
 * @constructor
 */
const throttle = (fn, wait) => {
	var timer = null;
	return function () {
		var context = this;
		var args = arguments;
		if (!timer) {
			timer = setTimeout(function () {
				fn.apply(context, args);
				timer = null;
			}, wait)
		}
	}
}

/**
 * 防抖
 * @param fn
 * @param wait
 * @returns {Function}
 * @constructor
 */
const debounce = (fn, t) => {
  let delay = t || 300
  let timer
  return function () {
    let args = arguments
    if (timer) {
      clearTimeout(timer)
    }
    let callNow = !timer
    timer = setTimeout(() => {
      timer = null
    }, delay)
    if (callNow) fn.apply(this, args)
  }
}


/**
 * 时间戳转化为年 月 日 时 分 秒
 * number: 传入时间戳
 * format：返回格式，支持自定义，但参数必须与formateArr里保持一致
*/
const tsToDate = (number,format)=> {

  var formateArr  = ['Y','M','D','h','m','s'];
  var returnArr   = [];

  var date = new Date(number);
  returnArr.push(date.getFullYear());
  returnArr.push(date.getMonth() + 1 < 10? '0' + (date.getMonth() + 1): date.getMonth() + 1);
  returnArr.push(date.getDate() < 10? '0' + date.getDate() : date.getDate());

  returnArr.push(date.getHours() < 10? '0' + date.getHours() : date.getHours());
  returnArr.push(date.getMinutes() < 10? '0' + date.getMinutes() : date.getMinutes());
  returnArr.push(date.getSeconds() < 10? '0' + date.getSeconds() : date.getSeconds());

  for (var i in returnArr)
  {
    format = format.replace(formateArr[i], returnArr[i]);
  }
  return format;
}

    /**
     * 进行相隔时间判断
     *
     * true 删除显示时间
     * false 保留显示时间
     */
  const timeBeApart = (uppTime, preTime) => {
      if (!uppTime) {
        return false
      }
      var dateDiff = preTime - uppTime// 时间差的毫秒数
      var dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000))// 计算出相差天数
      var leave1 = dateDiff % (24 * 3600 * 1000)    // 计算天数后剩余的毫秒数
      var hours = Math.floor(leave1 / (3600 * 1000))// 计算出小时数
      // 计算相差分钟数
      var leave2 = leave1 % (3600 * 1000)    // 计算小时数后剩余的毫秒数
      var minutes = Math.floor(leave2 / (60 * 1000))// 计算相差分钟数

      // console.log('相差' + dayDiff + '天')
      // console.log('相差' + hours + '小时')
      // console.log('相差' + minutes + '分钟')

      if (dayDiff >= 1 || hours >= 1 || minutes > 4) {
        return false
      } else {
        return true
      }
  }

// 计算两个时间戳相差的小时数
const diffInHours = (curTime, preTime) => {
	if (!curTime || !preTime) {
		return 
	}

	var diffInMilliseconds = Math.abs(curTime - preTime)
  var diffInHours = Math.floor(diffInMilliseconds / 1000 / 3600)
  return diffInHours;
}

/**
 * tabbar国际化
 */
const transTabbar = (isLogin)=> {
  // const tabbarList4Anonymous = [
  //   { text: '首页', iconPath: '/static/images/tabbar/homepage.png', selectedIconPath: '/static/images/tabbar/homepage-sel.png'},
  //   { text: '分类', iconPath: '/static/images/tabbar/category.png', selectedIconPath: '/static/images/tabbar/category-sel.png'},
  //   { text: '会员中心', iconPath: '/static/images/tabbar/vip.png', selectedIconPath: '/static/images/tabbar/vip-sel.png'},
  //   { text: '我的', iconPath: '/static/images/tabbar/user.png', selectedIconPath: '/static/images/tabbar/user-sel.png'}
  // ]
  // const tabbarList4Login = [
  //   { text: '首页', iconPath: '/static/images/tabbar/homepage.png', selectedIconPath: '/static/images/tabbar/homepage-sel.png'},
  //   { text: '分类', iconPath: '/static/images/tabbar/category.png', selectedIconPath: '/static/images/tabbar/category-sel.png'},
  //   { text: '会员中心', iconPath: '/static/images/tabbar/vip.png', selectedIconPath: '/static/images/tabbar/vip-sel.png'},
  //   { text: '我的', iconPath: '/static/images/tabbar/user.png', selectedIconPath: '/static/images/tabbar/user-sel.png'}
  // ]
  // const targetTabbarList = isLogin ? tabbarList4Login : tabbarList4Anonymous
  // targetTabbarList.forEach((item, index) => {
  //   uni.setTabBarItem({
  //     index: index,
  //     text: item.text,
  //     iconPath: item.iconPath,
  //     selectedIconPath: item.selectedIconPath
  //   })
  // })

}

/**
 * 移除购物车Tabbar的数字
 */
const removeTabBadge = () => {
	let pl = ''
	// #ifdef MP-WEIXIN
	pl = 'mp'
	// #endif
	uni.removeTabBarBadge({
		index: pl=='mp' ? 2 : 2
	})
}

/**
 * 定位经纬度换算
 * @param {Obj}  经纬度对象
 * @return {Obj} 转换后的经纬度对象
 */
const locationConversions = (locationObj) => {
	// 转换坐标
	let x_pi = 3.14159265358979324 * 3000.0 / 180.0;
	let x = locationObj.longitude;
	let y = locationObj.latitude;
	let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
	let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
	let lngs = z * Math.cos(theta) + 0.0065;
	let lats = z * Math.sin(theta) + 0.006;

	return {
		latitude: lats,
		longitude: lngs
	}
}

/**
 * 装修页面中的跳转方法
 * @param {Object} item 跳转信息
 */
export const featureRoute = (item) => {
	// item.type: 1商品详情; 2分类页; 3个人中心; 4自定义页面 6新人专区 7免费商品页;
	if (item.type==1) {
		uni.navigateTo({
			url: '/pages/prod/prod?prodid=' + item.link
		})
    return
	}
	// 跳转自定义页面
	if(item.type==2) {
		uni.navigateTo({
			url: '/pages/category/category',
		})
    return
	}
  if (item.type==3) {
    uni.switchTab({
      url: '/pages/user/user',
    })
    return
  }

	// 跳转微页面   使用自定义页面的逻辑
	if(item.type==5) {
		if (!item.link) {
			return
		}
		item.link = item.link.replace(/&amp;/g, '&')
		if (item.link == '/pages/category/category' || item.link == '/pages/basket/basket' || item.link == '/pages/user/user' || item.link == '/pages/index/index') {
			uni.switchTab({
				url: item.link
			})
		} else if (item.link.indexOf('/packageDistribution') > -1 || item.link.indexOf('/packageMemberIntegral') > -1) {
			checkAuthInfo(()=>{
				uni.navigateTo({
					url: item.link
				})
			})
		} else {
			uni.navigateTo({
				url: item.link
			})
		}
    return
	}
	// 跳转自定义页面
	if(item.type==4) {
		if (!item.link) {
			return
		}
		item.link = item.link.replace(/&amp;/g, '&')

    // #ifdef H5
    if (item.link.startsWith('http')) {
      window.location.href = item.link
      return
    }
    // #endif
		if (item.link == '/pages/category/category' || item.link == '/pages/basket/basket' || item.link == '/pages/user/user' || item.link == '/pages/index/index' || item.link == '/pages/vip/vip') {
			uni.switchTab({
				url: item.link
			})
		} else if (item.link.indexOf('/packageDistribution') > -1 || item.link.indexOf('/packageMemberIntegral') > -1) {
			checkAuthInfo(()=>{
				uni.navigateTo({
					url: item.link
				})
			})
		} else {
			uni.navigateTo({
				url: item.link
			})
		}
    return
	}

  uni.navigateTo({
    url: item.link
  })
}

const isObjectEmpty = (obj) => {
  if (obj === null) return true;
  if (obj === undefined) return true;
  if (JSON.stringify(obj) === '{}') {
    return true;
  }
  return false
}

/**
 * 请求系统支付开关
 * @param {Boolean} forcedRefresh 是否强制刷新数据
 */
 const getSysPaySwitch = (forcedRefresh)=> {
	// 获取缓存中的配置
	const paySwitchInfo = uni.getStorageSync('paySwitchInfo') || {}
	if (!paySwitchInfo.expiredTime || new Date().getTime() > paySwitchInfo.expiredTime || forcedRefresh || !paySwitchInfo.switchInfo || isObjectEmpty(paySwitchInfo.switchInfo)) {
		const params = {
			url: '/sys/config/info/getSysPaySwitch',
			method: 'GET',
			callBack: res => {
				const paySwitchInfo = {
					switchInfo: res,
					expiredTime: 5 * 60 * 60 * 1000 + new Date().getTime()
				}
				uni.setStorageSync('paySwitchInfo', paySwitchInfo)
			},
		}
		http.request(params)
	}
}


 // 上报from和cps数据
const reportData = () => {
  const reportParamsObj = uni.getStorageSync('reportParams'); // 获取需要上报的参数
  if (!reportParamsObj) return
  http.request({
    url: '/market/report',
    method: 'POST',
    data: reportParamsObj,
  })
}

 /**
  * 保存登录成功后的数据
  */
const saveLoginSuccessData = (loginRes) => {
  uni.setStorageSync('isPrivacy', 1)
	uni.setStorageSync('hadLogin', true)
  uni.setStorageSync('token', loginRes.accessToken)
  if (loginRes.basToken) {
    uni.setStorageSync('wenetToken', loginRes.basToken);
  }
  uni.setStorageSync('loginResult', loginRes) // 保存整个登录数据
  const expiresTimeStamp = loginRes.expiresIn * 1000 / 2 + new Date().getTime()
  // 缓存token的过期时间
  uni.setStorageSync('expiresTimeStamp', expiresTimeStamp)
  store.commit('setIsLogin', {
    isLogin: true
  })
  store.commit('refreshUserInfo', {
    forceRefresh: true
  })

  // 还原全局 正在登录状态
  getApp().globalData.isLanding = false
  while (getApp().globalData.requestQueue.length) {
    http.request(getApp().globalData.requestQueue.pop())
  }
  reportData()

	// 请求购物车数量
	// http.getCartCount()
}

const clearLoginData = () => {
  uni.removeStorageSync('loginResult');
  uni.removeStorageSync('token');
  uni.removeStorageSync('recentSearch');
  uni.removeStorageSync('hadBindUser');
  uni.removeStorageSync('code');
  uni.removeStorageSync('userInfo');
  removeTabBadge()
  // 重置uuid、uuidSession、sessionTimeStamp、step、flowAnalysisLogDto
  uni.setStorageSync('uuid', getUuid())
  uni.setStorageSync('uuidSession', getUuid())
  uni.setStorageSync('sessionTimeStamp', new Date().getTime())
  uni.setStorageSync('step', 0)
  uni.setStorageSync('flowAnalysisLogDto', '')
  // 删除缓存的wenet账号信息
  uni.removeStorageSync('simpleWenetAccount')
  uni.removeStorageSync('wenetAccount')
  uni.removeStorageSync('wenetToken')
  uni.removeStorageSync('originalWenetAccount')
  uni.removeStorageSync('currentShop')
  uni.removeStorageSync('routeUrlAfterLogin')
  uni.removeStorageSync('unImprovedInfo')
  store.commit('clearUserInfo')
}

const refreshPage = (url) => {
  uni.reLaunch({
    url: url || 'pages/index/index'
  })
}


const whiteListPage = [
  'pages/prod/prod',
  'packageActivities/pages/spellGroupDetails/spellGroupDetails',
  'pages/auto-login/auto-login',
  'packageShop/pages/new-customer-zone/new-customer-zone',
  'packageShop/pages/free-prods/free-prods',
  'packageUser/pages/improve-info/improve-info',
]

/**
 * 
 * @param {*} isRefreshToken 
 * @returns 登录后的跳转
 */
const routeAfterLogin = (isRefreshToken, options) => {
  // 若为刷新token的登录，则不需要跳转到上一页面
  if (isRefreshToken) {
    return
  }
  if (options && options.needImproveExtendInfo) {
    uni.reLaunch({
      url: '/packageuser/pages/improve-info/improve-info'
    })
    return
  }
  const routeUrlAfterLogin = uni.getStorageSync('routeUrlAfterLogin')
  const pages = getCurrentPages()
	// 页面栈中只有一个页面
	if (pages.length === 1) {
		if (whiteListPage.includes(pages[0].route)) {

			refreshPage(routeUrlAfterLogin)
		} else {
      // 首页会读取缓存中的autoRedirectToNewCustomerZone字段，如果为true，则跳转到新客专区
      uni.setStorageSync('autoRedirectToNewCustomerZone', true)
      refreshPage()
    }
		return
	}
  const prevPage = pages[pages.length - 2]
  if (!prevPage) {
    // 首页会读取缓存中的autoRedirectToNewCustomerZone字段，如果为true，则跳转到新客专区
    uni.setStorageSync('autoRedirectToNewCustomerZone', true)
    refreshPage()
    return
  }
  if (routeUrlAfterLogin.includes('pages/index/index')) {
    uni.setStorageSync('autoRedirectToNewCustomerZone', true)
  }
  // 非tabbar页面
  refreshPage(routeUrlAfterLogin)
}


/**
 * 登录成功
 * @param {Object} loginRes 登录成功返回的数据
 * @param {Boolean} isRefreshToken 该次登录是否为刷新token;
 */
const loginSuccess = (loginRes, isRefreshToken, options) => {
  saveLoginSuccessData(loginRes);
  
  routeAfterLogin(isRefreshToken, options);
}

/**
 * 刷新token
 */
const refreshToken = (params) => {

  const refreshToken = uni.getStorageSync('loginResult').refreshToken
  const expiresTimeStamp = uni.getStorageSync('expiresTimeStamp')
  if (!(refreshToken && expiresTimeStamp && expiresTimeStamp < new Date().getTime())) {
    return params
  }
	getApp().globalData.isLanding = true
	getApp().globalData.requestQueue.push(params);
	return {
		url: '/token/refresh',
		method: 'POST',
		login: true,
		isRefreshing: true,
		dontTrunLogin: true,
		data: {
			refreshToken,
		},
		callBack: res => {
			getApp().globalData.isLanding = false
			loginSuccess(res, true)
		},
		errCallBack: errMsg => {
			uni.hideLoading()
			// 清除refreshToken 过期时间
			uni.removeStorageSync('expiresTimeStamp')
			uni.removeStorageSync('tempUid')
			uni.removeStorageSync('loginResult');
			uni.removeStorageSync('token');
			uni.removeStorageSync('hadBindUser');
			uni.removeStorageSync('code');
			uni.removeStorageSync('userInfo');
			uni.removeStorageSync('expiresTimeStamp');

			// 还原全局 正在登录状态
			getApp().globalData.isLanding = false
			while (getApp().globalData.requestQueue.length) {
				let queueParam = getApp().globalData.requestQueue.pop()
				http.request(queueParam)
			}
		}
	}
}

const refreshTokenForBindWenet = (options = {
  isRefreshToken: true
}) => {
  const refreshToken = uni.getStorageSync('loginResult').refreshToken
  const params = {
		url: '/token/refresh',
		method: 'POST',
		login: true,
		isRefreshing: true,
		dontTrunLogin: true,
		data: {
			refreshToken,
      refreshOrg: true
		},
		callBack: res => {
			loginSuccess(res, options.isRefreshToken)
      options.onSuccess && options.onSuccess();
		},
	}

  http.request(params)
}

const isLoginPage = (page) => {
  const loginPageKeywords = ['accountLogin', 'register', 'phoneLogin', 'login']
  for(let i = 0; i < loginPageKeywords.length; i++) {
    if (page.includes(loginPageKeywords[i])) {
      return true
    }
  }
  return false
}
const parseUrlToObj = (url)=> {
  if (!url) {
    return {};
  }
  const res = {};
	const kvArr = url.split('&');
	kvArr.forEach((kvStr) => {
		const [k, v] = kvStr.split('=');
		if (res[k]) {
			if (Array.isArray(res[k])) {
				(res[k]).push(v);
			} else {
				res[k] = [res[k], v] ;
			}
		} else {
			res[k] = v;
		}
	});
  return res;
}

const isPhonenumber = (str) => {
  const reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
  return reg.test(str);
}

const checkNewUser = (shopId) => {
  return new Promise((resolve, reject) => {
    if (!shopId) {
      resolve(false)
    }
    const isNewUserInStorage = uni.getStorageSync('isNewUser')
    if (isNewUserInStorage && isNewUserInStorage.expiredTimestamp > new Date().getTime()) {
      resolve(isNewUserInStorage.value)
    } else {
      http.request({
        url: '/p/myOrder/hasValidInShop',
        method: 'GET',
        dontTrunLogin: true,
        data: {
          shopId,
        },
        callBack: (res) => {
          uni.setStorageSync('isNewUser', {
            value: res,
            expiredTimestamp: new Date().getTime() + 1000 * 60 * 60 * 24
          })
          resolve(res)
        },
        errCallBack: () => {
          resolve(false)
        }
      })
    }
  })
}

function calculateTimeStatus(createTime, endTime) {
  const createTimeDate = new Date(createTime);
  const now = new Date();

  if (now < createTimeDate) {
    // 计算距离 createTime 的剩余时间
    const timeDiff = createTimeDate.getTime() - now.getTime();
    
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    // 构建返回字符串，只包含有值的部分
    let resultString = '';
    if (days > 0) {
      resultString += `${days}天`;
    }
    if (hours > 0) {
      resultString += `${hours}小时`;
    }
    if (minutes > 0) {
      resultString += `${minutes}分钟`;
    }
		resultString += '后可用'
    return resultString;
  } else {
    // 已经开始，返回结束时间
    return `有效期至 ${formatTimeExceptSecond(new Date(endTime))}`;
  }
}

function padWithZeroNumber (number) {
  if (number < 10) {
    return `0${number}`
  }
  return number
}

// 获取两个时间的时间差，返回格式：xx天xx时xx分xx秒
function getDiffTime (startTime, endTime) {
  const diff = endTime - startTime
  const day = Math.floor(diff / 1000 / 60 / 60 / 24)
  const hour = Math.floor(diff / 1000 / 60 / 60 % 24)
  const minute = Math.floor(diff / 1000 / 60 % 60)
  const second = Math.floor(diff / 1000 % 60)
  if (day > 0) {
    return `${day}天${padWithZeroNumber(hour)}小时`
  }
  return `${padWithZeroNumber(hour)}:${padWithZeroNumber(minute)}:${padWithZeroNumber(second)}`
}

function adaptIosDateFormat(str) {
  // 把 yyyy-MM-dd HH:mm:ss 转换为 yyyy/MM/dd HH:mm:ss
  return str.replace(/-/g, '/')
}

// 通过活动的开始时间和结束时间，计算活动的剩余时间或何时开始
function calcActivityTime (startTime, endTime) {
  if (!startTime || !endTime) return ''
  
  const start = new Date(adaptIosDateFormat(startTime))
  const end = new Date(adaptIosDateFormat(endTime))
  const now = new Date()
  if (now < start) {
    // 还有xx时间开始
    const str = getDiffTime(now, start)
    return {
      str: `${str}后开始`,
      needCountdown: true
    }
  } else if (now > end) {
    return {
      str: '已结束',
      needCountdown: false
    }
  } else {
    const str = getDiffTime(now, end)
    return {
      str,
      needCountdown: true
    }
  }
}

function getWxCode() {
  const appType = uni.getStorageSync('appType')
  return new Promise((resolve) => {
    if (appType === AppType.MINI) {
      wx.login({
        success: (res) => {
          resolve(res.code)
        }
      })
    } else {
      resolve('')
    }
  })
}

/**
 * @typedef {Object} LoginParams
 * @property {String} basUsername 用户名
 * @property {String} ou 学校编码
 * @property {String} uid basuid
 * @property {String} mobile 手机号
 * @param {LoginParams} loginParams 登录参数
 * 
 * @typedef {Object} Options
 * @property {Boolean} needImproveExtendInfo 是否需要完善扩展信息
 * @property {Boolean} immediateRedirect 是否立即跳转
 * @param {Options} options 配置项
 * @returns {Promise}
 */
function loginWithWenet(loginParams = {}, options = {}) {
  const { needImproveExtendInfo = false, immediateRedirect = true } = options
  return new Promise((resolve, reject) => {
    getWxCode().then((code) => {
      http.request({
        url: '/self/login',
        methods: 'POST',
        data: {
          ...loginParams,
          code,
        },
        callBack: (loginRes) => {
          if (loginRes) {
            if (immediateRedirect) {
              resolve({
                loginRes,
              })
              loginSuccess(loginRes, false, { needImproveExtendInfo })
            } else {
              saveLoginSuccessData(loginRes)
              resolve({
                loginRes,
                next: () => {
                  routeAfterLogin(false, { needImproveExtendInfo })
                }
              })
            }
          } else {
            resolve();
          }
        },
        errCallBack: (err) => {
          reject(err)
        }
      })
    })
  })
}

/**
 * 通过bas的学号查询其手机号，或通过bas的手机号查询其学号
 * @param {} param0 
 * @returns 
 */
function getWenetAnotherAccount({ username, mobile}) {
  const p = {}
  if (username) {
    p.username = username
  }
  if (mobile) {
    p.mobile = mobile
  }
  return new Promise((resolve, reject) => {
    http.request({
      domain: config.wenetUrl,
      url: '/v2/account/query/bind',
      method: 'POST',
      data: p,
      callBack: (res) => {
        resolve(res)
      },
      errCallBack: (err) => {
        reject(err)
      }
    })
  })
}


function getAreaSheetByOuName (ouList, ouName) {
  function round(areaList) {
    const result = [];
    areaList.forEach((item) => {
      const temp = {
        label: item.name,
        value: item.name,
      };
      if (item.children && item.children.length) {
        temp.children = round(item.children);
      }
      result.push(temp);
    });

    return result;
  }
  let targetOu = null;
  for (const item of ouList) {
    if (item.name === ouName) {
      targetOu = item;
    }
  }
  if (targetOu) {
    // res可以作为antd Cascader的数据 若需求变更 可随时更换
    const res = round(targetOu.children);
    // console.log(res);
    const firstList = [];
    const secondList = {};
    res.forEach((item) => {
      firstList.push(item.value);
      secondList[item.value] = [];
      item.children.forEach((sub) => {
        secondList[item.value].push(sub.value);
      });
    });
    return { firstList, secondList };
  }
  return {};
};


//身份证号合法性验证
//支持15位和18位身份证号
//支持地址编码、出生日期、校验位验证
const identityCodeValid = (code) => {
  let city = {
    11: "北京",
    12: "天津",
    13: "河北",
    14: "山西",
    15: "内蒙古",
    21: "辽宁",
    22: "吉林",
    23: "黑龙江 ",
    31: "上海",
    32: "江苏",
    33: "浙江",
    34: "安徽",
    35: "福建",
    36: "江西",
    37: "山东",
    41: "河南",
    42: "湖北 ",
    43: "湖南",
    44: "广东",
    45: "广西",
    46: "海南",
    50: "重庆",
    51: "四川",
    52: "贵州",
    53: "云南",
    54: "西藏 ",
    61: "陕西",
    62: "甘肃",
    63: "青海",
    64: "宁夏",
    65: "新疆",
    71: "台湾",
    81: "香港",
    82: "澳门",
    91: "国外 "
  };
  let pass = true;

  if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
    pass = false;
  } else if (!city[code.substr(0, 2)]) {
    pass = false;
  } else {
    //18位身份证需要验证最后一位校验位
    if (code.length === 18) {
      //校验码判断
      let c = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];  //系数
      let b = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']; //校验码对照表
      let id_array = code.split("");
      let sum = 0;
      for (let k = 0; k < 17; k++) {
        sum += parseInt(id_array[k]) * parseInt(c[k]);
      }
      if (id_array[17].toUpperCase() !== b[sum % 11].toUpperCase()) {
        pass = false;
      }
    }
  }
  return pass;
};

const clearNoValueParams = (params = {}) => {
  const filteredParams = {}
  for (const [key, value] of Object.entries(params)) {
    if (value !== null && value !== undefined && value !== '' && String(value) !== '') {
      filteredParams[key] = value
    }
  }
  return filteredParams
}

const getStudentIdFromWenetAccountInfo = (wenetAccountInfo = {}) => {
  return wenetAccountInfo.users?.find(item => item?.type === 'STUDENT_ID_ASSOCIATED_USER')?.username
}

  //判断进入的场景值 
const getSceneInfo = (scene) => {
  const sceneList = {
    "1000": "其他",
    "1001": "发现页小程序「最近使用」列表",
    "1005": "微信首页顶部搜索框的搜索结果页",
    "1006": "发现栏小程序主入口搜索框的搜索结果页",
    "1007": "单人聊天会话中的小程序消息卡片",
    "1008": "群聊会话中的小程序消息卡片",
    "1010": "收藏夹",
    "1011": "扫描二维码",
    "1012": "长按图片识别二维码",
    "1013": "扫描手机相册中选取的二维码",
    "1014": "小程序订阅消息",
    "1017": "前往小程序体验版的入口页",
    "1019": "微信钱包",
    "1020": "公众号 profile 页相关小程序列表",
    "1022": "聊天顶部置顶小程序入口",
    "1023": "安卓系统桌面图标",
    "1024": "小程序 profile 页",
    "1025": "扫描一维码",
    "1026": "发现栏小程序主入口，「附近的小程序」列表",
    "1027": "微信首页顶部搜索框搜索结果页「使用过的小程序」列表",
    "1028": "我的卡包",
    "1029": "小程序中的卡券详情页",
    "1030": "自动化测试下打开小程序",
    "1031": "长按图片识别一维码",
    "1032": "扫描手机相册中选取的一维码",
    "1034": "微信支付完成页",
    "1035": "公众号自定义菜单",
    "1036": "App 分享消息卡片",
    "1037": "小程序打开小程序",
    "1038": "从另一个小程序返回",
    "1039": "摇电视",
    "1042": "添加好友搜索框的搜索结果页",
    "1043": "公众号模板消息",
    "1044": "带 shareTicket 的小程序消息卡片 详情",
    "1045": "朋友圈广告",
    "1046": "朋友圈广告详情页",
    "1047": "扫描小程序码",
    "1048": "长按图片识别小程序码",
    "1049": "扫描手机相册中选取的小程序码",
    "1052": "卡券的适用门店列表",
    "1053": "搜一搜的结果页",
    "1054": "顶部搜索框小程序快捷入口",
    "1056": "聊天顶部音乐播放器右上角菜单",
    "1057": "钱包中的银行卡详情页",
    "1058": "公众号文章",
    "1059": "体验版小程序绑定邀请页",
    "1060": "微信支付完成页",
    "1064": "微信首页连Wi-Fi状态栏",
    "1065": "URL scheme 详情",
    "1067": "公众号文章广告",
    "1068": "附近小程序列表广告（已废弃）",
    "1069": "移动应用通过openSDK进入微信,打开小程序",
    "1071": "钱包中的银行卡列表页",
    "1072": "二维码收款页面",
    "1073": "客服消息列表下发的小程序消息卡片",
    "1074": "公众号会话下发的小程序消息卡片",
    "1077": "摇周边",
    "1078": "微信连Wi-Fi成功提示页",
    "1079": "微信游戏中心",
    "1081": "客服消息下发的文字链",
    "1082": "公众号会话下发的文字链",
    "1084": "朋友圈广告原生页",
    "1088": "会话中查看系统消息，打开小程序",
    "1089": "微信聊天主界面下拉，「最近使用」栏",
    "1090": "长按小程序右上角菜单唤出最近使用历史",
    "1091": "公众号文章商品卡片",
    "1092": "城市服务入口",
    "1095": "小程序广告组件",
    "1096": "聊天记录，打开小程序",
    "1097": "微信支付签约原生页，打开小程序",
    "1099": "页面内嵌插件",
    "1100": "红包封面详情页打开小程序",
    "1101": "远程调试热更新（开发者工具中，预览 -> 自动预览 -> 编译并预览）",
    "1102": "公众号 profile 页服务预览",
    "1103": "发现页小程序「我的小程序」列表",
    "1104": "微信聊天主界面下拉，「我的小程序」栏",
    "1106": "聊天主界面下拉，从顶部搜索结果页，打开小程序",
    "1107": "订阅消息，打开小程序",
    "1113": "安卓手机负一屏，打开小程序（三星）",
    "1114": "安卓手机侧边栏，打开小程序（三星）",
    "1119": "【企业微信】工作台内打开小程序",
    "1120": "【企业微信】个人资料页内打开小程序",
    "1121": "【企业微信】聊天加号附件框内打开小程序",
    "1124": "扫“一物一码”打开小程序",
    "1125": "长按图片识别“一物一码”",
    "1126": "扫描手机相册中选取的“一物一码”",
    "1129": "微信爬虫访问 详情",
    "1131": "浮窗",
    "1133": "硬件设备打开小程序 详情",
    "1135": "小程序profile页相关小程序列表,打开小程序",
    "1144": "公众号文章 - 视频贴片",
    "1145": "发现栏 - 发现小程序",
    "1146": "地理位置信息打开出行类小程序",
    "1148": "卡包-交通卡，打开小程序",
    "1150": "扫一扫商品条码结果页打开小程序",
    "1151": "发现栏 - 我的订单",
    "1152": "订阅号视频打开小程序",
    "1153": "“识物”结果页打开小程序",
    "1154": "朋友圈内打开“单页模式”",
    "1155": "“单页模式”打开小程序",
    "1157": "服务号会话页打开小程序",
    "1158": "群工具打开小程序",
    "1160": "群待办",
    "1167": "H5 通过开放标签打开小程序 详情",
    "1168": "移动/网站应用直接运行小程序",
    "1169": "发现栏小程序主入口，各个生活服务入口（例如快递服务、出行服务等）",
    "1171": "微信运动记录（仅安卓）",
    "1173": "聊天素材用小程序打开 详情",
    "1175": "视频号主页商店入口",
    "1176": "视频号直播间主播打开小程序",
    "1177": "视频号直播商品",
    "1178": "在电脑打开手机上打开的小程序",
    "1179": "#话题页打开小程序",
    "1181": "网站应用打开PC小程序",
    "1183": "PC微信 - 小程序面板 - 发现小程序 - 搜索",
    "1184": "视频号链接打开小程序",
    "1185": "群公告",
    "1186": "收藏 - 笔记",
    "1187": "浮窗",
    "1189": "表情雨广告",
    "1191": "视频号活动",
    "1192": "企业微信联系人profile页",
    "1193": "视频号主页服务菜单打开小程序",
    "1194": "URL Link 详情",
    "1195": "视频号主页商品tab",
    "1196": "个人状态打开小程序",
    "1197": "视频号主播从直播间返回小游戏",
    "1198": "视频号开播界面打开小游戏",
    "1200": "视频号广告打开小程序",
    "1201": "视频号广告详情页打开小程序",
    "1202": "企微客服号会话打开小程序卡片",
    "1203": "微信小程序压测工具的请求",
    "1206": "视频号小游戏直播间打开小游戏",
    "1207": "企微客服号会话打开小程序文字链",
    "1208": "聊天打开商品卡片",
    "1212": "青少年模式申请页打开小程序",
    "1215": "广告预约打开小程序",
    "1216": "视频号订单中心打开小程序",
    "1218": "微信键盘预览打开小程序",
    "1219": "视频号直播间小游戏一键上车",
    "1220": "发现页设备卡片打开小程序",
    "1223": "安卓桌面Widget打开小程序",
    "1225": "音视频通话打开小程序",
    "1226": "聊天消息在设备打开后打开小程序",
    "1228": "视频号原生广告组件打开小程序",
    "1230": "订阅号H5广告进入小程序",
    "1231": "动态消息提醒入口打开小程序",
    "1232": "搜一搜竞价广告打开小程序",
    "1233": "小程序搜索页人气游戏模块打开小游戏",
    "1238": "看一看信息流广告打开小程序",
    "1242": "小程序发现页门店快送模块频道页进入小程序",
    "1244": "#tag搜索结果页打开小程序",
    "1245": "小程序发现页门店快送搜索结果页进入小程序",
    "1248": "通过小程序账号迁移进入小程序",
    "1252": "搜一搜小程序搜索页「小功能」模块进入小程序",
    "1254": "发现页「动态」卡片 打开小程序",
    "1255": "发现页「我的」卡片 打开小程序",
    "1256": "pc端小程序面板「最近使用」列表",
    "1257": "pc端小程序面板「我的小程序」列表",
    "1258": "pc端小程序面板「为电脑端优化」模块",
    "1259": "pc端小程序面板「小游戏专区」模块",
    "1260": "pc端小程序面板「推荐在电脑端使用」列表",
    "1261": "公众号返佣商品卡片",
    "1265": "小程序图片详情页打开小程序",
    "1266": "小程序图片长按半屏入口打开小程序",
    "1267": "小程序图片会话角标打开小程序",
    "1272": "发现页「游戏」服务tab打开小程序",
    "1273": "发现页「常用的小程序」列表",
    "1278": "发现页「发现小程序」列表打开小程序",
    "1279": "发现页「发现小程序」合集页打开小程序",
    "1280": "下拉任务栏小程序垂搜「建议使用」打开小程序",
    "1281": "下拉任务栏小程序垂搜「发现小程序」打开小程序",
    "1286": "明文scheme打开小程序",
    "1292": "发现页「发现小程序」poi 详情页打开小程序",
    "1295": "下拉任务栏小程序垂搜「发现小程序」广告打开小程序",
    "1297": "发现-小程序-搜索「发现小程序」打开小程序",
    "1298": "下拉任务栏小程序垂搜「发现小程序」打开的合集访问小程序",
    "1299": "下拉任务栏小程序垂搜「发现小程序」poi 详情页打开小程序",
    "1300": "发现-小程序-搜索「发现小程序」打开的合集访问小程序",
    "1301": "发现-小程序-搜索「发现小程序」poi 详情页打开小程序",
  }
  return {
    key: scene,
    value: sceneList[scene] || '未知'
  };
}

module.exports = {
	getUrlKey: getUrlKey,
	dateToTimestamp: dateToTimestamp,
	formatTime: formatTime,
	formatHtml: formatHtml,
	endOfStartTime: endOfStartTime,
	checkAuthInfo: checkAuthInfo,
	checkPhoneNumber: checkPhoneNumber,
	checkUserName: checkUserName,
	throttle: throttle,
	getPageInfo: getPageInfo,
	tapLog: tapLog,
	getUuid: getUuid,
	checkSameDay: checkSameDay,
	tsToDate: tsToDate,
	checkIDCard: checkIDCard,
	transTabbar: transTabbar,
	removeTabBadge: removeTabBadge,
	locationConversions: locationConversions,
	debounce: debounce,
	timeBeApart,
	initPayType: initPayType,
	featureRoute: featureRoute,
	getSysPaySwitch: getSysPaySwitch,
	loginSuccess:loginSuccess,
	refreshToken: refreshToken,
  refreshTokenForBindWenet,
	checkPassword: checkPassword,
  isLoginPage,
	diffInHours,
  saveLoginSuccessData,
  routeAfterLogin,
	formatTimeExceptSecond,
	parseUrlToObj,
  checkNewUser,
	calculateTimeStatus,
  calcActivityTime,
  clearLoginData,
  loginWithWenet,
	getAreaSheetByOuName,
	identityCodeValid,
  clearNoValueParams,
  getStudentIdFromWenetAccountInfo,
  getSceneInfo,
  getWenetAnotherAccount,
  checkEmail,
};

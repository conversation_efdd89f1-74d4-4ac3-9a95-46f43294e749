// 简体中文语言包
export const index = {
	// index.vue
	'search': '搜索',
	'newProduct': '新品推荐',
	'limitedTimeOffer': '限时特惠',
	'groupDiscount': '优惠团购',
	'spikeSpecial': '秒杀专场',
	'coupons': '领券',
	'spike': '秒杀',
	'moreSpikes': '更多秒杀',
	'groupBuy': '团购',
	'moreGroupBuy': '更多拼团',
	// ￥?
	'join': '人团',
	'seeMore': '查看更多',
	'hotSale': '商城热销',
	'allLoaded': '已加载所有商品',

	// aBulkList.vue
	'businessesRelatedProducts': '商家还未发布团购相关商品',

	// accountLogin.vue
	'username': '账号',
	'enterUsername': '请输入手机号/用户名',
	'usernameWarn': '请输入账号',
	'password': '密码',
	'enterPassword': '请输入密码',
	'changePassword': '修改密码',
	'notUsername': '还没有账号？',
	'registeredUsername': '去注册',
	'phoneNumber': '手机号',
	'enterPhone': '请输入登录手机号',
	'phoneWarn': '手机号格式错误',
	'verificationCode': '验证码',
	'enterCodeFirst': '请先获取验证码',
	'getCode': '获取验证码',
	'enterCode': '请输入验证码',
	'newPassword': '新密码',
	'enterNew': '请输入新密码',
	'confirmNew': '确认密码',
	'confirmNewEmpty': '确认密码不能为空',
	'enterNewAgain': '请再次输入新密码',
	'comparedPassword': '确认密码与新密码不一致',
	'login': '登录',
	'consentOfLoginRepresentative': '登录代表同意',
	'consentOfRegisteredRepresentative':'注册代表同意',
	'termsOfService': '服务条款',
	'privacyPolicy': '隐私策略',
	'and': '和',

	'loginSuccessful': '登录成功!',
	'backHomepage': '回到首页',
	'confirmChanges': '确认修改',
	'BackLogin': '返回登录',
	'pwdCantBeEmpty' : '密码不能为空',
	// 弹窗的没测


	// accountSettings.vue
	'loginAccount': '登录账号',
	'enterNewPasswordTips': '请输入正确的密码格式',
	'passwordLength': '请设置6-12位登录密码',
	'enterPasswordAgain': '请再次输入密码',
	'accountTip': '提示：请用上面的账号密码在PC端打开店铺管理中心进行登录。',
	'enterCorrectLoginPassword': '请输入正确的登陆密码',
	'loginPasswordTips': '请输入正确的登陆密码',
	'setupSucceeded': '设置成功!',

	// afterSales.vue
	'shop': '店铺',
	'quantity': '数量',
	'piece': '件',
	'seeDetails': '查看详情',
	// 处理退款状态:(1.买家申请 2.卖家接受 3.买家发货 4.卖家收货 5.退款成功 6.买家撤回申请 7.商家拒绝 -1.退款关闭)
	'refund': '退款',
	'refund01': '仅退款',
	'refund02': '退货退款',
	'refund1': '你已提交退款申请，等待商家处理',
	'refund21': '商家已同意，等待退款',
	'refund22': '商家已同意，请按照给出地址寄回商品',
	'refund3': '等待商家确认收货并退款',
	'refund4': '商家已确认收货，等待退款到账',
	'refund5': '退款成功',
	'refund6': '退款申请已撤销',
	'refund7': '商家拒绝，可修改退款申请或申请客服介入处理',
	'refund_1': '退款申请已关闭',
	'refundEmpty': '还没有退款/售后相关订单~',
	'sellerRemarks': '卖家备注',


	// alterShopPassword.vue
	'oldPassword': '旧密码',
	'enterOldPassword': '请输入旧登录密码',
	'newPasswordLength': '请设置6-12位新登录密码',
	'enterNewPassword': '请再次输入新登录密码',
	'alterShopPasswordTips': '提示：请用上面的重新设置的密码在PC端打开店铺管理中心进行登录。',
	'successfullyModified': '修改成功!',

	// applyDist.vue
	'applyDistributor': '成为分销员',


	// applyDistCon.vue
	'mobilePhone': '手机号码',
	'actualName': '真实姓名',
	'identification': '身份证号码',
	'identificationTips': '请输入正确的身份证号码',
	'uploadIDFront': '请上传身份证人像面',
	'uploadIDBack': '请上传身份证国徽面',
	'uploadIDHand': '请上传手持身份证照片',
	'enterRealName': '请输入真实姓名',
	'applicationSubmitted': '申请已提交，请等待审核',

	// applyRefund.vue
	'orderNumber': '订单编号',
	// ￥?
	'refundMethod': '退款方式',
	'cargoStatus': '货物状态',
	'refundReason': '退款原因',
	'returnquantity': '退货数量',
	'refundAmount': '退款金额',
	'refundAmounts': '请输入退款金额',
	'maxAmount': '最多可退',
	'enterMobilePhone': '请输入退货人手机号码',
	'refundDescription': '退款说明',
	'descriptionLength': '必填，最多50字',
	'uploadCertificate': '上传凭证',
	'logisticsVouchers': '物流凭证',
	'uploadPics': '可上传5张图片',
	'submit': '提交申请',
	'goodsNotReceived': '未收到货',
	'goodsReceived': '已收到货',
	'pleaseChoose': '请选择',
	'wrongShot': '拍错/多拍/不喜欢',
	'refundConsensus': '协商一致退款',
	'damagedGoods': '商品破损/少件',
	'productNot': '商品与描述不符',
	'sellerSendsWrong': '卖家发错货',
	'qualityProblem': '质量问题',
	'includingFreight': '含运费￥',
	'returnQuantityTips1': '退货数量不能小于1！',
	'returnQuantityTips2': '退货数量不能大于订单商品数量！',
	'selectReason': '请选择退款原因',
	'fillCorrectAmount': '请正确填写退款金额',
	'refundAmountTips': '退款金额超出本单最多可退总额，请重新输入',
	'fillRefundDescription': '请填写退款说明',
	'fillRefundDescriptionTips': '请填写不超50字的说明',
	'prompt': '提示',
	'promptTips': '本次申请的退款商品是本单最后一件商品，退款金额中包含本单运费(如有)，一旦提交申请，本单所有商品的退款申请均不可撤销，是否确认提交',
	"orderChangeTips": '订单状态已发生改变，请重新提交申请',
	"maximumRefund": '最多可退￥',
	"pleaseRetype": '，请重新输入',
	"autoReturn": ' 将自动退回',


	// basket.vue
	'selectAll': '全选',
	'delete': '删除',
	'total': '总计',
	// ￥
	'instantReduction': '立减',
	'settlement': '结算',
	'selectPromotion': '选择促销活动',
	'notPromotion': '不参加促销',
	'amountDetails': '金额明细',
	'comTotal': '商品总额',
	'promotionalDiscount': '促销立减',
	'shoppingTips': '您还没有添加商品到购物车哦~',
	'chooseCom': '选择结算商品',
	'delivery': '配送',
	'deliveryTips': '不同配送方式的商品暂不支持同时结算，请分开下单',
	'expressDelivery': '快递配送',
	'sameDelivery': '同城配送',
	'toSettle': '去结算',
	'inTotal': '共',
	'pickStore': '到店自提',
	'selectProduct': '请选择商品',
	'deleteProductTips': '确认要删除选中的商品吗?',
	'basketSelectedCount': '当前已选择',
	'basketCountOver': '项商品，购物车最高支持50项一起结算，请分开下单',

	// binding-phone.vue
	'bind': '绑定',
	'enterMobileNumber': '请输入手机号码',
	'enterMobileNumberTips': '手机号输入错误',
	// 弹窗未弄


	// category.vue
	'searchGoods': '搜索您想要的商品',
	'viewAll': '查看全部',


	// chooseRefundWay.vue
	'chooseRefundMethod': '选择退款方式',
	'goodsTips1': '未收到货，或无需退货仅需退款',
	'goodsTips2': '已收到货，需要退货退款',
	// console的未弄


	// confirmOrder.vue
	'addShippingAddress': '添加收货地址',
	'shippingAddress': '收货地址',
	'buyerMessage': '买家留言',
	'buyerTips': '',
	'goodsAmount': '商品金额',
	'shipping': '运费',
	'submitOrders': '提交订单',
	'wsConnectionFailed': 'ws连接失败',
	'desperatelyBuying': '正在拼命抢购',
	'secFailTips': '活动过于火爆，请稍后再试',
	'seckillReduce': '秒杀优惠',



	// couponCenter.vue
	'universalCoupon': '通用神券',
	'discount': '折',
	'available': '满',
	'availableUse': '元可用',
	'universalAll': '全平台商品通用',
	'universalOnly': '仅可购买平台部分商品',
	'nocoupons': '已抢光',
	'nocoupons1': '已抢光',
	'haveCoupons': '已抢',
	'getIt': '立即领取',
	'getItByCoupon': '领券购买',
	'toUse': '去使用',
	'recommendCoupons': '推荐好券',
	'endTips': '已加载全部数据',
	'merchantCoupon': '商家还未发布优惠券哦',
	'successfullyReceived': '领券成功',
	'couponEventGoods': '优惠券活动商品',


	// delivery-address.vue
	'asDefaultAddress': '设为默认地址',
	'addNewAddress': '新增收货地址',
	'noShippingAddress': '您还没有收货地址',
	'addNewAddressTips': '新增收货地址已达到上限',


	// deliveryCertificate.vue
	'pickup': '自提点',
	'pickingCode': '提 货 码',
	'returnOrderDetails': '返回订单详情',


	// DetailsOfRefund.vue
	'refundDetails': '退款详情',
	'refundDetails11': '退款申请待商家处理',
	'refundDetails12': '你已成功发起退款申请，等待商家处理',
	'refundDetails71': '商家已拒绝退款申请',
	'refundDetails72': '你可以撤销退款申请，与卖家积极协商后重新申请',
	'refundDetails2': '商家同意退款',
	'refundDetails21': '商家已同意，等待系统退款',
	'refundDetails22': '商家已同意，请按照商家给出的退货地址寄回商品',
	'refundDetails31': '等待商家确认收货并退款',
	'refundDetails32': '你已退货，商家将在收货后尽快处理',
	'refundDetails41': '商家确认收货',
	'refundDetails42': '商家已确认收货，等待系统退款',
	'refundDetails51': '退款成功',
	'refundDetails_1': '退款申请关闭',
	'refundDetails61': '申请已撤回',
	'refundDetails62': '你已撤回本次退款申请',
	'refundDetails13': '如果商家同意，申请将达成并退款给你',
	'refundDetails01': '如果商家拒绝，你可以撤销退款申请，与卖家积极协商后重新申请',
	'refundDetails23': '如果商家同意，请按照给出的退货地址退货',
	'refundDetails33': '如果商家确认收货，申请将达成并退款给你',
	'refundDetails02': '如果商家拒绝收货，你可以修改退货物流信息或者与卖家积极协商',
	'moreExplanation': '更多说明',
	'putAway': '收起',
	'refundDetails73': '拒绝理由',
	'returnAddress': '退货地址',
	'recipient': '收件人',
	'recipientName': '请输入收件人名字',
	'detailedAddress': '详细地址',
	'refundInformation': '退款信息',
	'logisticsInformation': '物流信息',
	'refundInstructions': '退款说明',
	'refundNumber': '退款编号',
	'applicationTime': '申请时间',
	'quantityReturned': '退货数量',
	'applicationCanceled': '撤销申请',
	'fillInReturn': '填写退货物流',
	'revoke': '撤销',
	'revokeTips': '确定要撤销本次申请?',
	'modifyReturn' : '修改退货物流',
	'refundVoucher': '退款凭证',
	"modifyRefundAmount": "修改退款金额",


	// dis-center.vue
	'noAnnouncement': '暂无公告',
	'vip':'会员',
	'myBalance': '可提现余额（元）',
	'totalIncome': '总收益（元）',
	'withdrawalsNow': '立即提现',
	'promoteGoods': '推广商品',
	'distributionWinWin': '分销共赢',
	'inviteFriends': '邀请好友',
	'promoteRewards': '推广奖励',
	'myUser': '我的用户',
	'myPromotion': '我的推广',
	'distributorList':'分销员列表',
	'incomeBreakdown': '收益明细',
	'withdrawalsRecord': '提现记录',


	// discountDetail.vue
	'endOfDistance': '距结束',
	'specialty': '专场',
	'immediatelyBuy': '立即抢购',


	// draw-rule.vue
	'WithdrawalRules': '提现规则',
	'WithdrawalRules1': '1.单次提现额度不得低于',
	'yuan': '元',
	'WithdrawalRules2': '2.单次提现额度不得高于',
	'WithdrawalRules3': '3.每个用户每天提现次数无限制',
	'WithdrawalRules31': '3.每个用户每',
	'WithdrawalRules32': '天有',
	'WithdrawalRules34': '月有',
	'WithdrawalRules33': '次提现的机会',


	// editAddress.vue
	'name': '姓名',
	'mobileNumberLength': '11位手机号码',
	'area': '所在地区',
	'confirm': '确定',
	'addressTips': '如楼号/单元/门牌号',
	'saveAddress': '保存收货地址',
	'deleteAddress': '删除收货地址',
	'selectDetailedAddress': '请您填写字数不小于5的详细地址',
	'LatitudeNotEmpty': '经纬度不能为空',
	'deleteAddressTips': '确定要删除此收货地址吗?',

	// express-delivery.vue

	// groupConfirmOrder.vue
	'group': '团',
	'group1': '选择商品开团/参团',
	'group2': '邀请好友参团',
	'group3': '人满成团',
	'headPrice': '团长价',
	'groupPurchasePrice': '团购价',
	'tips': '提示',
	'groupPurchaseOffer': '团购优惠',


	// income-details.vue
	'cumulativeIncome': '累计收益(元)',
	'availableBalance': '可提余额(元)',
	'toBeSettled': '待结算(元)',
	'monthEarnings': '本月收益',
	'todayEarnings': '今日收益',
	'reward': '奖励',
	'other': '其他',
	'reward1': '直推奖励',
	'reward2': '间推奖励',
	'reward3': '邀请奖励',
	'noProfitToday': '今天还没有收益哦~',


	// InvitationCards.vue
	'invitationCard': '邀 请 卡',
	'inviteJoin': '邀你一起加入，推广赢奖励',
	'screenshotTips': '直接截屏保存到相册',


	// login.vue
	'wenetMall': 'WeNet商城',
	'wenetMallJoin': 'WeNet商城欢迎你的加入!',
	'weChatLogin': '微信一键登录',
	'existingLogin': '已有账号登录',
	'privilegeGrantFailed': '授权失败',
	'privilegeGrantSucceed': '授权成功',


	// logisticsInfo.vue
	'package': '包裹',
	'carriageSource': '承运来源',
	'waybillNumber': '运单编号',
	'copy': '复制',
	'noPackageTips': '暂无包裹信息',
	'alreadyReceived': '已揽收',
	'inTransit': '运输途中',
	'signedInReceived': '已签收',
	'questionableDelivery': '问题件',
	'arriveDestinationCity': '到达目的城市',
	'noDeliveryInformation': '暂无配送信息',

	// myCoupon.vue
	'unused': '未使用',
	'usageRecord': '使用记录',
	'expired': '已过期',
	'couponTips': '这里还没有相关优惠券~',
	'deleteCouponTips': '确定要删除此优惠券吗？',
	'UserClicksCancel': '用户点击取消',


	// myShop.vue
	// 这个页面静态的!? 暂时不弄


	// my-users.vue
	'people': '人',
	'avatar': '头像',
	'nickname': '昵称',
	'bindingDate': '绑定日期',
	'noUsersYet': '还没有用户哦~',
	'usernameEmptyTips':'用户名不能为空',
	'nicknameEmptyTips':'昵称不能为空',

	// openAShop.vue
	'auditFailure': '审核失败',
	'auditFailureTips': '您的开店申请审核未通过，请您修改申请信息后再次提交开店申请',
	'storeInformation': '店铺信息',
	'storeName': '店铺名称',
	'storeNameTips': '输入店铺名称，2-10字',
	'enterCorrectStore': '请输入正确的店铺名称',
	'shopDescription': '店铺描述',
	'shopDescriptionTips': '填写店铺描述，10-200字',
	'shopLogo': '店铺Logo',
	'upShopLogo': '上传店铺Logo',
	'location': '地理位置',
	'getLocation': '点击获取位置',
	'latitude': '纬度：',
	'longitude': '经度：',
	'shopPhone': '联系电话',
	'shopPhoneTips': '请填写店铺联系电话',
	'businessLicense': '营业执照',
	'upBusinessLicense': '上传营业执照',
	'ID': '身份证件',
	'upBothIDCard': '上传身份证正反两面',
	'positive': '正面',
	'Negative': '反面',
	'submitApplication': '提交申请',
	'submitApplicationTips': '为必填信息。申请人必须保证以上所填信息的真实性。如有虚假信息，因此而产生的所有后果均由申请人承担。',
	'shopDescriptionTips2': '请输入正确店铺描述',
	'upShopLogoTips': '请上传店铺Logo',
	'enterCorrectAddress': '请输入正确的详细地址：不得少于5字',
	'locationTips': '请选择地理位置',
	'upBusinessLicenseTips': '请上传营业执照',
	'upBothIDCardTips': '请上传身份证正反两面',
	'submittedSuccessfully': '提交成功，等待审核',
	'shopNameTips': '店铺名称仅允许汉字、英文和数字',

	// order-detail.vue
	'waitingBuyerPay': '等待买家支付',
	'waitingForDelivery': '等待商家发货',
	'waitingBuyerReceipt': '等待买家确认收货',
	'orderCompleted': '订单已完成',
	'orderCancelled': '订单已取消',
	'itInaGroup': '正在拼团中...',
	'buyerPayment': '买家付款',
	'merchantShipment': '商家发货',
	'buyerPickUp': '买家提货',
	'merchantDelivery': '商家配送',
	'transactionComplete': '交易完成',
	'deliveryVoucher': '提货凭证',
	'deliveryCodeTips': '暂无提货码',
	'picker': '提 货 人',
	'appointmentTime': '预约时间',
	'requestRefund': '申请退款',
	'checkRefund': '查看退款',
	'orderTime': '下单时间',
	'paymentMethod': '支付方式',
	'deliveryMethod': '配送方式',
	'OrderNotes': '订单备注',
	'preferential': '优惠',
	'orderTotal': '订单总额',
	'wholeOrderRefund': '整单退款',
	'viewGroupDetails': '查看团购详情',
	'contactCustomerService1':'联系客服',
	'confirmTheCost': '确认花费',
	'integralPayment': '积分支付',
	'noNeedDelivery': '无需快递',
	'sellerDelivery': '快递配送',
	'copySucceeded': '复制成功',
	'copyFailure': '复制失败',


	// orderList.vue
	'all': '全部',
	'toBePaid': '待支付',
	'toBeDelivered': '待发货',
	'toBeReceived': '待收货',
	'toBeEvaluated': '待评价',
	'completed': '已完成',
	'cancelled': '已取消',
	'inAGroup': '拼团中',
	'refunding': '退款中',
	'refundComplete': '退款完成',
	'partialRefundCompleted': '部分退款完成',
	'refundClosed': '退款关闭',
	'items': '件商品',
	'cancelOrder': '取消订单',
	'payment': '付款',
	'viewLogistics': '查看物流',
	'confirmRceipt': '确认收货',
	'evaluation': '评价',
	'noOrderTips': '这里还没有相关订单~',
	'cancelOrderTips': '要取消此订单？',
	'no': '否',
	'yes': '是',
	'orderExpired': '订单已过期，请重新下单',
	'orderStatusChanged': '订单状态已经发生改变，请重新下单',
	'haveRceivedGoods': '我已收到货？',
	'sureDeleteOrder': '确定要删除此订单吗？',
	'gift': '赠品',
	'removeGiftTip1': '移除赠品后退款金额将减去赠品价格￥',
	'removeGiftTip2': '，是否确定移除？',
	'removeGiftTip3': '赠品金额大于订单当前可退金额，不可移除',
	'removeGiftBtn': '移除',

	// pay-result.vue
	'paymentFailed': '支付失败',
	'paymentFailedTips1': '请在',
	'paymentFailedTips2': '30分钟',
	'paymentFailedTips3': '内完成付款',
	'paymentFailedTips4': '否则订单会被系统取消',
	'checkOrder': '查看订单',
	'payAgain': '重新支付',
	'paymentSuccessful': '支付成功',
	'thankPurchase': '感谢您的购买',
	'continueShopping': '继续购物',

	// paySuccess.vue
	'orderPaymentSuccessful': '订单支付成功',
	'viewDeliveryCode': '查看提货码',
	'deliveryAddress': '提货地址',
	'takeDeliveryTime': '提货时间',
	'iGotIt': '我知道了',


	// payWay.vue
	'theRemainingTime': '剩余时间',
	'PayWithAli': '支付宝支付',
	'payWithWeChat': '微信支付',
	'payWithWeChatFriend': '微信好友帮忙付',
	'determinePayment': '确定付款',


	// personalInformation.vue
	'gender': '性别',
	'female': '女',
	'male': '男',
	'userName': '用户名',
	'userNameCannotChange': '用户名无法更改',


	// prod.vue
	'aGroup': '拼团',
	'onlyStart': '距开始还有',
	'onlyEnd': '距结束仅剩',
	'day': '天',
	'time': '时',
	'minute': '分',
	'second': '秒',
	'collection': '收藏',
	'collected': '已收藏',
	'follow': '关注',
	'groupPrice': '人拼团价',
	'originalPrice': '原价',
	'promotion': '促销',
	'every': '每',
	'maximumReduction': '最高减免',
	'getCoupons': '领券',
	'reduce': '减',
	'makeDa': '打',
	'piecesZ': '张',
	'joinAGroup': '以下小伙伴正在发起拼团，你可以直接参加',
	'lack': '还差',
	'lack1': '人成团，剩余',
	'toGatherGroup': '去凑团',
	'groupInvitation': '支付开团邀请',
	'groupInvitationTips': '人参团，人数不足自动退款',
	'selected': '已选',
	'praise': '好评',
	'itemTiao': '条',
	'mediumEvaluation': '中评',
	'badEvaluation': '差评',
	'havePictures': '有图',
	'anonymousEvaluation': '匿名评价',
	'viewAllEvaluation': '查看全部评价',
	'selfShop': '自营店铺',
	'homepage': '首页',
	'shoppingCart': '购物车',
	'addShoppingCart': '加入购物车',
	'buyNow': '立即购买',
	'individualShopping': '单独购买',
	'startAGroup': '开团',
	'coupon': '优惠券',
	'outOfStock': '无货',
	'inventory': '库存',
	'productEvaluation': '商品评价',
	'rating': '好评度',
	'shopReply': '店铺回复',
	'noProductReviewsTips': '还没有商品评价~',
	'clickLoadMore': '点击加载更多',
	'shareFriendsNow': '立即分享给好友',
	'shareFriendsTips1': '朋友通过你分享的页面成功购买后，你可获得对应的佣金，佣金可“个人中心-分销员中心" 里查看',
	'weChat': '微信',
	'QRCode': '二维码',
	'myDistributorCenter': '我的分销员中心',
	'saveAlbum': '保存至相册',
	'shareFriendsTips2': '点击屏幕右上角将本商品分享给好友',
	'earn': '赚',
	'quitPlaying': '退出播放',
	'collectionAdded': '已添加收藏',
	'collectionCancelled': '已取消收藏',
	'successfullyAddedCart': '加入购物车成功',
	'insufficientStock': '库存不足',
	'purchaseLimit': '限购',
	'shartTips': '这个商品不错，快来看看吧！',
	'cancleCollection': '取消收藏',
	'actNotBegin': '活动未开始',
	'leastTips': '商品数量不能小于1',
	'enterShop': '进店看看',
	'discountPackage': '优惠套餐',
	'packages': '套餐',
	'packagePrice': '套餐价',
  'savesYou': '为您节省',
	'Giveaways': '赠品',
	'numberOfPackages': '套餐数量',
	"notAvailableForPurchase": "活动商品不可购买",

	// prod-classify.vue
	'limitedTimeOffer1': '限时特惠：以下商品可使用满',
	'dailyUpdate': '每日上新',
	'mallHotSale': '商城热卖',
	'moreBaby': '更多宝贝',
	'dailyPurchase': '每日疯抢',


	// prodComm.vue
	'shareTips': '把使用心得分享给想买的他们吧',
	'maxUpPic': '最多可上传9张照片',
	'postReview': '发表评价',
	'score': '评分',
	'evaluationEmpty': '评价不能为空',
	'evaluationSuccessful': '评价成功，感谢您！',

	// promotion-order.vue
	'unsettlement': '未结算',
	'settled': '已结算',
	'incomeExpired': '已失效',
	// 数组
	'aTotalProducts': '共1件商品',
	'commission': '佣金',


	// promotionProd.vue
	'enterProductName': '请输入商品名称',
	'newest': '最新',
	'sales': '销量',
	'price': '价格',
	'expectedEarn': '预计可赚',
	'shareIt': '分享',
	'downloadComplete': '已保存至相册',
	'failedSave': '保存失败，请再次点击保存到相册',
	'failedSaveTips': '保存失败，请授权相关访问权限',


	// register.vue
	'enterValidPhone': '请输入正确的手机号',
	'haveAnAccount': '已有账号?',
	'goToLogin': '去登录',
	'goToBind': '去绑定',
	'nextStep': '下一步',
	'setNickname': '设置昵称',
	'pleaseSetNickname': '请您设置昵称',
	'setAccountNumber': '设置账号',
	'pleaseSetAccount': '请您设置账号/用户名',
	'setAccountNumberTips': '账号由长度4~16位的字母、数字和下划线组成',
	'setPassword': '设置密码',
	'pleaseSetPassword': '请您设置密码',
	'passwordVerification': '密码由字母加数字或符号至少两种以上字符组成6-20位半角字符，区分大小写',
	'confirmPasswordAgain': '请再次确认密码',
	'ConfirmPasswordNot': '确认密码与密码不一致',
	'registrationSuccessful': '恭喜你，注册成功！',
	// 弹窗


	// shopInfo.vue
	// 静态的!


	// salesmanLevel.vue
	'hierarchicalRules': '等级规则',
	'introductionUpgradeRules': '升级规则介绍',
	'piecesBi': '笔',
	'upgradeRules': '升级规则',
	'upgradeRules1': '累计收益达到',
	'upgradeRules2': '绑定客户数量达到',
	'upgradeRules3': '累计邀请分销员数量达到',
	'upgradeRules4': '累计邀请客户数量达到',
	'upgradeRules5': '累计支付成功单数',
	'upgradeRules6': '累计消费金额达到',
	'upgradeRules7': '累计成交订单达到',
	'upgradeRules8': '累计成交金额达到',
	'purchaseDesignatedGoods': '购买指定商品',
	'upgradeImmediately': '立即升级',
	'upgradeSuccessful': '升级成功！',

	// search-page.vue
	'commodity': '商品',
	'cancel': '取消',
	'enterKeywordSearch': '输入关键字搜索',
	'popularSearches': '热门搜索',
	'searchHistory': '搜索历史',
	'inpKeyWords': '请输入关键字',


	// search-prod-show.vue
	'Comprehensive': '综合',

	// searchShopShow.vue
	'peopleConcerned': '人关注',
	'lookShop': '进店看看',
	'noData': '暂无数据',

	// selectStore.vue
	'findPickUpPoints': '输入名称寻找周边自提点',
	'recentPickUpPoint': '最近自提点',
	'address': '地址',
	'pickUpTime': '时间',
	'pickupNearby': '附近自提点',
	'saveAndUse': '保存并使用',
	'failedGetAddress': '获取地址失败，无法加载自提点列表',


	// shopCategory.vue
	'searchForItems': '搜索店内商品',
	'noProductsTips': '此分类下暂无商品',
	'noProducts': '没有更多商品了',
	'noMoreActivities': '没有更多活动了',

	// 静态的标记

	// shopInfo.vue
	'certificateInformation': '证件信息',
	'storeProfile': '店铺简介',
	'wenetSelfSupermarket': 'WeNet自营超市~~',
	'openingTime': '开店时间',
	'openingTime1': '2019年09月18日',

	// shopPage.vue
	'viewMoreProducts': '查看更多商品',
	'haveSpacePeople': '人',
	'tenThousandPeople': '万人',
	'shopHotSale': '店铺热卖',
	'storeStatusTips': '店铺状态异常，请联系平台客服处理',
	'storeStatusTips2': '店铺状态异常',
	'storeStatusTips3': '店铺已下线',
	'storeReviewTips4': '商家尚未营业',
	'storeReviewTips5': '商家已暂停营业',

	// shopProds.vue
	'default': '默认',


	// user.vue
	'registration': '立即注册',
	'paidMembership': '付费会员',
	'ordinaryMembership': '普通会员',
	'buyMembership': '购买会员',
	'loginNow': '立即登录',
	'clickAccountLogin': '点击账号登录',
	'myOrder': '我的订单',
	'refundAfterSale': '退款/售后',
	'scanCodePickup': '自提扫码',
	'distributioncenter': '分销中心',
	'couponCenter': '领券中心',
	'membershipCentre': '会员中心',
	'myDiscountCoupon': '我的优惠券',
	'myCollection': '我的收藏',
	'storeAccountSettings': '店铺账号设置',
	'modifyShopPassword': '修改店铺密码',
	'myShop': '我的店铺',
	'messageBox': '客服消息',
	'contactCustomerService': '联系平台',
	'signOut': '退出登录',
	'openInWeChatBrowser': '请在微信浏览器打开',
	'failedInvokeScan': '唤起扫码失败，请稍后重试',
	'applicationReview': '您提交的申请正在审核中',
	'applicationFailed': '您的申请未通过，具体原因请咨询客服，重新进行申请',
	'distributorBanned': '您的分销员身份已被封禁，具体原因请咨询客服',
	'distributionPromotionHasBeenClosed' : '分销推广已关闭',
	'distributorCleared': '您的分销身份已被封禁，请联系管理员',
	'myCollectionGoods': '我的收藏商品',
	'shopOpeningReview': '开店申请审核中',
	'myMessage': '我的消息',
	'changeLangs': 'Switch to English',
	'freeShop': '免费开店',
	'userIntegral': '积分',
	'commonTool': '常用功能',
	'vipContent': '会员专属',
	'vipBtn': '去购买',


	// shopSearch.vue
	'searchSllShop': '搜索本店所有商品',
	'shopHotSearch': '店铺热搜',

	// shopSearchResult.vue

	// snapUpDetail.vue
	'secondKillOver': '秒杀已结束',
	'select': '选择',
	'remaining': '剩余',
	'secondKillPrice': '秒杀价',
	'purchaseQuantity': '购买数量',
	'secondKillTips': '当前秒杀商品不可购买',
	'retailPricePurchase': '零售价购买',


	// snapUpList.vue
	'onlyLeft': '仅剩',
	'businessesReleased': '商家还未发布秒杀相关商品',

	// spellGoodsDetails.vue
	// 待定标记

	// spellGroupDetails.vue
	'groupJoiningRules1': '拼团规则：邀请',
	'groupJoiningRules2': '人参团，人数不足自动退款',
	'groupJoinTime': '拼团剩余时间',
	'groupJoiningSuccess': '拼团成功',
	'groupJoiningFailure': '拼团失败',
	'teamLeader': '团长',
	'viewAllMembers': '查看全部团员',
	'peopleJoinGroup': '人，快来加入我们的团吧！',
	'peopleGroupSuccess': '人，即可拼团成功！',
	'JoinGroupNow': '立即参团',
	'viewTeam': '查看团队',
	'viewOrderDetails': '查看订单详情',
	'inviteFriendsJoin': '邀请好友参团',
	'jionInGroup': '参团',


	// stationOrderList.vue
	'confirmPickUp': '确认核销',
	'SelectOrderPickup': '请选择需要核销的订单',
	'confirmPickUpTips': '是否确认核销?',
	'pickUpSuccessfully': '核销成功！',

	// sub-category.vue
	'noCommodity': '此分类下暂无商品',

	// submit-order.vue
	'mailToHome': '邮寄到家',
	'fillReceivingInformation': '填写收货信息',
	'historicalAddress': '历史选用地址',
	'itemGe': '个',
	'consignee': '收货人',
	'consigneeTips': '请输入收货人姓名',
	'enterContactNumber': '请输入联系电话',
	'yourRegion': '所在区域',
	'selectProvinceCity': '请选择省市区',
	'enteDetailedAddress': '请输入详细地址',
	'selectPickUpAddress': '选择提货地址',
	'fillPersonInformation': '填写提货人信息',
	'historicalPickPerson': '历史提货人',
	'enterNamePerson': '请输入提货人姓名',
	'enterPhonePerson': '请输入提货人电话',
	'haveDiscount': '已优惠',
	'notAvailable': '暂无可用',
	'zhangAvailable': '张可用',
	'storeNotes': '买家留言',
	'storeNotesTips': '选填',
	'productsNotSupported': '以下商品不支持',
	'chooseAnotherDelivery': '请选择其他配送方式',
	'platformCoupons': '平台优惠券',
	'pointsDeduction': '积分抵扣',
	'enterPoints': '请输入积分数量',
	'enterCorrectPoints': '请输入正确的积分数量',
	'mostUserPoints': '该订单最多使用',
	'integral': '积分',
	'notEnough': '积分不足!',
	'multipleOf10': '请输入10的倍数',
	'notUsePoints': '不使用积分抵扣',
	'prodTransfee': '商品运费',
  'freightPayable': '应付运费',
	'gradeFreeAmount': '等级免运费金额',
	'memberDiscountAmount': '会员折扣',
	'storeCoupons': '店铺优惠券',
	'eventOffer': '活动优惠',
	'totalDiscountAmount': '总优惠金额',
	'availableCoupons': '可用优惠券',
	'unavailableCoupons': '不可用优惠券',
	'getCouponTips': '这里还没有优惠券，快去领券吧~',
	'NoRelevantCoupons': '暂无相关优惠券',
	'modifyDeductiblePoints': '修改抵扣积分',
	'selectDeliveryMethod': '选择配送方式',
	'ordinaryExpress': '普通快递',
	'optionalAddress': '可选用地址',
	'pickUpPerson': '提货人',
	'choosePickUpTime': '选择提货时间',
	'enterPointsTips': '请输入正确的积分数量',
	'pleaseSelectSddress': '请选择地址',
	'saveAndUseTips': '请完善地址信息并保存',
	'productNotSupported': '此商品暂不支持',
	'productNotSupportedStop': '此商品暂不支持到店自提',
	'selectPickPoint': '请选择自提点',
	'fillDeliveryPersonInformation': '请填写提货人信息',
	'selectPickUpTime': '请选择提货时间',
	'newAddressesLimit': '新增地址已达上限(10条)',
	'enterCorrectPhone': '请输入正确的手机号码',
	'selectLatitudeLongitude': '请选择经纬度',
	'savedSuccessfully': '保存成功',
	'authorityTips': '请在「右上角」 - 「关于」 - 「右上角」 - 「设置」授予获取位置信息的权限',
	'use': '使用',
	'accountSurplus': '账户剩余',
	'accountSurplus2': '积分, 该订单最大可用',
	'month': '月',
	'beforeYesterday': '前天',
	'yesterday': '昨天',
	'today': '今天',
	'tomorrow': '明天',
	'afterTomorrow': '后天',
	'courier': '快递',
	'duplicateErrorTips': '请勿同时提交订单',

	// take-notes.vue
	'noRecords': '暂无记录',
	'totalWithdrawal': '总提现',
	'wechatPayment': '微信打款',
	'theFallback': '回退',
	'rejection': '拒绝提现',
	'withdrawing': '提现中',
	'hasArrived': '已到账',
	'loading': '正在加载...',
	'noMore': '没有更多了~',
	'noPromotionProduct': '没有找到推广商品哦~',
	'noRecordsFound': '没有找到相关记录',
	'noWithdrawalRecord': '还没有提现记录哦~',

	// withdrawal.vue
	'withdrawalAmount': '提现金额',
	'availableCashAmount': '可提现金额￥',
	'withdrawAll': '全部提现',
	'confirmWithdrawal': '确认提现',
	'enterCorrectAmount': '请输入正确金额！',
	'insufficientBalance': '余额不足',
	'lessThanWithdrawalAmount': '提现金额小于最小提现金额',
	'withdrawalApplicationSubmitted': '提现申请已提交',

	// writeReturnLogistics.vue
	'logisticsCompany': '物流公司',
	'remarks': '备注说明',
	'logisticsOrderNo': '物流单号',
	'logisticsOrderNoSucced': '请输入正确的物流单号',
	'logisticsOrderNoTips': '请输入物流单号',
	'optional200Words': '选填，最多200字',


	// packageMemberIntegral/pages/buyVip/buyVip.vue
	// buyVip.vue
	'expire': ' 到期',
	'notPayingMember': '您还不是付费会员~',
	'current': '当前',
	'notPurchased': '未购买',
	'week': '周',
	'season': '季',
	'year': '年',
	'growthValueUp': '成长值达',
	'readyToBuy': '即可购买',
	'currentGrowthValue': '当前成长值',
	'VIPLevelRights': 'VIP等级权益',
	'totalPrice': '总价',
	'choosePaymentMethod': '选择支付方式',
	'growthValueTips': '成长值不足，无法购买',
	'upgradeMemberTips1': '您当前为',
	'upgradeMemberTips2': '即将购买',
	'upgradeMemberTips3': '升级会员将覆盖当前会员时限，是否确认购买？',
	'cannotBuyLower': '付费会员期间无法购买低等级会员',

	// convertProdDet.vue
	'redeemNow': '立即兑换',
	'insufficientProductInventory': '商品库存不足！',

	// exchangeDetails.vue
	'itemFen': '份',
	'recent30Days': '近30天已兑换',
	'productIntroduction': '商品介绍',

	// integralIndex.vue
	'myIntegral': '我的积分',
	'detailed': '明细',
	'earnPoints': '赚积分',
	'pointsExchange': '积分兑换',
	'pointCenter': '积分中心',
	'pointDetail': '积分明细',

	// integralSubmitOrder.vue
	'preferentialAmount': '优惠金额',
	'confirmSpend': '确认花费',
	'scoreToPay': '积分支付？',

	// luckyDraw.vue
	'luckyDraw1': '当转盘停止转动后才有效',
	'luckyDraw2': '积分不足,请先赚取积分!',
	'addAddressFirst': '请先添加收货地址',

	// memberCenter.vue
	'currentGrowthFull': '当前成长值已满',
	'upgradeStillNeeded': '升级还需',
	'growthValue': '成长值',
	'memberGrowthDescription': '会员成长说明',
	'grade': '等级',
	'correspondingGrowthValue': '所需成长值',
	'exclusiveMembers': '会员专属权益',
	'moreInterests': '更多权益',
	'itemXiang': '项',

	// memberIndex.vue
	'currentPoints': '当前积分',
	'dailySignIn': '每日签到',
	'signIn': '可签到',
	'signedIn': '已签到',
	'dayItem': '第',
	'integralActivity': '积分活动',
	'pointsMall': '积分商城',
	'exchangePointsGoods': '积分兑换好物',
	'largeTurntable': '大转盘',
	'PointsLuckyDraw': '积分抽奖活动',
	'integralTask': '积分任务',
	'shoppingMall': '商城购物',
	'shoppingMallGet': '商城购物可获得积分',
	'itemYuan': '元/积分',
	'goShopping': '去购物',
	'registerNewUsers': '注册新用户',
	'registerNewUsersTips': '每位用户注册成为新会员可获得',
	'toComplete': '去完成',
	'comingOnlineSoon': '即将上线，敬请期待~',
	'hasAlreadySigned': '这天已经签到过啦~',

	// integralDetail.vue
	'integralStrategy': '积分攻略',
	'rewardPointsGoods': '购买商品获赠积分',
	'refundShopping': '退款扣除购物获赠积分',
	'pointsLevelPromotion': '等级提升获赠积分',
	'signGetPoints': '签到获赠积分',
	'refundGoodsPoints': '商品退款退回抵现积分',
	'purchaseGoodsPoints': '购买商品抵扣积分',
	'noDetails': '暂无明细',
	'pointsExpired': '积分过期',
	'rechargeGetPoints': '充值余额获赠积分',
	'registerEarnPoints': '注册获赠积分',
	'platformChanges': '系统变更',


	// packageMemberIntegral/components
	// IntegralGoodsList/IntegralGoodsList.vue
	'marketValue': '市场价',
	'thankYourPatronage': '谢谢惠顾',
	'goods': '物品',
	'clickLottery': '点击抽奖',
	'threePoints': '300积分',
	'drawPrize': '抽一次奖',
	'goodLuck': '运气爆棚!',
	'thankParticipation': '谢谢参与',
	'luckNextTime': '运气留给下次吧~',
	'error': '错误',
	'wrongServer': '服务器开了点小差，待会儿再来吧~',


	// wenetshop-station/components
	// coupon.vue
	'universal': '平台',
	// "commodity": '商品',
	'allUniversal': '全场通用(特殊商品除外)',
	'specifiedItems': '部分商品可用',
	'invalid': '天后失效',
	'useItNow': '立即使用',

	// loginPopup.vue
	'welcomeWenetshop': '欢迎来到WeNet商城',
	'authorizeLogin': '请授权登录，获得完整购物体验',

	// production.vue
	'timeLimitedPrice': '限时价',

	// shop-tabbar.vue
	'shopHomepage': '店铺首页',
	'allCommodities': '全部商品',
	'commodityClassification': '商品分类',
	'storeSearch': '店内搜索',

	// tki-barcode.vue
	'barcodeSavedSuccessfully': '条形码保存成功',
	// tki-qrcode.vue
	'generating': '二维码生成中',
	'qrCodeCannotBeEmpty': '二维码内容不能为空',
	'bqrCodeSavedSuccessfully': '二维码保存成功',

	// index.vue下的tabber导航栏
	// "homepage": '首页',
	'classification': '分类',
	'cart': '购物车',
	'myself': '我的',
	'find': '发现中心',
  'stores': '店铺街',



	// pages.json
	'viewStartupLog': '查看启动日志',
	'personalCenter': '个人中心',
	'shoppingCart2': '购物车',
	'classifiedGoods': '分类商品',
	'editShippingAddress': '编辑收货地址',
	'orderList': '订单列表',
	'orderDetails': '订单详情',
	'bindingMobilePhone': '绑定手机号码',
	'paymentResults': '支付结果',
	'searchResult': '搜索结果',
	'commodityDetails': '商品详情',
	'wenetCollection': 'WeNet精选',
	'latestAnnouncement': '最新公告',
	'withdrawal': '提现',
	'groupDetails': '拼团详情',
	'secondsKillProductDetails': '秒杀商品详情',
	'detailsGroupProducts': '拼团商品详情',
	'membersTheDetails': '团员详情',
	'makeSureOrder': '确认订单',
	'collectionShops': '收藏店铺',
	'applyOpenShop': '申请开店',
	'groupBuyingList': '团购列表',
	'storeSearchResults': '店铺搜索结果',
	'changeStorePassword': '修改店铺密码',
	'userLogin': '用户登录',
	'userRegistration': '用户注册',
	'checkoutCounter': '收银台',
	'logisticsDetails': '物流详情',
	'shopSearch': '店铺搜索',
	'selectPickUpPoint': '选择自提点',
	'pickUpOrderList': '自提点订单列表',
	'writeDeliveryOrder': '核销到店自提订单',
	'personalData': '个人资料',
	'liveList': '直播列表',
	'wenetMultiStore': 'WeNet商城',
	'positionInformation': '位置信息',
	'specialOfferList': '优惠活动',


	// liveBroadcast.vue
	'tabbarLive': '直播',
	'liveBroadcast': '直播',
	'inputBlogger': '输入博主/直播间/商品...',
	'liveing': '直播中',
	'notStarted': '未开始',
	'finished': '已结束',
	'noBroadcasting': '禁播',
	'suspend': '暂停',
	'abnormal': '异常',
	'liveBroadcastTips': '暂无相关直播数据',
	'pleaseOpenInWechat': '请在微信小程序打开',
	'inLive': '直播中',
	'more': '更多',


	'pickitUp': '自提',

	// recent-news.vue
	'read': '已读',
	'unread': '未读',
	// js
	'serverWrong': '服务器出了点小差',
	'loginExpired': '登录已过期，请重新登录！',
	'cannotPurchaseTips': '您已被禁用，不能购买，请联系客服',
	'configInformationFailed': 'config信息验证失败',
	'userCancelsSharing': '用户取消分享',
	'purchaseSuccessful': '购买成功!',
	"currentBalance": '当前余额',


	'expect': '预计',
	'startDelivery': '开始发货',
	'service': '客服',
	'preSale': '预售',
	'selectCoordinates': '配送方式为同城配送时地址信息需包含经纬度，请选择经纬度',
	'virtualMonth': '月',
	'dayOfAMonth': '日',
	'cityAddressPrompt': '当配送方式为同城配送时，用户地址信息中需包含经纬度，请前往地址页面将已选地址经纬度补充完整',
	'balancePay': '余额支付',
	'confirmBalancePay': '是否确认使用余额支付？点击确认按钮后将从余额支付本单金额',
	'cancelBalancePay': '已取消使用余额支付',
	'shortBalance': '余额不足，请先充值！',
	'currencySymbol': '￥',
	'balance': '余额',
	'messageNotify': '消息通知',

	//myWallet
	'totalBalance': '总余额',
	'recharge': '充值',
	'balanceDetails': '余额明细',
	'rechargeBalance': '余额充值',
	'rechargeGift': '充值赠送',
	'shoppingConsumption': '购物消费',
	'noBalanceDetails': '暂无余额明细',

	'handsel': '送',
	'handsels': '赠送',
	'sheet': '张',
	'needToPay': '需要支付',
	'payFail': '支付失败，请重新支付！',

	'PCS': '平台客服',
	'platformAnnouncement':'平台公告',
	'systemNotification': '系统通知',
	'youHaveNoMessages': '您还没有消息记录',
	'send': '发送',
	'noMessage': '暂无消息',

	'maximumDiscount': '最多优惠',

	'goodsBePicked': '待提货',

	'totalAmount': '总额',

	'selStore': '请选择自提点',
	'locateFailure': '获取定位失败，无法加载自提点列表。',
	'platformModification': '系统变更',

	'customerOffline': '当前客服不在线',
	'loginOtherSide': '用户在别处登陆,请刷新页面',
	'reLogin': '无法获取用户信息,请重新登陆',
	'copyLink': '复制链接',
	'copySuccess': '已复制',

	'imageTypeRestrictions': '请上传正确图片格式',
	'pageSkipping': '页面跳转中',
	// 装修页相关
	'pageGone': '页面不存在！',
	'secondKillClosed' : '该秒杀商品已关闭!',
	"secErrorTips": '活动过于火爆，重新提交试试吧~',
	'paypalPay': 'PayPal支付',
	"loading": '加载中...',

	// 客服消息
	'sendLink': '发送链接',
	'productLinks': '商品链接',
	'newMessage':'条新消息',
	"clickToLoadMore": "点击加载更多",
	"sendLink": "发送链接",
	"send": "发送",
	"inquiring": "正在查询",
	"myOrders": "我的订单",
	"recentlyViewed": "最近浏览",
	"orderNumber": "订单编号",
	"none": "无",
	"loading":"加载中",
	"onLine": "(在线)",
	"offLine": "(离线)",
	"allLoaded": "已全部加载完毕",
	"noMore": "没有更多了",
	"read": "已读",
	"unRead": "未读",
	"noRecord": "暂无记录",
	"customerOffline": "当前客服不在线",
	"loginOtherSide": "用户在别处登陆,请刷新页面",
	"reLogin": "无法获取用户信息,请重新登陆",
	"sendOrderNumber": "发送订单号",
	"pleaseLoginAgain": "账号已下线，请重新登录",
	"paymentAmount": "支付金额",
	"onlineCustomerService": "在线客服",
	"productLinks":"商品链接",

	// 订单状态
	"pendingPayment": "待支付",
	"pendingDelivery": "待发货",
	"pendingReceipt": "待收货",
	"pendinEvaluation": "待评价",
	"evaluated": "已评价",
	"shipped": "已发货",
	"completed": "已完成",
	"canceled": "已取消",
	"grouping": "拼团中",
	"onlineRecharge": "在线充值",
	"rechargeOffer": "充值优惠",
	"additionalGifts": "额外赠送",
	"customAmount": "自定义金额",
	"maximumTopUp": "单次最多可充值",
	"millionYuan": "万元",
  "enterRechargeAmount": "请输入充值金额",
  "rechargeAmountMinimum": "充值金额不能小于0.01",
  "rechargeAmountMaximum": "充值金额不能超过10万",
	"stationCodeTips": "请扫描正确的自提点二维码",
	"stationNotFound": "未查询到相关自提点，请重试",
  "memberPackage": "会员包邮",
  "storeDiscount": "店铺优惠",
  "shippingDiscount": "运费减免",
  "promotionOffer": "促销满减",
	"packageOffer": "套餐优惠",
  "merchantsModifyPrices": "商家改价",

	//发票
	"invoice": {
    "InvoiceDetails": "发票详情",
		"onvoiceIssuance": "开具发票",
    "orderInfo": "订单信息",
    "orderStatus": "订单状态",
    "orderNumber": "订单编号",
    "orderCreateTime": "下单时间",
    "invoiceType": "发票类型",
    "generalInvoice": "普通电子发票",
    "noInvoice": "本次不开具发票",
    "invoiceInfo": "发票信息",
    "invoiceStatus": "发票状态",
    "invoiced": "已开票",
    "application": "申请中",
    "invoiceContent": "发票内容",
    "productDetails": "商品明细",
    "invoiceTitle": "发票抬头",
    "companyTaxNumber": "公司税号",
    "invoiceDownload": "发票下载",
    "uploadTime": "开票时间",
    "invoiceExchange": "发票换开",
    "viewInvoice": "查看发票",
    "updateInvoice": "修改发票",
    "personal": "个人",
    "unit": "单位",
		"electronic": "电子",
    "personalName": "个人名称",
    "inputTips1": "请输入“个人”或个人姓名",
    "unitName": "单位名称",
    "inputTips2": "请输入单位名称",
    "inputTips3": "请输入纳税人识别号",
		"inputTips4": "请输入正确的纳税人识别号",
    "TaxpayerIDNumber": "纳税人识别号",
    "invoiceInstructions": "发票须知",
    "invoiceTips1": "依照税局最新开票法规，发票开具内容均为明细",
    "invoiceTips2": "电子普通发票和纸质普通发票具备同等法律效力，可支持报销入账",
    "invoiceTips3": "开票金额为实际支付金额，不包括优惠券、积分、红包等支付金额",
    "invoiceTips4": "电子发票可在确认收货后，在“发票详情”页面下载",
    "saveInvoiceInformation": "保存发票信息",
    "viewInvoiceDetail": "查看发票详情",
    "InvoicingRequest": "申请开票",
    "update": "修改",
    "successTip": "申请成功",
    "successTip1": "更新成功",
		"amountPaid": "实付金额",
		"questions": "常见问题",
    "questions1": "发票类型说明：默认开具电子普通发票，电子普通发票和纸质普通发票具备同等法律效力，可支持报销入账和售后维权，如需开具增值税专用发票，请与商家联系；",
    "questions2": "发票开具时间：订单确认收货后，将由商家开具，可在“个人中心 - 我的订单 - 查看发票”页面查看进度；",
    "questions3": "发票开具金额：开票金额为您实际支付金额，不包括优惠券、积分、红包等支付金额；",
    "questions4": "发票开具内容：依照税局最新开票法规，发票内容均为商品明细，将显示详细商品名称与价格信息；",
    "questions5": "发票哪里下载：订单完成后，可在“个人中心 - 我的订单 - 查看发票”页面下载发票。",
		"updateMsg": "申请重开成功",
		"successMsg": "申请开票成功"
  },
	// 满减
	"amount": "满额减",
	"pieces": "满件减",
	"amountDiscount": "满额折",
	"piecesDiscount": "满件折",
	"noDiscount": "不参与促销",

	"inputAllSpace": "内容不能全为空格",
	"more": "更多",

	// 虚拟商品
	"pleaseEnter": "请输入",
	"allMsg": "全部留言",
	"viewMsg": "查看留言",
	"gotIt": "我知道了",
	"toBeUsed": "待使用",
	"sheets": "张",
	"used": "已使用",
	"expirationDate": "有效期限",
	"longTermValidity": "购买后长期有效",
	"to": " 至 ",
	"numberOfWriteOffs": "核销次数",
	"multipleWriteOffs": "多次核销",
	"singleWriteOffs": "单次核销",
	"noWriteOffRequired": "无需核销",
	"voucherCode": "券码",
	"refunded": "已退款",
	"totalAmount": "总金额",
	"validityDate": "有效期",
	"tipsForUsingVoucherCode": "为保障您的权益，未消费前请不要将二维码/条形码提供给商家",
	"savePicTips": "长按二维码保存到相册",
	"usageInstructions": "使用说明",
	"afterPurchase": "购买后",
	"effective": "有效",
	"validOnTheSameDay": "购买后当天24点前有效",
	"purchase": "核销码",
	"validDay": "天内有效",
	"refundsAreNotAllowed": "不支持申请退款",
	"requiredMessage": "请填写必填项留言",
	"virtualGoods": "虚拟商品",
	"SelfPickupGoods": "自提商品",
	"pendingWriteOffs": "待核销",
	"mesgCannotBeAllSpaces": "留言不能全为空格！",
	"history": "我的足迹",
	"historyEmpty": "您还没有浏览过商品",
	"deleteHistory": "删除足迹",
	"deleteHistoryTips": "确定要删除足迹吗",

	// privacy 隐私协议&服务条款弹窗
	"termsServiceAndPrivacyPolicy": "服务条款和隐私策略",
	"content1": "请你务必审慎阅读、充分理解“服务条款”和“隐私策略”各条款，包括但不限于：为了更好的向你提供服务，我们需要收集你的设备标识、操作日志等信息用于分析、优化应用性能。",
	"uCanRead": "你可阅读",
	"content2": "了解详细信息。如果你同意，请点击下面按钮开始接受我们的服务。",
	"haveReadAndAppgree": "已阅读并同意",
	"disagree": "暂不同意",
	"agreeAndAccept": "同意并接受"
};

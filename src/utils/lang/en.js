// 英文语言包
export const index = {
	// index.vue
	'search': 'Search',
	'newProduct': 'New product',
	'limitedTimeOffer': 'Limited time offer',
	'groupDiscount': 'Group discount',
	'spikeSpecial': 'Seckill discount',
	'coupons': 'Coupons',
	'spike': 'Second kill',
	'moreSpikes': 'More',
	'groupBuy': 'Group buy ',
	'moreGroupBuy': 'More',

	'join': ' people join',
	'seeMore': 'More',
	'hotSale': 'Hot in mall',
	'allLoaded': 'All products loaded',

	// aBulkList.vue
	'businessesRelatedProducts': 'Businesses have not yet released group buying related products',

	// accountLogin.vue
	'username': 'Username',
	'enterUsername': 'User name / Mobile number',
	'usernameWarn': 'Please input Username',
	'password': 'Password',
	'enterPassword': 'Password',
	'changePassword': 'Change password',
	'notUsername': 'No account yet?',
	'registeredUsername': 'Go to register',
	'phoneNumber': 'Mobile',
	'enterPhone': 'Enter phone number',
	'phoneWarn': 'Malformed phone number',
	'verificationCode': 'Verification code',
	'enterCodeFirst': 'Please get the verification code first',
	'getCode': 'Get verification code',
	'enterCode': 'code',
	'newPassword': 'New password',
	'enterNew': 'New password',
	'confirmNew': 'Confirm password',
	'confirmNewEmpty': 'Verify that the password cannot be blank',
	'enterNewAgain': 'Reenter new password',
	'comparedPassword': 'Confirm password is inconsistent with new password',
	'login': 'Login',
	'consentOfLoginRepresentative' : 'Consent of Login Representative',
	'consentOfRegisteredRepresentative':'Consent of Registered Register',
	'termsOfService': 'Terms of service',
	'privacyPolicy': 'Privacy policy',
	'and': 'and',
	'loginSuccessful': 'Login successful!',
	'backHomepage': 'Back to the home',
	'confirmChanges': 'Confirm the changes',
	'BackLogin': 'Back to login',
	'pwdCantBeEmpty' : 'Password cannot be empty',

	// accountSettings.vue
	'loginAccount': 'Login account',
	'passwordLength': 'Please set 6-12 digits login password',
	'enterPasswordAgain': 'Please enter the password again',
	'confirm': 'Confirm',
	'accountTip': '',
	'enterCorrectLoginPassword': 'Please enter the correct login password',
	'loginPasswordTips': 'The confirmation password is inconsistent with the login password',
	'setupSucceeded': 'Setup succeeded!',

	// afterSales.vue
	'shop': 'Shop',
	'quantity': 'Quantity',
	'piece': ' piece',
	'seeDetails': 'View details',

	'refund': 'Refunds',
	'refund01': 'Only refund',
	'refund02': 'Return and refund',
	'refund1': 'You have submitted a refund request, waiting for the merchant to process',
	'refund21': 'Merchant has agreed, waiting for refund',
	'refund22': 'The merchant has agreed, please return the goods according to the given address',
	'refund3': 'Waiting for the merchant to confirm receipt and refund',
	'refund4': 'The merchant has confirmed the receipt and is waiting for the refund to arrive',
	'refund5': 'Refund successfully',
	'refund6': 'Refund request has been cancelled',
	'refund7': 'The merchant refuses, you can modify the refund application or apply for customer service intervention',
	'refund_1': 'Refund request is closed',
	'refundEmpty': 'There is no refund/after-sale related orders~',
	'sellerRemarks': 'Seller notes',
	// alterShopPassword.vue
	'oldPassword': 'Old password',
	'enterNewPasswordTips': 'Please enter the correct password format',
	'enterOldPassword': 'Current password',
	'newPasswordLength': 'Set 6-12 Bit new password',
	'enterNewPassword': 'Reenter new password',
	'alterShopPasswordTips': 'Tip: Please use the reset password above to open the store management center on the PC to log in. Merchant management terminal address',
	'successfullyModified': 'Modification successful!',

	// applyDist.vue
	'applyDistributor': 'Apply to become a distributor',

	// applyDistCon.vue
	'mobilePhone': 'Mobile',
	'actualName': 'Actual name',
	'identification': 'Identification number',
	'identificationTips': 'Please enter a valid ID number',
	'uploadIDFront': 'Please upload ID face',
	'uploadIDBack': 'Please upload ID card national emblem',
	'uploadIDHand': 'Please upload a hand-held ID photo',
	'enterRealName': 'please enter your real name',
	'applicationSubmitted': 'Application has been submitted, please wait for review',

	// applyRefund.vue
	'orderNumber': 'Order number',
	'refundMethod': 'Refund method',
	'cargoStatus': 'Cargo status',
	'refundReason': 'Reason for return',
	'returnquantity': 'Quantity returned',
	'refundAmount': 'Refund amount',
	'refundAmounts': 'Please enter refund amount',
	'maxAmount': 'Maximum refundable',
	'enterMobilePhone': 'Phone number',
	'refundDescription': 'Refund instructions',
	'descriptionLength': 'Required, up to 50 characters',
	'uploadCertificate': 'Upload certificate',
	'logisticsVouchers': 'Logistics Vouchers',
	'uploadPics': '5 pictures can be uploaded',
	'submit': 'Submit',
	'goodsNotReceived': 'Goods not received',
	'goodsReceived': 'Goods received',
	'pleaseChoose': 'Please choose',
	'wrongShot': 'Wrong shot / more shots / dislike',
	'refundConsensus': 'Refund by consensus',
	'damagedGoods': 'Damaged goods / missing items',
	'productNot': 'The product does not match the description',
	'sellerSendsWrong': 'Seller sends wrong goods',
	'qualityProblem': 'Quality problem',
	'includingFreight': 'Including freight￥',
	'returnQuantityTips1': 'Return quantity cannot be less than 1!',
	'returnQuantityTips2': 'Return quantity cannot be greater than order quantity!',
	'selectReason': 'Please select the reason for the refund',
	'fillCorrectAmount': 'Please fill in the correct refund amount',
	'refundAmountTips': 'The refund amount exceeds the maximum refundable amount of this order, please re-enter',
	'fillRefundDescription': 'Please fill in the refund description',
	'fillRefundDescriptionTips': 'Please fill in a description of no more than 50 words',
	'prompt': 'Prompt',
	'promptTips': 'The refund item requested for this time is the last item of this order, and the refund amount includes the shipping cost (if any) of this order. Once the application is submitted, the refund application for all the goods in this order cannot be cancelled. Confirm to submit',
	"orderChangeTips": 'Order status has changed, please resubmit your request',
	"maximumRefund": 'Maximum refund ￥',
	"pleaseRetype": ', Please re-enter',
	"autoReturn": ' will be returned automatically',
	// basket.vue
	'selectAll': 'All',
	'delete': 'Delete',
	'total': 'Total',
	'instantReduction': 'Reduction',
	'settlement': 'Settlement',
	'selectPromotion': 'Select promotion',
	'notPromotion': 'Not participating in promotion',
	'amountDetails': 'Amount details',
	'comTotal': 'Total merchandise',
	'promotionalDiscount': 'Promotional discount',
	'shoppingTips': 'You haven\'t added the product to the shopping cart~',
	'chooseCom': 'Choose settlement products',
	'delivery': 'Delivery',
	'deliveryTips': 'Products of different delivery methods currently do not support simultaneous settlement',
	'expressDelivery': 'Express delivery',
	'sameDelivery': 'Same city delivery',
	'toSettle': 'To settle',
	'inTotal': 'Total ',
	'pickStore': 'Take at store',
	'selectProduct': 'Please select a product',
	'deleteProductTips': 'Are you sure you want to delete the selected product?',
	'basketSelectedCount': 'Currently selected ',
	'basketCountOver': ' items; The shopping cart supports up to 50 items to be settled together, please order separately',

	// binding-phone.vue
	'bind': 'Bind',
	'enterMobileNumber': 'Please enter the phone number',
	'enterMobileNumberTips': 'Phone number input error',

	// category.vue
	'searchGoods': 'Search for the goods you want',
	'viewAll': 'View all',

	// chooseRefundWay.vue
	'chooseRefundMethod': 'Choose a refund method',
	'goodsTips1': 'No goods received, or no need to return, only refund',
	'goodsTips2': 'The goods have been received and need to be returned for a refund',

	// confirmOrder.vue
	'addShippingAddress': 'Add shipping address',
	'shippingAddress': 'Shipping address',
	'buyerMessage': 'Buyer message',
	'buyerTips': 'Recommended to communicate first',
	'goodsAmount': 'The amount of goods',
	'shipping': 'Transportation costs',
	'submitOrders': 'Submit orders',
	'wsConnectionFailed': 'Ws connection failed',
	'desperatelyBuying': 'Are desperately buying',
	'secFailTips': 'The activity is very hot, please try again later',
	'seckillReduce': 'Seckill Reduce',

	// couponCenter.vue
	'universalCoupon': 'Universal coupon',
	'discount': ' discount',
	'available': 'Available for ',
	// "available": 'Available',
	'availableUse': ' yuan can use',
	'universalAll': 'Universal for all categories',
	'universalOnly': 'Only part of the platform products can be purchased',
	'nocoupons': 'None',
	'nocoupons1': 'None',
	'haveCoupons': 'Grabbed ',
	'getIt': 'Receive',
	'getItByCoupon': 'Receive It By Use Coupon',
	'toUse': 'To use',
	'recommendCoupons': 'Recommend good coupons',
	'endTips': 'All data loaded',
	'merchantCoupon': 'The merchant has not released the coupon yet',
	'successfullyReceived': 'Successfully received coupons',
	'couponEventGoods': 'Coupon event goods',

	// delivery-address.vue
	'asDefaultAddress': 'set as the default address',
	'addNewAddress': 'Add new shipping address',
	'noShippingAddress': 'You have no shipping address',
	'addNewAddressTips': 'The new delivery address has reached the upper limit',

	// deliveryCertificate.vue
	'pickup': 'Pickup point',
	'pickingCode': 'Picking code',
	'returnOrderDetails': 'Return order details',

	// DetailsOfRefund.vue
	'refundDetails': 'Refund details',
	'refundDetails11': 'Refund request is pending processing by merchant',
	'refundDetails12': 'You have successfully initiated a refund request, waiting for the merchant to process',
	'refundDetails71': 'The merchant has rejected the refund request',
	'refundDetails72': 'You can cancel the refund application and reapply after active negotiation with the seller',
	'refundDetails2': 'Merchant agrees to refund',
	'refundDetails21': 'The merchant has agreed, waiting for the system to refund',
	'refundDetails22': 'The merchant has agreed, please return the goods according to the return address given by the merchant',
	'refundDetails31': 'Wait for the merchant to confirm receipt and refund',
	'refundDetails32': 'You have returned the goods, the merchant will deal with it as soon as possible after receipt',
	'refundDetails41': 'Merchant confirms receipt',
	'refundDetails42': 'The merchant has confirmed the receipt and is waiting for the system to refund',
	'refundDetails51': 'Refund successfully',
	'refundDetails_1': 'Refund application closed',
	'refundDetails61': 'Application has been withdrawn',
	'refundDetails62': 'You have withdrawn this refund request',
	'refundDetails13': 'If the merchant agrees, the application will be completed and a refund will be given to you',
	'refundDetails01': 'If the merchant refuses, you can cancel the refund application and reapply after active negotiation with the seller',
	'refundDetails23': 'If the merchant agrees, please return the product according to the return address given',
	'refundDetails33': 'If the merchant confirms the receipt, the application will be completed and a refund will be given to you',
	'refundDetails02': 'If the merchant refuses to accept the goods, you can modify the returned goods flow information or actively negotiate with the seller',
	'moreExplanation': 'More explanation',
	'putAway': 'Put away',
	'refundDetails73': 'Reason for rejection',
	'returnAddress': 'Return address',
	'recipient': 'Recipient',
	'recipientName': 'Please enter the recipient\'s name',
	'detailedAddress': 'Address',
	'refundInformation': 'Refund information',
	'logisticsInformation': 'Logistics information',
	'refundInstructions': 'Refund instructions',
	'integral': ' integral',
	'refundNumber': 'Refund number',
	'applicationTime': 'Application time',
	'quantityReturned': 'Quantity returned',
	'applicationCanceled': 'Application canceled',
	'fillInReturn': 'Fill in return logistics',
	'revoke': 'Revoke',
	'revokeTips': 'Are you sure you want to cancel this application?',
	'modifyReturn' : 'Modify return logistics',
	'refundVoucher': 'Refund Voucher',
	"modifyRefundAmount": "Modify refund amount",


	// dis-center.vue
	'noAnnouncement': 'No announcement',
	'vip':'Vip',
	'myBalance': 'Withdrawal balance (yuan)',
	'totalIncome': 'Total income (yuan)',
	'withdrawalsNow': 'Withdrawals now',
	'promoteGoods': 'Promote goods',
	'distributionWinWin': 'Distribution win-win',
	'inviteFriends': 'Invite friends',
	'promoteRewards': 'Promote Rewards',
	'myUser': 'My user',
	'myPromotion': 'My promotion',
	'distributorList': 'Distributor list',
	'incomeBreakdown': 'Income breakdown',
	'withdrawalsRecord': 'Withdrawals record',

	// discountDetail.vue
	'endOfDistance': 'End of distance',
	'specialty': 'specialty Occasion',
	'immediatelyBuy': 'Buy',

	// draw-rule.vue
	'WithdrawalRules': 'Withdrawal rules',
	'WithdrawalRules1': '1. The single withdrawal limit shall not be less than ',
	'yuan': ' yuan',
	'WithdrawalRules2': '2. The single withdrawal limit cannot be higher than ',
	'WithdrawalRules3': '3. Unlimited number of withdrawals per user per day',
	'WithdrawalRules31': '3. Each user every',
	'WithdrawalRules32': ' days has ',
	'WithdrawalRules34': ' Month has ',
	'WithdrawalRules33': 'opportunity to withdraw cash',

	// editAddress.vue
	'name': 'Name',
	'mobileNumberLength': '11-digit mobile number',
	'area': 'Area',
	'addressTips': 'Building/unit/house number',
	'saveAddress': 'Save the shipping address',
	'deleteAddress': 'Delete shipping address',
	'selectDetailedAddress': 'Please fill in the detailed address with no less than 5 words',
	'LatitudeNotEmpty': 'Latitude and longitude cannot be empty',
	'deleteAddressTips': 'Are you sure you want to delete this shipping address?',

	// groupConfirmOrder.vue
	'group': 'group',
	'group1': 'Choose a product to start a group/join a group',
	'group2': 'Invite friends to join the group',
	'group3': 'The number of people reaches a group',
	'headPrice': 'Head price',
	'groupPurchasePrice': 'Group purchase price',
	'tips': 'Tips',

	// income-details.vue
	'cumulativeIncome': 'Cumulative income (yuan)',
	'availableBalance': 'Available balance (yuan)',
	'toBeSettled': 'To be settled (yuan)',
	'monthEarnings': 'Earnings this month',
	'todayEarnings': 'Today\'s earnings',
	'reward': 'Reward',
	'other': 'Other',
	'reward1': 'Direct push bonus',
	'reward2': 'Interpush bonus',
	'reward3': 'Invitation reward',
	'noProfitToday': 'There is no profit today~',

	// InvitationCards.vue
	'inviteJoin': 'Invite you to join, promote and win rewards',
	'screenshotTips': 'Save the screenshot directly to the album',

	// login.vue
	'wenetMall': 'WeNet Cosmic Edition',
	'wenetMallJoin': 'WeNet Cosmic Edition welcomes you to join!',
	'weChatLogin': 'WeChat one-click login',
	'existingLogin': 'Existing account login',
	'privilegeGrantFailed': 'privilege grant failed',
	'privilegeGrantSucceed': 'privilege grant succeed',

	// logisticsInfo.vue
	'package': 'package',
	'carriageSource': 'Source of carriage',
	'waybillNumber': 'Waybill number',
	'copy': 'Copy',
	'noPackageTips': 'No package information',
	'alreadyReceived': 'Already received',
	'inTransit': 'In transit',
	'signedInReceived': 'Signed in received',
	'questionableDelivery': 'Questionable delivery',
	'arriveDestinationCity': 'Arrive destination city',
	'noDeliveryInformation': 'No delivery information available',

	// myCoupon.vue
	'unused': 'Unused',
	'usageRecord': 'Usage record',
	'expired': 'Expired',
	'couponTips': 'There is no relevant coupon here yet~',
	'deleteCouponTips': 'Are you sure you want to delete this coupon?',
	'UserClicksCancel': 'User clicks to cancel',

	// myShop.vue

	// my-users.vue
	'people': 'people',
	'avatar': 'Avatar',
	'nickname': 'Nickname',
	'bindingDate': 'Binding date',
	'noUsersYet': 'No users yet',
	'usernameEmptyTips':'The user name cannot be empty',
	'nicknameEmptyTips':'The nick name cannot be empty',

	// openAShop.vue
	'auditFailure': 'Audit failure',
	'auditFailureTips': 'Your application for opening a shop has not been approved. Please revise your application information and submit your application again',
	'storeInformation': 'Store Information',
	'storeName': 'Shop name',
	'storeNameTips': 'Enter the shop name, 4-20 characters',
	'shopDescription': 'Shop description',
	'shopDescriptionTips': 'Fill in the store description, 10-200 words',
	'shopLogo': 'Shop logo',
	'upShopLogo': 'Upload shop logo',
	'location': 'Location',
	'getLocation': 'Click to get location',
	'latitude': 'Lat：',
	'longitude': 'Long：',
	'shopPhone': 'Shop phone',
	'shopPhoneTips': 'Please fill in the store contact number',
	'businessLicense': 'Business license',
	'upBusinessLicense': 'Upload business license',
	'ID': 'ID',
	'upBothIDCard': 'Upload both sides of ID card',
	'positive': 'Positive',
	'Negative': 'Negative',
	'submitApplication': 'Submit application',
	'submitApplicationTips': 'Required information. Applicants must ensure the authenticity of the above information. If there is false information, all consequences arising therefrom shall be borne by the applicant.',
	'enterCorrectStore': 'Please enter the correct store name',
	'shopDescriptionTips2': 'Please input the correct store description',
	'upShopLogoTips': 'Please upload store logo',
	'enterCorrectAddress': 'Please enter the correct detailed address: no less than 5 words',
	'locationTips': 'Please select the location',
	'upBusinessLicenseTips': 'Please upload business license',
	'upBothIDCardTips': 'Please upload both sides of ID card',
	'submittedSuccessfully': 'Submitted successfully, waiting for review',
	'shopNameTips': 'Chinese, English and numbers',

	// order-detail.vue
	'waitingBuyerPay': 'Waiting for buyer to pay',
	'waitingForDelivery': 'Waiting for delivery',
	'waitingBuyerReceipt': 'Waiting for buyer to confirm receipt',
	'orderCompleted': 'Order completed',
	'orderCancelled': 'Order cancelled',
	'itInaGroup': 'Waiting to form a group...',
	'buyerPayment': 'Buyer payment',
	'merchantShipment': 'Merchant Shipment',
	'buyerPickUp': 'Buyer pick up',
	'merchantDelivery': 'Merchant Delivery',
	'transactionComplete': 'Transaction complete',
	'deliveryVoucher': 'Delivery voucher',
	'deliveryCodeTips': 'No delivery code',
	'picker': 'Picker',
	'appointmentTime': 'Time ',
	'requestRefund': 'Request a refund',
	'checkRefund': 'Check Refund',
	'orderTime': 'Order time',
	'paymentMethod': 'Payment method',
	'deliveryMethod': 'Delivery Method',
	'OrderNotes': 'Order notes',
	'preferential': 'Preferential',
	'orderTotal': 'Order total',
	'wholeOrderRefund': 'Refund',
	'viewGroupDetails': 'Group details',
	'contactCustomerService1':'Customer Service',
	'confirmTheCost': 'Confirm the cost',
	'integralPayment': 'Integral payment',
	'noNeedDelivery': 'There is no need for express delivery',
	'sellerDelivery': 'Courier delivery',
	'copySucceeded': 'Copy succeeded',
	'copyFailure': 'Copy failure',

	// orderList.vue
	'all': 'All',
	'toBePaid': 'Unpay',
	'toBeDelivered': 'Undelivered',
	'toBeReceived': 'Unreceived',
	'toBeEvaluated': 'To be evaluated',
	'completed': 'Completed',
	'cancelled': 'Cancelled',
	'inAGroup': 'In a group',
	'refunding': 'Refunding',
	'refundComplete': 'Refund complete',
	'partialRefundCompleted': 'Partial refund completed',
	'refundClosed': 'Refund closed',
	'items': 'items',
	'cancelOrder': 'Cancel order',
	'payment': 'Payment',
	'viewLogistics': 'View Logistics',
	'confirmRceipt': 'Confirm receipt',
	'evaluation': 'Evaluation',
	'noOrderTips': 'There is no related order here yet~',
	'cancelOrderTips': 'Do you want to cancel this order?',
	'no': 'No',
	'yes': 'Yes',
	'orderExpired': 'The order has expired. Please order again',
	'orderStatusChanged': 'Order status has changed, please place a new order',
	'haveRceivedGoods': 'I have received the goods?',
	'sureDeleteOrder': 'Are you sure you want to delete this order?',
	'gift': 'giveaway',
	'removeGiftTip1': 'The refund amount will be reduced by the price ￥',
	'removeGiftTip2': ' of the gift after the gift is removed, whether to determine removal?',
	'removeGiftTip3': 'The amount of the gift is greater than or equal to the current refundable amount of the main product and cannot be removed',
	'removeGiftBtn': 'remove',

	// pay-result.vue
	'paymentFailed': 'Payment failed',
	'paymentFailedTips1': 'Please',
	'paymentFailedTips2': 'within 30 minutes',
	'paymentFailedTips3': 'to complete the payment ',
	'paymentFailedTips4': 'Otherwise the order will be cancelled by the system',
	'checkOrder': 'Check order',
	'payAgain': 'Pay again',
	'paymentSuccessful': 'Payment successful',
	'thankPurchase': 'Thank you for your purchase',
	'continueShopping': 'Continue shopping',

	// paySuccess.vue
	'orderPaymentSuccessful': 'Order payment is successful',
	'viewDeliveryCode': 'View delivery code',
	'deliveryAddress': 'Delivery Address',
	'takeDeliveryTime': 'Take delivery time',
	'iGotIt': 'I got it!',

	// payWay.vue
	'theRemainingTime': 'the remaining time',
	'PayWithAli': 'Ali-Pay',
	'payWithWeChat': 'WeChat Pay',
	'payWithWeChatFriend': 'WeChat Pay By Friend',
	'determinePayment': 'Determine the payment',

	// personalInformation.vue
	'gender': 'Gender',
	'female': 'female',
	'male': 'male',
	'userName': 'User Name',
	'userNameCannotChange': 'Username cannot be changed',

	// prod.vue
	'aGroup': 'Join a group',
	'onlyStart': 'Distance start',
	'onlyEnd': 'Before the end only left',
	'day': ' day',
	'time': 'hour',
	'minute': 'minute',
	'second': 'second',
	'collection': 'Collection',
	'collected': 'Collected',
	'follow': 'Follow',
	'groupPrice': 'people group price',
	'originalPrice': 'Original price',
	'promotion': 'Promotion',
	'every': 'Every ',
	'maximumReduction': 'Maximum reduction',
	'getCoupons': 'Get coupons',
	'reduce': ' reduce ',
	'makeDa': 'have',
	'piecesZ': ' pieces',
	'joinAGroup': 'The following partners are starting a group, you can join them directly',
	'lack': 'lack',
	'lack1': 'people in groups, surplus',
	'toGatherGroup': 'Group',
	'groupInvitation': 'Pay for group invitation',
	'groupInvitationTips': 'people in groups, automatic refund for insufficient number',
	'selected': 'Selected',
	'praise': 'Praise',
	'itemTiao': 'items',
	'mediumEvaluation': 'Medium evaluation',
	'badEvaluation': 'Bad evaluation',
	'havePictures': 'Have pictures',
	'anonymousEvaluation': 'Anonymous evaluation',
	'viewAllEvaluation': 'View all evaluation',
	'selfShop': 'Self-operated shop',
	'homepage': 'Home',
	// "shoppingCart": 'ShoppingCart',
	'shoppingCart': 'Cart',
	// "addShoppingCart": 'Add to ShoppingCart',
	'addShoppingCart': 'Add to cart',
	'buyNow': 'Buy now',
	'individualShopping': 'Buy Direct',
	'startAGroup': 'Start a group',
	'coupon': ' Coupon',
	'outOfStock': 'Out of stock',
	'inventory': 'Inventory',
	'productEvaluation': 'Product evaluation',
	'rating': 'Rating',
	'shopReply': 'Shop reply',
	'noProductReviewsTips': 'No product reviews yet~',
	'clickLoadMore': 'Click to load more',
	'shareFriendsNow': 'Share to friends now',
	'shareFriendsTips1': 'After a friend makes a successful purchase through the page you shared, you can get the corresponding commission, which can be viewed in "Personal Center-Distributor Center"',
	'weChat': 'WeChat',
	'QRCode': 'QR code',
	'myDistributorCenter': 'My Distributor Center',
	'saveAlbum': 'Save to album',
	'shareFriendsTips2': 'Click on the upper right corner of the screen to share this product with friends',
	'earn': 'earn',
	'quitPlaying': 'Quit playing',
	'collectionAdded': 'Collection added',
	'collectionCancelled': 'Collection cancelled',
	'successfullyAddedCart': 'Successfully added to shopping cart',
	'insufficientStock': 'Insufficient stock',
	'purchaseLimit': 'Purchase limit',
	'shartTips': 'It\'s good to limit the purchase of this product. Come and have a look!',
	'cancleCollection': 'Cancel',
	'actNotBegin': 'Activity not started',
	'leastTips': 'The least quantity 1',
	'enterShop': 'Enter',
	'discountPackage': 'DiscountPackage',
	'packages': 'Packages',
	'packagePrice': 'Price',
  'savesYou': 'Saving',
	'Giveaways': 'Giveaways',
	'numberOfPackages': 'Number',
	// prod-classify.vue
	'limitedTimeOffer1': 'Limited time offer: The following products can be used up',
	'noProducts': 'No products',
	'noMoreActivities': 'No more activities',
	'dailyUpdate': 'Daily update',
	'mallHotSale': 'Mall hot sale',
	'moreBaby': 'More baby',
	'dailyPurchase': 'Daily purchase',
	"notAvailableForPurchase": "active products not available for purchase",

	// prodComm.vue
	'shareTips': 'Share your experience with those who want to buy',
	'maxUpPic': 'Up to 9 photos can be uploaded',
	'postReview': 'Post a review',
	'evaluationEmpty': 'Evaluation cannot be empty',
	'score': 'Score',
	'evaluationSuccessful': 'The evaluation is successful, thank you!',

	// promotion-order.vue
	'unsettlement': 'Unsettlement',
	'settled': 'Settled',
	'incomeExpired':'Invalid',
	'aTotalProducts': 'A total of 1 products',
	'commission': 'commission',
	'noPromotionProduct': 'No promotion product was found~',
	'noRecordsFound': 'No records were found',

	// promotionProd.vue
	'enterProductName': 'Please enter the product name',
	'newest': 'Newest',
	'sales': 'Sales',
	'price': 'Price',
	'expectedEarn': 'Expected to earn',
	'shareIt': 'Share it',
	'downloadComplete': 'Saved to album!',
	'failedSave': 'Failed to save. Please Click Save to album again',
	'failedSaveTips': 'Failed to save. Please authorize relevant access rights',

	// register.vue
	'enterValidPhone': 'Please enter a valid phone number',
	'haveAnAccount': 'Already have an account?',
	'goToLogin': 'Go to login',
	'goToBind': 'Go to bind',
	'nextStep': 'Next step',
	'setNickname': 'Nickname',
	'pleaseSetNickname': 'Please set your nickname',
	'setAccountNumber': 'Set account',
	'pleaseSetAccount': 'Please set your account/username',
	'setAccountNumberTips': 'The account number is composed of letters, numbers and underscores with a length of 4-16 digits',
	'setPassword': 'Set password',
	'pleaseSetPassword': 'Please set your password',
	'passwordVerification': 'The password is composed of at least two characters of letters plus numbers or symbols, 6-20 half width characters, case sensitive',
	'confirmPasswordAgain': 'Please confirm the password again',
	'ConfirmPasswordNot': 'Confirm that the password does not match the password',
	'registrationSuccessful': 'Congratulations, the registration is successful!',

	// shopInfo.vue

	// salesmanLevel.vue
	'hierarchicalRules': 'Hierarchical rules',
	'introductionUpgradeRules': 'Introduction to upgrade rules',
	'piecesBi': 'pieces',
	'upgradeRules': 'Upgrade rules',
	'upgradeRules1': 'The cumulative income reaches',
	'upgradeRules2': 'The number of bound customers reaches',
	'upgradeRules3': 'The cumulative number of invited distributors reached',
	'upgradeRules4': 'The cumulative number of invited customers reached',
	'upgradeRules5': 'The cumulative number of successful payment',
	'upgradeRules6': 'The cumulative consumption amount reached',
	'upgradeRules7': 'The cumulative transaction orders reached',
	'upgradeRules8': 'The cumulative transaction amount reached',
	'purchaseDesignatedGoods': 'Purchase designated goods',
	'upgradeImmediately': 'Upgrade immediately',
	'upgradeSuccessful': 'Upgrade successful!',

	// search-page.vue
	'commodity': 'Goods',
	'enterKeywordSearch': 'Enter keyword search',
	'cancel': 'Cancel',
	'popularSearches': 'Popular searches',
	'searchHistory': 'Search history',
	'inpKeyWords': 'Please enter keywords',

	// search-prod-show.vue
	'Comprehensive': 'Comprehensive',

	// searchShopShow.vue
	'peopleConcerned': ' follows',
	'lookShop': 'Enter',
	'noData': 'No data',

	// selectStore.vue
	'findPickUpPoints': 'Enter a name to find nearby pick-up points',
	'recentPickUpPoint': 'Recent pick-up point',
	'address': 'Address',
	'pickUpTime': 'Time',
	'pickupNearby': 'Pick-up point nearby',
	'saveAndUse': 'Save and use',
	'failedGetAddress': 'Failed to get address, unable to load the list of bootstrap points',

	// shopCategory.vue
	'searchForItems': 'Search for in-store items',
	'noProductsTips': 'No products in this category',


	// shopInfo.vue
	'certificateInformation': 'Certificate information',
	'storeProfile': 'Store profile',
	'wenetSelfSupermarket': 'WeNet self operated supermarket~~',
	'openingTime': 'Opening time',
	'openingTime1': 'September 18, 2019',

	// shopPage.vue
	'viewMoreProducts': 'View more products',
	'haveSpacePeople': ' People ',
	'tenThousandPeople': ' Ten Thousand People ',
	'shopHotSale': 'Shop Hot Sale',
	'storeStatusTips': 'Store status is abnormal, please contact the platform customer service',
	'storeStatusTips2': 'Store status exceptions',
	'storeStatusTips3': 'Store is offline',
	'storeReviewTips4': 'Merchant not yet open',
	'storeReviewTips5': 'The merchant has suspended its business',

	// shopProds.vue
	'default': 'Default',

	// user.vue
	'registration': 'Register Now',
	'paidMembership': 'Paying',
	'ordinaryMembership': 'Ordinary',
	'buyMembership': 'Buy VIP',
	'loginNow': 'Login Now',
	'clickAccountLogin': 'Click on account to log in',
	'myOrder': 'My Order',
	'refundAfterSale': 'After sale',
	'scanCodePickup': 'Scan code from pickup',
	'distributioncenter': 'Distribution center',
	'couponCenter': 'Coupon Center',
	'membershipCentre': 'Membership Centre',
	'myDiscountCoupon': 'My discount coupon',
	'myCollection': 'My collection',
	'storeAccountSettings': 'Store account settings',
	'modifyShopPassword': 'Modify shop password',
	'myShop': 'My shop',
	'messageBox': 'Store customer service',
	'contactCustomerService': 'Contact platform',
	'signOut': 'Sign out',
	'openInWeChatBrowser': 'Please open it in WeChat browser',
	'failedInvokeScan': 'Failed to invoke the scan code, please try again later',
	'applicationReview': 'The application you submitted is under review',
	'applicationFailed': 'Your application failed, please contact customer service for specific reasons and apply again',
	'distributorBanned': 'Your identity as a distributor has been banned, please contact customer service for specific reasons',
	'distributorCleared': 'Your distribution status has been blocked, please contact the administrator',
	'myCollectionGoods': 'My collection',
	'shopOpeningReview': 'Application for shop opening is under review',
	'myMessage': 'My message',
	'changeLangs': '切换为简体中文',
	'freeShop': 'Free shop',
	'userIntegral': 'Integral',
	'distributionPromotionHasBeenClosed': 'Distribution promotion has been closed',
	'commonTool': 'Common functions',
	'vipContent': 'Member exclusive',
	'vipBtn': 'To buy',



	// shopSearch.vue
	'searchSllShop': 'Search all products in this shop',
	'shopHotSearch': 'Shop hot search',

	// shopSearchResult.vue

	// snapUpDetail.vue
	'secondKillOver': 'Second kill is over',
	'select': 'select',
	'remaining': 'Remainder ',
	'secondKillPrice': 'Second kill price',
	'purchaseQuantity': 'Purchase quantity',
	'secondKillTips': 'The current seckill products cant\`s be purchased',
	'retailPricePurchase': 'Retail price purchase',

	// snapUpList.vue
	'onlyLeft': 'Remainder ',
	'businessesReleased': 'Businesses have not yet released related products',

	// spellGoodsDetails.vue

	// spellGroupDetails.vue
	'groupJoiningRules1': 'Group joining rules: Invite',
	'groupJoiningRules2': 'people to join the group, if there are not enough people, the refund will be automatically',
	'groupJoinTime': 'The rest of the group joining',
	'groupJoiningSuccess': 'Group joining success',
	'groupJoiningFailure': 'Group joining failure',
	'teamLeader': 'Leader',
	'viewAllMembers': 'View all league members',
	'peopleJoinGroup': 'people, come and join our group!',
	'peopleGroupSuccess': 'people, you can group success!',
	'JoinGroupNow': 'Join the group now',
	'jionInGroup': 'Jion in',
	'viewTeam': 'View team',
	'viewOrderDetails': 'View order details',
	'inviteFriendsJoin': 'Invite friends',

	// stationOrderList.vue
	'confirmPickUp': 'Confirmation of write-offs',
	'SelectOrderPickup': 'Please select the order to be written off',
	'confirmPickUpTips': 'Whether to confirm the write-off ?',
	'pickUpSuccessfully': 'write-offs  success!',

	// sub-category.vue
	'noCommodity': 'There is no commodity under this category',

	// submit-order.vue
	'mailToHome': 'Mail to home',
	'fillReceivingInformation': 'Fill in the receiving information',
	'historicalAddress': 'Historical choosed address',
	'itemGe': ' item',
	'consignee': 'Consignee',
	'consigneeTips': 'Please enter the name of the consignee',
	'enterContactNumber': 'Please enter your contact number',
	'yourRegion': 'Area',
	'selectProvinceCity': 'Please select the province and city',
	'enteDetailedAddress': 'Please enter the detailed address',
	'selectPickUpAddress': 'Select pick up address',
	'fillPersonInformation': 'Fill in pick up person information',
	'historicalPickPerson': 'Historical pick up person',
	'enterNamePerson': 'Please enter the name of the pick up person',
	'enterPhonePerson': 'Please enter the phone of the pick up person',
	'haveDiscount': 'Preferential',
	'notAvailable': 'Not available',
	'zhangAvailable': 'available',
	'storeNotes': 'Buyer Message',
	'storeNotesTips': 'Optional',
	'productsNotSupported': 'The following products are not supported',
	'chooseAnotherDelivery': 'Please choose another delivery method',
	'saveAndUseTips': 'Please fill the form and save',
	'platformCoupons': 'Platform coupons',
	'pointsDeduction': 'Points deduction',
	'enterPoints': 'Enter the number of points',
	'enterCorrectPoints': 'Please check the number of points',
	'multipleOf10': 'Please enter an integer multiple of ten',
	'notUsePoints': 'Do not use points to deduct',
	'prodTransfee': 'Product shipping',
  'freightPayable': 'Freight Payable',
	'gradeFreeAmount': 'Grade free freight amount',
	'memberDiscountAmount': 'Member discount',
	'storeCoupons': 'Store coupons',
	'eventOffer': 'Event offer',
	'totalDiscountAmount': 'Total discount amount',
	'availableCoupons': 'Available coupons',
	'unavailableCoupons': 'Unavailable coupons',
	'getCouponTips': 'There is no coupon here yet, go get the coupon~',
	'NoRelevantCoupons': 'No relevant coupons',
	'modifyDeductiblePoints': 'Modify deductible points',
	'selectDeliveryMethod': 'Select delivery method',
	'ordinaryExpress': 'Ordinary Express',
	'optionalAddress': 'Optional address',
	'pickUpPerson': 'Pick up person',
	'choosePickUpTime': 'Choose pick up time',
	'enterPointsTips': 'Please enter the correct number of points',
	'mostUserPoints': 'The order can use up to ',
	'integral': ' integral',
	'notEnough': 'Not enough integral!',
	'pleaseSelectSddress': 'Please select address',
	'productNotSupported': 'This product is not currently supported',
	'productNotSupportedStop': 'This product does not support pick-up at the store',
	'selectPickPoint': 'Please select a pick-up point',
	'fillDeliveryPersonInformation': 'Please fill in the delivery person information',
	'selectPickUpTime': 'Please select pick up time',
	'newAddressesLimit': 'The number of new addresses has reached the upper limit (10 entries)',
	'enterCorrectPhone': 'Please enter the correct phone number',
	'selectLatitudeLongitude': 'Please select latitude and longitude',
	'savedSuccessfully': 'Saved successfully',
	'authorityTips': 'Please grant permission to obtain location information in "Upper Right Corner"-"About"-"Upper Right Corner"-"Settings"',
	'use': 'Use',
	'accountSurplus': 'Account surplus',
	'accountSurplus2': 'points, the maximum available order',
	'month': 'month',
	'beforeYesterday': 'The day before yesterday',
	'yesterday': 'Yesterday',
	'today': 'Today',
	'tomorrow': 'Tomorrow',
	'afterTomorrow': 'The day after tomorrow',
	'courier': 'Courier',
	'duplicateErrorTips': 'Please do not submit orders at the same time',

	// take-notes.vue
	'noRecords': 'No records',
	'totalWithdrawal': 'Total withdrawal',
	'wechatPayment': 'Wechat payment',
	'theFallback': 'The fallback',
	'withdrawing': 'Withdrawing',
	'hasArrived': 'Has arrived',
	'rejection': 'Refusal to withdraw cash',
	'loading': 'Loading...',
	'noMore': 'No more~',
	'noWithdrawalRecord': 'No withdrawal record yet~',

	// withdrawal.vue
	'withdrawalAmount': 'Withdrawal Amount:',
	'availableCashAmount': 'Available cash amount ￥',
	'withdrawAll': 'All',
	'confirmWithdrawal': 'Confirm withdrawal',
	'enterCorrectAmount': 'Please enter the correct amount!',
	'insufficientBalance': 'Insufficient balance',
	'lessThanWithdrawalAmount': 'The withdrawal amount is less than the minimum withdrawal amount',
	'withdrawalApplicationSubmitted': 'The withdrawal application has been submitted',

	// writeReturnLogistics.vue
	'logisticsCompany': 'Logistics company',
	'remarks': 'Remarks',
	'logisticsOrderNo': 'Logistics order No',
	'logisticsOrderNoSucced': 'logistics order Succesed',
	'logisticsOrderNoTips': 'Logistics order No',
	'optional200Words': 'Optional, up to 200 words',


	// packageMemberIntegral/pages/buyVip/buyVip.vue
	// buyVip.vue
	'expire': ' expire',
	'notPayingMember': 'You are not a paying member yet~',
	'current': 'Current',
	'notPurchased': 'None',
	'week': ' week',
	'season': ' season',
	'year': ' year',
	'growthValueUp': 'Growth value up to',
	'readyToBuy': 'Ready to buy',
	'currentGrowthValue': 'Current growth value',
	'VIPLevelRights': 'VIP level rights',
	'totalPrice': 'Total price',
	'choosePaymentMethod': 'Choose payment method',
	'growthValueTips': 'Not enough growth value to buy',
	'upgradeMemberTips1': 'You are currently',
	'upgradeMemberTips2': 'About to buy',
	'upgradeMemberTips3': 'Upgrade member will cover the current member time limit. Are you sure to purchase?',
	'cannotBuyLower': 'Users cannot purchase lower level members during the membership period',

	// convertProdDet.vue
	'redeemNow': 'Redeem now',
	'insufficientProductInventory': 'Insufficient product inventory!',

	// exchangeDetails.vue
	'itemFen': 'item',
	'recent30Days': 'It has been exchanged in recent 30 days',
	'productIntroduction': 'Product introduction',

	// integralIndex.vue
	'myIntegral': 'My integral',
	'detailed': 'Detailed',
	'earnPoints': 'Earn points',
	'pointsExchange': 'Points exchange',
	'pointCenter': 'Points Center',
	'pointDetail': 'Points Details',


	// integralSubmitOrder.vue
	'preferentialAmount': 'Preferential amount',
	'confirmSpend': 'Confirmation of spending',
	'scoreToPay': 'points to pay ?',

	// luckyDraw.vue
	'luckyDraw1': 'It is only effective when the turntable stops rotating',
	'luckyDraw2': 'Insufficient points, please earn points first!',
	'addAddressFirst': 'Please add delivery address first',

	// memberCenter.vue
	'currentGrowthFull': 'Current growth value is full',
	'upgradeStillNeeded': 'Upgrade still needed',
	'growthValue': ' growth value',
	'memberGrowthDescription': 'Member growth description',
	'grade': 'Grade',
	'correspondingGrowthValue': 'Required growth value',
	'exclusiveMembers': 'Exclusive rights and interests of members',
	'moreInterests': 'More interests',
	'itemXiang': 'item',

	// memberIndex.vue
	'currentPoints': 'Current points',
	'dailySignIn': 'Daily sign in',
	'signIn': 'Sign in',
	'signedIn': 'Signed in',
	'dayItem': '',
	'integralActivity': 'Integral activity',
	'pointsMall': 'Points mall',
	'exchangePointsGoods': 'Exchange points for good goods',
	'largeTurntable': 'Large turntable',
	'PointsLuckyDraw': 'Points lucky draw',
	'integralTask': 'Integral task',
	'shoppingMall': 'Shopping mall',
	'shoppingMallGet': 'Shopping mall can get points',
	'itemYuan': ' yuan / integral',
	'goShopping': 'Go shopping',
	'registerNewUsers': 'Register new users',
	'registerNewUsersTips': 'Each user who signs up as a new member will be rewarded',
	'toComplete': 'To complete',
	'comingOnlineSoon': 'Coming online soon, please look forward to it~',
	'hasAlreadySigned': 'This day has already signed in~',

	// integralDetail.vue
	'integralStrategy': 'Integral strategy',
	'rewardPointsGoods': 'Free points for purchases',
	'refundShopping': 'Refunds less points earned on purchases',
	'pointsLevelPromotion': 'Level up and get bonus points',
	'signGetPoints': 'Get points for signing in',
	'refundGoodsPoints': 'Credit for product refunds',
	'purchaseGoodsPoints': 'Credit for purchase of goods',
	'noDetails': 'No details',
	'pointsExpired': 'Expired Points',
	'rechargeGetPoints': 'Recharge balance to get bonus points',
	'registerEarnPoints': 'Get points for registration',
	'platformChanges': 'System changes',


	// IntegralGoodsList/IntegralGoodsList.vue
	'marketValue': 'Market value',
	'thankYourPatronage': 'Thank you for your patronage',
	'goods': 'Goods',
	'clickLottery': 'Click on the lottery',
	'threePoints': '300 points',
	'drawPrize': 'draw a prize',
	'goodLuck': 'Good luck!',
	'thankParticipation': 'Thank you for your participation',
	'luckNextTime': 'Luck for next time~',
	'error': 'Error',
	'wrongServer': 'There\'s something wrong with the server. Come back later~',


	// coupon.vue
	'universal': 'Universal',
	// "commodity": 'Commodity',
	'allUniversal': 'Universal (except special items)',
	'specifiedItems': 'Specified items available',
	'invalid': ' days later invalid',
	'useItNow': 'Use',

	// loginPopup.vue
	'welcomeWenetshop': 'Welcome to Wenet Cosmic Edition',
	'authorizeLogin': 'Please authorize login to get the complete shopping experience',

	// production.vue
	'timeLimitedPrice': 'Time limited price',

	// shop-tabbar.vue

	// tki-barcode.vue
	'barcodeSavedSuccessfully': 'Barcode saved successfully',
	// tki-qrcode.vue
	'generating': 'generating',
	'qrCodeCannotBeEmpty': 'QR code content cannot be empty',
	'bqrCodeSavedSuccessfully': 'QR code saved successfully',


	'shopHomepage': 'Home',
	'allCommodities': 'Commodities',
	'commodityClassification': 'Categories',
	'storeSearch': 'Search',

	// index.vue
	'classification': 'Categories',
	'cart': 'Cart',
	'myself': 'Account',
	'find': 'Discovery Center',
  'stores': 'Stores',


	// pages.json
	'viewStartupLog': 'View startup log',
	'personalCenter': 'Personal Center',
	'shoppingCart2': 'Shopping Cart',
	'classifiedGoods': 'Classified Goods',
	'editShippingAddress': 'Edit shipping address',
	'orderList': 'Order list',
	'orderDetails': 'Order details',
	'bindingMobilePhone': 'Binding mobile phone number',
	'paymentResults': 'Payment results',
	'searchResult': 'Search result',
	'commodityDetails': 'Commodity details',
	'wenetCollection': 'WeNet collection',
	'latestAnnouncement': 'Latest announcement',
	'invitationCard': 'Invitation card',
	'withdrawal': 'Withdrawal',
	'groupDetails': 'Group details',
	'secondsKillProductDetails': 'Seconds kill product details',
	'detailsGroupProducts': 'Details of group products',
	'membersTheDetails': 'Members of the details',
	'makeSureOrder': 'Make sure the order',
	'collectionShops': 'Collection of shops',
	'applyOpenShop': 'Apply to open a shop',
	'storeSearchResults': 'Store search results',
	'groupBuyingList': 'Group buying list',
	'changeStorePassword': 'Change store password',
	'userLogin': 'User login',
	'userRegistration': 'User registration',
	'checkoutCounter': 'checkout counter',
	'logisticsDetails': 'Logistics details',
	'shopSearch': 'Shop search',
	'selectPickUpPoint': 'Select pick up point',
	'pickUpOrderList': 'Pick up point order list',
	'writeDeliveryOrder': 'Write off self delivery order',
	'personalData': 'personal data',
	'liveList': 'Live list',
	'wenetMultiStore': 'WeNet Cosmic Edition',
	'positionInformation': 'Position information',
	'specialOfferList': 'special offer list',


	// liveBroadcast.vue
	'tabbarLive': 'Live',
	'liveBroadcast': 'Live broadcast',
	'inputBlogger': 'Input blogger / Studio / product',
	'liveing': 'Liveing',
	'notStarted': 'Not started',
	'finished': 'Finished',
	'noBroadcasting': 'No broadcasting',
	'suspend': 'Suspend',
	'abnormal': 'Abnormal',
	'liveBroadcastTips': 'No relevant live data',
	'pleaseOpenInWechat': 'Please open it in wechat applet',
	'inLive': 'live',
	'more': 'More',


	'pickitUp': 'Pick it up',

	// recent-news.vue
	'read': 'Read',
	'unread': 'Unread',

	// js
	'serverWrong': 'There\'s something wrong with the server',
	'loginExpired': 'Login has expired, please login again!',
	'cannotPurchaseTips': 'You have been disabled and cannot purchase. Please contact customer service',
	'configInformationFailed': 'Config information validation failed',
	'userCancelsSharing': 'User cancels sharing',
	'purchaseSuccessful': 'Purchase successful!',
	"currentBalance": 'Current balance',


	'expect': 'anticipated',
	'startDelivery': 'start shipping',
	'service': 'service',
	'preSale': 'Pre Sale',
	'selectCoordinates': 'When the delivery method is in the same city, the address information must include the latitude and longitude,Please select latitude and longitude',
	'virtualMonth': '-',
	'dayOfAMonth': '',
	'cityAddressPrompt': 'When the delivery method is intra-city delivery,User address information must include latitude and longitude,Please go to the address page to complete the latitude and longitude of the selected address',
	'balancePay': 'Balance Pay',
	'confirmBalancePay': 'Whether to confirm payment with balance?After clicking the confirm button, the amount of this order will be paid from the balance',
	'cancelBalancePay': 'Payment with balance cancelled',
	'shortBalance': 'Insufficient balance, Please recharge first!',
	'currencySymbol': '￥',
	'balance': 'Balance',
	'messageNotify': 'Message',

	//myWallet
	'totalBalance': 'Total Balance',
	'recharge': 'Recharge ',
	'balanceDetails': 'Balance Details',
	'rechargeBalance': 'Balance top-up',
	'rechargeGift': 'Recharge Gift',
	'shoppingConsumption': 'Shopping Spending',
	'noBalanceDetails': 'No balance details yet',

	'handsel': 'handsel ',
	'handsels': 'handsel ',
	'sheet': '',
	'needToPay': 'Need to pay ',
	'payFail': 'Payment failed, please pay again!',

	'PCS': 'Platform customer service',
	'platformAnnouncement':'Platform announcement',
	'systemNotification': 'System notification',
	'youHaveNoMessages': 'You have no messages',
	'send': 'Send',
	'noMessage': 'No messages for the time being',

	'maximumDiscount': 'Most discount ',

	'goodsBePicked': 'undelivery',

	'totalAmount': 'Total Amount',

	'selStore': 'Please select pick-up point',
	'locateFailure': 'Failed to obtain positioning, unable to load the self-pickup point list.',
	'platformModification': 'System changes',

	'loginOtherSide': 'Users login elsewhere, please refresh the page',
	'customerOffline': 'Customer service is not online',
	'reLogin': 'Please relogin and try again',
	'copyLink': 'Copy link',
	'copySuccess': 'Copied',

	'imageTypeRestrictions': 'Please upload the correct picture format',
	'pageSkipping': 'Page skipping',
	// 装修页相关
	'pageGone': 'Page does not exist!',
	'error' : 'Error',
	'secondKillClosed' : 'The second kill product has been closed!',
	"secErrorTips": 'The activity is too hot, please retry later~',
	'paypalPay': 'PayPal Pay',
	"loading": 'Loading...',

	// 客服消息
		'sendLink': 'Send link',
		'productLinks': 'Product Links',
		'newMessage': 'A new message',
    "clickToLoadMore": "Click to load more",
    "mallCustomerService": "Mall Customer Service",
    "sendLink": "Send link",
    "send": "Send",
    "none": "None",
    "inquiring": "Inquiring",
    "myOrders": "My Orders",
		"onLine": "On-line",
    "offLine": "Off-line",
    "recentlyViewed": "Recently Viewed",
    "orderNumber": "Order number",
    "loading":"Loading",
		"allLoaded": "All loaded",
    "noMore": "No more",
    "read": "Read",
    "unRead": "Un read",
    "noRecord": "No record",
    "customerOffline": "Current customer service is not online",
    "loginOtherSide": "The user is logged in elsewhere, please refresh the page",
    "sendOrderNumber": "Send orderNumber",
    "pleaseLoginAgain": "Account is offline, please login again",
    "paymentAmount": "Payment amount",
    "onlineCustomerService": "Online Customer Service",
    "productLinks":"Product links",
		"onlineRecharge": "Online Recharge",
		"rechargeOffer": "Recharge Offer",
		"additionalGifts": "additional gifts ",
		"customAmount": "Custom Amount",
		"maximumTopUp": "Maximum single top-up ",
		"millionYuan": " million yuan",
   	"enterRechargeAmount": "Please enter the recharge amount",
  	"rechargeAmountMinimum": "The recharge amount cannot be less than 0.01",
  	"rechargeAmountMaximum": "The recharge amount cannot exceed 100,000",

	// 订单状态
	"pendingPayment": 'Pending Payment',
	"pendingDelivery": 'Pending Delivery',
	"pendingReceipt": 'Pending Receipt',
	"pendinEvaluation": 'Pending Evaluation',
	"grouping": 'Grouping',
	"evaluated": 'Evaluated',
	"shipped": 'Shipped',
	"completed": 'Completed',
	"canceled": 'Canceled',
	"stationCodeTips": "Please scan the correct pick-up station QR code",
	"stationNotFound": "No relevant pick-up station found, please try again",
  "memberPackage": "Member Package",
  "storeDiscount": "Store Discount",
  "shippingDiscount": "Shipping Discount",
  "promotionOffer": "Promotion Offer",
	"packageOffer": "Package Offer",
  "merchantsModifyPrices": "Merchants modify prices",

	// 发票
	"invoice": {
    "InvoiceDetails": "Invoice details",
		"onvoiceIssuance": "onvoice issuance",
    "orderInfo": "Order information",
    "orderStatus": "Order Status",
    "orderNumber": "Order Number",
    "orderCreateTime": "createTime",
    "invoiceType": "Invoice type",
    "generalInvoice": "Ordinary electronic invoices",
    "noInvoice": "No invoice",
    "invoiceInfo": "Invoice information",
    "invoiceStatus": "Invoice Status",
    "invoiced": "invoiced",
    "application": "Application in progress",
    "invoiceContent": "Invoice content",
    "productDetails": "Product Details",
    "invoiceTitle": "Invoice Title",
    "companyTaxNumber": "Company Tax Number",
    "invoiceDownload": "Invoice Download",
    "uploadTime": "Invoicing time",
    "invoiceExchange": "Invoice exchange",
    "viewInvoice": "View Invoice",
    "updateInvoice": "Amend invoice",
    "personal": "Personal",
    "unit": "Unit",
		"electronic": "electronic",
    "personalName": "Personal name",
    "inputTips1": "Please enter \"Individual\" or your name",
    "unitName": "unit Name",
    "inputTips2": "Please enter the name of the unit",
    "inputTips3": "Please enter the taxpayer identification number",
	  "inputTips4": "Please enter the correct taxpayer identification number",
    "TaxpayerIDNumber": "Taxpayer ID Number",
    "invoiceInstructions": "Invoice Instructions",
    "invoiceTips1": "In accordance with the latest Inland Revenue Department invoicing regulations, invoices are issued in detail",
    "invoiceTips2": "Electronic invoices and paper invoices have the same legal effect and can support reimbursement to the accounts",
    "invoiceTips3": "The invoiced amount is the actual payment amount, excluding the payment amount of coupons, points, red packets, etc.",
    "invoiceTips4": "Electronic invoices and paper invoices have the same legal effect and can support reimbursement to the accounts",
    "saveInvoiceInformation": "Save invoice information",
    "viewInvoiceDetail": "view invoice detail",
    "InvoicingRequest": "Invoicing request",
    "update": "update",
    "successTip": "Successful application",
    "successTip1": "Update successful",
		"amountPaid": "Actual amount paid",
		"questions": "Frequently Asked Questions",
    "questions1": "Invoice type: default invoice, electronic invoice and paper invoice have the same legal effect, can support the reimbursement of accounts and after-sales maintenance, if you need to issue a special VAT invoice, please contact the merchant；",
    "questions2": "Invoice issuance time: the invoice will be issued by the merchant after the order has been confirmed and can be viewed on the \"Personal Centre - My Orders - View Invoice\" page.",
    "questions3": "Invoice amount: The invoice amount is the actual amount you paid, excluding the payment amount of coupons, points, red packets, etc.",
    "questions4": "Invoicing content: In accordance with the latest tax regulations, invoices will contain a breakdown of goods and will display detailed information on the name and price of the goods.",
    "questions5": "Where to download invoices: Once your order is complete, you can download invoices from the \"Personal Centre - My Orders - View Invoices\" page.",
		"updateMsg": "Application for reopening successful",
		"successMsg": "Application for invoicing successful"
	},
	// 满减
	"amount": "Amount",
	"pieces": "Pieces",
	"amountDiscount": "Amount-discount",
	"piecesDiscount": "Pieces-discount",
	"noDiscount": "Not participating in promotion",

	"inputAllSpace": "The content cannot be blank",
	"more": "Greater",

	// 虚拟商品
	"pleaseEnter": "Please enter ",
	"allMsg": "All messages",
	"viewMsg": "View Message",
	"gotIt": "I got it",
	"toBeUsed": "Not used",
	"sheets": " sheets",
	"used": "Used",
	"expirationDate": "Expiration date",
	"longTermValidity": "Long-term validity after purchase",
	"to": " to ",
	"numberOfWriteOffs": "Number of write-offs",
	"multipleWriteOffs": "Multiple write-offs",
	"singleWriteOffs": "Single write-off",
	"noWriteOffRequired": "No write-off required",
	"voucherCode": "voucher code",
	"refunded": "refunded",
	"totalAmount": "Total amount",
	"validityDate": "Validity date",
	"tipsForUsingVoucherCode": "To protect your rights, please do not provide the QR code/barcode to the merchant before consumption",
	"savePicTips": "Long press the QR code to save to the album",
	"usageInstructions": "Usage instructions",
	"afterPurchase": "Valid after purchase",
	"effective": "effective",
	"validOnTheSameDay": "Valid until 24:00 on the day of purchase",
	"purchase": "Valid within ",
	"validDay": " days of purchase",
	"refundsAreNotAllowed": "Refund requests are not supported",
	"requiredMessage": "Please fill in the required fields to leave a message",
	"virtualGoods": "virtual goods",
	"SelfPickupGoods": "self-pickup goods",
	"pendingWriteOffs": "Pending write-offs",
	"mesgCannotBeAllSpaces": "Messages cannot be all spaces!",
	"history": "My Footprint",
	"historyEmpty": "You have not viewed the product",
	"deleteHistory": "Delete Footprint",
	"deleteHistoryTips": "Are you sure you want to delete the footprint?",

	// privacy 隐私协议&服务条款弹窗
	"termsServiceAndPrivacyPolicy": "Terms of Service and Privacy Policy",
	"content1": "Please be sure to read and fully understand the Terms of Service and Privacy Policy, including but not limited to: In order to provide you with better service, we need to collect your device identification, operation logs and other information for analysis and optimization of application performance.",
	"uCanRead": "You can read",
	"content2": "learn more. If you agree, please click the button below to start receiving our services.",
	"haveReadAndAppgree": "Have read and agree",
	"disagree": "Disagree",
	"agreeAndAccept": "Agree and accept"

};

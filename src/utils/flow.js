// 数据分析.js

import http from '../utils/http'

/**
 * 处理页面信息
 * @param {*} to 目标路由信息
 */
function getPageInfo(to) {
  var path = to.path
	var query = to.query
	var pageInfo = {}
	if (path == '/pages/index/index' || path == '' || path == '*') {
		pageInfo.pageId = 1
	} else if (path == '/pages/prod/prod' && !query.seckillId && !query.groupActivityId) {
		pageInfo.pageId = query.bannerEnter == '1' ? 2 : 3
		pageInfo.bizType = 0
		pageInfo.bizData = query.prodid
	} else if (path == '/pages/snapUpDetail/snapUpDetail' && query.seckillId) {
		pageInfo.pageId = 4
		pageInfo.bizType = 2
		pageInfo.bizData = query.seckillId
	} else if (path == '/pages/prod/prod' && query.groupActivityId) {
		pageInfo.pageId = 5
		pageInfo.bizType = 1
		pageInfo.bizData = query.prodid
	} else if (path == '/pages/category/category') {
		pageInfo.pageId = 6
	} else if (path == '/pages/specialDiscount/specialDiscount') {
		pageInfo.pageId = 7
	} else if (path == '/pages/basket/basket') {
		pageInfo.pageId = 8
	} else if (path == '/pages/order-detail/order-detail') {
		pageInfo.pageId = 9
	} else if (path == '/pages/payWay/payWay') {
		pageInfo.pageId = 10
		pageInfo.bizData = query.orderNumbers
	} else if (path == '/pages/pay-result/pay-result') {
		pageInfo.pageId = 11
		pageInfo.bizData = query.orderNumbers
	}	else if (path == '/pages/user/user') {
		pageInfo.pageId = 12
	} else if (path == '/pages/orderList/orderList') {
		pageInfo.pageId = 13
	} else if (path == '/pages/ics/ics') {
		pageInfo.pageId = 14
	}
	return pageInfo
}
/**
 * 获取唯一值
 */
function getUuid() {
  var s = [];
	var hexDigits = "0123456789abcdef";
	for (var i = 0; i < 36; i++) {
		s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
	}
	s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
	s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
	s[8] = s[13] = s[18] = s[23] = "-";

	var uuid = s.join("");
	return uuid;
}

exports.getPageInfo = getPageInfo
exports.getUuid = getUuid

var http = require('./http.js')
var util = require('./util.js')
var Constant = require('./constant.js')
import Wechat from './wechat.js'
import store from '../store.js'


import i18n from '../main.js'

export default {

	/**
	 * 订单支付
	 * @param {Number} payType 支付类型;见util/constant.js
	 * @param {String} orderNumbers 订单编号
	 * @param {Boolean} isPurePoints 是否纯积分订单(积分+钱订单与普通订单一致)
	 * @param {Boolean} dvyType // 配送类型 1:快递 2:自提 3：无需快递 4:同城配送
	 * @param {Number/String} vipId 要购买的会员等级id
	 * @param {Number/String} balance 余额充值
	 * @param {Number/String} orderType 订单类型 0普通 1团购 2秒杀 3积分
	 * @param {Number/String} ordermold 1虚拟商品
   * @param {Boolean} hideBalancePay 是否隐藏余额支付
	 */
	toOrderPay (payType, orderNumbers, isPurePoints, dvyType, vipId, balance, orderType, ordermold, hideBalancePay) {
		uni.showLoading({
			// #ifndef MP-TOUTIAO
			mask: true
			// #endif
		})
    const balanceId = balance ? balance.balanceId : null
    const customRechargeAmount = balance ? balance.customRechargeAmount : null
		let redirectUrl = '/packageUser/pages/myWallet/myWallet'
		// #ifdef H5
		var {
			protocol,
			host
		} = window.location
		if (balanceId) {
			redirectUrl = `${protocol}//${host}` + '/packageUser/pages/myWallet/myWallet'
		} else if (vipId) {
			redirectUrl = `${protocol}//${host}` + '/pages/vip/vip'
		} else {
			redirectUrl = `${protocol}//${host}` + '/pages/orderList/orderList?sts=0'
		}
		// #endif
		// 常规订单请求参数
		const commonOrderData = {
			payType: payType,
			orderNumbers: orderNumbers,
			returnUrl: redirectUrl
		}
		// 购买vip订单请求参数
		const buyVipOrderData = {
			id: vipId,
			scene: '',
			payType: payType,
			returnUrl: redirectUrl
		}
		// 充值余额
		const rechargeBalanceData = {
			payType: payType,
			returnUrl: redirectUrl,
			id: balanceId,
			customRechargeAmount: customRechargeAmount,
			scene: ''
		}
    const appType = uni.getStorageSync('appType')
    if (Constant.AppType.MP == appType) {
      const officalAccountOpenid = uni.getStorageSync('officalAccountOpenid');
      commonOrderData.openId = officalAccountOpenid
      buyVipOrderData.openId = officalAccountOpenid
      rechargeBalanceData.openId = officalAccountOpenid
    }
		const params = {
			url: vipId ? "/p/level/payLevel" : balanceId ? '/p/balance/pay' : "/p/order/pay",
			method: "POST",
			data: vipId ? buyVipOrderData : balanceId ? rechargeBalanceData : commonOrderData,
			callBack: (res)=> {
				let _this = this
				uni.hideLoading()
				// 积分订单: 纯积分 isPurePoints=1 或者 纯积分支付
				if (isPurePoints == 1 || payType == 0) {
					uni.redirectTo({
						url: '/pages/pay-result/pay-result?sts=1&orderNumbers=' + orderNumbers + '&orderType=' + orderType
					});
				} else {
					// 微信h5支付
					if (Constant.PayType.WECHATPAY_H5 == payType) {
						var url = encodeURIComponent(redirectUrl)
						window.location.replace(res + "&redirect_url=" + url)
						return;
					}
					// 微信公众号支付
					else if (Constant.PayType.WECHATPAY_MP == payType) {
						Wechat.callWexinPay(res, () => {
							//支付成功
							_this.routeToAfterPay(true, dvyType, vipId, balanceId, orderNumbers, payType, orderType, ordermold)
						}, () => {
							// 支付失败
							_this.routeToAfterPay(false, dvyType, vipId, balanceId, orderNumbers, payType, orderType, ordermold)
						})
					}
					//支付宝支付
					else if (Constant.PayType.ALIPAY_H5 == payType) {
						const div = document.createElement('div')
						div.innerHTML = res
						document.body.appendChild(div)
						document.forms[0].submit()
						div.remove()
					}
					// 余额支付
					else if (Constant.PayType.BALANCEPAY == payType && !vipId) {
						// type 1支付成功 2用户信息异常 3余额不足
						if (res.type == 1) {
							_this.routeToAfterPay(true, dvyType, vipId, balanceId, orderNumbers, payType, orderType, ordermold)
						} else {
							uni.showToast({
								title: res.message,
								icon: 'none',
								duration: 1500
							})
						}
					}
					// PayPal支付
					else if (Constant.PayType.PAYPAL == payType) {
						console.log("paypal支付")
						console.log("res",res)
						window.location.replace(res)
					}
					// 余额购买会员
					else if (Constant.PayType.BALANCEPAY == payType && vipId) {
						// type 1支付成功 2用户信息异常 3余额不足
						if (res.type == 1) {
							uni.showToast({
								title: i18n.t('index.purchaseSuccessful'),
								icon: 'none',
								duration: 1000
							})
							setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/vip/vip'
                })
							}, 1000)
						} else {
							uni.showToast({
								title: res.message,
								icon: 'none',
								duration: 1500
							})
						}
					}
					else {
						// #ifdef APP-PLUS
						var wxAppPayInfo = {}
						if (payType == Constant.PayType.WECHATPAY_APP) {
							wxAppPayInfo = {
								"sign": res.sign,
								"prepayid": res.prepayId,
								"partnerid": res.partnerId,
								"appid": res.appId,
								"timestamp": res.timeStamp,
								"noncestr": res.nonceStr,
								"package": res.packageValue
							}
						}
						uni.getProvider({
							service: 'payment',
							success: res1 => {
								uni.requestPayment({
									provider: payType == Constant.PayType.WECHATPAY_APP ? 'wxpay' : 'alipay',
									orderInfo: payType == Constant.PayType.WECHATPAY_APP ? wxAppPayInfo : res,
									success: payMentSuss => {
										_this.routeToAfterPay(true, dvyType, vipId, balanceId, orderNumbers, payType, orderType, ordermold)
									},
									fail: (paymentFail) => {
										_this.routeToAfterPay(false, dvyType, vipId, balanceId, orderNumbers, payType, orderType, ordermold)
									}
								})
							},
							fail: (failRes) => {
								console.log('fail')
							}
						})
						// #endif
						// #ifdef MP-WEIXIN
							wx.requestPayment({
								timeStamp: res.timeStamp,
								nonceStr: res.nonceStr,
								package: res.packageValue,
								signType: res.signType,
								paySign: res.paySign,
								success: e => {
									_this.routeToAfterPay(true, dvyType, vipId, balanceId, orderNumbers, payType, orderType, ordermold, hideBalancePay)
								},
								fail: err => {
									_this.routeToAfterPay(false, dvyType, vipId, balanceId, orderNumbers, payType, orderType, ordermold, hideBalancePay)
								}
							})
						// #endif
					}
				}
        if (vipId) {
          console.log('强制刷新')
          setTimeout(() => {
            store.commit('refreshUserInfo', {
              forceRefresh: true
            })
          }, 2000)  

        }
			},
			errCallBack: err => {
				uni.showModal({
					title: i18n.t('index.tips'),
					content: err.data,
					confirmText: i18n.t('index.confirm'),
					showCancel: false,
					success: errModSucc => {
						this.routeToAfterPay(false, dvyType, vipId, balanceId, orderNumbers, payType, orderType, ordermold)
						// 支付失败时将强制刷新支付类型
						util.getSysPaySwitch(true)
					}
				})
			}
		}
		http.request(params)
	},

	/**
	 * 支付成功或失败后的统一跳转
	 * @param {Boolean} isSuccess 是否支付成功
	 * @param {Number} dvyType 配送类型
	 * @param {Number} vipId 购买会员id
	 * @param {Number} balanceId 余额充值id
	 * @param {String} orderNumbers 订单编号
	 * @param {Number/String} payType 支付类型
	 * @param {String} orderType 订单类型
	 */
	routeToAfterPay(isSuccess, dvyType, vipId, balanceId, orderNumbers, payType, orderType, ordermold, hideBalancePay) {
    // 购买vip 成功与否都跳转同一个链接
		if (vipId) {
      store.commit('refreshUserInfo', {
        forceRefresh: true
      });
      uni.reLaunch({
        url: '/pages/vip/vip'
      })
      return
		}
    let url = ''
		if (isSuccess) {
			// 支付成功后的跳转
			url = balanceId ? '/packageUser/pages/myWallet/myWallet' : dvyType == 2 ? '/pages/paySuccess/paySuccess?sts=1&orderNumbers=' + orderNumbers + '&dvyType=' + dvyType : '/pages/pay-result/pay-result?sts=1&orderNumbers=' + orderNumbers+ '&payType=' + payType + '&orderType=' + orderType + '&ordermold=' + ordermold
		} else {
			// 支付失败的跳转
			url = balanceId ? '/packageUser/pages/rechargeBalance/rechargeBalance' : '/pages/pay-result/pay-result?sts=0&orderNumbers=' + orderNumbers + '&payType=' + payType + '&ordermold=' + ordermold + '&hideBalancePay=' + hideBalancePay
		}
		uni.redirectTo({
			url: url
		})
	},

	/**
	 * 唤起支付
	 * @param {Object} paydata 支付需要的参数
	 * @param {Object} cb 成功回调
	 * @param {Object} errorCb 失败回调
	 */
	toPay ({
		payType,
		redirectPage,
		paydata,
		cb,
		errorCb
	}) {
		if (Constant.PayType.WECHATPAY_H5 == payType) {
			var redirectUrl = ''
			if (!redirectPage) {
				redirectUrl = window.location.href
			} else {
				var {
					protocol,
					host
				} = window.location
				redirectUrl = `${protocol}//${host}` + redirectPage
			}

			var url = encodeURIComponent(redirectUrl)
			window.location.replace(paydata + "&redirect_url=" + url)
		}
		// 微信公众号支付
		else if (Constant.PayType.WECHATPAY_MP == payType) {
			Wechat.callWexinPay(paydata, cb, errorCb)
		}
	},
}

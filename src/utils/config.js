// 配置文件
let domain, imDomain, imWsDomain, domainAddress, wenetUrl, icsUrl, wechatSelfStaticUrl

// 微信公众号的appId;  小程序appId配置在 manifest.json ->  mp-weixin -> appid
const officalAccountAppId = process.env.VUE_APP_WX_OFFICAL_ACCOUNT_APP_ID
console.log('当前环境', process.env.VUE_APP_FLAG, process.env.VUE_APP_WX_MP_NAME);
// 图片域名
const picDomain = process.env.VUE_APP_IMAGE_URL
const iconDomain = process.env.VUE_APP_ICON_URL

imDomain = process.env.VUE_APP_IM_DOMAIN
imWsDomain = process.env.VUE_APP_IM_WS_DOMAIN
domainAddress = process.env.VUE_APP_H5_URL

domain = process.env.VUE_APP_API_URL
wenetUrl = process.env.VUE_APP_WENET_URL
icsUrl = process.env.VUE_APP_ICS_URL
const wxMpName = process.env.VUE_APP_WX_MP_NAME
const talkingdataAppId = process.env.VUE_APP_TALKINGDATA_APP_ID
const wxAppid = process.env.VUE_APP_WX_APP_ID
const wenetApiUrl = process.env.VUE_APP_WENET_API_URL
wechatSelfStaticUrl = process.env.VUE_APP_BAS_WECHAT_SELF_STATIC_URL

// 济南大学会员中心微页面地址 /pages/vip/vip 
const ujnVipRenovationId = process.env.VUE_APP_UJN_VIP_RENOVATIONID
const ujnVipShopId = process.env.VUE_APP_UJN_VIP_SHOPID

// #ifdef H5
// h5环境让nginx代理
domain = process.env.VUE_APP_H5_API_URL
wenetUrl = process.env.VUE_APP_H5_WENET_URL
wechatSelfStaticUrl = process.env.VUE_APP_H5_BAS_WECHAT_SELF_STATIC_URL
// #endif

const wechatSelfUrl = process.env.VUE_APP_WECHAT_SELF_URL

const ouThatBindIspWithPassword = process.env.VUE_APP_OU_THAT_BIND_ISP_WITH_PASSWORD.split(',')

exports.officalAccountAppId = officalAccountAppId
exports.picDomain = picDomain
exports.domain = domain
exports.imDomain = imDomain
exports.imWsDomain = imWsDomain
exports.domainAddress = domainAddress
exports.wenetUrl = wenetUrl
exports.iconDomain = iconDomain
exports.wxMpName = wxMpName
exports.talkingdataAppId = talkingdataAppId
exports.wxAppid = wxAppid
exports.wenetApiUrl = wenetApiUrl
exports.wechatSelfUrl = wechatSelfUrl
exports.ujnVipRenovationId = ujnVipRenovationId
exports.ujnVipShopId = ujnVipShopId
exports.icsUrl = icsUrl
exports.ouThatBindIspWithPassword = ouThatBindIspWithPassword
exports.wechatSelfStaticUrl = wechatSelfStaticUrl
// #ifdef H5
const uma = {
	init:()=>{},
	_inited:false,
	trackEvent:()=>{},
	setOpenid:()=>{},
	setUserid:()=>{},
	setUnionid:()=>{},
	pause:()=>{},
	resume:()=>{}
}
// #endif
// #ifdef MP-WEIXIN
import uma from 'umtrack-wx';
uma.init({
    // appKey:'6617418e940d5a4c493cfcf1',
    // appKey:'662b50bd940d5a4c4948fdd1',
    appKey: process.env.VUE_APP_UMENG_APPKEY,
    useOpenid:true,
    autoGetOpenid:false,
    debug:true,
    uploadUserInfo: true,
    enableVerify: true
});
// #endif


/**
 * 友盟埋点-自定义事件
 * 详情见https://databank.umeng.com/sdc/track/code-detail?key=solu240408101323107yvtydptgkomdu&type=custom
 */
class UmaTracker {
  userId = '00000';
  schoolName = '00000';

  setUserBasicInfo({userId, schoolName} = {}) {
    this.userId = userId;
    this.schoolName = schoolName;
  }

  // 聚合参数
  aggregateParams(params = {}) {
    const { Wenet_Key_UserID, Wenet_Key_School, ...res } = params
    res.Wenet_Key_UserID = Wenet_Key_UserID ? Wenet_Key_UserID : this.userId;
    res.Wenet_Key_School = Wenet_Key_School ? Wenet_Key_School : this.schoolName;
    return res;
  }

  ViewLogin(params) {
    uma.trackEvent('Wenet_Event_ViewLogin', params);
  }
  // 登录失败
  LoginFailed(params) {
    uma.trackEvent('Wenet_Event_LoginFailed', params);
  }
  // 登录成功
  LoginSuc(params) {
    uma.trackEvent('Wenet_Event_LoginSuc', this.aggregateParams(params));
  }
  // 功能按钮点击
  ModularClick(params) {
    uma.trackEvent('Um_Event_ModularClick', this.aggregateParams(params));
  }
  // 页面浏览 （友盟接入后自带一个页面统计，这个可以用来做更精细的统计）
  PageView(params) {
    uma.trackEvent('Um_Event_PageView', this.aggregateParams(params));
  }

  // 付费
  PaySuc(params) {
    uma.trackEvent('Um_Event_PaySuc', this.aggregateParams(params));
  }

  // 充值点击
  RechargeClick(params) {
    uma.trackEvent('Um_Event_RechargeClick', this.aggregateParams(params));
  }

  // 充值失败
  RechargeFailed(params) {
    uma.trackEvent('Um_Event_RechargeFailed', this.aggregateParams(params));
  }

  // 充值成功
  RechargeSuc(params) {
    uma.trackEvent('Um_Event_RechargeSuc', this.aggregateParams(params));
  }

  // 注册成功
  RegisterSuc(params) {
    uma.trackEvent('Wenet_Event_RegisterSuc', this.aggregateParams(params));
  }

  // 搜索点击
  SearchClick(params) {
    uma.trackEvent('Um_Event_SearchClick', this.aggregateParams(params));
  }

  // 搜索
  SearchSuc(params) {
    uma.trackEvent('Um_Event_SearchSuc', this.aggregateParams(params));
  }

  // 前往补充信息
  ImproveInfo(params) {
    uma.trackEvent('Wenet_Event_ImproveInfo', this.aggregateParams(params));
  }

  // 触发了隐藏tabbar
  TriggerHideTabbar(params) {
    uma.trackEvent('Wenet_Event_TriggerHideTabbar', this.aggregateParams(params));
  }

  // 触发了显示tabbar
  TriggerShowTabbar(params) {
    uma.trackEvent('Wenet_Event_TriggerShowTabbar', this.aggregateParams(params));
  }



}

uma.install =function(Vue){
  Vue.prototype.$uma = uma;
  Vue.prototype.$umaTracker = new UmaTracker();
}

export default uma
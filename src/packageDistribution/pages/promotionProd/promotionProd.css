page {
  background: #f9f9f9;
  position: relative;
}

.promotionProd {
  padding: 0;
  margin: 0;
}

/** 搜索框 */

.pd-seach {
  border: none;
  background: #fff;
  padding-top: 30rpx;
  padding-bottom: 15rpx;
}

.pd-seach .pd-seach-icon image {
  width: 20px;
  height: 20px;
  text-align: center;
  position: absolute;
  top: 40rpx;
  margin-left: 15px;
}

.pd-seach input {
  width: 95%;
  background-color: whitesmoke;
  border: 1rpx solid whitesmoke;
  border-radius: 50rpx;
  font-size: 30rpx;
  align-items: center;
  margin: auto;
/*  text-align: center; */
padding: 0 30px;
box-sizing: border-box;
  height:60rpx;
  line-height: 60rpx;
}

/** 搜索框 end*/

/** 头部导航 */

.pd-tab-list {
  height: 80rpx;
  width: 100%;
  line-height: 90rpx;
  color: gray;
  font-size: 28rpx;
  background-color: white;
  display: flex;
  justify-content: space-around;
  border-bottom: 1rpx solid whitesmoke;
  align-items: center;
}

.tab-item.down::after, .tab-item.up::after {
/*  content: " ";
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  right: -24rpx;
  top: 40rpx;
  border: 10rpx solid transparent; */
}

.tab-item.down::after {
  border-top: 10rpx solid #eb2444;
}

.tab-item.up::after {
  top: 30rpx;
  border-bottom: 10rpx solid #eb2444;
}

.tab-item {
  position: relative;
}

.tab-item.up, .tab-item.down {
  color: #476AEC;
}

.pd-tab {
  background: #fff;
}

/** 头部导航 end */

/** 商品列表 */

.pd-main {
  padding: 20rpx;
	box-sizing: border-box;
  width: 100%;
  font-size: 28rpx;
}

.pd-item {
  padding: 20rpx;
  width: 670rpx;
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

.pd-item-top {
  overflow: auto;
}

.pd-item-left {
  float: left;
  width: 220rpx;
}

.pd-item-left image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 6rpx;
  border-radius: 20rpx;
}

.pd-item-right {
  position: relative;
  float: left;
  width: 450rpx;
}

.pd-item-tit {
  height: 80rpx;
  line-height: 40rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #333;
  font-size: 28rpx;
}

.pd-item-price {
  font-weight: bold;
  margin: 20rpx 0;
  color: #333;
  font-size: 28rpx;
}

.pd-item-info {
  font-size: 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.pd-item-info .rate {
  color: #476AEC;
  margin-right: 20rpx;
  font-size: 24rpx;
}

.pd-item-price text {
  font-size: 22rpx;
}

.pd-item-btm {
  text-align: right;
}

.pd-item-btm .btn {
  padding: 6rpx 30rpx;
  background: #476AEC;
  color: #fff;
  text-align: center;
  border-radius: 50rpx;
  display: inline-block;
  font-size: 24rpx;
}

/** 商品列表 end*/

.loading, .empty {
  text-align: center;
  font-size: 28rpx;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  color: #666;
}

/*分享弹窗 */

.promo-share {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 100;
}

.promo-tit {
  padding-top: 40rpx;
  font-size: 28rpx;
  text-align: center;
  margin-left: 80rpx;
}

.promo-main {
  background: #fff;
  position: absolute;
  bottom: 0rpx;
  width: 100%;
  height: 400rpx;
  z-index: 9998;
}

.promo-icons-close image {
  width: 45rpx;
  height: 45rpx;
  float: right;
  padding-right: 20rpx;
  padding-top: 20rpx;
}

.promo-desc {
  margin-left: 73rpx;
  width: 600rpx;
  text-align: center;
}

.promo-desc text {
  padding: 20rpx;
  font-size: 24rpx;
  color: #999;
  line-height: 34rpx;
  display: inline-block;
}

.promo-icons {
  font-size: 20rpx;
  color: #666;
  display: flex;
  justify-content: space-around;
  padding: 10rpx 150rpx;
}

.promo-icons image {
  width: 75rpx;
  height: 75rpx;
  border-radius: 50%;
}

.promo-img1 {
  display: flex;
  flex-flow: column;
  align-items: center;
  line-height: 75rpx;
  font-size: 22rpx;
  background: none;
  border: 0;
  margin: 0;
  padding: 0;
}

.promo-img1::after {
  border: 0;
}

.promo-btn {
  font-size: 28rpx;
  width: 90%;
  height: 80rpx;
  border: 1px #e5e5e5 solid;
  text-align: center;
  line-height: 80rpx;
  margin-left: 40rpx;
}

/*分享弹窗 end */

/** 二维码弹窗 */

.code-popup {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.code-main {
  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  position: fixed;
  width: 500rpx;
  height: 700rpx;
}

.close-png {
  width: 50rpx;
  height: 50rpx;
  margin-top: -10rpx;
}

.code-v {
  padding: 20rpx;
  border-radius: 6rpx;
  background: #fff;
}

.wx-code {
  width: 460rpx;
  height: 460rpx;
}

.close-v {
  text-align: right;
  height: 60rpx;
}

.code-txt {
  margin-top: 20rpx;
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  background: #3a86b9;
  border-radius: 6rpx;
  height: 80rpx;
  line-height: 80rpx;
}

/** 二维码弹窗 end */


/* 引导蒙版 */
.guide-share-mask {
  position: fixed;
  left: 0;
  top: 0;
  display: block;
  width: 100%;
  height: 100%;
}
.guide-share-mask .mask {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.7);
}

.guide-share-mask .guide-content {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 100%;
  height: auto;
}
.guide-content .close {
	position: absolute;
	left: 40rpx;
	top: 40rpx;
	width: 40rpx;
	height: 40rpx;
}
.guide-content .close > image {
	width: 100%;
	height: 100%;
}
.guide-share-mask .guide-content .share-img {
  display: block;
  width: 400rpx;
  height: 400rpx;
  margin-top: 60rpx;
  margin-bottom: 30rpx;
  margin-left: 35%;
}
.guide-content .share-img image {
  display: inline-block;
  width: 100%;
  height: 100%;
}
.guide-content .share-word {
  display: block;
  color: #fff;
  text-align: center;
}
.guide-content .share-word .big-word {
  font-size: 32rpx;
}
.guide-content .share-word .small-word {
  font-size: 26rpx;
  line-height: 2em;
}

/* 引导蒙版 end */

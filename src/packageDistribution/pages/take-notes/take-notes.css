/* pages/take-notes/take-notes.wxss */
page{
  background: #f4f4f4;
}
.empty{
  width: 100%;
  font-size: 32rpx;
  color: #707070;
  margin: auto;
  text-align: center;
  line-height: 200rpx;
}
.total{
  padding: 30rpx;
  font-size: 32rpx;
  background:#fff;
}
.total .total-num{
  color: #e43130;
}
.items {
  display: flex;
  position: relative;
  padding:20rpx 30rpx;
  line-height: 42rpx;
  background:#fff;
  border-bottom: 1rpx solid #f0f0f0;
}
.items:last-child {
  border: none!important;
}
.items::after{
 content: "";
 width: 100%;
 height: 1rpx;
 background: #f4f4f4;
 position: absolute;
 top: 0;
 left: 0;
}
.items:first-child::after{
  width: 0;
}
.items .left{
  flex: 1;
}
.items .right{
  text-align: right;
}
.items .left .text{
font-size: 28rpx;

}
.items .right .number{
  font-size: 32rpx;
  color: #e43130;
}
.items .left .date{
  font-size: 25rpx;
  color: #777777;
}
.items .right .status{
font-size:25rpx;
color: #777777;
}

.loading, .empty {
  text-align: center;
  font-size: 28rpx;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  color: #999;
  margin-top: 50rpx;
}
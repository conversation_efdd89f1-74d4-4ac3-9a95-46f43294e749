<template>
<view class="claimer-mian">

  <image :src="pic" class="dist-pic" mode="widthFix"></image>
  <view class="dist-cont">
    <rich-text :nodes="content" space="emsp"></rich-text>
  </view>
  <!-- <button class='claimer-mian-btn'>申请成为分销员</button> -->
  <view class="claimer-mian-btn" @tap="onApplyDist">{{i18n.applyDistributor}}</view>
  <DistributionAgreementDrawer
    :show="showDrawer"
    @agreed="handleAgreementAgreed" 
    @cancel="handleAgreementCancel"
    :z_index="100"  
  />
</view>
</template>

<script>
import DistributionAgreementDrawer from '@/components/distribution-agreement-drawer/distribution-agreement-drawer.vue'
import http from '@/utils/http.js'
import util from '@/utils/util.js'

export default {
  data() {
    return {
      shopid: 1,
      content: '',
      pic: '',
      title: '',
      scene: "",
      // 是否可以立即成为分销员
      canApplyDist: false,
      showDrawer: false,
      // 用户是否同意过分销协议
      isAgreeDistProtocol: false,
    };
  },

  components: {
    DistributionAgreementDrawer
  },
  props: {},
  computed:{
  	i18n() {
  		return this.$t('index')
  	},
    userInfo() {
      return this.$store.state.userInfo
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    //加载分销员推广
    // console.log(decodeURIComponent(options.scene),'11')
    this.setData({
      scene: this.$Route.query.scene
    });
    this.getCondition();
    this.getClaimerList(this.shopid);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    handleAgreementAgreed() {
      this.isAgreeDistProtocol = true
      this.showDrawer = false
      this.onApplyDist()
    },
    handleAgreementCancel() {
      this.showDrawer = false
    },
    getCondition() {
      var params = {
        url: "/p/distribution/register/condition",
        method: "GET",
        callBack: res => {
          const needIdCardNo = res.identityCardNumber;
          const neddIdCardPic = res.identityCardPic;
          const needRealName = res.realName;
          this.setData({
            canApplyDist: !needIdCardNo && !neddIdCardPic && !needRealName
          });
        }
      };
      http.request(params);
    },

    registerDist() {
      uni.showLoading();
      var params = {
        url: "/p/distribution/register/addDistributionUser",
        method: "post",
        data: {
          sharerCardNo: this.scene,
          userMobile: this.userInfo.userMobile,
          isAgreeDistProtocol: true
        },
        callBack: res => {
          uni.hideLoading()
          if (res) {
            uni.showModal({
              content: "您已成功成为分销员",
              showCancel: false,
              confirmText: this.i18n.confirm,
              complete: ()=> {
                uni.redirectTo({
                  url: 'packageDistribution/pages/dis-center/dis-center',
                })
              }
            });
          } else {
            uni.showModal({
              content: "您已成功提交审核",
              showCancel: false,
              confirmText: this.i18n.confirm,
              complete: ()=> {
                uni.switchTab({
                  url: '/pages/user/user',
                })
              }
            });
          }
        }
      };
      http.request(params);
    },
    /**
     * 查看
     */
    getClaimerList: function (shopid) {
      //uni.showLoading();
      var params = {
        url: "/p/distribution/recruit/info",
        method: "GET",
        // data: {
        //   shopId:shopid,
        // },
        callBack: res => {

           res.content = res.content.replace(/<p([\s\w"=\/\.:;]+)((?:(style="[^"]+")))/ig, '<p')
          .replace(/<p>/ig, '<p style="font-size: 15px; line-height: 25px; word-wrap: break-word; word-break: normal;">')
          .replace(/<img([\s\w"-=\/\.:;]+)((?:(height="[^"]+")))/ig, '<img$1')
          .replace(/<img([\s\w"-=\/\.:;]+)((?:(width="[^"]+")))/ig, '<img$1')
          .replace(/<img([\s\w"-=\/\.:;]+)((?:(style="[^"]+")))/ig, '<img$1')
          .replace(/<img([\s\w"-=\/\.:;]+)((?:(alt="[^"]+")))/ig, '<img$1')
          .replace(/<img([\s\w"-=\/\.:;]+)/ig, '<img style="width: 100%;" $1');
          
          this.setData({
            content: util.formatHtml(res.content),
            pic: res.pic,
            title: res.title
          }); //设置标题

          wx.setNavigationBarTitle({
            title: res.title
          });
        }
      };
      http.request(params);
    },
    onApplyDist: function () {
      if (!this.isAgreeDistProtocol) {
        this.showDrawer = true
        return
      }
      if (this.canApplyDist) {
        this.registerDist();
        return
      }
      // console.log(this.data.scene)
      if (this.scene) {
        uni.navigateTo({
          url: '/packageDistribution/pages/applyDistCon/applyDistCon?scene=' + this.scene
        });
      } else {
        uni.navigateTo({
          url: '/packageDistribution/pages/applyDistCon/applyDistCon'
        });
      }
    }
  }
};
</script>
<style>
@import "./applyDist.css";
</style>
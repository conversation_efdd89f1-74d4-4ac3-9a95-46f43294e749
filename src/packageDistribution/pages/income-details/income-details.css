/* pages/income-details/income-details.wxss */

page {
  background: #fff;
}

.income-header{
	width: 700rpx;
	height: 300rpx;
	box-sizing: border-box;
  background: linear-gradient(to right,#5875E0,#476AEC);
  background: -webkit-linear-gradient(to right, #5875E0, #476AEC);
  display: flex;
  flex-direction: column;
  border-radius: 16rpx;
  margin: 20rpx auto;
}
.income-title{
	font-size: 28rpx;
	color: #fff;
	padding: 30rpx;
	border-bottom: 2rpx solid #fff;
}
.income-list{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	margin-top: 30px;
}
.income-list-item{
	display: flex;
	flex-direction: column;
	align-items: center;
}
.income-list-item-num{
	font-size: 28rpx;
	color: #fff;
	margin-bottom: 20px;
}
.income-list-item-name{
	font-size: 28rpx;
	color: #fff;
}


/* 底色白色 */
/* .income-detail {
  padding-bottom: 20rpx;
  border-top: 1rpx solid #e1e1e1;
  background: #fff;
    box-shadow: 0 4px 10px 0 rgba(83, 83, 83, 0.288);
} */

/* 卡片 */
.income-detail {
  padding-bottom:20rpx;
  border-top:1rpx solid #e1e1e1;
  /* background:#3a86b9; */
  /* background:#e43130; */
  box-shadow:0 4px 10px 0 rgba(83, 83, 83, 0.288);
  border-radius:30rpx;
  width:96%;
  margin: 10rpx auto 0;
  color: #ffffff;
  background-image: linear-gradient(#323444 20%, #565969 80%);
}
.gather-income {
  padding: 20rpx 0;
  text-align: center;
}

.gather-income .title {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.number {
  display: inline-block;
  margin: auto;
  text-align: center;
  background: #fff;
  /* color: #3a86b9; */
  color: #bea379;
  padding: 10rpx 30rpx;
  padding-top: 14rpx;
  border-radius: 50rpx;
  line-height: 1em;
  /* border: 1rpx solid #fff; */
}

.income-item {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}

.items {
  width: 50%;
  text-align: center;
  font-size: 28rpx;
  border-right: 1rpx solid #bea379;
}

.items > view {
  line-height: 2em;
}

.items:last-child {
  border: none;
}

.block-title {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  font-size: 28rpx;
  line-height: 70rpx;
  margin-top: 30rpx;
  background: rgb(247, 247, 247);
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.block-lists .list-item {
  padding: 10rpx 30rpx;
}

.block-lists .list-item .tit {
  display: flex;
  padding: 20rpx 0rpx;
  font-size: 28rpx;
  justify-content: space-between;
  border-bottom: 2rpx solid #ebebeb;
}
.block-lists .list-item .tit .origin {
  flex: 1;
}

.block-lists .list-item .tit .income-num {
  text-align: right;
  color: #476AEC;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 28rpx;
}
.income-fail{
  color: #666;
}

.block-lists .list-item .tit .income-num .invalid-num {
  color: #999;
}

.block-lists .list-item .date {
  font-size: 22rpx;
  color: #999;
  padding-top: 10rpx;
}


.loading, .empty {
  text-align: center;
  font-size: 28rpx;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  color: #999;
  margin-top: 50rpx;
}
.origin-status{
  font-size: 22rpx;
}
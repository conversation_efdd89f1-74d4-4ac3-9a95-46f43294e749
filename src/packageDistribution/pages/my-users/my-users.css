/* pages/my-users/my-users.wxss */

.user-bg {
  background: linear-gradient(to right,#5875E0,#476AEC);
  background: -webkit-linear-gradient(to right, #5875E0, #476AEC);
  font-size: 36rpx;
  width: 700rpx;
  margin: 20rpx auto;
  height: 200rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.user-bg-my {
  display: block;
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 0 20rpx;
}
.user-bg .text {
  display: block;
  text-align: center;
  font-size: 32rpx;
  color: #ffffff;
}

.user-bg .text .number {
  display: inline-block;
  font-size: 36rpx;
  margin: 0 20rpx;
  vertical-align: middle;
}

.title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-sizing: border-box;
}

.title .subtitle {
  font-size: 28rpx;
  line-height: 80rpx;
}

.lists{
  margin-bottom: 10rpx;
  padding: 0 20px;
}
.list-item {
  display: flex;
  justify-content: space-between;
  align-items:center;
  height:100rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #ebebeb;
}

.items {
  font-size: 28rpx;
  text-align: center;
  word-wrap: break-word;
  word-break: break-all;
}

.img {
  width: 88rpx;
  height: 88rpx;
}

.img image {
  width: 100%;
  height: 100%;
  border-radius: 50rpx;
}


.loading, .empty {
  text-align: center;
  font-size: 26rpx;
  width: 100%;
  height: 120rpx;
  line-height: 120rpx;
  color: #999999;
  margin-top: 50rpx;
}
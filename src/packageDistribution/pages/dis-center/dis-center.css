/* pages/dis-centers/dis-center.wxss */

page {
  background: #f4f4f4;
}
.banner {
  position: relative;
  width: 750rpx;
}
.banner .bg{
	font-size: 0;
	width: 750rpx;
	background: center url('../../static/images/icon/dis-center-banner.png') no-repeat;
	background-size: 100% auto;
	height: 155px;
}

/* 用户信息 */
.user-information image {
  display: block;
  width: 100%;
  height: 100%;
}
.user-information {
  height: 140rpx;
  position: relative;
  font-size: 30rpx;
}
.user-info-box {
  display: flex;
  align-items: center;
  padding: 20px 30px 0;
}
.user-head-portrait {
  display: inline-block;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 20rpx;
  overflow: hidden;
}
.username {
  font-size: 30Rpx;
  font-family: PingFang SC;
  line-height: 42rpx;
  color: #FFFFFF;
  opacity: 1;
  color: #FFFFFF ;
  word-break: break-all;
}
.username .grade{
	font-size: 20rpx;
	width: 60px;
	height: 20px;
	margin: 0 14rpx;
	border-radius: 10px;
	background: linear-gradient(to right,#F7D14B,#F2A83B);
	background: -webkit-linear-gradient(to right,#F7D14B,#F2A83B);
	display: inline-block;
	line-height: 20px;
	text-align: center;
}
.right{
	flex: 1;
	width: auto;
	display: inline-block;
	vertical-align: middle;
}


.banner .card .content{
	background-color: #FFFFFF;
	border-radius: 12rpx;
	width: 100%;
	height: 100%;
}
.banner .card .content .content-top{
	height: 166rpx;
	display: flex;
	/* justify-content: space-around; */
	padding: 30rpx;
	box-sizing: border-box;
}
.banner .card .content .content-buttom{
	height: 88rpx;
	display: flex;
	justify-content: space-around;
}
.banner .card .content .content-buttom .draw-btn-left{
	text-align: center;
	color: #FFFFFF;
}
.banner .card .content .content-buttom .draw-btn-right{
	text-align: center; 
	color: #E43130;
}
.banner .card .content .content-buttom .btn-text {
  width: auto;
	max-width: 300rpx;
  /* height: 68rpx; */
  display: inline-block;
  padding: 14rpx 30rpx;
  font-size: 26rpx;
  font-weight: 400;

  border-radius: 34rpx;
  border: 1rpx #E43130 solid;
  line-height: 26rpx;
}
.banner .card .content .item{
	text-align: center;
	vertical-align: middle;
	display: inline-block;
	width: 50%;
	background-color: white;
}
.banner .card .content .item .income-money{
	font-size: 40rpx;
	line-height: 56rpx;
	color: #333333;
}
.banner .card .content .item .income-text{
	font-size: 24rpx;
	color: #777777;
	font-weight: 400;
	line-height: 34rpx;
}

/* 列表 */
.sorting-menu{
  width: 694rpx;
  margin: 0 auto;
  margin-top: 24rpx;
  display: flex;
  align-items: center; 
}
.menu-cont {
  width: 100%;
  height: auto;
  border-radius: 12rpx;
  padding: 40rpx 40rpx;
  background-color: #FFFFFF;
  box-sizing: border-box;
}
.menu-cont .menu-cont-list {
  display: flex;
  align-items: center;
  height: 108rpx;
  background: #fff;
  height: 100rpx;
}
.menu-cont .menu-cont-list:last-child {
  border: none;
}
.menu-cont .menu-cont-list .icon {
  width: 60rpx;
  height: 60rpx;
  font-size: 0;
}
.menu-cont .menu-cont-list .icon image {
  width: 100%;
}
.menu-cont .menu-cont-list .list-title {
  font-size: 28rpx;
  margin-left: 20rpx;
  font-weight: 400;
  line-height: 40rpx;
  color: #333333;
  font-family: PingFang SC;
  flex: 1;
}
.arrow { 
  width:16rpx;
  height:16rpx;
  border-top:4rpx solid #DBDBDB;
  border-right:4rpx solid #DBDBDB;
  transform: rotate(45deg);
}

/* 分销业务栏 */
.lists {
  width: 694rpx;
  height: 140rpx;
  margin: 50px auto 0;
  display: flex;
  justify-content: space-around;
  background-color: #FFFFFF;
  margin-top: 24rpx;
  border-radius: 12rpx;
}
.lists .list-item {
  display: flex;
  align-items: center;
  height: 100%;
}
.lists .list-item .item-text {
  display: inline-block;
  margin-left: 18rpx;
}
.lists .list-item .item-text .item-text-title{
	font-size: 28rpx;
	font-weight: 400;
	line-height: 40rpx;
	color: #333333;
	margin-bottom: 4rpx ;
}
.lists .list-item .item-text .item-text-desc{
	font-size: 24rpx;
	font-weight: 400;
	line-height: 34rpx;
	color: #AAAAAA;
}
.list-item .icon {
  display: inline-block;
  width: 84rpx;
  height: 84rpx;
}
.list-item .icon image{
	width: 100%;
	height: 100%;
}




.enter-grade {
  position: absolute;
  right: 0;
  top: 22%;
  /* transform: translateY(-50%); */
  display: inline-block;
  padding: 5rpx 10rpx;
  font-size: 24rpx;
  background: #b39566;
  border-radius: 50rpx 0 0 50rpx;
  color: #fff;
}
.grade-icon {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  vertical-align: middle;
}
.grade-text {
  display: inline-block;
  vertical-align: middle;
  /* line-height: 1em; */
}
.user-info {
  /* background: #3a86b9; *//* background: #00a0e9;  WeNet蓝 */
  background: #fff;
}
.user-info .info-cont {
  padding: 30rpx 50rpx 30rpx 50rpx;
}


	/* 横排 */
	
	/* .income {
	  display: flex;
	  color: #000;
	  margin-bottom: 25rpx;
	}
	 */
	/* .income .income-left {
	  width: 50%;
	  text-align: center;
	 */  /* border-right: 1rpx solid #ffffff80; */
	/*  border-right: 1rpx solid #8a8a8a80;
	}
	 */
	/* .income .income-right {
	  width: 50%;
	  text-align: center;
	}
	
	.income .item {
	  font-size: 28rpx;
	  margin-bottom: 20rpx;
	  color: #999;
	}
	
	.income .num {
	  font-size: 40rpx;
	}
	
	.user-info .info-cont  .rule {
	  color: #000;
	  text-align: center;
	  margin-bottom: 10rpx;
	}
	
	.user-info .info-cont .rule .symbol-what {
	  font-size: 23rpx;
	  border: 1rpx solid #999;
	  border-radius: 50rpx;
	  padding: 0rpx 8rpx;
	  color: #999;
	}
	
	.user-info .info-cont .rule .rule-tip {
	  font-size: 24rpx;
	  margin-left: 10rpx;
	  color: #999;
	}
	
	.user-info .info-cont .draw-btn {
	  background: #fff;
	  padding: 8rpx 0rpx;
	  text-align: center;
	}
	.user-info .info-cont .draw-btn .btn {
	  display: inline-block;
	  padding: 12rpx 24rpx;
	  font-size: 28rpx;
	  border-radius: 50rpx;
	  color: #e43130;
	  border: 2rpx solid #e43130;
	}
	 */
	/* 竖排 */
	
	/* .user-info .info-cont .total-income {
	  display: flex;
	  justify-content: space-between;
	  border-bottom: 1rpx solid #ffffff80;
	  padding-bottom: 20rpx;
	}
	
	.user-info .info-cont .total-income .income-dt {
	  color: #fff;
	  line-height: 60rpx;
	}
	
	.user-info .info-cont .total-income .income-dt .total {
	  font-size: 26rpx;
	}
	
	.user-info .info-cont .total-income .income-dt .num {
	  font-size: 50rpx;
	}
	
	.user-info .info-cont .total-income .rule {
	  color: #fff;
	}
	
	.user-info .info-cont .total-income .rule .symbol-what {
	  font-size: 26rpx;
	  border: 1rpx solid #fff;
	  border-radius: 50rpx;
	  padding: 0rpx 8rpx;
	}
	
	.user-info .info-cont .total-income .rule .rule-tip {
	  font-size: 26rpx;
	  margin-left: 10rpx;
	}
	
	.user-info .info-cont .withdraw {
	  color: #fff;
	  font-size: 26rpx;
	  display: flex;
	  justify-content: space-between;
	  align-items: center;
	  padding-top: 20rpx;
	}
	
	.user-info .info-cont .withdraw .draw-btn {
	  border: 2rpx solid #fff;
	  border-radius: 8rpx;
	  padding: 2rpx 26rpx;
	} */
	

	
	/* .menu-cont-list .small-icon {
	  width: 40rpx;
	  height: 40rpx;
	  font-size: 0;
	}
	 */
/* 卡片一栏 */
.card{
	width: 700rpx;
	background-color: #fff;
	border-radius: 10px;
	margin: 0 auto 0;
}
.card-title{
	font-size: 14px;
	color: #333;
	padding: 10px 20px;
	border-bottom: 2rpx solid #F5F5F5;
}
.three-box{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	margin-top: 30px;
}
.three-item{
	display: flex;
	flex-direction: column;
	align-items: center;
}
.num{
	font-size: 20px;
	color: #476AEC;
	font-weight: bold;
}
.name{
	font-size: 12px;
	color: #888;
	margin-top: 20rpx;
}
.bottom-total{
	height: 36px;
	background: linear-gradient(to right,#ECD7BD,#DCB382);
	background: -webkit-linear-gradient(to right, #ECD7BD, #DCB382);
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding: 0 10px;
	border-radius: 0 0 20rpx 20rpx;
	margin-top: 30px;
}
.total{
	font-size: 14px;
	color: #90663A;
}
.price-btn{
	display: flex;
	flex-direction: row;
	align-items: center;
}
.price{
	font-size: 12px;
	color: #90663A;
	margin-right: 20rpx;
}
.r-arrow{
	width: 18px;
	height: 18px;
}
.product-box{
	display: flex;
	flex-direction: row;
	align-items: center;
	flex-wrap: wrap;
	padding-bottom: 30px;
}
.product-item{
	width: 33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-top: 30px;
}
	
.product-icon{
	width: 44px;
	height: 44px;
	margin-bottom: 10px;
}
.product-name{
	font-size: 14px;
	color: #888;
}
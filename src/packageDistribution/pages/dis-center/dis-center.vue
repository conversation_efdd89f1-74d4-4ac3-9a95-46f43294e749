<template>
	<!--pages/dis-centers/dis-center.wxml-->
	<view class="container">
		<!-- 用户信息栏 -->
		<view class="banner">
			<view class="bg">
				<view class="user-information">
					<view class="user-info-box">
						<!-- 头像 -->
						<view class="user-head-portrait">
							<image :src="distInfo.pic?distInfo.pic:`${staticPicDomain}images/icon/head04.png`"></image>
							<!-- <open-data type="userAvatarUrl"></open-data> -->
						</view>
						<view class="right">
							<!-- 用户名 -->
							<view class="username">
								{{distInfo.nickName}}
								<view class="grade"  @tap="toSalesmanLevel">
									<!-- {{i18n.vip}}{{distInfo.levelName}} -->
									分销员
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			
			<view class="card" style="margin-Top: -50px;">
				<view class="card-title">我的资金</view>
				<view class="three-box">
					<view class="three-item">
						<text class="num">{{toPrice(dayAmount)}}</text>
						<text class="name">今日收益</text>
					</view>
					<view class="three-item">
						<text class="num">{{toPrice(monthAmount)}}</text>
						<text class="name">本月收益</text>
					</view>
					<view class="three-item">
						<text class="num">{{toPrice(addupAmount)}}</text>
						<text class="name">总收益</text>
					</view>
				</view>
				<view class="bottom-total">
					<text class="total">已结算佣金：¥{{toPrice(settledAmount)}}</text>
					<!-- <view class="price-btn" @tap="toWithdrawal">
						<text class="price">立即提现</text>
						<image class="r-arrow" :src="`${staticPicDomain}images/icon/arrow.png`"></image>
					</view> -->
				</view>
			</view>
			
			<!-- 实名认证提示 -->
			<view class="real-name-notice">
				<view class="notice-icon">
					<text class="notice-warning">!</text>
				</view>
				<view class="notice-text">请确保该微信账号已完善实名信息，否则佣金将无法到账</view>
			</view>
			
			<view class="card" style="margin-top: 15px;">
				<view class="card-title">推广管理</view>
				<view class="product-box">
					<!-- <view class="product-item"@tap="toWalletCashPage">
						<image class="product-icon" :src="`${staticPicDomain}images/icon/p1.png`"></image>						
						<text class="product-name">提现记录</text>
					</view> -->
					<view class="product-item" @tap="toIncomeDetailsPage">
						<image class="product-icon" :src="`${staticPicDomain}images/icon/p2.png`"></image>						
						<text class="product-name">收益明细</text>
					</view>
					<view class="product-item" @tap="toInvitationCards">
						<image class="product-icon" :src="`${staticPicDomain}images/icon/p3.png`"></image>						
						<text class="product-name">邀请好友</text>
					</view>
					<view class="product-item" @tap="toMyUserPage">
						<image class="product-icon" :src="`${staticPicDomain}images/icon/p4.png`"></image>						
						<text class="product-name">我的用户</text>
					</view>
					<view class="product-item" @tap="toPromotionOrderPage">
						<image class="product-icon" :src="`${staticPicDomain}images/icon/p5.png`"></image>						
						<text class="product-name">我的推广</text>
					</view>
					<view class="product-item" @tap="toPromotionProdPage">
						<image class="product-icon" :src="`${staticPicDomain}images/icon/p6.png`"></image>						
						<text class="product-name">推广商品</text>
					</view>					
				</view>
			</view>	
		</view>
    <distribution-agreement-drawer 
      :show="showDrawer" 
      @agreed="handleAgreementAgreed" 
      @cancel="handleAgreementCancel"
      :z_index="100"
    />
	</view>
</template>


<script>
	// pages/dis-centers/dis-center.js
	var http = require("../../../utils/http.js");
  import DistributionAgreementDrawer from '@/components/distribution-agreement-drawer/distribution-agreement-drawer.vue'


	export default {
		data() {
			return {
				unsettledAmount: 0,
				// 待结算金额
				settledAmount: 0,
				// 可提现金额
				invalidAmount: 0,
				// 已失效金额
				addupAmount: 0,
				monthAmount: 0,
				dayAmount: 0,
				// 累计收益
				disNotice: [],
				// 分销公告
				distInfo: {},
        // 用户是否同意过分销协议
        isAgreeDistProtocol: false,
        showDrawer: false
			};
		},

    components: {
      DistributionAgreementDrawer
    },
		props: {},
		computed:{
			i18n() {
				return this.$t('index')
			},
      userInfo() {
        return this.$store.state.userInfo
      }
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			//头部导航标题
			uni.setNavigationBarTitle({
				title:this.i18n.distributioncenter
			});
			// 分销员信息
			this.getDisInfo();
			// 分销钱包数据
			this.getDisInfoData();
			// 获取分销公告
			this.getDistributionMsg();
		},
		methods: {
      // 处理协议弹窗取消
      handleAgreementCancel() {
        this.showDrawer = false
        uni.switchTab({
          url: '/pages/user/user'
        })
      },
      // 用户同意了协议
      handleAgreementAgreed() {
        var params = {
          url: "/p/distribution/register/addDistributionUser",
          method: "post",
          data: {
            userMobile: this.userInfo.userMobile,
            isAgreeDistProtocol: true
          },
          callBack: res => {
            this.showDrawer = false
            this.getDisInfo()
          },
          errCallBack: (err) => {
            uni.hideLoading()
            uni.showToast({
              title: err.data,
              icon: 'none'
            })
            reject(err)
          }
        };
        http.request(params);
      },
			/**
			 * 获取分销员信息
			 */
			getDisInfo() {
				http.request({
					url: "/p/distribution/user/distributionUserInfo",
					method: "GET",
					data: {
						shopId: 1
					},
					callBack: res => {

						if (res.state === -1) {
							uni.showModal({
							    title: '',
								showCancel:false,
                                content: this.i18n.distributorBanned,
								confirmText: this.i18n.confirm,
								showCancel: false,
								confirmColor: "#eb2444",

								success(res) {
									if (res.confirm) {
										uni.switchTab({
											url: '/pages/user/user'
										})
									}
								}
							})
						}

						if (res.state === 2) {
							uni.showToast({
								title: this.i18n.distributorCleared,
								icon: 'none',
								// #ifndef MP-TOUTIAO
                mask: true
              	// #endif
							})
							uni.showModal({
							    title: '',
                                content: this.i18n.distributorCleared,
								confirmText: this.i18n.confirm,
								confirmColor: "#eb2444",
								showCancel: false,
								success(res) {
								}
							})
						}
						this.distInfo = res
            if (!res.isAgreeDistProtocol) {
              this.showDrawer = true
            }
					}
				})
			},
			// 跳转到分销员等级页面
			toSalesmanLevel() {
				uni.navigateTo({
					url: '/packageDistribution/pages/salesmanLevel/salesmanLevel'
				});
			},

			// 跳转到公告页面
			toNotice() {
				if(this.disNotice.length > 0) {
					uni.navigateTo({
						url: '/packageUser/pages/recent-news/recent-news?isDist=' + true
					});
				}
			},

			// 获取分销公告
			getDistributionMsg: function() {
				var ths = this;
				var params = {
					url: '/p/distribution/msg/page',
					method: 'GET',
					data: {
						// current: 1,
						isTop: '' // size: 10

					},
					callBack: res => {
						ths.setData({
							disNotice: res.records
						});
					}
				};
				http.request(params);
			},

			/**
			 * 获取用户钱包数据
			 */
			getDisInfoData: function() {
				uni.showLoading();
				var params = {
					url: "/p/distribution/user/info",
					method: "GET",
					// data: {
					//   shopId: 1
					// },
					callBack: res => {
						this.setData({
							settledAmount: res.distributionUserWallet.settledAmount,
							addupAmount: res.distributionUserWallet.addupAmount,
							monthAmount: res.distributionUserWallet.monthAmount,
							dayAmount: res.distributionUserWallet.dayAmount
						});
						uni.hideLoading()
					}
				};
				http.request(params);
			},

			/**
			 * 跳转收入明细
			 */
			toIncomeDetailsPage: function() {
				uni.navigateTo({
					url: '/packageDistribution/pages/income-details/income-details'
				});
			},

			/**
			 * 跳转提现记录
			 */
			toWalletCashPage: function() {
				uni.navigateTo({
					url: '/packageDistribution/pages/take-notes/take-notes'
				});
			},

			/**
			 * 跳转提现规则
			 */
			toWalletCashRulePage: function() {
				uni.navigateTo({
					url: '/packageDistribution/pages/draw-rule/draw-rule'
				});
			},

			/**
			 * 跳转至我的用户
			 */
			toMyUserPage: function() {
				uni.navigateTo({
					url: '/packageDistribution/pages/my-users/my-users'
				});
			},

			/**
			 * 跳转我的推广界面
			 */
			toPromotionOrderPage: function() {
				uni.navigateTo({
					url: '/packageDistribution/pages/promotion-order/promotion-order'
				});
			},

			/**
			 * 跳转我的推广商品
			 */
			toPromotionProdPage: function() {
				uni.navigateTo({
					url: '/packageDistribution/pages/promotionProd/promotionProd'
				});
			},

			/**
			 * 跳转到邀请好友页面
			 */
			toInvitationCards: function() {
				uni.navigateTo({
					url: '/packageDistribution/pages/InvitationCards/InvitationCards'
				});
			},

			/**
			 * 跳转到提现
			 */
			toWithdrawal: function() {
				uni.navigateTo({
					url: '/packageDistribution/pages/withdrawal/withdrawal'
				});
			},

			/*
				跳转到分销员列表，已写好入口，等待对接
			*/
		   toDistributorListPage:function(){

		   }
		}
	};
</script>
<style>
	@import "./dis-center.css";
	
	/* 实名认证提示样式 */
	.real-name-notice {
		display: flex;
		align-items: center;
		margin: 15px 18px 0;
		padding: 12px 16px;
		background-color: #FFF5F6;
		border-radius: 8px;
		border-left: 4px solid #e4393c;
	}
	
	.notice-icon {
		margin-right: 8px;
		display: flex;
		align-items: center;
	}
	
	.notice-warning {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 20px;
		height: 20px;
		background-color: #e4393c;
		color: #fff;
		font-weight: bold;
		border-radius: 50%;
		font-size: 14px;
		text-align: center;
		line-height: 20px;
	}
	
	.notice-text {
		color: #e4393c;
		font-size: 14px;
		line-height: 1.4;
	}
</style>

.InvitationCards-mian {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
	background: linear-gradient(to bottom,#6041FF,#36C8FF);
	background: -webkit-linear-gradient(to bottom, #6041FF, #36C8FF);
	padding-top: 100px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.h-title{
	font-size: 26px;
	color: #fff;
	font-weight: bold;
}

.share-btn{
	width: 200px;
	height: 40px;
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 20px auto 0;
	background: linear-gradient(to right,#3EB0FF,#6041FF);
	background: -webkit-linear-gradient(to right, #3EB0FF, #6041FF);
	font-size: 14px;
	color: #fff;
	font-weight: bold;
}

.InvitationCards-ont {
  width: 500rpx;
  height: 840rpx;
  border-radius: 40rpx;
  background: #fff;
  margin-top: 20px;
}

.row-box{
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	margin: 20px 0;
}

.left-row{
	width: 24rpx;
	height: 24rpx;
	border-radius: 100%;
	background: #5c6ef7;
	margin-left: -12rpx;
}
.center-line{
	flex: 1;
	height: 2px;
	border-bottom: 4rpx dashed  #eee ;
	margin: 0 20rpx;
}
.right-row{
	width: 24rpx;
	height: 24rpx;
	border-radius: 100%;
	background: #5c6ef7;
	margin-right: -12rpx;
}

.InvitationCards-in {
  /* background-color: #fff5ee; */
  width: 500rpx;
  /* height: 812rpx; */
  /* border: 1px solid #ffebcd; */
  margin: auto;
  margin-top: 10rpx;
  border-radius: 10rpx;
  text-align: center;
}

.hrLineLeft {
  display: flex;
  align-items: center;
}

.hrLineLeft .line {
  width: 100rpx;
  height: 3rpx;
  background: rgba(255, 166, 0, 0.582);
}

.hrLineLeft .circle {
  width: 10rpx;
  height: 10rpx;
  background: rgba(255, 166, 0, 0.582);
  border-radius: 50%;
}

.hrLineRight .line {
  width: 100rpx;
  height: 3rpx;
  background: rgba(255, 166, 0, 0.582);
}

.hrLineRight .circle {
  width: 10rpx;
  height: 10rpx;
  background: rgba(255, 166, 0, 0.582);
  border-radius: 50%;
}

.hrLineRight {
  display: flex;
  align-items: center;
}

.title {
  display: flex;
  /* padding-left: 50rpx; */
  padding-top: 50rpx;
  justify-content: center;
}

.centerText {
  color: #cd5c5c;
  font-size: 32rpx;
  padding: 0 0.75rem;
}

.InvitationCards-in-image {
  overflow: hidden;
  display: block;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.2);
  margin: auto;
  margin-top: 56rpx;
}
.InvitationCards-in-image image{
	width: 100%;
	height: 100%;
}

.userinfo-name {
  font-size: 28rpx;
  margin-top: 10rpx;
	max-width: 320rpx;
	margin: 18rpx auto 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.InvitationCards-yaoqi {
  font-size: 28rpx;
  margin-top: 20rpx;
  color: grey;
}

.InvitationCards-in-image2 {
  display: flex;
  width: 300rpx;
  height: 300rpx;
  padding: 20rpx;
  margin: auto;
  background: #fff;
}

.InvitationCards-in-image2 image {
  width: 300rpx;
  height: 300rpx;
}

.InvitationCards-mian-img image {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-top: 30rpx;
  margin-left: 230rpx;
}

.InvitationCards-mian-text {
  position: relative;
  font-size: 27rpx;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  margin: auto;
  margin-top: 50rpx;
  /* background: #CD5C5C; */
  border-radius: 6rpx;
  color: #666;
  width: 300rpx;
}

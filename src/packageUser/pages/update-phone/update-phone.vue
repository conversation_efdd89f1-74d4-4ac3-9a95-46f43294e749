<template>
  <view class="con">
    <view class="login-form">
      <view class="login-form-title">更换手机号码</view>
      <view :class="['item',errorTips==1 || MobileEmpty? 'error':'']">
        <!-- 手机号 -->
        <NInput
          v-model="mobile"
          type="number"
          :placeholder="i18n.enterMobileNumber"
          @input="handleInpFocus"
          nomargin
        >
          <template #addonBefore>
            <text class="input-item">+86</text>
          </template>
        </NInput>
        <view class="error-text" v-if="errorTips==1"><text class="warning-icon">!</text>{{i18n.enterValidPhone}}</view>
        <view class="error-text" v-if="MobileEmpty"><text class="warning-icon">!</text>{{i18n.enterMobileNumber}}</view>
      </view>
      <view :class="['item',errorTips==2 || errorTips==6? 'error':'']">
        <!-- 验证码 -->
        <NInput
          v-model="validCode"
          type="text"
          :placeholder="i18n.enterCode"
          @input="handleInpFocus"
          nomargin
        >
          <template #addonAfter>
            <text class="input-item" @click="getCode" v-if="show">{{i18n.getCode}}</text>
            <text class="input-item getcode" v-if="!show">{{count}} s</text>
          </template>
        </NInput>
        <view class="error-text" v-if="errorTips==2"><text class="warning-icon">!</text>{{i18n.enterCode}}</view>
        <view class="error-text" v-if="errorTips==6"><text class="warning-icon">!</text>{{i18n.enterCodeFirst}}</view>
      </view>
    </view>

    <!-- 按钮 -->
    <button class="authorized-btn" @tap="handleConfirm">确认更换</button>

  </view>
</template>

<script>
	var http = require("@/utils/http");
	var util = require('@/utils/util.js');
  import NInput from '@/components/form/n-input'

	export default {
		data() {
			return {
				errorTips: 0, // 输入错误提示:  1手机号输入错误  2验证码输入错误  3账号输入错误  4密码输入错误  5验证密码输入错误
				mobile: '',

				// 验证码相关
				validCode: '',
				show: true,
				count: '',
				timer: null,
				MobileEmpty: false, // 手机号是否为空
			};
		},

		components: {
      NInput
		},

		computed: {
			i18n() {
				return this.$t('index')
			},
			appType() {
				return uni.getStorageSync('appType')
			}
		},

		methods: {

			/**
			 * 输入框聚焦
			 */
			handleInpFocus() {
				this.errorTips = 0
			},

			/**
			 * 获取验证码
			 */
			getCode() {
				if (!this.mobile) {
					this.MobileEmpty = true
					this.errorTips = 0
					return
				}
				if (!util.checkPhoneNumber(this.mobile)) {
					this.MobileEmpty = false
					this.errorTips = 1
					return
				}
				this.MobileEmpty = false
				this.errorTips = 0

				var params = {
					url: "/sms/sendLoginCode",
					method: "post",
					data: {
						mobile: this.mobile,
					},
					callBack: res => {
						const timeCount = 60;
						if (!this.timer) {
							this.count = timeCount
							this.show = false;
							this.timer = setInterval(() => {
								if (this.count > 0 && this.count <= timeCount) {
									this.count--;
								} else {
									clearInterval(this.timer);
									this.timer = null
									this.show = true
								}
							}, 1000)
						}
					}
				};
				http.request(params);
			},


			handleConfirm() {
        if (!this.validCode || !this.mobile) {
          uni.showToast({
            title: '请输入手机号和验证码',
            icon: 'none'
          })
          return
        }

        const params = {
          url: "/p/user/setUserInfo",
					method: "PUT",
          data: {
            userMobile: this.mobile,
            validCode: this.validCode,
          },
          callBack: res => {
            uni.showToast({
              title: '更换成功',
              icon: 'none'
            })
            uni.navigateBack()
          }
        }

        http.request(params);
			},
		},

}
</script>

<style scoped>

.con {
  padding: 64rpx 48rpx;
}
.login-form-title {
  font-size: 36rpx;
  color: #333;
  text-align: center;
  margin-bottom: 8%;
  font-weight: 500;
}
.authorized-btn {
  display: flex;
  width: 100%;
  padding: 24rpx 174rpx;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 170rpx;
  background: var(--primary-color);
	border: 0rpx;
	line-height: 1;
	margin-top: 96rpx;
}
.input-item {
  font-size: 28rpx;
	color: var(--primary-color);
  padding: 0 16rpx;
}
.input-item.getcode {
  width: 120rpx;
  text-align: right;
}

button::after{
  border: 0 !important;
}
.error .error-text {
	display: block;
	width: 100%;
	font-size: 28rpx;
	color: #e43130;
  text-align: left;
  margin-top: 10rpx;
}
.error .error-text .warning-icon {
  display: inline-block;;
  color: #fff;
  width: 26rpx;
  height: 26rpx;
  line-height: 26rpx;
  background: #e43130;
  border-radius: 50%;
  text-align: center;
  margin-right: 12rpx;
  font-size: 22rpx;
}
.to-login-or-bind {
	font-size: 28rpx;
	color: #00AAFF;
	display: flex;
	width: 100%;
  justify-content: space-between;
}

/* 条款声明 */
.statement{
  font-size: 26rpx;
  line-height: 2em;
}
.statement-text{
  margin-left:3px;
}
.statement-text text{
  color: #00AAFF;
  cursor: pointer;
}
.statement-text .check-box {
  transform: scale(.8);
}

</style>

<template>
  <view>
    <NInput name="user" v-model="emailForm.user" placeholder="请输入账号" />
    <NInput name="email" v-model="emailForm.email" placeholder="请输入邮箱" />
    <NInput name="verifyCode" v-model="emailForm.verifyCode" placeholder="请输入验证码">
      <template v-slot:addonAfter>
        <view class="addon-text addon-after">
          <text class="addon-text addon-after" @click="getCode" v-if="show">获取验证码</text>
          <text class="addon-text addon-after" v-if="!show">{{count}} s</text>
        </view>
      </template>
    </NInput>
    <NInput type="password" name="passowrd" v-model="emailForm.password" placeholder="密码由6-20位英文字母、数字组成" />
    <NInput type="password" name="confirm" v-model="emailForm.confirm" placeholder="请输入确认密码" />
    <NButton style="font-size: 28rpx" @tap="handleSubmitForm">提交修改</NButton>
  </view>
</template>

<script>
import http from '@/utils/http'
import utils from '@/utils/util'
import config from '@/utils/config'
import NInput from '@/components/form/n-input';
import NButton from '@/components/n-button';
import Dialog from '@/components/popup/dialog';

export default {
  name: 'email-form',
  data () {
    return {
      emailForm: {
        user: '',
        email: '',
        verifyCode: '',
        password: '',
        confirm: '',
        telexNumber: '',
        org: '',
      },
      show: true,
      count: '',
    }
  },
  components: {
    NInput,
    NButton,
    Dialog,
  },
  mounted() {
    const query = this.$Route.query
    this.emailForm.telexNumber = query?.telexNumber
    this.emailForm.org = query?.org
  },
  methods: {
    getCode() {
      const { user, email, telexNumber } = this.emailForm
      if (!user || !email) {
        uni.showToast({
          title: '请填写账号及邮箱',
          icon: 'none'
        })
        return
      }
      if (!utils.checkEmail(email)) {
        uni.showToast({
          title: '请填写正确的邮箱',
          icon: 'none'
        })
        return false
      }
      this.$umaTracker.ModularClick({
        Um_Key_ButtonName: '获取验证码',
        Um_Key_SourcePage: '忘记密码',
        Um_Key_SourceLocation: '忘记密码',
      })
      var params = {
        domain: config.wenetUrl,
        url: "/api/v1/email/verifyCode",
        method: "post",
        contentType: 'application/x-www-form-urlencoded',
        data: {
          user: `${telexNumber}${user}`,
          email,
        },
        callBack: res => {
          uni.showToast({
            title: '验证码发送成功',
            icon: 'none'
          })
          this.startCountDown()
        },
        errCallBack: err => {
          uni.showToast({
            title: '验证码发送失败',
            icon: 'none'
          })
        }
      };
      http.request(params);
    },
    startCountDown() {
      const timeCount = 60;
      if (!this.timer) {
        this.count = timeCount
        this.show = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= timeCount) {
            this.count--;
          } else {
            clearInterval(this.timer);
            this.timer = null
            this.show = true
          }
        }, 1000)
      }
    },
    checkForm() {
      const { user, email, verifyCode, password, confirm, } = this.emailForm
      if (!user || !email || !verifyCode || !password || !confirm) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return false
      }
      if (!utils.checkEmail(email)) {
        uni.showToast({
          title: '请填写正确的邮箱',
          icon: 'none'
        })
        return false
      }
      if(!utils.checkPassword(password)) {
        uni.showToast({
          title: '密码由6-20位英文字母、数字组成',
          icon: 'none'
        })
        return false
      }
      if (confirm !== password) {
        uni.showToast({
          title: '两次密码不一致',
          icon: 'none'
        })
        return false
      }
      return true
    },
    handleSubmitForm() {
      if (!this.checkForm()) {
        return
      }
      const { user, email, verifyCode, password, confirm, telexNumber } = this.emailForm
      var params = {
        domain: config.wenetUrl,
        url: "/api/v1/email/password",
        method: "post",
        contentType: 'application/x-www-form-urlencoded',
        data: {
          user: `${telexNumber}${user}`,
          email,
          verifyCode,
          password,
          confirm,
        },
        callBack: res => {
          uni.showToast({
            title: '找回密码成功',
            icon: 'none',
            duration: 1000,
            success: () => {
              setTimeout(() => {
                uni.redirectTo({
                  url: `/pages/login/login`,
                })
              }, 1000);
            }
          })
        },
        errCallBack: err => {
          const code = err.data.code
          let errMsg = '找回密码失败'
          
          if(code === 2003) {
            errMsg = '验证码错误'
          }
          uni.showToast({
            title: errMsg,
            icon: 'none'
          })
        }
      };
      http.request(params);
    },
  },
}
</script>

<style scoped>
.addon-text {
  color: var(--primary-color);
  font-size: 28rpx;
}
.addon-after {
  min-width: 160rpx;
  text-align: right;
}
</style>
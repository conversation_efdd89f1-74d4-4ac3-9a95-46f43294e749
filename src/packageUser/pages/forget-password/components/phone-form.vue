<template>
  <view>
    <NInput name="username" v-model="phoneForm.phone" placeholder="请输入手机号码" >
      <template v-slot:addonBefore>
        <view class="addon-text addon-before">
          +86
        </view>
      </template>
    </NInput>
    <NInput name="verifyCode" v-model="phoneForm.verifyCode" placeholder="请输入验证码">
      <template v-slot:addonAfter>
        <view class="addon-text addon-after">
          <text class="addon-text addon-after" @click="getCode" v-if="show">获取验证码</text>
          <text class="addon-text addon-after" v-if="!show">{{count}} s</text>
        </view>
      </template>
    </NInput>
    <NInput type="password" name="passowrd" v-model="phoneForm.password" placeholder="密码由6-20位英文字母、数字组成" />
    <NInput type="password" name="confirm" v-model="phoneForm.confirm" placeholder="请输入确认密码" />
    <NButton block  @click="handleSubmitForm">提交修改</NButton>
    
    <view class="contact-text">
      <contact-button type="normal">
        <text style="color: rgba(0, 0, 0, 0.65)">手机号不再使用? 前往联系客服</text>
      </contact-button>
    </view>
  </view>
</template>

<script>
import http from '@/utils/http'
import utils from '@/utils/util'
import config from '@/utils/config'
import NInput from '@/components/form/n-input';
import NButton from '@/components/n-button';
import Dialog from '@/components/popup/dialog';
import ContactButton from '@/components/contact-button'

export default {
  name: 'phone-form',
  data () {
    return {
      phoneForm: {
        type: 'RETRIEVE',
        phone: '',
        verifyCode: '',
        password: '',
        confirm: '',
      },
      show: true,
      count: '',
    }
  },
  components: {
    NInput,
    NButton,
    Dialog,
    ContactButton,
  },
  methods: {
    getCode() {
      if (!this.phoneForm.phone) {
        uni.showToast({
          title: '请填写手机号码',
          icon: 'none'
        })
        return
      }
      if (!utils.checkPhoneNumber(this.phoneForm.phone)) {
        uni.showToast({
          title: '请填写正确的手机号码',
          icon: 'none'
        })
        return
      }
      this.$umaTracker.ModularClick({
        Um_Key_ButtonName: '获取验证码',
        Um_Key_SourcePage: '忘记密码',
        Um_Key_SourceLocation: '忘记密码',
      })
      var params = {
        domain: config.wenetUrl,
        url: "/api/v1/verifyCode2",
        method: "post",
        contentType: 'application/x-www-form-urlencoded',
        data: this.phoneForm,
        callBack: res => {
          uni.showToast({
            title: '验证码发送成功',
            icon: 'none'
          })
          this.startCountDown()
        },
        errCallBack: err => {
          uni.showToast({
            title: '验证码发送失败',
            icon: 'none'
          })
        }
      };
      http.request(params);
    },
    startCountDown() {
      const timeCount = 60;
      if (!this.timer) {
        this.count = timeCount
        this.show = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= timeCount) {
            this.count--;
          } else {
            clearInterval(this.timer);
            this.timer = null
            this.show = true
          }
        }, 1000)
      }
    },
    checkForm() {
      const { phone, verifyCode, password, confirm} = this.phoneForm
      if (!phone || !verifyCode || !confirm || !password) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return false
      }
      if (!utils.checkPhoneNumber(phone)) {
        uni.showToast({
          title: '请填写正确的手机号码',
          icon: 'none'
        })
        return false
      }
      if(!utils.checkPassword(password)) {
        uni.showToast({
          title: '密码由6-20位英文字母、数字组成',
          icon: 'none'
        })
        return false
      }
      if (confirm !== password) {
        uni.showToast({
          title: '两次密码不一致',
          icon: 'none'
        })
        return false
      }
      return true
    },
    handleSubmitForm() {
      if (!this.checkForm()) {
        return
      }
      const { phone, verifyCode, password, confirm } = this.phoneForm
      var params = {
        domain: config.wenetUrl,
        url: "/api/v1/password",
        method: "post",
        contentType: 'application/x-www-form-urlencoded',
        data: {
          user: phone,
          verifyCode: verifyCode,
          password,
          confirm,
        },
        callBack: res => {
          uni.showToast({
            title: '找回密码成功',
            icon: 'none',
            duration: 1000,
            success: () => {
              setTimeout(() => {
                uni.redirectTo({
                  url: `/pages/login/login`,
                })
              }, 1000);
            }
          })
        },
        errCallBack: err => {
          const code = err.data.code
          let errMsg = '找回密码失败'
          
          if(code === 2003) {
            errMsg = '验证码错误'
          }
          uni.showToast({
            title: errMsg,
            icon: 'none'
          })
        }
      };
      http.request(params);
    },
  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped lang="scss">
.addon-text {
  color: $wenet-color-brand;
  font-size: 28rpx;
}
.addon-before {
  margin-right: 20rpx;
}
.addon-after {
  min-width: 160rpx;
  text-align: right;
}
.contact-text {
  margin-top: 64rpx;
  text-align: center;
}
</style>
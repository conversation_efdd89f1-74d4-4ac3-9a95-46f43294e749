<template>
  <view>
    <SchoolSelector v-model="selectedOrg" @change="handleSchoolSelectorChange" v-if="needSchoolSelector" />
    <NInput name="user" v-model="idNumberForm.user" placeholder="请输入账号" />
    <NInput name="identification" v-model="idNumberForm.identification" placeholder="请输入身份证号">
      <template v-slot:addonAfter>
        <view class="addon-text addon-after">
          <text class="addon-text addon-after" @click="getCode" v-if="show">验证</text>
          <text class="addon-text addon-after addon-after-success" v-if="!show">验证通过</text>
        </view>
      </template>
    </NInput>
    <NInput type="password" name="passowrd" v-model="idNumberForm.password" placeholder="密码由6-20位英文字母、数字组成" />
    <NInput type="password" name="confirm" v-model="idNumberForm.confirm" placeholder="请输入确认密码" />
    <NButton block @click="handleSubmitForm">提交修改</NButton>
  </view>
</template>

<script>
import http from '@/utils/http'
import utils from '@/utils/util'
import config from '@/utils/config'
import NInput from '@/components/form/n-input';
import NButton from '@/components/n-button';
import Dialog from '@/components/popup/dialog';
import SchoolSelector from '@/components/login-with-wenet/SchoolSelector'

export default {
  name: 'idNumber-form',
  data () {
    return {
      idNumberForm: {
        user: '',
        identification: '',
        password: '',
        confirm: '',
      },
      show: true,
      count: '',
      selectedOrg: {},
    }
  },
  props: {
    ou: {
      type: String,
      default: ''
    },
    prefix: {
      type: String,
      default: ''
    }
  },
  components: {
    NInput,
    NButton,
    Dialog,
    SchoolSelector
  },
  mounted() {
  },
  computed: {
    needSchoolSelector() {
      return !this.ou || !this.prefix
    }
  },
  watch: {
    'idNumberForm.identification': function(newVal, oldVal) {
      this.show = true;
    }
  },
  methods: {
    getCode() {
      const { user, identification } = this.idNumberForm
      if (!user || !identification) {
        uni.showToast({
          title: '请填写账号及身份证号',
          icon: 'none'
        })
        return
      }
      if(!utils.identityCodeValid(identification)) {
        uni.showToast({
          title: '请输入正确的身份证号',
          icon: 'none'
        })
        return
      }
      this.$umaTracker.ModularClick({
        Um_Key_ButtonName: '获取验证码',
        Um_Key_SourcePage: '忘记密码',
        Um_Key_SourceLocation: '忘记密码',
      })
      const prefix = this.needSchoolSelector ? this.selectedOrg.telexNumber : this.prefix
      var params = {
        domain: config.wenetUrl,
        url: "/api/v1/verify/identification",
        method: "post",
        contentType: 'application/x-www-form-urlencoded',
        data: {
          user: `${prefix}${user}`,
          identification,
        },
        callBack: res => {
          if(res === true) {
            this.show = false
            uni.showToast({
              title: '验证通过',
              icon: 'none'
            })
          }
          else {
            uni.showToast({
              title: '验证失败',
              icon: 'none'
            })
          }
        },
        errCallBack: err => {
          uni.showToast({
            title: '验证失败',
            icon: 'none'
          })
        }
      };
      http.request(params);
    },
    checkForm() {
      const { user, identification, password, confirm} = this.idNumberForm
      if (!user || !identification || !password || !confirm || (this.needSchoolSelector && !this.selectedOrg.ou)) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return false
      }
      if(this.show) {
        uni.showToast({
          title: '请先验证身份证号',
          icon: 'none'
        })
        return false
      }
      if(!utils.identityCodeValid(identification)) {
        uni.showToast({
          title: '请输入正确的身份证号',
          icon: 'none'
        })
        return false
      }
      if(!utils.checkPassword(password)) {
        uni.showToast({
          title: '密码由6-20位英文字母、数字组成',
          icon: 'none'
        })
        return false
      }
      if (confirm !== password) {
        uni.showToast({
          title: '两次密码不一致',
          icon: 'none'
        })
        return false
      }
      return true
    },
    handleSubmitForm() {
      if (!this.checkForm()) {
        return
      }
      const { user, identification, password, confirm } = this.idNumberForm
      const prefix = this.needSchoolSelector ? this.selectedOrg.telexNumber : this.prefix
      var params = {
        domain: config.wenetUrl,
        url: "/api/v1/identification/password",
        method: "post",
        contentType: 'application/x-www-form-urlencoded',
        data: {
          user: `${prefix}${user}`,
          identification,
          password,
          confirm,
        },
        callBack: res => {
          uni.showToast({
            title: '找回密码成功',
            icon: 'none',
            duration: 1000,
            success: () => {
              setTimeout(() => {
                uni.redirectTo({
                  url: `/pages/login/login`,
                })
              }, 1000);
            }
          })
        },
        errCallBack: err => {
          uni.showToast({
            title: '找回密码失败',
            icon: 'none'
          })
        }
      };
      http.request(params);
    },
  },
}
</script>

<style scoped lang="scss">
.addon-text {
  color: $wenet-color-brand;
  font-size: 28rpx;
}
.addon-after {
  min-width: 160rpx;
  text-align: right;
}
.addon-after-success {
  color: #0ab906;
}
</style>
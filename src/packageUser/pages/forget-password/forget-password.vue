<template>
  <view class="container">
    <view class="headline">
      <view class="headline-title">WeNet商城</view>
      <view class="headline-msg">网络产品一站服务</view>
    </view>
    <view class="password-container">
      <Tabs
        :tabs="tabs"
        :active-tab="activeForm"
        @change="changeTab"
      />
      <PhoneForm v-if="activeForm === 'PhoneForm'" :ou="ou" />
      <IdNumberForm v-if="activeForm === 'IdNumberForm'" :ou="ou" :prefix="prefix" />
    </view>
  </view>
</template>

<script>
import PhoneForm from './components/phone-form'
import IdNumberForm from './components/idNumber-form'
import Tabs from '@/components/tabs/tabs'

export default {
  data() {
    return {
      activeForm: 'PhoneForm',
      tabs: [
        { key: 'PhoneForm', label: '手机号找回' },
        { key: 'IdNumberForm', label: '身份证找回'},
      ],
      ou: '',
      prefix: ''
    };
  },
  components: {
    PhoneForm,
    IdNumberForm,
    Tabs,
  },
  props: {},
  mounted() {
  },
  onLoad(options) {
    this.ou = options.ou
    this.prefix = options.prefix
  },
  methods: {
    changeTab(key) {
      this.activeForm = key;
    },
  }
};
</script>
<style lang="scss" scoped>

page {
  position: relative;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background-color: $wenet-color-brand;
}

.headline {
  width: 100%;
  height: calc(100vh - 1228rpx);
  min-height: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.headline-title {
  color: #FEFEFE;
  font-size: 48rpx;
  font-weight: 600;
  line-height: 64rpx; 
  letter-spacing: 1rpx;
}
.headline-msg {
  color: #fff;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 44rpx;
}

.password-container {
  position: absolute;
  bottom: 0rpx;
  width: 100%;
  height: 1228rpx;
  max-height: 80%;
  box-sizing: border-box;
  padding: 64rpx 48rpx;
  border-radius: 60rpx 60rpx 0rpx 0rpx;
  background-color: #fff;
}

.password-container-title {
  text-align: center;
  padding-bottom: 64rpx;
  font-weight: 600;
  font-size: 36rpx;
}
</style>

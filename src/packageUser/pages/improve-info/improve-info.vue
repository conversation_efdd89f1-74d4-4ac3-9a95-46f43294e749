<template>
<view class="page">
  <scroll-view scroll-y="true">
    <view class="scroll-view-box">
      <page1 
        v-if="currentPage === 1" 
        :allInfoCondition="allInfoCondition"
        @switchPage="switchPage"
        @infoComplete="handleInfoComplete"
      />
      <page2 
        v-if="currentPage === 2" 
        :allInfoCondition="allInfoCondition"
        @switchPage="switchPage"
        @infoComplete="handleInfoComplete"
      />
    </view>
    <n-loading :show="loading"/>
  </scroll-view>
</view>
</template>

<script>
import page1 from './page1.vue'
import page2 from './page2.vue'
import config from '@/utils/config'
var http = require("@/utils/http.js");
import { routeAfterLogin } from '@/utils/util'
import store from '@/store'

export default {
  name: 'CompleteInfo',
  data () {
    return {
      orgExtendedInfo: {}, // 学校所需扩展信息
      ouAreaSheet: {}, // 当前学校的校区地址信息
      wenetAccountInfo: {}, // 用户的wenet账号信息
      wenetToken: '',
      currentPage: 0,
      allInfoCondition: {
        indentificationCondition: true,
        studentIdCondition: true,
        addressCondition: true,
        extendInfoCondition: true,
      },
      loading: false,
    }
  },
  components: {
    page1,
    page2,
  },
  onLoad() {
    const loginActionData = uni.getStorageSync('loginActionData')
    this.$umaTracker.ImproveInfo({
      Wenet_Key_Reason: loginActionData?.way === 'login' ? '登录' : '注册',
      Wenet_Key_LoginType: loginActionData?.type,
    })
  },
  onShow() {
    // #ifdef MP-WEIXIN
      uni.hideHomeButton()
		// #endif
    uni.setStorageSync('unImprovedInfo', true);
  },
  computed: {
  },
  mounted() {
    this.wenetToken = uni.getStorageSync('wenetToken')
    this.getWenetAccountInfo()
  },
  watch: {
    allInfoCondition: {
      handler: function(val, oldVal) {
        let page = 1
        // 判断第一页
        if(val.indentificationCondition && val.studentIdCondition) {
          page = 2
          // 判断第二页
          if (val.addressCondition && val.extendInfoCondition) {
            page = 1
          }
        }
        this.currentPage = page
      },
      deep: true
    },
  },  
  methods: {
    getWenetAccountInfo() {
      var params = {
        domain: config.wenetUrl,
        url: '/api/v1/account/me',
        method: "GET",
        data: {},
        dontTrunLogin: true,
        overWriteToken: this.wenetToken,
        callBack: (res) => {
          this.wenetAccountInfo = res
          this.$umaTracker.setUserBasicInfo({
            userId: res.person.uid, 
            schoolName: res.org.localityName
          })
          setTimeout(() => {
            this.getOrgExtendInfo()
          }, 0);
        },
      };
      http.request(params);
    },
    getOrgExtendInfo() {
      var params = {
        url: "/v1/projection/org/visible",
        domain: config.wenetUrl, 
        method: "GET",
        callBack: (res) => {
          const { 
            identificationRule, studentIdRule, addressRule, extendedFileds, 
          } = res?.find(item => item.ou === this.wenetAccountInfo.org.ou)

          this.orgExtendedInfo = { identificationRule, studentIdRule, addressRule, extendedFileds }

          setTimeout(() => {
            this.checkAllInfoFilled()
          }, 0)
        }
      };
      http.request(params);
    },
    checkAllInfoFilled() {
      const realname = this.wenetAccountInfo.identification?.realName
      const idNumber = this.wenetAccountInfo.identification?.idNumber
      const addressInfo = {
        campus: this.wenetAccountInfo.person?.initials,
        floor: this.wenetAccountInfo.person?.carLicense,
        dorm: this.wenetAccountInfo.person?.roomNumber,
      }
      const studentIdInfo = {
        username: this.wenetAccountInfo.users?.find(item => item?.type === 'STUDENT_ID_ASSOCIATED_USER')?.username,
      }
      const userExtendInfo = this.wenetAccountInfo.person?.extendInfoList || []
      const orgExtendFields = this.orgExtendedInfo.extendedFileds || []

      if(this.orgExtendedInfo.identificationRule !== 'NO_DISPLAY') {
        if(!realname || !idNumber) {
          this.allInfoCondition.indentificationCondition = false
        }
      }
      if(this.orgExtendedInfo.studentIdRule !== 'NO_DISPLAY') {
        if(!studentIdInfo.username) {
          this.allInfoCondition.studentIdCondition = false
        }
      }
      if(this.orgExtendedInfo.addressRule !== 'NO_DISPLAY') {
        if(!addressInfo.campus || !addressInfo.floor || !addressInfo.dorm) {
          this.allInfoCondition.addressCondition = false
        }
      }
      for (let i = 0; i < orgExtendFields.length; i++) {
        if (orgExtendFields[i].rule !== 'NO_DISPLAY') {
          const extendInfo = userExtendInfo.find(item => item.name === orgExtendFields[i].name)
          if (!extendInfo?.value) {
            this.allInfoCondition.extendInfoCondition = false
            break;
          }
        }
      }

      // 当用户已经登录并进入该页面，但bas又将必填/选填信息设为不显示时，需清除 unImprovedInfo，并跳转至上一页
      if(this.allInfoCondition.indentificationCondition && this.allInfoCondition.addressCondition && this.allInfoCondition.studentIdCondition && this.allInfoCondition.extendInfoCondition) {  
        this.loading = true
        uni.setStorageSync('unImprovedInfo', false);
        
        setTimeout(() => {
          this.loading = false
          routeAfterLogin(false)
        }, 500)
      }
    },
    switchPage(page) {
      this.currentPage = page
    },
    // 用户填写完所有的信息,已经可以登录了
    handleInfoComplete() {
      const loginActionData = uni.getStorageSync('loginActionData')
      const type = loginActionData?.type
      const way = loginActionData?.way
      const isImproveMobile = loginActionData?.Wenet_Key_IsImproveMobile || false
      if (way === 'login') {
        this.$umaTracker.LoginSuc({
          Wenet_Key_LoginType: type,
          Wenet_Key_IsImproveInfo: true,
          Wenet_Key_IsImproveMobile: isImproveMobile,
        })
      } else if (way === 'register') {
        this.$umaTracker.RegisterSuc({
          Um_Key_RegisterType: type,
          Wenet_Key_IsImproveInfo: true,
          Wenet_Key_IsImproveMobile: isImproveMobile,
        })
      }
      store.commit('refreshUserInfo', { forceRefresh: true })
      uni.showToast({
        title: '提交成功',
        icon: 'none',
        success: () => {
          uni.setStorageSync('unImprovedInfo', false);
          setTimeout(() => {
            routeAfterLogin(false)
          }, 500)
        }
      })
    }
  },
}
</script>

<style scoped>
.page {
  width: 100%;
  box-sizing: border-box;
  padding: 94rpx 48rpx;
  border-radius: 60rpx 60rpx 0rpx 0rpx;
  background-color: #fff;
}
.form-item{
  display: flex;
  box-sizing: border-box;
  width: 100%;
  height: 84rpx;
  align-items: center;
  margin-bottom: 48rpx;
}
.basic-btn {
  display: flex;
  width: 100%;
  padding: 32rpx 174rpx;
  justify-content: center;
  align-items: center;
  line-height: 1;
  border-radius: 170rpx;
}

.basic-btn::after {
  border: 0;
}
button[type=primary] {
  background-color: var(--primary-color);
}
.form-item text {
  position: relative;
  flex-shrink: 0;
  color: #5f5e5e;
  min-width: 128rpx;
}
.input-style {
  flex: 1;
  height: 84rpx;
  border: none;
  background: transparent;
  padding: 0rpx 32rpx;
  border-radius: 16rpx;
  background: #F5F5F5;
}
.input-style:focus {
  outline: none;
}
.input-style[disabled] {
  color: #999 !important;
}

.view-box {
  margin: 60rpx 0rpx; 
  padding-bottom: 60rpx;
  border-bottom: dashed 1px #bbbbbb;
}
.view-box:last-child {
  border-bottom: none;
}
.title {
  display: block;
  margin-bottom: 20rpx;
}
.title--required::before {
  content: '*';
  display: inline-block;
  position: absolute;
  z-index: 999;
  color: red;
  top: -12rpx;
  left: -16rpx;
}
.scroll-view-box {
  padding-left: 16rpx !important;
  box-sizing: border-box !important;
}
</style>
<template>
  <view class="page">
    <view class="scroll-view-box" v-if="!loading">
      <view class="view-box" v-show="orgExtendedInfo.addressRule !== 'NO_DISPLAY'">
        <b class="title">地址信息：</b>
        <view class="form-item">
          <text :class="{'title--required': orgExtendedInfo.addressRule === 'REQUIRE'}">校区：</text>
          <UniDataSelect 
            class="input-style" 
            :localdata="ouAreaSheetFirstList" 
            :disabled="isDisabled('initials', 'person')"
            :format="isDisabled('initials', 'person')? '***' : '{text}'"
            v-model="addressInfo.campus"
            placeholder="请选择校区"
          />
        </view>
        <view class="form-item">
          <text :class="{'title--required': orgExtendedInfo.addressRule === 'REQUIRE'}">宿舍楼：</text>
          <UniDataSelect 
            class="input-style" 
            :disabled="isDisabled('carLicense', 'person')"
            :format="isDisabled('carLicense', 'person') ? '***' : '{text}'"
            :localdata="ouAreaSheetSecondList"
            v-model="addressInfo.floor"
            placeholder="请选择宿舍楼"
            emptyTips="请先选择校区"
          />
        </view>
        <view class="form-item">
          <text :class="{'title--required': orgExtendedInfo.addressRule === 'REQUIRE'}">宿舍号：</text>
          <input 
            class="input-style" 
            placeholder="请输入宿舍号"
            v-model="addressInfo.dorm" 
            :disabled="isDisabled('roomNumber', 'person')" 
            :type="isDisabled('roomNumber', 'person') ? 'password' : 'text'"
          />
        </view>
      </view>

      <view class="view-box" v-show="isShowExtendedFileds">
        <b class="title">补充信息：</b>
        <view v-for="(item) in orgExtendedInfo.extendedFileds" :key="item.id">
          <view class="form-item" v-if="item.type === 'SELECT' && item.rule !== 'NO_DISPLAY'">
            <text :class="{'title--required': item.rule === 'REQUIRE'}">{{item.name}}：</text>
            <UniDataSelect 
              class="input-style" 
              :clear="true"
              v-model="extendInfo[item.name]"
              :localdata="getVaildOptions(item.selectValue)" 
              :placeholder="`请选择${item.name}`"
              :disabled="isDisabled(item.name)"
              :format="isDisabled(item.name) ? '***' : '{text}'"
            />
          </view>
          <view class="form-item" v-if="item.type === 'TEXT' && item.rule !== 'NO_DISPLAY'">
            <text :class="{'title--required': item.rule === 'REQUIRE'}">{{item.name}}：</text>
            <input 
              class="input-style" 
              v-model="extendInfo[item.name]" 
              :placeholder="`请输入${item.name}`" 
              :disabled="isDisabled(item.name)"
              :type="isDisabled(item.name) ? 'password' : 'text'"
            />
          </view>
        </view>
      </view>

      <NButton block type="primary" @click="submitForm" >提交</NButton>
    </view>
    <n-loading :show="loading" />
  </view>
  </template>
  
  <script>
  import UniDataSelect from '@/components/uni-data-select/uni-data-select';
  import { getAreaSheetByOuName, clearNoValueParams } from '@/utils/util'
  import config from '@/utils/config'
  var http = require("@/utils/http.js");
  import callWenetApi from '@/utils/call-wenet-api'
  import NButton from '@/components/n-button'
  
  export default {
    name: 'CompleteInfo',
    data () {
      return {
        orgExtendedInfo: {}, // 学校所需扩展信息
        ouAreaSheet: {}, // 当前学校的校区地址信息
        wenetAccountInfo: {}, // 用户的wenet账号信息
        addressInfo: {
          campus: '', // 校区
          floor: '',  // 宿舍楼
          dorm: '', // 宿舍号
        },
        extendInfo: {
        },
        wenetToken: '',
        loading: true,
      }
    },
    components: {
      UniDataSelect,
      NButton,
    },
    computed: {
      ouAreaSheetFirstList() {
        return this.ouAreaSheet?.firstList?.map(item => ({ text: item, value: item}) )
      },
      ouAreaSheetSecondList() {
        const currentCampus = this.addressInfo.campus
        return this.ouAreaSheet?.secondList?.[currentCampus]?.map(item => ({ text: item, value: item}) )
      },
      isShowExtendedFileds() {
        return this.orgExtendedInfo?.extendedFileds?.filter(item => item.rule !== 'NO_DISPLAY')?.length > 0
      },
    },
    mounted() {
      this.wenetToken = uni.getStorageSync('wenetToken')
      this.init()
    },
    methods: {
      startLoading() {
        this.loading = true;
      },
      stopLoading() {
        this.loading = false;
      },
      async init() {
        try {
          const wenetAccountInfo = await this.getWenetAccountInfo()
          await this.getOrgExtendInfo(wenetAccountInfo)
          await this.getOrgAreaSheet(wenetAccountInfo)
          this.backfillInfo(wenetAccountInfo)
        } catch(err){
          console.log(err)
        } finally {
          this.stopLoading()
        }
      },
      getWenetAccountInfo() {
        return new Promise((resolve, reject) => {
          var params = {
            domain: config.wenetUrl,
            url: '/api/v1/account/me',
            method: "GET",
            data: {},
            dontTrunLogin: true,
            overWriteToken: this.wenetToken,
            callBack: (res) => {
              this.wenetAccountInfo = res
              resolve(res)
            },
          };
          http.request(params);
        })
      },
      getOrgExtendInfo(wenetAccountInfo) {
        return new Promise((resolve, reject) => {
          var params = {
            url: "/v1/projection/org/visible",
            domain: config.wenetUrl, 
            method: "GET",
            callBack: (res) => {
              const { 
                identificationRule, studentIdRule, addressRule, extendedFileds, 
              } = res?.find(item => item.ou === wenetAccountInfo?.org?.ou)
    
              this.orgExtendedInfo = { identificationRule, studentIdRule, addressRule, extendedFileds }
              resolve()
            }
          };
          http.request(params);
        })
      },
      getOrgAreaSheet(wenetAccountInfo) {
        return new Promise((resolve, reject) => {
          var params = {
            url: "/s3-configuration/building.json",
            domain: config.wenetApiUrl,
            method: "GET",
            callBack: (res) => {
              const ouAreaSheet = getAreaSheetByOuName(res, wenetAccountInfo?.org?.localityName)
              this.ouAreaSheet = ouAreaSheet
              resolve()
            }
          };
          http.request(params);
        })
      },
      backfillInfo(wenetAccountInfo = {}) {
        const { person } = wenetAccountInfo
  
        this.addressInfo = {
          campus: person?.initials,
          floor: person?.carLicense,
          dorm: person?.roomNumber,
        }
        person?.extendInfoList?.forEach(item => {
          this.extendInfo[item.name] = item.value
        })
      },
      getVaildOptions(stringArr) {
        return stringArr.map(item => ({ text: item, value: item}) )
      },
      isDisabled(name, parentName) {
        // 基本信息只有一级父元素，可以根据父元素及其变量名称取值
        if(parentName) {
          return this.wenetAccountInfo?.[parentName]?.[name] ? true : false
        }
        // 扩展字段有两级父元素，需要特殊处理
        return this.wenetAccountInfo.person?.extendInfoList?.find(item => item.name === name && !!item.value) ? true : false
      },
      isFillAddressForm() {
        return new Promise((resolve, reject) => {
          if(this.orgExtendedInfo.addressRule === 'REQUIRE') {
            if(!this.addressInfo.campus || !this.addressInfo.floor || !this.addressInfo.dorm) {
              reject('请填写完整的地址信息')
            }
          }
          resolve()
        })
      },
      isFillExtendForm() {
        return new Promise((resolve, reject) => {
          this.orgExtendedInfo.extendedFileds?.map((item) => {
            if(item.rule === 'REQUIRE' && !this.extendInfo[item.name]) {
              reject(`请填写${item.name}`)
            }
          })
          resolve()
        })
      },
      async submitForm() {
        this.$umaTracker.ModularClick({
          Um_Key_ButtonName: '提交',
          Um_Key_SourcePage: '学校补充信息页',
          Um_Key_SourceLocation: '补充信息',
          Um_Key_UserLevel: this.wenetAccountInfo.org.localityName,
        })
        try {
          await this.isFillAddressForm()
          await this.isFillExtendForm()
        } catch (err) {
          uni.showToast({
            title: err,
            icon: 'none',
          })
          return
        }
        const { campus, floor, dorm } = this.addressInfo
        const school = this.wenetAccountInfo.org.localityName
        const params = {
          address: {
            school,
            dorm,
            campus,
            floor,
          },
          personExtendInfos: {
            extendInfoList: Object.keys(clearNoValueParams(this.extendInfo)).map(key => ({ name: key, value: this.extendInfo[key] })),
          }
        }
        callWenetApi({
          url: `/v2/account/update/info/step/2`,
          method: "POST",
          data: clearNoValueParams(params),
        }).then(() => {
          this.submitSuccess()
        })
      },
      submitSuccess() {
        this.$emit('infoComplete')
      },
  
    },
  }
  </script>
  
  <style scoped lang="scss">
  .page {
    width: 100%;
    box-sizing: border-box;
    border-radius: 60rpx 60rpx 0rpx 0rpx;
    background-color: #fff;
  }
  .form-item{
    display: flex;
    box-sizing: border-box;
    width: 100%;
    height: 84rpx;
    align-items: center;
    margin-bottom: 48rpx;
  }
  .form-item text {
    position: relative;
    flex-shrink: 0;
    color: #5f5e5e;
    min-width: 128rpx;
  }
  .input-style {
    flex: 1;
    height: 84rpx;
    border: none;
    background: transparent;
    padding: 0rpx 32rpx;
    border-radius: 16rpx;
    background: #F5F5F5;
  }
  .input-style:focus {
    outline: none;
  }
  .input-style[disabled] {
    color: #999 !important;
  }
  
  .view-box {
    margin: 60rpx 0rpx; 
    padding-bottom: 60rpx;
    border-bottom: dashed 1px #bbbbbb;
  }
  .view-box:last-child {
    border-bottom: none;
  }
  .title {
    display: block;
    margin-bottom: 20rpx;
  }
  .title--required::before {
    content: '*';
    display: inline-block;
    position: absolute;
    z-index: 999;
    color: $wenet-color-error;
    top: -12rpx;
    left: -16rpx;
  }
  .scroll-view-box {
    padding-left: 16rpx !important;
    box-sizing: border-box !important;
  }
  </style>
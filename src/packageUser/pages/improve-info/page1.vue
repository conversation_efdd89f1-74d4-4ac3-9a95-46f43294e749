<template>
  <view class="page">
    <view class="scroll-view-box" v-if="!loading">
      <!-- 实名制信息部分 -->
      <view class="view-box" v-show="orgExtendedInfo.identificationRule !== 'NO_DISPLAY' && currentStep === 'identification'">
        <b class="title">实名制信息：</b>
        <view class="form-item">
          <text :class="{'title--required': orgExtendedInfo.identificationRule === 'REQUIRE'}">姓名：</text>
          <input 
            class="input-style" 
            placeholder="请输入真实姓名" 
            v-model="identificationInfo.realName" 
            :disabled="isDisabled('realName', 'identification')" 
            :type="isDisabled('realName', 'identification') ? 'password' : 'text'" 
          />
        </view>
        <view class="form-item">
          <text :class="{'title--required': orgExtendedInfo.identificationRule === 'REQUIRE'}">身份证号：</text>
          <input 
            class="input-style" 
            placeholder="请输入身份证号" 
            v-model="identificationInfo.idNumber" 
            :disabled="isDisabled('idNumber', 'identification')" 
            :type="isDisabled('idNumber', 'identification') ? 'password' : 'text'" 
          />
        </view>
        <NButton block type="primary" @click="submitIdentificationForm">提交</NButton>
      </view>

      <!-- 学号信息部分 -->
      <view class="view-box" v-show="orgExtendedInfo.studentIdRule !== 'NO_DISPLAY' && currentStep === 'studentId'">
        <!-- 如果已完成实名信息提交，显示提示 -->
        <view class="info-tip" v-if="hasIdentification && orgExtendedInfo.identificationRule !== 'NO_DISPLAY'">
          已完成实名信息提交
        </view>
        <b class="title">学号信息：</b>
        <view class="form-item">
          <text :class="{'title--required': orgExtendedInfo.studentIdRule === 'REQUIRE'}">学号：</text>
          <input 
            class="input-style" 
            placeholder="请输入学号" 
            v-model="studentIdInfo.username" 
            :disabled="isStudentIdDisabled" 
            :type="isStudentIdDisabled ? 'password' : 'text'" 
          />
        </view>
        <NButton block type="primary" v-if="isToPage2" @click="submitStudentIdForm">下一步</NButton>
        <NButton block type="primary" v-else @click="submitStudentIdForm">提交</NButton>
      </view>
    </view>
    <n-loading :show="loading" />
  </view>
  </template>
  
  <script>
  import UniDataSelect from '@/components/uni-data-select/uni-data-select';
  import { identityCodeValid, clearNoValueParams } from '@/utils/util'
  import config from '@/utils/config'
  var http = require("@/utils/http.js");
  import callWenetApi from '@/utils/call-wenet-api'
  import NButton from '@/components/n-button'

  
  export default {
    name: 'CompleteInfo',
    data () {
      return {
        orgExtendedInfo: {}, // 学校所需扩展信息
        ouAreaSheet: {}, // 当前学校的校区地址信息
        wenetAccountInfo: {}, // 用户的wenet账号信息
        identificationInfo: {
          realName: '', // 真实姓名
          idNumber: '', // 身份证号
        },
        studentIdInfo: {
          username: '', // 学号，带前缀
        },
        wenetToken: '',
        loading: true,
        currentStep: 'identification', // 当前步骤：'identification' 或 'studentId'
        hasIdentification: false, // 是否已填写实名信息
        hasStudentId: false, // 是否已填写学号信息
      }
    },
    props: {
      allInfoCondition: {
        type: Object,
        default: {},
      },
    },
    components: {
      UniDataSelect,
      NButton,
    },
    computed: {
      isToPage2() {
        return this.allInfoCondition.addressCondition && this.allInfoCondition.extendInfoCondition ? false : true
      },
      isStudentIdDisabled() {
        return this.getUsername(this.wenetAccountInfo)
      },
    },
    mounted() {
      this.wenetToken = uni.getStorageSync('wenetToken')
      this.init()
    },
    methods: {
      startLoading() {
        this.loading = true;
      },
      stopLoading() {
        this.loading = false;
      },
      getUsername(wenetAccountInfo) {
        return wenetAccountInfo?.users?.find(item => item?.type === 'STUDENT_ID_ASSOCIATED_USER')?.username
      },
      async init() {
        try {
          const wenetAccountInfo = await this.getWenetAccountInfo()
          await this.getOrgExtendedInfo(wenetAccountInfo)
          this.backfillInfo(wenetAccountInfo)
          this.determineInitialStep()
        } finally {
          this.stopLoading()
        }
      },
      
      // 确定初始步骤
      determineInitialStep() {
        const hasIdentification = !!(this.wenetAccountInfo?.identification?.idNumber && this.wenetAccountInfo?.identification?.realName)
        const hasStudentId = !!this.getUsername(this.wenetAccountInfo)
        
        this.hasIdentification = hasIdentification
        this.hasStudentId = hasStudentId
        
        // 如果实名信息已填写但学号未填写，则显示学号表单
        if (hasIdentification && !hasStudentId && this.orgExtendedInfo.studentIdRule !== 'NO_DISPLAY') {
          this.currentStep = 'studentId'
        } 
        // 如果学号已填写但实名信息未填写，则显示实名信息表单
        else if (!hasIdentification && hasStudentId && this.orgExtendedInfo.identificationRule !== 'NO_DISPLAY') {
          this.currentStep = 'identification'
        }
        // 默认显示实名信息表单
        else {
          this.currentStep = 'identification'
        }
      },
      getWenetAccountInfo() {
        this.startLoading()
        return new Promise((resolve, reject) => {
          var params = {
            domain: config.wenetUrl,
            url: '/api/v1/account/me',
            method: "GET",
            data: {},
            dontTrunLogin: true,
            overWriteToken: this.wenetToken,
            callBack: (res) => {
              this.wenetAccountInfo = res
              resolve(res)
            },
          };
          http.request(params);
        })
      
      },
      getOrgExtendedInfo(wenetAccountInfo) {
        return new Promise((resolve, reject) => {
          var params = {
            url: "/v1/projection/org/visible",
            domain: config.wenetUrl, 
            method: "GET",
            callBack: (res) => {
              const { 
                identificationRule, studentIdRule
              } = res?.find(item => item.ou === wenetAccountInfo?.org?.ou)
    
              this.orgExtendedInfo = { identificationRule, studentIdRule }
              resolve(res)
            }
          };
          http.request(params);
        })
      },
      backfillInfo(wenetAccountInfo = {}) {
        const { identification } = wenetAccountInfo
  
        this.identificationInfo = {
          realName: identification?.realName,
          idNumber: identification?.idNumber,
        }
        this.studentIdInfo.username = this.getUsername(wenetAccountInfo)
      },
      getVaildOptions(stringArr) {
        return stringArr.map(item => ({ text: item, value: item}) )
      },
      isDisabled(name, parentName) {
        // 基本信息只有一级父元素，可以根据父元素及其变量名称取值
        if(parentName) {
          return this.wenetAccountInfo?.[parentName]?.[name] ? true : false
        }
        // 扩展字段有两级父元素，需要特殊处理
        return this.wenetAccountInfo.person?.extendInfoList?.find(item => item.name === name && !!item.value) ? true : false
      },
      isFillIdentificationForm() {
        return new Promise((resolve, reject) => {
          if(this.orgExtendedInfo.identificationRule === 'REQUIRE') {
            if(!this.identificationInfo.realName || !this.identificationInfo.idNumber) {
              reject('请填写完整的实名制信息')
            }
            else if(!identityCodeValid(this.identificationInfo.idNumber)) {
              reject('身份证号格式错误')
            }
          }
          if(this.orgExtendedInfo.identificationRule === 'NO_REQUIRE') {
            if(this.identificationInfo.realName && this.identificationInfo.idNumber) {
              if(!identityCodeValid(this.identificationInfo.idNumber)) {
                reject('身份证号格式错误')
              }
            }
            else if((!this.identificationInfo.realName && this.identificationInfo.idNumber) || 
              (this.identificationInfo.realName && !this.identificationInfo.idNumber)){
              reject('姓名与身份证号只能一起提交或均不提交')
            }
          }
          resolve()
        })
      },
      isFillStudentIdForm() {
        return new Promise((resolve, reject) => {
          if(this.orgExtendedInfo.studentIdRule === 'REQUIRE') {
            if(!this.studentIdInfo.username) {
              reject('请填写完整的学号信息')
            }
          }
          resolve()
        })
      },
      // 提交实名信息表单
      async submitIdentificationForm() {
        this.$umaTracker.ModularClick({
          Um_Key_ButtonName: '提交',
          Um_Key_SourcePage: '学校补充信息页',
          Um_Key_SourceLocation: '实名信息',
          Um_Key_UserLevel: this.wenetAccountInfo.org.localityName,
        })
        
        try {
          await this.isFillIdentificationForm()
        } catch (err) {
          uni.showToast({
            title: err,
            icon: 'none',
          })
          return
        }
        
        let params = {}
        
        if (this.orgExtendedInfo.identificationRule !== 'NO_DISPLAY') {
          if(!(this.wenetAccountInfo?.identification?.idNumber && this.wenetAccountInfo?.identification?.realName)) {
            params.idNumber = this.identificationInfo.idNumber
            params.realName = this.identificationInfo.realName
          }
        }
        
        if (Object.keys(params).length === 0) {
          // 如果没有需要提交的参数，直接进入下一步
          this.afterIdentificationSubmitted()
          return
        }
        
        this.startLoading()
        callWenetApi({
          url: `/v2/account/update/info/step/1`,
          method: "POST",
          data: clearNoValueParams(params),
        }).then(() => {
          this.stopLoading()
          uni.showToast({
            title: '实名信息提交成功',
            icon: 'none',
          })
          this.afterIdentificationSubmitted()
        }).catch(() => {
          this.stopLoading()
        })
      },
      
      // 实名信息提交后的处理
      afterIdentificationSubmitted() {
        this.hasIdentification = true
        
        // 如果需要填写学号且尚未填写，则切换到学号填写步骤
        if (this.orgExtendedInfo.studentIdRule !== 'NO_DISPLAY' && !this.hasStudentId) {
          this.currentStep = 'studentId'
        } else {
          // 否则按原有逻辑处理
          this.proceedToNextStage()
        }
      },
      
      // 提交学号表单
      async submitStudentIdForm() {
        this.$umaTracker.ModularClick({
          Um_Key_ButtonName: '提交',
          Um_Key_SourcePage: '学校补充信息页',
          Um_Key_SourceLocation: '学号信息',
          Um_Key_UserLevel: this.wenetAccountInfo.org.localityName,
        })
        
        try {
          await this.isFillStudentIdForm()
        } catch (err) {
          uni.showToast({
            title: err,
            icon: 'none',
          })
          return
        }
        
        let params = {}
        
        if (this.orgExtendedInfo.studentIdRule !== 'NO_DISPLAY' && !this.getUsername(this.wenetAccountInfo)) {
          params.studentId = this.studentIdInfo.username
        }
        
        if (Object.keys(params).length === 0) {
          // 如果没有需要提交的参数，直接进入下一步
          this.afterStudentIdSubmitted()
          return
        }
        
        this.startLoading()
        callWenetApi({
          url: `/v2/account/update/info/step/1`,
          method: "POST",
          data: clearNoValueParams(params),
        }).then(() => {
          this.stopLoading()
          uni.showToast({
            title: '学号信息提交成功',
            icon: 'none',
          })
          this.afterStudentIdSubmitted()
        }).catch(() => {
          this.stopLoading()
        })
      },
      
      // 学号信息提交后的处理
      afterStudentIdSubmitted() {
        this.hasStudentId = true
        
        // 如果需要填写实名信息且尚未填写，则切换到实名信息填写步骤
        if (this.orgExtendedInfo.identificationRule !== 'NO_DISPLAY' && !this.hasIdentification) {
          this.currentStep = 'identification'
        } else {
          // 否则按原有逻辑处理
          this.proceedToNextStage()
        }
      },
      
      // 进入下一阶段（page2或完成）
      proceedToNextStage() {
        if (this.isToPage2) {
          this.$emit('switchPage', 2)
        } else {
          this.$emit('infoComplete')
        }
      },
      
      // 原有的submitForm方法保留，但不再使用
      async submitForm() {
        // 保留原方法，但不再使用
      },
      submitSuccess() {
        if(this.isToPage2) {
          this.$emit('switchPage', 2)
        } else {
          this.$emit('infoComplete')
        }
      },
  
    },
  }
  </script>
  
  <style scoped lang="scss">
  .page {
    width: 100%;
    box-sizing: border-box;
    border-radius: 60rpx 60rpx 0rpx 0rpx;
    background-color: #fff;
  }
  .form-item{
    display: flex;
    box-sizing: border-box;
    width: 100%;
    height: 84rpx;
    align-items: center;
    margin-bottom: 48rpx;
  }
  .form-item text {
    position: relative;
    flex-shrink: 0;
    color: #5f5e5e;
    min-width: 128rpx;
  }
  .input-style {
    flex: 1;
    height: 84rpx;
    border: none;
    background: transparent;
    padding: 0rpx 32rpx;
    border-radius: 16rpx;
    background: #F5F5F5;
  }
  .input-style:focus {
    outline: none;
  }
  .input-style[disabled] {
    color: #999 !important;
  }
  
  .view-box {
    margin: 60rpx 0rpx; 
    padding-bottom: 60rpx;
    border-bottom: dashed 1px #bbbbbb;
  }
  .view-box:last-child {
    border-bottom: none;
  }
  .title {
    display: block;
    margin-bottom: 20rpx;
  }
  .title--required::before {
    content: '*';
    display: inline-block;
    position: absolute;
    z-index: 999;
    color: $wenet-color-error;
    top: -12rpx;
    left: -16rpx;
  }
  .scroll-view-box {
    padding-left: 16rpx !important;
    box-sizing: border-box !important;
  }
  .info-tip {
    font-size: 28rpx;
    color: $wenet-color-success;
    margin-bottom: 20rpx;
    padding: 16rpx 0;
    background-color: #f0f9eb;
    border-radius: 8rpx;
    text-align: center;
  }
  </style>
<template>
	<view class="terms-container">
		<!-- 服务条款/隐私协议 -->
		<!-- <view v-html="serviceTerms">
		</view> -->
		 <rich-text :nodes="content"></rich-text>
	</view>
</template>

<script>
	var http = require("@/utils/http.js");
	var util = require("@/utils/util.js");

  const stsMap = {
    'serviceTerms': '服务条款',
    'servicePolicy': '隐私策略'
  }
	export default {
		data() {
			return {
				content:''
			}
		},
		onLoad:function(options){
			this.ServiceTermsConfig(options.sts)
      uni.setNavigationBarTitle({
        title: stsMap[options.sts] || '服务条款'
      });
		},
		computed:{
			i18n() {
				return this.$t('index')
			}
		},
		methods: {
			/*
			key = 'service-terms'代表服务条款，key = 'service-policy'代表隐私策略
			*/
			ServiceTermsConfig(key) {
				let data;
				let params = {
					url: '/sys/config/info/' + key,
					method: "GET",
					callBack:res =>{
						this.setData({
							content: util.formatHtml(res.content)
						})
					}
				}
				http.request(params)
			}
		}
	}
</script>

<style>
@import url("./termsPage.css");
</style>

<!-- 个人资料 -->
<!-- 个人资料 -->
<!-- images/personalInformation/personalInformation.wxml -->
<template>
  <view class="personal-information">
    <!-- 头像 -->
    <view class="head-portrait clearfix">
      <view class="head-txt">{{i18n.avatar}}</view>
      <view class="station-photo-con">
        <view class="add-img" @tap="getUploadImg">
          <image class="add-img-icon" :src="photoFiles?photoFiles:( pic? pic : `${staticPicDomain}images/icon/head04.png`)" mode="widthFix"></image>
        </view>
      </view>
    </view>
    <view class="persone-info">
			<!-- 用户名 -->
			<!-- <view v-if="username" class="msg-item user-name">
			  <view class="msg-item-txt">{{i18n.userName}}</view>
			  <view class="right-arrow">
			    <input type="text" class="choose-value" :value="username" disabled @click="handleUserNameClick">
			  </view>
			</view> -->
      <!-- 昵称 -->
      <view class="msg-item nickname">
        <view class="msg-item-txt">{{i18n.nickname}}</view>
        <view class="right-arrow">
          <input type="text" class="choose-value" :value="nickName" @input="getNickNameInt" maxlength="50">
          <!-- <view class="choose-value">
            {{nickName}}
          </view> -->
          <!-- <image :src="`${staticPicDomain}images/icon/more.png`" class="right-arrow-img"></image> -->
        </view>
      </view>
      <view class="msg-item nickname" v-if="!!basUsername">
        <view class="msg-item-txt">上网账号</view>
        <view class="right-arrow">
          <input type="text" class="choose-value" disabled :value="basUsername" maxlength="50">
        </view>
      </view>
      <view class="msg-item nickname" v-if="!!ouName">
        <view class="msg-item-txt">学校</view>
        <view class="right-arrow">
          <input type="text" class="choose-value" disabled :value="ouName" maxlength="50">
        </view>
      </view>
      <view class="section" @tap="translate">
				<text>{{i18n.gender}}</text>
				<view class="pca">{{genderArray[sex]}}</view>
				<template v-if="showPicker">
					<view class="animation-element-wrapper" :animation="animation" :style="'visibility:' + (show ? 'visible':'hidden')"
					 @tap.stop="hiddenFloatView">
						<view class="animation-element" @tap.stop="nono">
							<text class="right-bt" @tap.stop="hiddenFloatView">{{i18n.confirm}}</text>
							<view class="line"></view>
							<picker-view indicator-style="height: 50px;" @change="bindChange" @tap.stop="nono">
								<picker-view-column>
									<view v-for="(item, index) in genderArray" :key="index">{{item}}</view>
								</picker-view-column>
							</picker-view>
						</view>
					</view>
				</template>
				<view class="arrow">
					<image :src="`${staticPicDomain}images/icon/more.png`"></image>
				</view>
			</view>
      <!-- <view class="section" @tap="toUpdatePhone">
				<text>手机号</text>
				<view class="pca">{{phoneNumber}}</view>
				<view class="arrow">
					<image :src="`${staticPicDomain}images/icon/more.png`"></image>
				</view>
			</view> -->

    </view>
    <view class="three-box">
			<view class="three-item" @tap="toTermsOfService('servicePolicy')">
				<text class="text">隐私策略</text>
				<image class="arrow" :src="`${staticPicDomain}images/icon/set-arrow.png`"/>
			</view>
			<view class="three-item" @tap="toTermsOfService('serviceTerms')">
				<text class="text">服务条款</text>
				<image class="arrow" :src="`${staticPicDomain}images/icon/set-arrow.png`"/>
			</view>
			<view class="three-item" @tap="toTermsOfService('profitPolicy')">
				<text class="text">分销协议</text>
				<image class="arrow" :src="`${staticPicDomain}images/icon/set-arrow.png`"/>
			</view>
		</view>

    <view class="three-box">
      <view class="three-item" @tap="logout">
				<text class="text">退出登录</text>
				<image class="arrow" :src="`${staticPicDomain}images/icon/set-arrow.png`"/>
			</view>
    </view>

    <view class="save-btn">
      <NButton v-if="isChange" type="primary" block @click="setUserInfo">保存</NButton>
    </view>
  </view>
</template>

<script>
//index.js
//获取应用实例
var http = require("../../../utils/http.js");
var config = require("../../../utils/config.js");
var util = require("../../../utils/util.js");
import { AppType } from '@/utils/constant';
const app = getApp().globalData;
import NButton from '@/components/n-button'

var t = 0;
var show = false;
var moveY = 200;

export default {
  data() {
    return {
      photoFiles:'',
			// genderArray: ['男', '女'],
      // genderArray: [this.i18n.female, this.i18n.male],
			// 不能直接这样的，要读取后保存进来
			genderArray: [],
      genderIndex: 0,
      date: '2020-01-01', //用户生日
      dateIndex: 0,

      // birthDate: '',   //用户生日
      nickName: '',  //用户昵称
      score: 0,  //积分
      sex: 0, //性别
      oldSex:'', //原先性别
      sexNumber:0, // 判断有无滑动
      phoneNumber: '',  //手机号
      countryCode: '',  //区号
      pic:'',
      isChange: false, // 是否修改用户信息
      animation: "",
      show: "",
      showPicker: false,
			username: '',
      basUsername: '',
      ouName: '',
    };
  },

  components: {
    NButton
  },
  props: {},

  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },

  onLoad: function () {
    //加载会员信息
  this.genderArray.push(this.i18n.male)
	this.genderArray.push(this.i18n.female)
    this.queryUserInfo()
  },
  onShow: function () {
	  //头部导航标题
	  uni.setNavigationBarTitle({
	      title:this.i18n.personalData
	  });
  },
  onHide: function() {
    this.showPicker = false
  },
  onReady: function() {
    this.animation = wx.createAnimation({
      transformOrigin: "50% 50%",
      duration: 0,
      timingFunction: "ease",
      delay: 0
    });
    this.animation.translateY(200 + 'vh').step();
    this.setData({
      animation: this.animation.export(),
      show: show
    });
  },
  methods: {
    toUpdatePhone() {
      uni.navigateTo({
        url: "/packageUser/pages/update-phone/update-phone"
      })
    },
		/**
		 * 用户名点击事件
		 */
		handleUserNameClick() {
			uni.showToast({
				title: this.i18n.userNameCannotChange,
				icon: 'none'
			})
		},
    /**
     * 上传图片
     */
    getUploadImg: function (e) {
		// uni.chooseImage(  uni.chooseFile
      uni.chooseImage({
        count: 1, // 默认9
        sizeType: ['compressed'],
        sourceType: ['album'],
        success: (res) => {
          var tempFilePaths = res.tempFilePaths;
          uni.showLoading({
            // #ifndef MP-TOUTIAO
            mask: true
            // #endif
          })
          var params = {
            url: "/p/file/upload",
            filePath: tempFilePaths[0],
            name: 'file',
            callBack: (res2) => {
              uni.hideLoading();
              this.photoFiles = JSON.parse(res2).resourcesUrl + JSON.parse(res2).filePath,
              this._photoFiles = JSON.parse(res2).filePath
							this.isChange = true
            },
          };
          http.upload(params);
        },
				fail: (failMsg) => {
					console.log(failMsg)
				}
      })
    },


    // 获取用户信息
    queryUserInfo : function() {
      uni.showLoading();
      var params = {
        url: "/p/user/userInfo",
        method: "GET",
        data: {},
        callBack: (res) => {
          uni.hideLoading();
          // var date = this.data.date
          // var phoneNumber = this.data.phoneNumber
          this.date =  res.birthDate,  //用户生日
          this.nickName =  res.nickName,  //用户昵称
          this.score =  res.score,  //积分
          this.sex =  Number(res.sex), //性别
          this.oldSex = Number(res.sex), //原本的性别
          this.phoneNumber =  res.userMobile, //用户手机号
          this.pic = res.pic
					this.username = res.username
          this.basUsername = res.basUsername
          this.ouName = res.ouName
          console.log(this.username);
          if(!res.birthDate){
            // 获取当前日期并改变日期展示格式
            var curDate = new Date();
            var seperator1 = "-";
            var month = curDate.getMonth() + 1 < 10 ? "0" + (curDate.getMonth() + 1) : curDate.getMonth() + 1;   //截取月份
            var strDate = curDate.getDate() < 10 ? "0" + curDate.getDate() : curDate.getDate();   //截取日期
            this.date = curDate.getFullYear() + seperator1 + month + seperator1 + strDate  //将年月日用'-'拼接起来
            console.log(this.date)
          }
        }
      };
      http.request(params);
    },

    //移动按钮点击事件
    translate: function(e) {
      if (t == 0) {
        moveY = 0;
        show = false;
        t = 1;
      } else {
        moveY = 200;
        show = true;
        t = 0;
      }
      this.setData({
        show: true,
        showPicker: true
      }); // this.animation.translate(arr[0], arr[1]).step();

      this.animationEvents(this, moveY, show);
    },

    //隐藏弹窗浮层
    hiddenFloatView(e) {
      //console.log(e);
      if(!this.sexNumber){
        this.setData({
          sex:0
        })
          if(this.sex!=this.oldSex){
            this.isChange = true
          }
      }
      moveY = 200;
      show = true;
      t = 0;
      this.animationEvents(this, moveY, show);
    },

    //动画事件
    animationEvents: function(that, moveY, show) {
      //console.log("moveY:" + moveY + "\nshow:" + show);
      that.animation = wx.createAnimation({
        transformOrigin: "50% 50%",
        duration: 400,
        timingFunction: "ease",
        delay: 0
      });
      that.animation.translateY(moveY + 'vh').step();
      that.setData({
        animation: that.animation.export()
      });
    },

    //用户昵称
    getNickNameInt: function(e) {
      this.nickName = e.detail.value
			this.isChange = true
    },

    // 选择性别
    bindChange: function(e) {
      this.sex = e.detail.value[0]
      console.log(this.sex);
      this.setData({
        sexNumber : this.sexNumber+=1
      })
      if(this.sex!=Number(this.oldSex)){
        this.isChange = true
      }else{
        this.isChange = false
      }
    },

    // 选择日期
    bindDateChange: function (e) {
      this.date = e.detail.value
			this.isChange = true
    },

    /**
     * 设置用户信息
     */
    setUserInfo() {
      // console.log(!String(this.userName).trim(),!this.userName, this.userName);
			if (!this.username || !this.username.trim()) {
				uni.showToast({
					title: this.i18n.usernameEmptyTips,
					icon:'none',
				})
				return
			}

      if (!this.nickName || !this.nickName.trim()) {
				uni.showToast({
					title: this.i18n.nicknameEmptyTips,
					icon:'none',
				})
				return
			}

			if (this.isChange ) {
				uni.showLoading();
				var params = {
					url: "/p/user/setUserInfo",
					method: "PUT",
					data: {
						avatarUrl:this.photoFiles,
						birthDate: this.date,
						sex: this.sex,
						nickName: this.nickName
					},
					callBack: (res) => {
						uni.hideLoading();
						uni.showToast({
							title: this.i18n.successfullyModified,
							icon: 'none',
							duration: 1000
						})
						setTimeout(() => {
							this.queryUserInfo()
							// uni.switchTab({
							//   url: '/pages/user/user'
							// })
							this.$Router.pushTab('/pages/user/user')
						},1000)
					}
				};
				http.request(params);
			}
    },

    /**
     * 去条款页
     */
    toTermsOfService(key){
      uni.navigateTo({
        url: "/packageUser/pages/termsPage/termsPage?sts=" + key
      })
    },
    /**
     * 退出登录
     */
    logout: function() {
      const appType = uni.getStorageSync("appType");

      util.tapLog(3)
      const params = {
        url: appType === AppType.MINI ? '/social/logOut' : '/logOut',
        method: 'POST',
        data: {
          basToken: uni.getStorageSync('wenetToken')
        },
        callBack: res => {
          this.isAuthInfo = false
          util.clearLoginData();
          let emptyObj = {}
          this.setData({
            orderAmount: emptyObj,
            couponNum: 0,  //优惠券数量
            score: 0, //用户积分
            totalBalance: 0, //用户余额
            notifyNum: 0,  // 消息提醒
            messageCount: 0,
          })
          // this.$Router.pushTab('/pages/index/index')
          uni.reLaunch({
            url: '/pages/index/index'
          })
          uni.setStorageSync("unUseCouponCount", 0)
          uni.removeTabBarBadge({index:2})
        },
        errCallBack: errMsg => {
          console.log(errMsg)
        }
      }
      http.request(params)
    },

    nono(){}
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  onPullDownRefresh: function () {

  },
  onShareAppMessage: function (e) {
    return {

    };
  },
};
</script>
<style lang="scss" scoped>
image {
  display: block;
  width: 100%;
  height: 100%;
}

.personal-information {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: auto;
  background: $page-background;
  border-top: 1rpx solid #eee;
}

// 头像区域
.head-portrait {
  position: relative;
  margin-bottom: 20rpx;
  background: #fff;
  padding: 32rpx 24rpx;
  
  .head-txt {
    display: inline-block;
    vertical-align: middle;
    line-height: 110rpx;
    font-size: 30rpx;
    color: $wenet-color-text-primary;
  }
  
  .station-photo-con {
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%);
    
    .add-img-icon {
      display: inline-block;
      overflow: hidden;
      width: 130rpx !important;
      height: 130rpx !important;
      border-radius: 50%;
      vertical-align: middle;
    }
  }
}

// 个人信息区域
.persone-info {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 0 24rpx;
  
  .msg-item {
    border-bottom: 1rpx solid #eee;
    background: #fff;
    font-size: 28rpx;
    padding: 32rpx 0;
    
    &:last-child {
      border-bottom: 0;
    }
    
    .msg-item-txt {
      display: inline-block;
      line-height: 1em;
      color: $wenet-color-text-primary;
    }
    
    .right-arrow {
      float: right;
      margin-left: 25rpx;
      
      .choose-value {
        display: inline-block;
        font-size: 28rpx;
        color: $wenet-color-text-secondary;
        vertical-align: middle;
        line-height: 1em;
        text-align: right;
        padding-right: 26rpx;
      }
    }
  }
  
  // 性别选择区域
  .section {
    display: flex;
    align-items: center;
    width: 100%;
    font-size: 28rpx;
    padding: 32rpx 0;
    line-height: 48rpx;
    box-sizing: border-box;
    border-bottom: 1rpx solid #eee;
    color: $wenet-color-text-primary;
    
    text {
      width: 20%;
    }
    
    .pca {
      width: 70%;
      padding: 0 20rpx;
      text-align: right;
      color: $wenet-color-text-secondary;
    }
    
    .arrow {
      width: 28rpx;
      height: 28rpx;
      
      image {
        width: 100%;
        height: 100%;
        vertical-align: top;
      }
    }
  }
}

// 选择器相关样式
picker-view {
  background-color: white;
  padding: 0;
  width: 100%;
  height: 380rpx;
  bottom: 0;
  position: fixed;
  
  &-column view {
    vertical-align: middle;
    font-size: 30rpx;
    line-height: 30rpx;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.animation-element-wrapper {
  display: flex;
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.animation-element {
  display: flex;
  position: fixed;
  width: 100%;
  height: 530rpx;
  bottom: 0;
  background-color: #fff;
}

.right-bt {
  right: 24rpx;
  top: 20rpx;
  position: absolute;
  display: inline-block;
  text-align: right;
  color: $wenet-color-brand;
  font-size: 28rpx;
}

.line {
  display: block;
  position: fixed;
  height: 2rpx;
  width: 100%;
  margin-top: 89rpx;
  background-color: #eee;
}

// 设置项区域
.three-box {
  width: 100%;
  box-sizing: border-box;
  background: #fff;
  padding: 0 24rpx;
  margin-top: 20rpx;
  
  .three-item {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 28rpx 0;
    border-bottom: 1rpx solid #eee;
    
    &:last-child {
      border-bottom: none;
    }
    
    .text {
      font-size: 28rpx;
      color: $wenet-color-text-primary;
    }
    
    .arrow {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.save-btn {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}
</style>

/* packageUser/pages/recent-news/recent-news.wxss */

.recent-news {
  background: #fff;
}

.recent-news .news-item {
  padding: 20rpx 20rpx 0 20rpx;
	overflow: hidden;
  position: relative;
}

.recent-news .news-item::after {
  content: " ";
  width: 100%;
  height: 2rpx;
  background-color: #e1e1e1;
  display: block;
}

.recent-news .news-item .news-item-title {
  font-size: 28rpx;
  text-align: left;
	word-break: break-word;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recent-news .news-item .news-item-date {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 10rpx;
  margin-bottom: 20rpx;
}

/* 消息类型tab */
.member-message-container {
	max-width: 100%;
}
.tab{
  display: flex;
	width: 100%;
  justify-content: space-between;
  background-color: #fafafa;
  padding: 10rpx;
	box-sizing: border-box;
	position: fixed;
	top: 0;
	left: 0;
	height: 80rpx;
	z-index: 9;
}
.tab-item{
  flex: 1;
  display: flex;
  font-size: 28rpx;
  justify-content: center;
  align-items: center;
  padding: 10rpx;
  color: #000;
}
.active{
  color:#4B87FF;
}
.message-list {
	margin-top: 80rpx;
}
.message-tit{
  font-weight: 600;
}
.message-content{
  font-size: 28rpx;
  margin-top: 10rpx;
	word-break: break-word;
}
/* 列表为空 */
.empty {
  margin-top: 100rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 2em;
}
/* pages/delivery-address/delivery-address.wxss */

page {
  background-color: #fff;
  border-top: 2rpx solid #e9eaec;
  padding-bottom: 150rpx;
}


.main {
  margin-bottom: 20rpx;
}

/* #ifdef APP-PLUS */
.main {
  margin-bottom: 120rpx;
}
/* #endif */

.address {
  margin-bottom: 15rpx;
  width: 100%;
  background-color: #fff;
  /* border-bottom: 2rpx solid #e9eaec; */
}

.address .personal {
  position: relative;
  padding: 20rpx 0rpx;
  margin: 0 30rpx;
  border-bottom: 3rpx solid #e9eaec;
}

.info-tit {
  width: 95%;
  margin: 10rpx 0;
  word-break: break-word;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.address .personal .info-tit .name {
  margin-right: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
	max-width: 90%;
  display: inline-block;
	word-break: break-all;
}

.address .personal .info-tit .tel {
  font-size: 28rpx;
  font-weight: bold;
}

.address .personal .info-tit .defalt{
	width: 70rpx;
	height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #fff;
	background: var(--primary-color);
	margin-left: 10px;
	 border-radius: 12rpx;
}

.address .personal .info-tit image {
  position: absolute;
  right: 0rpx;
  top: 30rpx;
  width: 32rpx;
  height: 32rpx;
  margin-left: 50rpx;
  vertical-align: middle;
}

.personal .addr {
  font-size: 28rpx;
  margin: 10rpx 0;
  margin-top: 20rpx;
}

.personal .addr .addr-get {
  display: inline-block;
  color: #888;
  font-size: 24rpx;
  width: 100%;
	word-break: break-word;
}

.address .select-btn {
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.address .select-btn .box {
  font-size: 28rpx;
}

.footer {
  position: fixed;
  bottom: 20px;
  left: 25rpx;
  width: 700rpx;
  height: 72rpx;
  line-height: 100rpx;
  text-align: center;
  background-color: var(--primary-color);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer text {
  font-size: 28rpx;
  color: #fff;
}

.empty .img {
  text-align: center;
  margin-top: 130rpx;
}

.empty .img image {
  width: 100rpx;
  height: 100rpx;
  display: block;
  margin: auto;
}

.empty .txt {
  margin-top: 30rpx;
  font-size: 24rpx;
  text-align: center;
  color: #999;
}


/* 列表为空 */
.empty {
  margin-top: 100rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 2em;
}
<template>
  <view class="pay-info">
    <!-- 代付金额 -->
    <view class="pay-number">
      <view class="pay-greeting">
        {{ !isShare ? '你好，' : '我是' }}{{ userPhone }}
      </view>
      <view class="pay-prompt" :class="{'pay-prompt-share': isShare}">
        <text v-if="!isShare">代付订单已创建成功，发送给好友帮你付款吧~</text>
        <WxDialogBox v-else-if="orderStatus == 1" text="我好喜欢这个商品，帮我付一下吧，谢谢啦" />
        <WxDialogBox v-else text="先不用帮我付了，谢谢啦" />
      </view>

      <view class="pay-price-prompt">
        仅需支付
      </view>
      <view class="price">
        <view class="big-num" v-if="actualTotal"><text class="small-num">￥</text>{{parsePrice(actualTotal)[0]}}</view>
        <view class="big-num" v-if="actualTotal">.{{parsePrice(actualTotal)[1]}}</view>
      </view>
      <view class="price">
        <view v-if="!actualTotal">¥ 0.00</view>
      </view>
      <!-- 代付款并且没超时 -->
      <view class="time-box" v-if="orderStatus == 1 && !isOvertime">优惠仅剩：<text class="time-hour">{{hou}}:{{min}}:{{sec}}</text></view>
    </view>

    <!-- 订单列表 -->
    <view class="prod-item">
      <view v-for="(item, prodId) in orderItems" :key="prodId" class="prod-box">
        <view class="item-cont" >
          <view class="prod-pic">
            <image :src="item.pic"></image>
          </view>
          <!-- 拼团商品展示icon -->
          <view class="prod-info">
            <view class="prodname">
              <view class="text">{{item.prodName}}</view>
            </view>
            <view class="suk-con">
              <view class="sku-name">{{item.skuName || ''}}</view>
            </view>
            <!-- 价格 -->
            <view class="price-nums">
              <view class="prodprice"><text class="symbol">￥</text>
                <text class="big-num">{{parsePrice(item.price)[0]}}</text>
                <text class="small-num">.{{parsePrice(item.price)[1]}}</text>
                <text class="small-num" v-if="orderType==3">{{`&nbsp;+&nbsp;`}}</text>
                <text class="small-num" v-if="orderType==3">{{item.useScore / item.prodCount}} {{i18n.integral}}</text>
              </view>
              <text class="number">×{{ item.prodCount }}</text>
            </view>
          </view>
        </view>
        
        <!-- 赠品 -->
        <view v-if="item.giveawayList && item.giveawayList.length" class="gift-prods">
          <view
            v-for="giftItem in item.giveawayList"
            :key="giftItem.orderItemId"
            class="gift-item"
          >
            <text class="name">{{'【' + i18n.gift + '】'}}{{ giftItem.prodName }}</text>
            <text class="number">×{{ giftItem.prodCount }}</text>
          </view>
        </view>

      </view>
      
      <!-- 总计 -->
      <view class="total-num">
        <view class="totals">
          <text class="prodcount">{{i18n.inTotal}}{{totalNum}}{{i18n.items}}</text>
          <view class="prodprice">{{i18n.total}}：
            <text class="symbol" >￥</text>
            <text class="big-num">{{parsePrice(actualTotal)[0]}}</text>
            <text class="small-num" decode="true">.{{parsePrice(actualTotal)[1] + '&nbsp;'}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 好友代付按钮 -->
    <view class="pay-btn">
      <button v-if="!isShare" class="primary-btn red-btn" :disabled="orderStatus != 1" open-type="share">
        <text>发送给微信好友</text>
      </button>
      <button v-else-if="orderStatus == 1 && !isOvertime" class="primary-btn red-btn" @tap="toPay">
        <text>帮TA付</text>
      </button>
      <!-- 订单状态已超时 || 前端计算已到超时时间 -->
      <button v-else-if="orderStatus == 6 || isOvertime" class="primary-btn gray-btn">
        <text>订单已关闭</text>
		  </button>
      <button v-else class="primary-btn gray-btn">
        <text>订单已完成</text>
		  </button>
    </view>

  </view>
</template>

<script>
import http from "@/utils/http.js"
import config from "@/utils/config.js";
import WxDialogBox from '@/components/wx-dialog-box'

export default {
  name: 'pay-for-friend',
  data () {
    return {
      picDomain: config.picDomain,
      orderNumbers: '', //订单号
      actualTotal: 0, //订单价格
      orderType: '', // 订单类型 0普通 1团购 2秒杀 3积分
      timer: '', //定时器名称
      endTime: '', //订单过期时间
      hou: '', //时
      min: '', //分
      sec: '', //秒
      userPhone: '',
      totalNum: 0,
      orderItems: [], // 订单列表
      isShare: 0, // 0 不是分享状态   1 是分享状态
      orderStatus: 1,
      isOvertime: false,
      payTimer: '',
    }
  },
  computed:{
    i18n() {
      return this.$t('index')
    }
  },
  components: {
    WxDialogBox,
  },
  mounted () {

  },
  onShow: function () {
    const query = this.$Route.query

    uni.setNavigationBarTitle({
      title: '代付支付'
    });
    this.orderType = query.orderType
    this.orderNumbers = query.orderNumbers
    this.isShare = query?.isShare
    this.loadOrderDetail(query.orderNumbers)
  },
  onShareAppMessage: async function() {
    let { categoryName } = await this.loadOrderDetail(this.orderNumbers)

    return {
      title: `我很喜欢这个${categoryName}，请你帮我付款吧`,
      path: '/packageUser/pages/pay-for-friend/pay-for-friend?orderNumbers=' + this.orderNumbers + '&isShare=1' + '&orderType=' + this.orderType,
    };
  },
  methods: {
    // 加载订单数据
    loadOrderDetail: function (orderNum) {
      return new Promise((resolve, reject) => {
        http.request({
          url: "/pub/order/info",
          method: "GET",
          data: {
            orderNumber: orderNum,
          },
          callBack: (res) => {
            this.setData({
              orderItems: res.orderItemDtos,
              totalNum: res.totalNum,
              actualTotal: res.actualTotal,
              orderStatus: res.status,
              userPhone: res.userPhone,
              endTime: res.endTime,
            });

            this.getCountDown()

            if(res.status != 1) {
              clearInterval(this.payTimer)
              this.timer = null
            }
            resolve({
              categoryName: res?.orderItemDtos?.[0]?.categoryName
            })
          }
        });
      })
    },
    toPay() {
      uni.showLoading()
      const params = {
        url: "/pub/order/pay",
        method: "POST",
        data:{
          payType: 1,
          orderNumbers: this.orderNumbers,
          returnUrl: '',
          friendOpenId: uni.getStorageSync('openid'),
        },
        callBack: (res)=> {
          uni.hideLoading()
          wx.requestPayment({
            timeStamp: res.timeStamp,
            nonceStr: res.nonceStr,
            package: res.packageValue,
            signType: res.signType,
            paySign: res.paySign,
            complete: () => {
              this.payTimer = setInterval(() => {
                this.loadOrderDetail(this.orderNumbers)
              }, 2000)
            }
          })
        },
        errCallBack: err => {
          uni.showToast({
            icon: 'none',
            title: err.data,
            duration: 2000,
            complete: () => {
              this.loadOrderDetail(this.orderNumbers)
            }
          })
        }
      }
      http.request(params)
    },
    // 倒计时
    getCountDown: function() {
      var nowTime = new Date().getTime(); //现在时间（时间戳）
      var endTime = new Date(this.endTime?.replace(/\-/g, '/')).getTime(); //结束时间（时间戳）
      var time = (endTime - nowTime) / 1000; //距离结束的毫秒数
      // 获取时、分、秒
      let hou = parseInt(time % (60 * 60 * 24) / 3600);
      let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
      let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
          hou = this.timeFormin(hou),
          min = this.timeFormin(min),
          sec = this.timeFormin(sec)
      this.setData({
        hou: this.timeFormat(hou),
        min: this.timeFormat(min),
        sec: this.timeFormat(sec)
      })
      // 每1000ms刷新一次
      if (time > 0) {
        this.setData({
          timer: setTimeout(this.getCountDown, 1000)
        })
      } else {
        this.setData({
          isOvertime: true,
        })
      }
    },
    //小于10的格式化函数（2变成02）
    timeFormat(num) {
      return num < 10 ? '0' + num : num;
    },
    //小于0的格式化函数（不会出现负数）
    timeFormin(num) {
      return num < 0 ? 0 : num;
    },
    encryptPhoneNumber(phoneNumber) {
      const encrypted = phoneNumber?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
      return encrypted;
    },
  },
}
</script>

<style scoped>
@import "./pay-for-friend.css";
</style>

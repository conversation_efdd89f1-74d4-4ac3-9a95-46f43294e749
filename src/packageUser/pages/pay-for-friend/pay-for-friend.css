.pay-info {
	min-height: 100vh;
	background: #F8F8F8;
}

.pay-info image {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.pay-info .pay-number {
	padding: 24rpx 32rpx;
	background: linear-gradient(135deg, var(--primary-color) -10%, #FFA5A5 100%);
	border-radius: 0 0 30rpx 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(255,107,107,0.15);
	position: relative;
	overflow: hidden;
}

.pay-info .pay-number::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg, 
		rgba(255,255,255,0.1) 0%,
		rgba(255,255,255,0.05) 100%);
	pointer-events: none;
}

.pay-info .pay-number .pay-greeting {
	margin-bottom: 12rpx;
	color: #fff;
	background-color: rgba(255,255,255,0.15);
	padding: 12rpx 20rpx;
	border-radius: 30rpx;
	width: fit-content;
	box-sizing: border-box;
	font-size: 28rpx;
	backdrop-filter: blur(4px);
	transform: translateZ(0);
	position: relative;
	z-index: 1;
}

.pay-info .pay-number .pay-prompt {
	text-align: center;
	margin-bottom: 24rpx;
	color: #fff;
	background-color: rgba(255,255,255,0.15);
	padding: 12rpx 20rpx;
	border-radius: 30rpx;
	width: fit-content;
	box-sizing: border-box;
	font-size: 28rpx;
	backdrop-filter: blur(4px);
	transform: translateZ(0);
	position: relative;
	z-index: 1;
}

.pay-info .pay-number .pay-prompt.pay-prompt-share {
  background-color: unset;
}

.pay-info .pay-number .pay-price-prompt {
	text-align: center;
	margin-bottom: 16rpx;
	color: #fff;
	font-size: 32rpx;
	font-weight: 500;
}

.pay-info .pay-number .price {
	display: flex;
	align-items: baseline;
	justify-content: center;
	font-weight: 600;
	color: #fff;
}

.pay-info .pay-number .price .small-num {
	font-size: 40rpx;
}

.pay-info .pay-number .price .big-num {
	font-size: 72rpx;
	line-height: 1;
	margin: 0 4rpx;
}

.pay-info .pay-number .time-box {
	margin-top: 16rpx;
	font-size: 24rpx;
	text-align: center;
	color: rgba(255,255,255,0.9);
	display: flex;
	align-items: center;
	justify-content: center;
}

.pay-info .pay-number .time-box .time-hour {
	color: #fff;
	background: rgba(0, 0, 0, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	margin: 0 8rpx;
	font-weight: 600;
	font-size: 28rpx;
	display: inline-block;
	min-width: 44rpx;
	text-align: center;
	box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	letter-spacing: 2rpx;
}

/* 添加冒号样式 */
.pay-info .pay-number .time-box .time-hour + .time-hour::before {
	content: ':';
	position: absolute;
	left: -14rpx;
	color: #fff;
	font-weight: bold;
}

.pay-info .ways {
	padding: 0 20rpx;
}

.pay-info .ways .item {
	display: flex;
	align-items: center;
	border-bottom: 1px solid #eee;
	padding: 20rpx 0;
}

.pay-info .ways .item .pay-name {
	flex: 1;
	display: flex;
	align-items: center;
}

.pay-info .ways .item .pay-name .img {
	width: 44rpx;
	height: 44rpx;
	font-size: 0;
}

.pay-info .ways .item .pay-name .name {
	font-size: 24rpx;
	margin-left: 20rpx;
}

.pay-info .sure-pay {
	position: fixed;
	bottom: 40rpx;
	left: 20rpx;
	right: 20rpx;
	width: auto;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 80rpx;
	text-align: center;
	background: var(--primary-color);
	font-size: 24rpx;
	font-weight: 600;
	color: #fff;
}

.prod-item {
	margin: 16rpx;
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.prod-item .prod-box {
	border-bottom: 1rpx solid #f5f5f5;
}

.prod-item .item-cont {
	position: relative;
	display: flex;
	padding: 16rpx;
}

.prod-item .item-cont .prod-pic {
	width: 160rpx;
	height: 160rpx;
	border-radius: 12rpx;
	overflow: hidden;
}

.prod-item .item-cont .prod-info {
	flex: 1;
	margin-left: 24rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.prod-item .item-cont .prod-info .prodname {
	display: flex;
	align-items: flex-start;
}

.prod-item .item-cont .prod-info .prodname .text {
	font-size: 28rpx;
	line-height: 40rpx;
	color: #333;
	font-weight: 500;
}

.prod-item .item-cont .prod-info .suk-con {
	margin-top: 12rpx;
}

.prod-item .item-cont .prod-info .suk-con .sku-name {
	font-size: 24rpx;
	color: #999;
}

.prod-item .price-nums {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.prod-item .price-nums .prodprice {
	color: var(--primary-color);
	font-weight: 500;
}

.prod-item .price-nums .number {
	color: #999;
	font-size: 26rpx;
}

.prod-item .total-num {
	padding: 16rpx;
	background: #FAFAFA;
	border-radius: 0 0 16rpx 16rpx;
}

.total-num .totals {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.prod-item .total-num .prodcount {
	font-size: 26rpx;
	color: #666;
}

.prod-item .total-num .prodprice {
	color: #333;
	font-size: 28rpx;
}

.prod-box .gift-prods {
	padding: 8rpx 16rpx 16rpx 200rpx;
}

.prod-box .gift-prods .gift-item {
	display: flex;
	justify-content: space-between;
}

.pay-btn {
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.primary-btn {
	font-size: 32rpx;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 40rpx;
	font-weight: 500;
	transition: all 0.3s ease;
}

.red-btn {
	background: var(--primary-color);
	color: #fff;
	box-shadow: 0 4rpx 12rpx rgba(255,107,107,0.3);
}

.red-btn:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 6rpx rgba(255,107,107,0.2);
}

.gray-btn {
	background: #999;
	color: #fff;
	opacity: 0.8;
}

.gift-prods {
	padding: 8rpx 16rpx 16rpx 196rpx;
	background: #FAFAFA;
}

.gift-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 24rpx;
	color: #666;
	padding: 8rpx 0;
}

.gift-item .name {
	flex: 1;
	margin-right: 20rpx;
}
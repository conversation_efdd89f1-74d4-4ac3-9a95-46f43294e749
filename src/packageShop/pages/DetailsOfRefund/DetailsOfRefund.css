/* packageShop/pages/DetailsOfRefund/DetailsOfRefund.wxss */
.display {
  display: none;
}
image {
  width: 100%;
  height: 100%;
}
.page {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #f7f7f7;
}
/* 头部状态 */
.describe-box {
  background: #fff;
  margin-bottom: 20rpx;
}
.process {
  padding: 20rpx 25rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.current-process {
  font-size: 30rpx;
  font-weight: bold;
  padding: 10rpx 0;
}
.acount-for-process {
  font-size: 28rpx;
  color: #444;
  line-height: 1.5em;
  word-break: break-word;
}
.red {
  color: #e43130;
}
/* 隐藏内容 */
.more-content {
  border-top: 1rpx dashed #ebebeb;
  margin-top: 15rpx;
  padding-top: 10rpx;
}
.cont {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.8em
}
/* 更多说明 */
.more-instructions {
  padding: 18rpx 0;
  padding-bottom: 22rpx;
  text-align: center;
  font-size: 27rpx;
}
.more-txt {
  vertical-align: middle;
}
.down-arrow-icon {
  display: inline-block;
  width: 40rpx;
  height: 25rpx;
  vertical-align: middle;
}


/* 退款流程 */
.refund-schedule {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 0 25rpx;
}
.refund-sum {
  font-size: 28rpx;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
}
.progress-bar {
  position: relative;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
}
.block {
  display: inline-block;
  text-align: center;
}
.round {
  display: block;
  width: 13rpx;
  height: 13rpx;
  border-radius: 50%;
  background: #999999;
  margin: 0 auto;
  margin-top: 15rpx;
}
/* 激活状态圆点 */
.round-current {
  background: #3388ff;
}
.block-text {
  margin-top: 15rpx;
  text-align: center;
}
.block-text-tit {
  font-size: 26rpx;
  line-height: 2em;
  color: #777;
}
.block-text-time {
  font-size: 22rpx;
  color: #aaa;
}
.line {
  position: absolute;
  top: 39rpx;
  display: inline-block;
  width: 215rpx;
  height: 5rpx;
  background: #bdbdbd;
}
/* 激活状态直线 */
.line-current {
  background: #3388ff;
}
.line01 {
  left: 145rpx;
}
.line02 {
  right: 115rpx;
}

/* 拒绝理由 */
.refuse-reason {
  font-size: 28rpx;
  background: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
	word-break: break-word;
}


/* 退货地址 */
.return-address {
  background: #fff;
  margin-bottom: 20rpx;
}
.addr-tit {
  padding: 10rpx 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
  font-size: 30rpx;
  font-weight: bold;
}
.addr {
  font-size: 14px;
  display: -webkit-box;
  /* -webkit-line-clamp: 2; */
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}
.address {
  padding: 20rpx;
  font-size: 28rpx;
}
.recipient {
  padding-bottom: 10rpx;
}
.mobile {
  margin-left: 50rpx;
}




/* 查看协商记录 */
/* .negotiate-record {
  position: relative;
  font-size: 25rpx;
  text-align: left;
  background: #fff;
  padding: 20rpx 25rpx;
  margin-bottom: 20rpx;
}
.more-arrow {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 22rpx;
  height: 22rpx;
} */


/* 退款信息 */
.refund-message {
  background: #fff;
  padding: 20rpx 25rpx;
}
.deliveryDto {
  margin-top: 20rpx;
}
.refund-txt {
  font-size: 30rpx;
  margin-bottom: 25rpx;
  font-weight: bold;
}

/* 店铺 */
.shop-box {
  display: block;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.shop-img {
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  vertical-align: middle;
  margin-right: 10rpx;
}
.shop-img > iamge {
  display: block;
  width: 100%;
  height: 100%;
}
.shopname {
  display: inline-block;
  font-size: 28rpx;
  line-height: 1em;
  vertical-align: middle;
  color: #444444;
}

/* 商品信息 */
.goods-msg-box {
  background: #f9f9f9;
  padding: 15rpx 20rpx;
  margin: 15rpx 0;
}
.img-box {
  display: inline-block;
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  background: #eee;
  vertical-align: top;
}
.goods-text {
  display: inline-block;
  vertical-align: top;
  width: 70%;
}
.goods-title {
  font-size: 28rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}
.goods-quantity {
  font-size: 28rpx;
  color: #888888;
  margin-top: 20rpx;
}
/* 具体信息 */
.refund-cont {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5em;
  margin-bottom: 20rpx;
	word-break: break-word;
}
/* 退款凭证 */
.refund-cont.refund-voucher {
  display: flex;
}
.refund-cont.refund-voucher .voucher-name {
  white-space: nowrap;
}
.refund-cont.refund-voucher image {
  display: inline-block;
  width: 100rpx;
  height: 100rpx;
}
.refund-cont.refund-voucher image:not(:last-child) {
  margin-right: 10rpx;
}
/* 联系商家 */
.contact-merchant {
  font-size: 28rpx;
  color: #57b2fcfd;
  text-align: center;
  padding-top: 20rpx;
  margin-top: 20rpx;
  border-top: 1rpx solid #f2f2f2;
}
button.contact-merchant{
  background: #fff;
}
button.contact-merchant::after{
  border:0;
}
/* 整单退款商品列表 */
.refund-goods-item{
  padding: 20rpx 0;
  border-bottom: 2rpx solid #eee;
}
.refund-goods-item:last-child{
  border-bottom:none;
}
/* 赠品 */
.refund-goods-item .gift-prods {
  margin-left: 170rpx;
  margin-top: 10rpx;
}
.refund-goods-item .gift-prods .gift-item {
  color: #333;
  font-size: 24rpx;
}
.refund-goods-item .gift-prods .gift-item:not(:last-child) {
  margin-bottom: 10rpx;
}
.refund-goods-item .gift-prods .gift-item .name,
.refund-goods-item .gift-prods .gift-item .num {
  display: inline-block;
  vertical-align: middle;
  padding-right: 10rpx;
  box-sizing: border-box;
}
.refund-goods-item .gift-prods .gift-item .name {
  width: 85%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.refund-goods-item .gift-prods .gift-item::after {
  content: '';
  display: inline-block;
  width: 12rpx;
  height: 12rpx;
  border-top: 2rpx solid #333333;
  border-right: 2rpx solid #333333;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.goods-msg-box.giveaway-list .goods-quantity .sku {
  margin-right: 10rpx;
}
.goods-msg-box.giveaway-list .goods-quantity .sku.gift-icon {
  display: inline-block;
  color: #fff;
  background: #e43130;
  padding: 6rpx;
  border-radius: 6rpx;
  box-sizing: border-box;
  font-size: 24rpx;
  line-height: 1em;
}


/* 脚部撤销 */
.foot-box {
  display: block;
  margin-top: 150rpx;
}
.footer {
  display: block;
  box-sizing: border-box;
  position: fixed;
  width: 100%;
  bottom: 0;
  font-size: 28rpx;
  background: #fff;
  padding: 0 30rpx;
  text-align: right;
  box-shadow: 1rpx 10rpx 10rpx 10rpx rgb(236, 236, 236);
}
.undo-apply {
  display: inline-block;
  box-sizing: border-box;
  padding: 15rpx 20rpx;
  font-size: 25rpx;
  border-radius: 5rpx;
  margin: 25rpx 0;
  margin-left: 15rpx;
  border: 1rpx solid #aaaaaa;
  color: #555555;
}
.undo-apply-end {
  display: inline-block;
  box-sizing: border-box;
  margin-left: 15rpx;
  color: #777;
  font-size: 30rpx;

}
.apply-current {
  border: 1rpx solid rgb(0, 173, 0);
  color: rgb(0, 173, 0);
}

.refund-text{
	display: inline-block;
	width: 140rpx;
}

/* 修改退款金额 */
.popup {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.popup .popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: block;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.2);
}
.popup .popup-con {
  position: fixed;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  display: block;
  width: 80%;
  font-size: 24rpx;
}
.popup .popup-con .pop-tit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #eee;
  padding: 20rpx;
  font-size: 28rpx;
}
.popup .popup-con .pop-tit .close {
  font-size: 30rpx;
}
.popup .popup-con .con.modify-amount {
  display: block;
  position: relative;
  padding: 20px;
  background: #fff;
  padding-bottom: 150rpx;
  box-sizing: border-box;
}
.popup .popup-con .con.modify-amount .int-box {
  display: block;
  width: 90%;
  margin: 0 auto;
}
.popup .popup-con .con.modify-amount .int-box .int {
  height: 40rpx;
	line-height: 40rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #ddd;
  font-size: 28rpx;
}
.popup .popup-con .con.modify-amount .int-box .max-amount {
  color: #999;
  margin-top: 5px;
}
.popup .popup-con .con.modify-amount .foot-btn {
  display: block;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  text-align: center;
  padding: 40rpx;
  box-sizing: border-box;
}
.popup .popup-con .con.modify-amount .foot-btn .confirm {
  display: inline-block;  
  padding: 16rpx 40rpx;
  background: #e1251b;
  color: #fff;
  border-radius: 4rpx;
  cursor: pointer;
}
.popup .popup-con .con.modify-amount .error {
  margin-top: 10rpx;
  color: #e1251b;
}

<template>
<!-- 退款详情 -->
<!--packageShop/pages/DetailsOfRefund/DetailsOfRefund.wxml-->

<view class="page">
  <!-- 头部状态 -->
  <view class="describe-box">
    <!-- 申请行进流程 -->
    <view class="process">
      <!-- 提交申请 -->
      <view class="current-process" v-if="returnMoneySts == 1">{{i18n.refundDetails11}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 1">{{i18n.refundDetails12}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 1">{{updateTime}}</view>
      <!-- 商家拒绝 -->
      <view class="current-process" v-if="returnMoneySts == 7">{{i18n.refundDetails71}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 7">{{i18n.refundDetails72}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 7">{{updateTime}}</view>
      <!-- 商家同意退款 -->
      <view class="current-process" v-if="returnMoneySts == 2">{{i18n.refundDetails2}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 2 && applyType == 1">{{i18n.refundDetails21}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 2 && applyType == 2">{{i18n.refundDetails22}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 2">{{updateTime}}</view>
      <!-- 寄出商品（退货退款情况下） -->
      <view class="current-process" v-if="returnMoneySts == 3">{{i18n.refundDetails31}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 3">{{i18n.refundDetails32}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 3">{{updateTime}}</view>
      <!-- 商家确认收货 -->
      <view class="current-process" v-if="returnMoneySts == 4">{{i18n.refundDetails41}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 4">{{i18n.refundDetails42}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 4">{{updateTime}}</view>
      <!-- 退款成功 -->
      <view class="current-process" v-if="returnMoneySts == 5">{{i18n.refundDetails51}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 5">{{refundTime}}</view>
      <!-- 退款申请关闭 -->
      <view class="current-process" v-if="returnMoneySts == -1">{{i18n.refundDetails_1}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == -1">
        <!-- 由于你超时未处理，系统已关闭本次退款申请 -->{{rejectMessage?rejectMessage:''}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == -1">{{updateTime}}</view>
      <!-- 买家撤回申请 -->
      <view class="current-process" v-if="returnMoneySts == 6">{{i18n.refundDetails61}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 6">{{i18n.refundDetails62}}</view>
      <view class="acount-for-process" v-if="returnMoneySts == 6">{{updateTime}}</view>


      <!-- 隐藏内容（仅退款） -->
      <view :class="showView?'display':'more-content'" v-if="returnMoneySts == 1">
        <view class="cont">{{i18n.refundDetails13}}</view>
        <view class="cont">{{i18n.refundDetails01}}</view>
        <!-- <view class="cont">如果商家逾期未处理，退款申请将自动达成并退款给你</view> -->
      </view>
      <!-- 隐藏内容（退货退款申请商品未寄出） -->
      <view :class="showView?'display':'more-content'" v-if="returnMoneySts == 2">
        <view class="cont">{{i18n.refundDetails23}}</view>
        <view class="cont">{{i18n.refundDetails01}}</view>
        <!-- <view class="cont">如果商家逾期未处理，请按照系统给出的退货地址退货</view> -->
      </view>
      <!-- 隐藏内容（退货退款并已寄出货物） -->
      <view :class="showView?'display':'more-content'" v-if="returnMoneySts == 3">
        <view class="cont">{{i18n.refundDetails33}}</view>
        <view class="cont">{{i18n.refundDetails02}}</view>
        <!-- <view class="cont">如果商家逾期未处理，系统将自动确认收货并退款给你</view> -->
      </view>

    </view>
    <!-- 申请行进流程end -->

       <!-- 更多说明 -->
       <view :class="showView?'more-instructions':'display'" @tap="onChangeShowState" v-if="returnMoneySts < 4 && returnMoneySts !== -1 && applyType == 2">
         <text class="more-txt">{{i18n.moreExplanation}}</text>
         <view class="down-arrow-icon">
           <image :src="`${staticPicDomain}images/icon/down-arrow.png`"></image>
         </view>
       </view>
       <!-- 收起 -->
       <view :class="showView?'display':'more-instructions'" @tap="onChangeShowState" v-if="returnMoneySts < 4 && returnMoneySts !== -1 && applyType == 2">
         <text class="more-txt">{{i18n.putAway}}</text>
         <view class="down-arrow-icon">
           <image :src="`${staticPicDomain}images/icon/up-arrow.png`"></image>
         </view>
       </view>

  </view>
  <!-- 头部状态end -->

  <!-- 【退款申请被拒绝时显示】拒绝理由 -->
  <view class="refuse-reason" v-if="returnMoneySts == 7">{{i18n.refundDetails73}}：{{rejectMessage}}</view>
	<view class="refuse-reason" v-if="showRefundReasonAndDesc && sellerMsg">{{i18n.sellerRemarks}}：{{ sellerMsg }}</view>

  <!-- 【退货退款状态下，买家同意后】退货地址 -->
  <view class="return-address" v-if="returnMoneySts == 2 && applyType == 2">
    <view class="addr-tit">{{i18n.returnAddress}}</view>
    <view class="address">
      <view class="recipient">
        <text class="addr-cont">{{i18n.recipient}}：{{refundDelivery.receiverName}}</text>
        <text class="mobile">{{refundDelivery.receiverMobile}}</text>
      </view>
      <view class="addr">{{i18n.detailedAddress}}：{{refundDelivery.receiverAddr}}</view>
    </view>
  </view>

  <!-- 退款信息 -->
  <view class="refund-message">
    <view class="refund-txt">{{i18n.refundInformation}}</view>

    <view class="goods-msg-box">
      <view class="refund-goods-item" v-for="(item, index) in orderItems" :key="index">
        <view class="item-con">
          <!-- 图片 -->
          <view class="img-box">
            <image :src="item.pic"></image>
          </view>
          <!-- 信息 -->
          <view class="goods-text">
            <view class="goods-title">{{item.prodName}}</view>
            <view class="goods-quantity">{{i18n.quantity}}：{{item.prodCount}}{{i18n.piece}}</view>
          </view>
        </view>
        <!-- 赠品：多个商品时展示 -->
        <view v-if="orderItems.length > 1 && item.giveawayList && item.giveawayList.length" class="gift-prods">
          <view
            v-for="(giftItem, giftItemIndex) in item.giveawayList"
            :key="giftItemIndex"
            class="gift-item"
              @tap="toProdDetail(giftItem.prodId)"
          >
            <text class="name">{{'【' + i18n.gift + '】'}}{{ giftItem.prodName }}</text>
            <text class="num">×{{ giftItem.prodCount }}</text>
          </view>
        </view>
        <!-- / 赠品 -->
      </view>
    </view>

    <!-- 赠品：单个商品时展示 -->
    <block v-if="orderItems.length==1 && orderItems[0].giveawayList && orderItems[0].giveawayList.length">
      <view v-for="(item, itemIndex) in orderItems[0].giveawayList" :key="itemIndex" class="goods-msg-box giveaway-list">
        <!-- 图片 -->
        <view class="img-box">
          <image :src="item.pic.indexOf('http') === -1 ? picDomain + item.pic : item.pic"></image>
        </view>
        <!-- 信息 -->
        <view class="goods-text">
          <view class="goods-title">{{item.prodName}}</view>
          <view class="goods-quantity">
            <text class="sku gift-icon">{{ i18n.gift }}</text>
            <text class="sku">{{ item.skuName }}</text>
            <text class="sku">×{{item.prodCount}}</text>
          </view>
        </view>
      </view>
    </block>
    <!-- / 赠品 -->

    <!-- 具体信息 -->
    <view class="refund-cont">{{i18n.refundMethod}}：{{['',i18n.refund01,i18n.refund02][applyType]}}</view>
    <template v-if="showRefundReasonAndDesc">
      <view class="refund-cont" v-if="buyerReason!=7">{{i18n.refundReason}}：{{refundProdReasonArray[buyerReason]}}</view>
      <view class="refund-cont">{{i18n.refundInstructions}}：{{buyerDesc}}</view>
    </template>
    <view class="refund-cont">{{i18n.refundAmount}}：
      <text class="red">￥{{refundAmount.toFixed(2)}}</text>
      <text class="red" v-if="refundAmount > 0 && refundScore > 0">+</text>
      <text class="red" v-if="refundScore > 0">{{refundScore}}{{i18n.integral}}</text>
    </view>
    <view class="refund-cont">{{i18n.refundNumber}}：{{refundSn}}</view>
    <view class="refund-cont">{{i18n.applicationTime}}：{{applyTime}}</view>
    <!-- 【退货申请显示】 -->
    <view class="refund-cont" v-if="goodsNum>0">{{i18n.quantityReturned}}：{{goodsNum}}</view>
    <!-- 退款凭证 -->
    <view class="refund-cont refund-voucher" v-if="photoFiles">
      <view class="voucher-name">{{i18n.refundVoucher}}：</view>
      <view class="voucher-pic">
        <block
          v-for="(pic, index) in photoFiles"
          :key="index"
        >
          <image
            :src="pic"
            mode="aspectFill"
            :data-cur="pic"
            @tap="refundPicPreView"
          ></image>
        </block>
      </view>
    </view>
    <!-- 联系商家 -->
    <!-- #ifndef MP-WEIXIN -->
    <view class="contact-merchant" v-if="shopCustomerSwitch">联系商家</view>
    <button class="contact-merchant" v-if="shopCustomerSwitch" open-type="contact">联系客服</button>
    <!-- #endif -->
  </view>

  <!-- 物流信息 -->
  <view class="refund-message deliveryDto" v-if="deliveryDto && deliveryDto.traceFlag">
    <view v-if="deliveryDto.traceFlag">
      <view class="refund-txt">{{i18n.logisticsInformation}}</view>
      <!-- 具体信息 -->
      <view class="refund-cont" v-for="(logItem,logIndex) in deliveryDto.traces" :key="logIndex">
        <view class="time">{{logItem.acceptTime}}</view>
        <view class="text-box">
          <!-- <view class="name">{{nickName}}</view> -->
          <view class="name acceptStation">{{logItem.acceptStation}}</view>
        </view>
      </view>
    </view>
  </view>


  <!-- 脚部撤销申请 -->
  <!-- applyType: 1,  //申请类型:1,仅退款,2退款退货 -->
  <!-- returnMoneySts： 处理退款状态:(1.买家申请 2.卖家接受 3.买家发货 4.卖家收货 5.退款成功 6.买家撤回申请 7.商家拒绝 -1.退款关闭) -->
  <view class="foot-box">
    <view class="footer">
      <!-- 【申请后-申请完成前】 -->
      <text class="undo-apply" @tap="cancelApplication" v-if="returnMoneySts ==1 || returnMoneySts == 7 || (returnMoneySts ==2 && applyType == 2)">{{i18n.applicationCanceled}}</text>
      <!-- 【商家拒绝时显示】 -->
      <!-- <text class="undo-apply apply-current" bindtap="toAlterRefundApply" wx:if="{{returnMoneySts ==1 || returnMoneySts == 7}}" data-refundsn="{{refundSn}}">修改申请</text> -->
      <!-- 【商家同意后-货物寄出前】 -->
      <text class="undo-apply apply-current" @tap="toLogisticsPage(refundSn, '0')" v-if="returnMoneySts == 2 && applyType == 2">{{i18n.fillInReturn}}</text>
      <text class="undo-apply apply-current" @tap="toLogisticsPage(refundSn, '1')" v-if="returnMoneySts == 3 && applyType == 2">{{i18n.modifyReturn}}</text>
      <!--
        修改退款金额：
        1、仅退款(applyTyp:1)：买家申请(returnMoneySts:1)状态下可以修改
        2、退货退款(applyTyp:2)：买家申请(returnMoneySts:1) ||
                               卖家处理(returnMoneySts:2) ||
                               买家发货(returnMoneySts:3)
                               状态下可以修改
        3、整单退款(refundType:1)：不可修改
      -->
      <text
        v-if="refundType !== 1 &&
              ((applyType == 1 && returnMoneySts == 1) ||
              (applyType == 2 &&
              (returnMoneySts == 1 ||
              returnMoneySts == 2 ||
              returnMoneySts == 3)))"
        class="undo-apply apply-current"
        @tap="modifyRefundAmount()"
      >
        {{i18n.modifyRefundAmount}}
      </text>
    </view>
  </view>

  <!-- 修改金额弹窗 -->
  <view v-if="isModifyRefundAmount" class="popup" @touchmove.stop.prevent="">
    <view class="popup-mask"></view>
    <view class="popup-con">
      <view class="pop-tit">
        <view class="tit">{{i18n.modifyRefundAmount}}</view>
        <view class="close" @tap="isModifyRefundAmount = false">×</view>
      </view>
      <view class="con modify-amount">
        <view class="int-box">
          <input
            v-model="modifyAmount"
            type="text"
            class="int"
            :placeholder="i18n.refundAmounts"
            @blur="handleAmountInput"
            @focus="handleAmountInput"
          >
          <view v-if="!refundAmountError" class="max-amount">{{i18n.maximumRefund + maxRefundAmount}}</view>
          <view v-if="refundAmountError" class="error">
            {{ modifyAmount && Number.parseFloat(modifyAmount) > maxRefundAmount ? i18n.maximumRefund + `${maxRefundAmount}` + i18n.pleaseRetype : i18n.refundAmounts }}
          </view>
        </view>
        <view class="foot-btn">
          <text class="confirm" @click="requestModify">{{i18n.confirmChanges}}</text>
        </view>
      </view>
    </view>
  </view>

</view>
</template>

<script>
// packageShop/pages/DetailsOfRefund/DetailsOfRefund.js
var http = require("@/utils/http.js");
import config from "@/utils/config.js";

export default {
  data() {
    return {
      //图片地址
      picDomain: config.picDomain,
      showView: true,
      refundType: 1, // 退款单类型（1:整单退款,2:单个物品退款）
      //隐藏是展示
      applyTime: '',
      //申请时间
      applyType: 1,
      //申请类型:1,仅退款,2退款退货
      goodsNum: '',
      //退货数量
      handelTime: '',
      //卖家处理时间
      orderAmount: '',
      //订单总金额
      orderItems: {},
      // 物流信息
      deliveryDto: {},
      //订单项
      refundAmount: '',
      //退款金额
      refundDelivery: [],
      //收货地址对象
      refundId: '',
      //记录ID
      refundTime: '',
      //退款时间
      returnMoneySts: 1,
      //处理退款状态:(1.买家申请 2.卖家接受 3.买家发货 4.卖家收货 5.退款成功 6.买家撤回申请 7.商家拒绝 -1.退款关闭)
      updateTime: '',
      //更新时间
      refundSn: '',
      //退款编号
      rejectMessage: '',
      //拒绝理由
      buyerReason: '',
      //退款原因
      buyerDesc: '', // 退款说明
      shopName: '', //店铺名称
      refundScore: '', // 积分
			refundProdReasonArray: [], // 退款原因
			sellerMsg: '',// 卖家备注
      // 退款凭证
      photoFiles: '',

      isModifyRefundAmount: false, // 修改金额弹窗
      modifyAmount: '', // 退款金额
      refundAmountError: false,
      // 最大可退款金额
      maxRefundAmount: 0
    };
  },

  components: {},
  props: {},
  computed:{
  	i18n() {
  		return this.$t('index')
  	},
    showRefundReasonAndDesc() {
      const reasonNumber = Number(this.buyerReason);
      if (isNaN(reasonNumber) || reasonNumber > 7 || reasonNumber < 0) {
        return false
      }
      return true
    },
    shopCustomerSwitch() {
        return this.$store.state.shopCustomerSwitch
      }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // [拍错/多拍/不喜欢','协商一致退款','商品破损/少件','商品与描述不符','卖家发错货','质量问题','其他']
		this.refundProdReasonArray.push(this.i18n.wrongShot, this.i18n.refundConsensus, this.i18n.damagedGoods,
    this.i18n.productNot, this.i18n.sellerSendsWrong, this.i18n.qualityProblem, this.i18n.other)
    this.setData({
      refundSn: options.refundSn
    }); //获取退款申请详情

    this.getRefundDetail();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
	  //头部导航标题
  	  uni.setNavigationBarTitle({
  	      title:this.i18n.refundDetails
  	  });},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},


  methods: {
    /**
     * 更多说明&收起
     */
    onChangeShowState: function () {
      var that = this;
      that.setData({
        showView: !that.showView
      });
    },

    /**
     * 获取退款申请详情
     */
    getRefundDetail: function (e) {
      var ths = this; // wx.showLoading({})

      var params = {
        url: "/p/orderRefund/info",
        method: "GET",
        data: {
          refundSn: this.refundSn //退款编号
        },
        callBack: function (res) {
          ths.setData({
            applyTime: res.applyTime,
            //申请时间
            applyType: res.applyType,
            //申请类型:1,仅退款,2退款退货
            goodsNum: res.goodsNum,
            //退货数量
            handelTime: res.handelTime,
            deliveryDto: res.deliveryDto,
            //卖家处理时间
            orderAmount: res.orderAmount,
            //订单总金额
            orderItems: res.orderItems,
            //订单项
            refundSn: res.refundSn,
            //退款编号
            refundAmount: res.refundAmount,
            //退款金额
            refundDelivery: res.refundDelivery,
            //收货地址对象
            refundId: res.refundId,
            //记录ID
            refundTime: res.refundTime,
            //退款时间
            returnMoneySts: res.returnMoneySts,
            //处理退款状态:(1.买家申请 2.卖家接受 3.买家发货 4.卖家收货 5.退款成功 6.买家撤回申请 7.商家拒绝 -1.退款关闭)
            updateTime: res.updateTime,
            //更新时间
            rejectMessage: res.rejectMessage,
            //拒绝理由
            buyerReason: res.buyerReason,
            //退款原因
            buyerDesc: res.buyerDesc, // 退款说明
            shopName: res.shopName,
            //积分数量
            refundScore: res.refundScore,
						sellerMsg: res.sellerMsg, // 卖家备注
            // 退款凭证
            photoFiles: res.photoFiles ? res.photoFiles.split(',') : '',
            // 退款单类型（1:整单退款,2:单个物品退款）
            refundType: res.refundType,
            // 最大可退款金额
            maxRefundAmount: Number.parseFloat(res.maxRefundAmount)
          });
          uni.hideLoading();
          
          if (ths.deliveryDto) {
             if (!res.deliveryDto.traces.length) { ths.deliveryDto.traceFlag = false }
             else { ths.deliveryDto.traceFlag = true}
          } else {
            ths.deliveryDto = {}
            ths.deliveryDto.traceFlag = false
          }

        }
      };
      http.request(params);
    },

    /**
     * 退款凭证 图片点击预览
     */
    refundPicPreView(e) {
      var current = e.currentTarget.dataset.cur
      uni.previewImage({
        current: current,
        urls: this.photoFiles
      })
    },

    /**
     * 撤销退货退款申请
     */
    cancelApplication: function (e) {
      uni.showModal({
        title: this.i18n.revoke,
        content: this.i18n.revokeTips,
        cancelText: this.i18n.cancel,
        confirmText: this.i18n.confirm,
        success: res => {
          if (res.confirm) {
            uni.showLoading({});
            var params = {
              url: "/p/orderRefund/cancel",
              method: "PUT",
              data: this.refundSn,
              //退款订单编号
              callBack: function (res) {
                uni.hideLoading()
                uni.redirectTo({
                  url: '/packageShop/pages/afterSales/afterSales'
                });
              }
            };
            http.request(params);
          } else {
          }
        }
      });
    },

    /**
     * 填写物流信息
     * isModify: 是否为修改物流
     */
    toLogisticsPage: function (refundSn, isModify) {
      uni.navigateTo({
        url: '/packageShop/pages/writeReturnLogistics/writeReturnLogistics?refundSn=' + refundSn + '&isModify=' + isModify
      });
    },

    /**
     * 跳转商品详情
     */
    toProdDetail(prodId) {
      this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodId }})
    },

    /**
     * 修改退款金额
     */
    modifyRefundAmount() {
      this.modifyAmount = ''
      this.refundAmountError = false
      this.isModifyRefundAmount = true
    },
    handleAmountInput() {
      this.refundAmountError = false
      if (!this.modifyAmount || !this.modifyAmount.trim()) {
        this.modifyAmount = this.modifyAmount.trim()
      }
      this.modifyAmount = this.checkInput(this.modifyAmount)
    },
    checkInput (num) {
      if (num) {
        var tmpVal = num.replace(/[^\d^\\.]/g, '')
        var reg = /^(0|([1-9]\d*))(\.\d{1,2})?$/ // 最多允许后输入两位小数
        if (!reg.test(tmpVal)) {
          tmpVal = tmpVal + ''
          tmpVal = tmpVal.substring(0, tmpVal.indexOf('.') + 3)
          // var n = (tmpVal.split('.')).length - 1
          // if (n > 1 || !/(?<!.)$/.test(tmpVal)) {
          //   tmpVal = tmpVal.substring(0, tmpVal.indexOf('.'))
          // }
        }
        return tmpVal
      } else {
        return ''
      }
    },
    requestModify() {
      if (!this.modifyAmount || !this.modifyAmount.trim() || this.modifyAmount <= 0) {
        this.modifyAmount = this.modifyAmount.trim()
        this.refundAmountError = true
        return
      }
      if (Number.parseFloat(this.modifyAmount) > this.maxRefundAmount) {
        this.refundAmountError = true
        return
      }
      uni.showLoading({});
      var params = {
        url: "/p/orderRefund/updateRefundAmount",
        method: "PUT",
        data: {
          refundAmount: this.modifyAmount,
          refundSn: this.refundSn
        },
        //退款订单编号
        callBack: res => {
          uni.hideLoading()
          uni.showToast({
            title: this.i18n.successfullyModified,
            duration: 1000
          })
          this.isModifyRefundAmount = false
          this.refundAmountError = false
          this.modifyAmount = ''
          this.getRefundDetail()
        }
      };
      http.request(params);
    }
  }
};
</script>
<style>
@import "./DetailsOfRefund.css";
</style>

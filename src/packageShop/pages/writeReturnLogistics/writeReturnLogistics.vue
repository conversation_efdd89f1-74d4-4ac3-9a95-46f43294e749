<template>
<!-- 填写退货物流 -->
<!--packageShop/pages/writeReturnLogistics/writeReturnLogistics.wxml-->

<view class="page">
  <!-- 物流公司 -->
  <!-- <picker class="picker-box" mode="selector" @change="chooseLogisticsCompany" :value="expressId" :range="deliveryList" range-key="dvyName">
    <view class="select-show select-box clearfix">
      <text class="select-text">{{i18n.logisticsCompany}}</text>
      <text class="select-results select-reason">{{deliveryList && deliveryList[expressId] ? deliveryList[expressId].dvyName : ''}}</text>
      <view class="right-arrow"><image :src="`${staticPicDomain}images/icon/more.png`"></image></view>
    </view>
  </picker> -->

  <view class="picker-box">
      <view class="select-show select-box clearfix" @tap="translate">
    <text class="select-text">{{i18n.logisticsCompany}}</text>
    <view class="select-results select-reason">{{expressName}}</view>

    <!-- 底部选择弹窗 -->
    <template v-if="showPicker">
      <view class="animation-element-wrapper" :animation="animation" :style="'visibility:' + (show ? 'visible':'hidden')"
        @tap.stop="hiddenFloatView">
        <view class="animation-element" @tap.stop="nono">
          <text class="right-bt" @tap.stop="hiddenFloatView">{{i18n.confirm}}</text>
          <view class="line"></view>
          <picker-view indicator-style="height: 50px;" @change="chooseLogisticsCompany" @tap.stop="nono">
            <picker-view-column>
              <view v-for="(item, index) in deliveryList" :key="index">{{item.dvyName}}</view>
            </picker-view-column>
          </picker-view>
        </view>
      </view>
    </template>
    <!-- 底部选择弹窗ENd -->

    <view class="right-arrow">
      <image :src="`${staticPicDomain}images/icon/more.png`"></image>
    </view>
  </view>
  </view>


  <!-- 退货信息 -->
  <view class="write-msg">
    <!-- 单号 -->
    <view class="item-num">
      <label class="item-num-tit">{{i18n.logisticsOrderNo}}</label>
      <input class="item-num-num" maxlength="20" :placeholder="i18n.logisticsOrderNoTips" :value="expressNo" @input="onExpressNoInput"></input>
    </view>
    <!-- 说明 -->
    <view class="item-num">
      <label class="item-num-tit">{{i18n.remarks}}</label>
      <input class="item-num-num refund-explain" :placeholder="i18n.optional200Words" maxlength="200" :value="senderRemarks" @input="onSenderRemarksInput"></input>
    </view>

    <!-- 上传照片 -->
    <view class="item-num">
      <label class="item-num-tit upload-tit">{{i18n.logisticsVouchers}}</label>
      <view class="upload-img">
        <view class="upload-tips">{{i18n.uploadPics}}</view>
        <!-- 预览缩略图 -->
          <block v-for="(imgItem, idx) in imgs" :key="idx">
            <view class="q-image-wrap">
              <!-- 图片缩略图  -->
              <image class="q-image" :src="imgItem.url" mode="aspectFill" :data-idx="idx"></image>
              <!-- 移除图片的按钮  -->
              <view class="q-image-remover" :data-idx="idx" @tap="removeImage"><image :src="`${staticPicDomain}images/icon/close2.png`"></image></view>
            </view>
          </block>
        <!-- 添加图片按钮 -->
        <view class="upload-btn" v-if="imgs.length < 5" @tap="getUploadImg">+</view>
      </view>
    </view>
    <!-- 上传照片end -->
  </view>
  <!-- 退货信息end -->


  <!-- 提交btn -->
  <view class="submit-btn" @tap="writeLogisticsMsg">{{i18n.submit}}</view>


</view>
</template>

<script>
import { picDomain } from '@/utils/config.js';
import util from '@/utils/util'
// packageShop/pages/writeReturnLogistics/writeReturnLogistics.js
var http = require("@/utils/http.js");
var t = 0;
var show = false;
var moveY = 200;

export default {
  data() {
    return {
      // 物流公司选择
      deliveryList: [],
      expressId: 0,
      //物流公司id
      expressName: '',
      //物流公司名称
      expressNo: '',
      //物流单号
      imgs: [],
      //图片凭证
      mobile: '',
      //手机号码
      refundSn: '',
      //退款编号名称
      senderRemarks: '', //备注信息
      animation: "",
      show: "",
      showPicker: false,
      // 是否为修改物流: 0否(填写物流); 1是(修改物流)
      isModify: 0

    };
  },

  components: {},
  props: {},

  computed: {
    i18n() {
      return this.$t('index')
    }
  },

  onHide: function() {
    this.showPicker = false
  },
  onReady: function() {
    this.animation = wx.createAnimation({
      transformOrigin: "50% 50%",
      duration: 0,
      timingFunction: "ease",
      delay: 0
    });
    this.animation.translateY(200 + 'vh').step();
    this.setData({
      animation: this.animation.export(),
      show: show
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      refundSn: options.refundSn,
      isModify: parseInt(options.isModify)
    });
    // 物流公司列表
    this.loadDeliveryData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    //头部导航标题
    uni.setNavigationBarTitle({
      title: this.i18n.fillInReturn
    });
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () { },
  methods: {
    /**
     * 获取物流信息(用于修改物流时回显)
     */
    getReturnLogistics() {
      const params = {
        url: '/p/orderRefund/info',
        method: 'GET',
        data: {
          refundSn: this.refundSn
        },
        callBack: res => {
          this.expressName = res.refundDelivery.deyName
          this.expressNo = res.refundDelivery.deyNu
          this.mobile = res.refundDelivery.mobile
          this.senderRemarks = res.refundDelivery.senderRemarks
          for (let i = 0; i < this.deliveryList.length; i++) {
            const element = this.deliveryList[i];
            if (element.dvyId === res.refundDelivery.deyId) {
              this.expressId = i
              break
            }
          }
          let imgs = []
          if (res.refundDelivery.imgs) {
            let tempImgs = res.refundDelivery.imgs.split(',')
            for (let i = 0; i < tempImgs.length; i++) {
              const element = tempImgs[i];
              imgs.push({
                url: picDomain + element,
                path: element
              })
            }
          }
          this.imgs = imgs
        },
        errCallBack: errMsg => {
          console.log(errMsg)
        }
      }
      http.request(params)
    },

    /**
     * 选择物流公司
     */
    chooseLogisticsCompany: function (e) {
      // console.log(e) 
      this.setData({
        expressId: this.deliveryList[e.detail.value].dvyId,
        expressName: this.deliveryList[e.detail.value].dvyName
      });
    },
    
    onExpressNoInput: function (e) {
      this.setData({
        expressNo: e.detail.value
      })
    },

    onSenderRemarksInput: function (e) {
      this.setData({
        senderRemarks: e.detail.value
      });
    },

    /**
     * 填写&提交物流信息
     */
    writeLogisticsMsg: function (e) {
      var reg = /^\s+$/g
      var r = /^[0-9a-zA-Z]+$/
      uni.showLoading();

      if (reg.test(this.expressNo) || reg.test(this.senderRemarks)) {
        wx.showToast({
          title: this.i18n.inputAllSpace,
          icon: "none"
        });
        return;
      }

      if ( !r.test(this.expressNo)  ) {
        wx.showToast({
          title: this.i18n.logisticsOrderNoSucced,
          icon: "none"
        });
        return;
      }

      if (this.expressNo.length == 0) {
        uni.showToast({
          icon: 'none',
          title: this.i18n.logisticsOrderNoTips
        });
      } else {
        var pics = '';
        this.imgs.forEach(function (item) {
          pics += item.path + ',';
        });

        if (pics != '') {
          pics = pics.substring(0, pics.length - 1);
        }
        var parmas = {
          url: this.isModify ? "/p/orderRefund/reSubmitExpress" : "/p/orderRefund/submitExpress",
          method: this.isModify ? "PUT" : "POST",
          data: {
            expressId: this.expressId,
            //物流公司id
            expressName: this.expressName,
            //物流公司名称
            expressNo: this.expressNo,
            //物流单号
            imgs: pics,
            //图片凭证
            mobile: this.mobile,
            //手机号码
            refundSn: this.refundSn,
            //退款编号名称
            senderRemarks: this.senderRemarks //备注信息

          },
          callBack: res => {
            uni.hideLoading()// 物流填写跳转页面
            uni.navigateTo({
              url: '/packageShop/pages/DetailsOfRefund/DetailsOfRefund?refundSn=' + this.refundSn
            });
            // uni.navigateBack({
            //   delta: 1
            // });
          }
        };
        http.request(parmas);
      }
    },

    /**
     * 上传图片
     */
    getUploadImg: function () {
      var ths = this;
      wx.chooseImage({
        count: 5,
        // 默认9
        sizeType: ['compressed'],
        sourceType: ['album'],
        success: function (res) {
          // 图片的本地临时文件路径列表
          var tempFilePaths = res.tempFilePaths;
          uni.showLoading({
            // #ifndef MP-TOUTIAO
            mask: true
            // #endif
          });
          tempFilePaths.forEach(item => {
            var params = {
              url: "/p/file/upload",
              filePath: item,
              name: 'file',
              callBack: function (res2) {
                uni.hideLoading()
                var img = {};
                img.path = JSON.parse(res2).filePath;
                img.url = JSON.parse(res2).resourcesUrl + JSON.parse(res2).filePath;
                var imgs = ths.imgs;
                imgs.push(img);
                ths.setData({
                  imgs: imgs
                });
              }
            };
            http.upload(params);
          })
        }
      });
    },

    /**
     * 删除图片
     */
    removeImage: function (e) {
      // const index = e.target.dataset.index
      // const idx = e.target.dataset.idx;
      var idx = e.currentTarget.dataset.idx;
      var imgs = this.imgs;
      imgs.splice(idx, 1);
      this.setData({
        imgs: imgs
      });
    },

    /**
     * 获取物流列表信息
     */

    loadDeliveryData() {
      uni.showLoading();
      http.request({
        url: "/p/delivery/list",
        method: "get",
        data: {},
        callBack: res => {
          uni.hideLoading()
          this.setData({
            deliveryList: res
          });
          if (this.isModify) {
            this.getReturnLogistics();
          }
        }
      });
    },

    //移动按钮点击事件
    translate: function(e) {
      if (t == 0) {
        moveY = 0;
        show = false;
        t = 1;
      } else {
        moveY = 200;
        show = true;
        t = 0;
      }
      this.setData({
        show: true,
        showPicker: true
      }); // this.animation.translate(arr[0], arr[1]).step();
      this.animationEvents(this, moveY, show);
	  if(!this.expressId) {
		const dvy = {
			detail: {
				value: [0]
			}
		}
		this.chooseLogisticsCompany(dvy)
	  }
    },

    //隐藏弹窗浮层
    hiddenFloatView(e) {
      //console.log(e);
      moveY = 200;
      show = true;
      t = 0;
      this.animationEvents(this, moveY, show);
    },

    //动画事件
    animationEvents: function(that, moveY, show) {
      //console.log("moveY:" + moveY + "\nshow:" + show);
      that.animation = wx.createAnimation({
        transformOrigin: "50% 50%",
        duration: 400,
        timingFunction: "ease",
        delay: 0
      });
      that.animation.translateY(moveY + 'vh').step();
      that.setData({
        animation: that.animation.export()
      });
    },

    nono(){}

  }
};
</script>
<style>
@import "./writeReturnLogistics.css";
</style>

<template>
	<!--pages/search-prod-show/search-prod-show.wxml-->
	<view class="container">

		<!-- 搜索框 -->
		<view class="fixed-box">
			<view class="search-bar">
				<view class="search-box">
					<input class="sear-input" :value="prodName" @input="getSearchContent" confirm-type="search" @confirm="toSearchConfirm"
					:placeholder="i18n.searchSllShop"
					/>
					<image :src="`${staticPicDomain}images/icon/search.png`" class="search-img"></image>
				</view>
				<view class="search-list-img" @tap="changeShowType">
					<image v-if="showType==1" :src="`${staticPicDomain}images/icon/search-col.png`"></image>
					<image v-if="showType==2" :src="`${staticPicDomain}images/icon/search-col2.png`"></image>
				</view>
			</view>
			<view class="tabs">
				<view class="tab-item complete" :class="{'text-brand': sortType==''}" @tap="onStsTap('')"">{{i18n.default}}</view>
				<view :class="'tab-item ' + (sortType=='sales' && (sts==2?'down':'up'))" @tap="onStsTap('sales')">{{i18n.sales}}</view>
				<view :class="'tab-item ' + (sortType=='price' && (sts==4?'down':'up'))" @tap="onStsTap('price')">{{i18n.price}}</view>
			</view>
		</view>

		<!-- 商品列表 -->
		<view class="prod-list">
			<!-- 纵向列表 -->
			<view class="prod-show" v-if="showType==1">
				<view class="hotsale-item-cont">
					<block v-for="(item, index) in searchProdList" :key="index">
						<prod :item="item" sts="6"></prod>
					</block>
				</view>
			</view>

			<!-- 横向列表 -->
			<view class="cont-item" v-if="showType==2">
				<block v-for="(item, index) in searchProdList" :key="index">
					<view class="show-item" @tap="toProdPage" :data-prodid="item.prodId">
						<view class="more-prod-pic">
							<image lazy-load :src="item.pic" class="more-pic"></image>
						</view>
						<view class="prod-text-right">
							<view class="prod-text more">{{item.prodName}}</view>
							<view class="cate-prod-info">{{item.commentNum}}{{i18n.evaluation}} {{item.positiveRating}}%{{i18n.praise}}</view>
							<view class="prod-price more">
								<text class="symbol">￥</text>
								<text class="big-num">{{parsePrice(item.price)[0]}}</text>
								<text class="small-num">.{{parsePrice(item.price)[1]}}</text>
							</view>
						</view>
					</view>
				</block>
			</view>
		</view>


		<!-- <view class="empty-tips" wx:if="{{searchProdList.length==0}}"> 暂无商品 </view> -->
		<!-- 空 -->
		<view class="empty" v-if="searchProdList.length==0">
			<view class="empty-icon">
				<image :src="`${staticPicDomain}images/icon/empty.png`"></image>
			</view>
			<view class="empty-text">{{i18n.noProducts}}</view>
		</view>
		<view class="tips" v-if="current == pages && current>1">{{i18n.allLoaded}}</view>

		<!-- 回到顶部 -->
    	<back-top-btn v-if="showBacktop"></back-top-btn>

      <n-loading />
	</view>
</template>

<script>
	// pages/search-prod-show/search-prod-show.js
	var http = require("../../../utils/http.js");
	import prod from "../../../components/production/production";
	import backTopBtn from "../../../components/backTopBtn/backTopBtn";

	export default {
		data() {
			return {
				sts: 21,
				showType: 2,
				searchProdList: [],
				prodName: "",
				shopId: undefined,
				current: 1,
				pages: 0,
				orderBy:1,//状态点击事件
				curLang: 'en', // 当前语言
				scrollTop: "",
				showBacktop: false,
				sortType: ''
			};
		},

		components: {
			prod,
			backTopBtn
		},
		props: {},

		computed:{
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			if (options.shopId) {
				this.setData({
					shopId: options.shopId
				});
			} else {
				this.setData({
					shopId: uni.getStorageSync("shopInfo").shopId
				});
			}

			this.setData({
				prodName: decodeURI(options.prodName),
				curLang: uni.getStorageSync('lang')
			});
			this.toLoadData();
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			//头部导航标题
			uni.setNavigationBarTitle({
			    title:this.i18n.storeSearchResults
			});
		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			// 店铺搜索结果分页
			if (this.current < this.pages) {
				this.current = this.current + 1
				this.toLoadData()
			}
		},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			// 页面滚动到指定位置指定元素固定在顶部
			onPageScroll: function (e) {
				this.scrollTop = e.scrollTop
				if (this.scrollTop > 80) {
					this.setData({
						showBacktop: true
					})
				} else if (this.scrollTop < 80) {
					this.setData({
						showBacktop: false
					})
				}
			},

			changeShowType: function() {
				var showType = this.showType;

				if (showType == 1) {
					showType = 2;
				} else {
					showType = 1;
				}

				this.setData({
					showType: showType
				});
			},
			//输入商品获取数据
			getSearchContent: function(e) {
				this.setData({
					prodName: e.detail.value
				});
			},
			//请求商品接口
			toLoadData: function() {
				var ths = this; //热门搜索

				var params = {
					// url: "/search/searchProdPage",
					url: "/search/page",
					method: "GET",
					data: {
						current: this.current,
						keyword: this.prodName,
						size: 10,
						sort: this.sts,
						orderBy: this.orderBy,
						shopId: this.shopId,
						isAllProdType: true,
						isActive: 1 // 过滤掉活动商品
					},
					callBack: function(res) {
						console.log(res)
						var temp = []
						if (params.data.current == 1) {
							temp = res.records[0].products
						} else {
							temp = ths.searchProdList.concat(res.records[0].products)
						}
						ths.setData({
							searchProdList: temp,
							pages: res.pages
						});
					}
				};
				http.request(params);
			},
			//当前搜索页二次搜索商品
			toSearchConfirm: function() {
				// 搜索栏为空时
				if(this.prodName === '' || !this.prodName.trim()){
					uni.showToast({
						title: this.i18n.inpKeyWords,
						icon:'none',
					})
					return
				}
				let recentSearch = wx.getStorageSync('recentSearch') || [];
				recentSearch = recentSearch.filter(item => item !== this.prodName);
				recentSearch.unshift(this.prodName);
				if (recentSearch.length > 10) {
					recentSearch.pop();
				}
				wx.setStorageSync('recentSearch', recentSearch);

				this.toLoadData();
			},

			/**
			 * 状态点击事件
			 * @param {String} sortType 排序类型
			 */
			onStsTap: function(sortType) {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				})
				// var sts = e.currentTarget.dataset.sts;
				// this.orderBy = this.sts == sts ? this.orderBy == 0 ? 1 : 0 : this.orderBy
				this.sortType = sortType
				if (!sortType) {
					this.sts = 0
				} else if (sortType=='sales') {
					this.sts = this.sts == 3 ? 2 : 3
				} else if (sortType=='price') {
					this.sts = this.sts == 5 ? 4 : 5
				}
				this.setData({
				  current: 1,
				  pages: 1
				});
				this.toLoadData();
			},
			toProdPage: function(e) {
				var prodid = e.currentTarget.dataset.prodid;
				// uni.navigateTo({
				// 	url: '/pages/prod/prod?prodid=' + prodid
				// });
				this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodid }})
			}
		}
	};
</script>
<style lang="scss" scoped>
/* pages/search-prod-show/search-prod-show.wxss */

page {
  background: #f4f4f4;
}
uni-page-wrapper {
	height: auto;
}

/* 搜索栏 */

.fixed-box {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 999;
  background: #fff;
}

.search-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: #777;
  background: #fff;
  z-index: 3;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

.search-bar .search-box {
  position: relative;
  height: 60rpx;
  background: #fff;
  z-index: 999;
  width: 80%;
  margin-right: 30rpx;
	border-bottom: 1px solid #999;
  flex: 1;
}

.sear-input {
  height: 60rpx;
  border: 0;
  margin: 0 30rpx 0 44rpx;
  line-height: 48rpx;
  vertical-align: top;
  background: #fff;
  font-size: 28rpx;
  color: #999;
}

.search-bar .search-hint {
  font-size: 28rpx;
  position: absolute;
  right: 30rpx;
  top: 31rpx;
  color: var(--primary-color);
}

.search-bar .search-box .search-img {
  width: 32rpx;
  height: 32rpx;
  position: absolute;
  top: 14rpx;
  display: block;
}

.search-bar .search-list-img {
  width: 40rpx;
  height: 40rpx;
  font-size: 0;
}

.search-bar .search-list-img image {
  width: 100%;
  height: 100%;
}

.fixed-box .tabs {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  padding: 10rpx 0;
  z-index: 999;
  background: #fff;
}

.fixed-box .tabs::after {
  content: '';
  background-color: #e1e1e1;
  left: 0;
  height: 1px;
  transform-origin: 50% 100% 0;
  bottom: 0;
  position: absolute;
  display: block;
  width: 100%;
}

.fixed-box .tabs .tab-item {
  position:relative;
  display: inline-block;
  width: 33.33%;
  text-align: center;
  font-size: 28rpx;
}

.fixed-box .tabs .tab-item.on {
  color: var(--primary-color);
}

/* 横向列表 */

.prod-show {
  display: block;
  background: #f4f4f4;
  margin-top: 210rpx;
}

/* .prod-show .prod-items {
  width: 375rpx;
  float: left;
  background: #fff;
  padding-bottom: 20rpx;
  box-sizing: border-box;
} */

/* 纵向列表 */

.prod-list .cont-item {
  padding: 0 20rpx 20rpx 20rpx;
  margin-top: 220rpx;
}

.prod-list .cont-item .show-item .more-prod-pic {
  text-align: center;
  width: 170rpx;
  height: 170rpx;
  font-size: 0;
}

.prod-list .cont-item .show-item .more-prod-pic .more-pic {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

.prod-list .cont-item .show-item {
  position: relative;
  display: flex;
  justify-content: flex-start;
  padding: 20rpx;
  border-radius: 20rpx;
  background: #fff;
  margin-bottom: 20rpx;
  box-shadow: 0 16rpx 32rpx 0 rgba(7, 17, 27, 0.05);
}

.prod-list .cont-item .show-item .prod-text-right {
  margin-left: 20rpx;
  width: 75%;
}

.prod-list .cont-item .show-item .prod-text-right .cate-prod-info {
  font-size: 22rpx;
  color: #999;
  margin: 10rpx 0 20rpx 0;
  padding: 0 15rpx;
}
.prod-info {
  font-size: 25rpx;
  padding: 0 15rpx;
  color: #888888;
}

.prod-list .cont-item .show-item .prod-text-right .go-to-buy {
  font-size: 26rpx;
  background: $wenet-color-brand;
  color: #fff;
  border-radius: 50rpx;
  width: 150rpx;
  text-align: center;
  padding: 8rpx 3rpx;
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
}

.prod-list .cont-item .show-item .prod-text-right .prod-text.more {
  margin: 0;
  height: 78rpx;
  font-size: 28rpx;
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #000;
}

.prod-list .cont-item .show-item .prod-text-right .prod-price.more {
  font-size: 28rpx;
  color: $wenet-color-brand;
  font-family: arial;
}

.empty-tips{
  color: #777;
  text-align: center;
  font-size: 26rpx;
  margin-top: 40rpx;
}
/* 列表为空 */
.empty {
  margin-top: 100rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 2em;
}


.tab-item.up, .tab-item.down {
  color: $wenet-color-brand;
}
.tab-item.down::after,.tab-item.up::after {
  content: " ";
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  left: 172rpx;
  top: 40rpx;
  border: 10rpx solid transparent;
}

.en.tab-item.down:first-child::after, .en.tab-item.up:first-child::after{
	left: 176rpx;
}

.tab-item.down::after {
  border-top: 10rpx solid $wenet-color-brand;
}

.tab-item.up::after {
  top: 26rpx;
  border-bottom: 10rpx solid $wenet-color-brand;
}

.tips {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}

</style>

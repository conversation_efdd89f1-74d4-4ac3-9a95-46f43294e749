/* 物流信息 */
page {
  display: block;
  width: 100%;
  background: #f7f8fa;
}

.package-list {
  position: fixed;
  left: 0;
  top: 0;
  background: #fff;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  width: 100%;
  z-index: 9;
  white-space: nowrap;
  border-bottom: 2rpx solid #f4f4f4;
  box-sizing: border-box;
}
.package-list .package-item {
  text-align: center;
  line-height: 74rpx;
}

.package-list-flex {
  display: flex;
  justify-content: center;
}
.package-list-flex .package-item {
  flex-grow: 1;
}
.package-list-flex .package-item .nav-txt {
  line-height: 74rpx;
  display: inline-block;
  box-sizing: border-box;
}

.package-overlength {
  overflow-x: scroll;
}
.package-overlength .package-item {
  display: inline-block;
  padding: 0 18rpx;
  margin: 0 20rpx;
  box-sizing: border-box;
}
.package-overlength .package-item .nav-txt {
  padding-top: 6rpx;
  line-height: 74rpx;
}
.package-info {
	margin-top: 20rpx;
}

.active {
  border-bottom: 6rpx solid var(--primary-color);
  color: var(--primary-color);
}


/* 包裹信息 */
.package-prod {
  background: #fff;
  display: block;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.mt100 {
  margin-top: 100rpx;
}

/* 单个包裹 */
.single-package {
  display: flex;
  flex-direction: column;
  margin: 10rpx 0;
}
.prod-pic {
  position: relative;
  display: inline-block;
  width: 130rpx;
  height: 130rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  margin-top: 5px;
}
image.prod-pic {
  display: block;
  background: #e6f8fc;
}
.prod-con {
	width: 100%;
  display: block;
  font-size: 24rpx;
}
.prod-status {
  font-size: 28rpx;
  /* font-weight: bold; */
  color: var(--primary-color);
  line-height: 1.8em;
}
.carrier {
  display: flex;
  align-items: center;
}
.prod-carrier,
.waybill-num {
  color: #666;
  line-height: 1.6em;
	word-break: break-word;
	max-width: 80%;
}
.copy-btn {
  margin-left: 24rpx;
  padding: 6rpx 14rpx;
  border: 1rpx solid #aaa;
  line-height: 1em;
  box-sizing: border-box;
  font-size: 22rpx;
  color: #777;
  border-radius: 6rpx;
}

/* 多个包裹 */
.prod-item {
  margin: 20rpx 0;
}
.prod-item-pic {
  position: relative;
  display: inline-block;
  width: 130rpx;
  height: 130rpx;
  border-radius: 12rpx;
  margin-right: 13rpx;
  margin-bottom: 16rpx;
}
.quantity {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 20rpx;
  padding: 0 10rpx;
  height: 34rpx;
  line-height: 34rpx;
  text-align: center;
  background: rgba(0, 0, 0, .3);
  border-radius: 12rpx 0 12rpx 0;
  color: #fff;
}


/* 物流信息 */
.logistics-info {
  display: block;
  background: #fff;
  padding: 20rpx 0;
}
.logistics-info .logistics-box .logistics {
  padding-bottom: 20rpx;
}

.logistics-info .logistics-box .logistics .item {
  margin-left: 40rpx;
  margin-right: 40rpx;
  font-size: 24rpx;
  border-left: 2rpx solid #ccc;
  position: relative;
  padding-left: 40rpx;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

.logistics-info .logistics-box .logistics .item:first-child::before {
  position: absolute;
  display: block;
  content: " ";
  font-size: 0;
  width: 28rpx;
  height: 28rpx;
  background: #fff;
  top: 0;
  left: -14rpx;
}

.logistics-info .logistics-box .logistics .item .time {
  position: relative;
}

.logistics-info .logistics-box .logistics .item .time::before {
  position: absolute;
  display: block;
  content: " ";
  font-size: 0;
  border-radius: 50%;
  top: 4rpx;
  left: -54rpx;
  width: 10rpx;
  height: 10rpx;
  background: #ccc;
  border: 8rpx solid #fff;
}

.logistics-info .logistics-box .logistics .item:first-child .time::before {
  background: #e43130;
  border-color: #fed1d4;
}

.logistics-info .logistics-box .logistics .item .des {
  color: #999;
  margin-top: 10rpx;
}

/* 物流为空 */
.logistics-info-empty {
  text-align: center;
  padding: 10rpx;
  font-size: 28rpx;
  color: #666;
}

/* 包裹信息为空 */
.expresses-empty {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  margin-top: 100rpx;
}

/* pages/openAShop/openAShop.wxss */
image {
  width: 100%;
  height: 100%;
}
.red {
  color: red;
}
.uni-input-placeholder{
	font-size: 28rpx;
}
.uni-textarea-placeholder{
	font-size: 28rpx;
}
.apple-for-ashop {
  background: #f7f7f7;
  width: 100%;
  height: 100%;
  overflow: auto;
}
.msg-box {
  display: block;
  background: #fff;
  margin-bottom: 15rpx;
  width: 100%;
  /* height: 100%; */
}
.msg-tit {
  padding: 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.shop-icon {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
  vertical-align: middle;
}
.shop-icon > image {
  display: block;
  width: 100%;
  height: 100%;
}
.shop-msg-tit {
  display: inline-block;
  font-size: 32rpx;
  font-weight: bold;
  vertical-align: middle;
}
.msg-cont-box {
  font-size: 28rpx;
  padding: 25rpx 0;
  margin: 0 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.cont-txt {
  display: inline-block;
  margin-right: 50rpx;
  vertical-align: middle;
}
.cont-int {
  display: inline-block;
  vertical-align: middle;
  width: 70%;
}
.align-top {
  vertical-align: top;
}
.cont-textarea {
  box-sizing: border-box;
  vertical-align: top;
  line-height: 1.5em;
  border: 1rpx solid #ececec;
  border-radius: 8rpx;
  padding: 10rpx 15rpx;
}

/* 地址 */
.addr {
  position: relative;
}
.pca {
  display: inline-block;
  width: 70%;
  /* padding: 0 20rpx; */
  vertical-align: middle;
}
.arrow {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 25rpx;
  height: 25rpx;
}
.arrow image {
  width: 100%;
  height: 100%;
  vertical-align: top;
}
.animation-element-wrapper {
  display: flex;
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}
.animation-element {
  display: flex;
  position: fixed;
  width: 100%;
  height: 470rpx;
  bottom: 0;
  background-color: rgba(255, 255, 255, 1);
}
.right-bt {
  right: 20rpx;
  top: 20rpx;
  position: absolute;
  width: 80rpx !important;
}
.line {
  display: block;
  position: fixed;
  height: 2rpx;
  width: 100%;
  margin-top: 89rpx;
  background-color: #eee;
}
picker-view {
  background-color: white;
  padding: 0;
  width: 100%;
  height: 380rpx;
  bottom: 0;
  position: fixed;
}
picker-view-column view {
  vertical-align: middle;
  font-size: 28rpx;
  line-height: 28rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}


/* 经纬度 */
.show-location {
  display: inline-block;
  vertical-align: middle;
  font-size: 30rpx;
  line-height: 1.5em;
  width: 45%;
}
.map-btn {
  float: right;
  color:#ffffff;
  background: #e43130;
  font-size: 25rpx;
  line-height: 1em;
  padding: 18rpx 15rpx 20rpx 15rpx;
  border-radius: 8rpx;
  vertical-align: middle;
}
/* 清除浮动 */
.clearfix::after {
  display: block;
  height: 0;
  overflow: hidden;
  content: '';
  clear: both;
}


/* 上传 */
.upload-files {
  display: inline-block;
  vertical-align: top;
}
.upload-tips {
  font-size: 26rpx;
  color: #888888;
  line-height: 2em;
  margin-bottom: 15rpx;
}
/* 预览 */
.preview-img-box {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: inline-block;
  margin-bottom: 15rpx;
  background: #eee;
}
.preview-logo{
  width: 120rpx;
  height: 120rpx;
}
image.preview-img {
  width: 100%;
  height: 100%;
}
.preview-img-del {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  width: 28rpx;
  height: 28rpx;
}
.card-upload {
  display: inline-block;
  margin-right: 50rpx;
}
.card-txt {
  font-size: 24rpx;
  text-align: center;
  color: #777;
}
.upload-icon {
  display: inline-block;
  width: 120rpx;
  height: 120rpx;
  line-height: 110rpx;
  text-align: center;
  border: 1rpx solid #e0e0e0;
  vertical-align: top;
  font-size: 90rpx;
  color: #ddd;
  margin-bottom: 15rpx;
}

/* 提交申请 */
.apply-btn {
  padding: 100rpx 30rpx 150rpx 30rpx;
}
.btn {
  font-size: 30rpx;
  text-align: center;
  background: #e43130;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  color: #fff;
  border-radius: 8rpx;
}
.foot-tips {
  font-size: 28rpx;
  color: #aaa;
  /* padding: 0 20rpx; */
  line-height: 1.5em;
  margin-top: 20rpx;
}
.audit-remark{
  font-size: 26rpx;
  background: #ffffcd;
  color:#333;
  padding:20rpx;
	word-break:break-all;
}

.container{
	height: 100vh;
	padding-bottom: 0;
}

.map{
	height: 100%;
}

.goOut{
	background-color: #fff;
	text-align: center;
}
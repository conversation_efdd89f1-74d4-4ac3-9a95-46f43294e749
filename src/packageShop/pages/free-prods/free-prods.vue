<template>
	<view class="shop-feature" style="padding-bottom: 80rpx;">
    <view v-if="!!currentShop">
      <feature ref="featureIndex" @pageLoaded="pageLoaded" :pageLoad="pageLoad" :pageId="currentShop.freePageId" :shopId="currentShop.shopId" :pageScorllTop="pageScorllTop"></feature>
    </view>
	</view>
</template>

<script>
	import feature from '@/components/feature/index/index'
	export default {
		data() {
			return {
				pageLoad: false,
				pageScorllTop: 0, // 页面滚动距离
			}
		},
		components: {
			feature,
		},
		mounted() {
		},
		onLoad: function() {
		},
    computed: {
      currentShop() {
        return this.$store.state?.currentShop
      }
    },
    watch: {
      currentShop: {
        handler: function(val, oldVal) {
          if (val?.shopId) {
            this.$refs.featureIndex?.getPageInfoById()
          }
        },
        deep: true
      }
    },
		onPageScroll: function(e) {
			this.pageScorllTop = e.scrollTop
		},
		onPullDownRefresh: function() {
			setTimeout(()=> {
				this.$nextTick(() => {
					this.$refs.featureIndex.getPageInfoById()
				})
				uni.stopPullDownRefresh(); //停止下拉刷新
			}, 100);
		},
		methods: {

			// 页面加载回调
			pageLoaded(e) {
				uni.setNavigationBarTitle({
					title: e.detail.title
				})
				// this.pageLoaded = false
				setTimeout(() => {
					uni.hideLoading()
					// this.pageLoaded = true
				}, 1)
			}
		}
	}
</script>

<style>

</style>

<template>
<!-- packageShop/pages/stationOrderList/stationOrderList.wxml -->
<view class="container">
  <view class="station-order-list" :class="{'bigger-bottom': curAppType !== appType.H5}">
    <view class="main">
      <!-- 订单列表 -->
      <block v-for="(item, shopIndex) in stationList" :key="item.orderNumber">
        <view class="prod-item">
          <!-- 店铺 -->
          <!-- <view class="shop-box">
            <view class="shop-icon">
              <image :src="`${staticPicDomain}images/icon/shop.png`"></image>
            </view>
            <view class="shop-name">{{item.shopName}}</view>
          </view> -->
          <!-- 订单编号 -->
          <view class="order-num">
            <checkbox color="#eb2444" class="check" @tap="orderRadioChange(shopIndex)" :data-shopindex="shopIndex" :checked="item.checked"></checkbox>
            <text>{{i18n.orderNumber}}：{{item.orderNumber}}</text>
            <view class="order-state">
              <text :class="['order-sts' , {red: (item.status < 5) }]">{{['',i18n.toBePaid,i18n.goodsBePicked,i18n.pendingWriteOffs,i18n.toBeEvaluated,i18n.completed,i18n.cancelled,i18n.inAGroup][item.status]}}</text>
              <view class="clear-btn" v-if="item.status==5 || item.status==6">
                <image :src="`${staticPicDomain}images/icon/clear-his.png`" class="clear-list-btn" @tap="delOrderList" :data-ordernum="item.orderNumber"></image>
              </view>
            </view>
          </view>
          <!-- 商品列表 -->
          <!-- 一个订单单个商品的显示 -->
          <block v-for="(prod, index) in item.orderItemDtos" :key="index">
            <view class="item-cont" @tap="toOrderDetailPage" :data-ordernum="item.orderNumber">
              <view class="prod-pic">
                <image :src="prod.pic"></image>
              </view>
              <view class="prod-info">
                <view class="prodname">{{prod.prodName}}</view>
                  <view class="prod-info-cont">{{prod.skuName}}</view>
                  <view class="price-nums">
                    <text class="prodprice">
                      <text class="symbol" v-if="item.actualTotal">￥</text>
                      <text class="big-num" v-if="item.actualTotal">{{parsePrice(prod.price)[0]}}</text>
                      <text class="small-num" v-if="item.actualTotal">.{{parsePrice(prod.price)[1]}}</text>
                      <text class="small-num" v-if="item.actualTotal && item.orderType==3" decode="true">&emsp;+&emsp;</text>
                      <text class="small-num" v-if="prod.useScore && item.orderType==3">{{prod.useScore}} {{i18n.integral}}</text>
                    </text>
                    <text class="prodcount">× {{prod.prodCount}}</text>
                  </view>
                </view>
            </view>
          </block>
          <view class="total-num">
            <view class="spell-group-icon" v-if="item.orderType">
            <!-- orderType 订单类型(0普通订单 1团购订单 2秒杀订单) -->
            {{['',i18n.aGroup,i18n.spike,i18n.integral][item.orderType]}}
            </view>
            <view class="spell-group-icon">{{item.orderMold === 1 ? i18n.virtualGoods : i18n.SelfPickupGoods}}</view>
            <view class="right">
              <view class="prodcount">{{i18n.inTotal}}{{item.productNums}}{{i18n.items}}</view>
              <view class="prodprice">{{i18n.total}}：<text class="symbol" v-if="item.actualTotal">￥</text>
                <text class="big-num" v-if="item.actualTotal">{{parsePrice(item.actualTotal)[0]}}</text>
                <text class="small-num" v-if="item.actualTotal">.{{parsePrice(item.actualTotal)[1]}}</text>
                <text class="small-num" v-if="item.actualTotal && item.orderType==3">+</text>
                <text class="small-num" v-if="item.orderType==3">{{item.orderItemDtos[0].useScore}} {{i18n.integral}}</text>
              </view>
            </view>
          </view>
          <!-- end 商品列表 -->
        </view>
      </block>

    </view>

    <!-- 到底了~ -->
    <view class="loadall" v-if="stationList.length > 5 && loadAll">{{i18n.endTips}}</view>

    <!-- 空 -->
    <view class="empty" v-if="!stationList.length">
      <view class="empty-icon">
        <image :src="`${staticPicDomain}images/icon/empty.png`"></image>
      </view>
      <view class="empty-text">{{i18n.noOrderTips}}</view>
    </view>
  </view>


  <!-- 提交按钮 -->
  <view class="foot">
    <view
      class="confirm"
      :class="{'grey': !orderNumbers.length}"
      @tap="confirmRemoval"
      v-if="stationList.length">{{i18n.confirmPickUp}}</view>
  </view>
</view>
<!-- end 订单列表 -->
</template>

<script>
var http = require("@/utils/http.js");
import { AppType } from '@/utils/constant'
export default {
  data() {
    return {
      stationList: [],
      stationId: 2,
      orderNumbers: [], //选中的订单

      // 分页
      current: 1,
      size: 10,
      pages: 0,
      total: 0,
      loadAll: false, // 加载完成

      timer: '',

      appType: AppType,
      // 当前设备类型
      curAppType: uni.getStorageSync('appType'),
    };
  },

  components: {},
  props: {},

  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
		options = { ...this.$Route.query }
    if (options.stationId) {
      this.stationId = decodeURI(options.stationId)
    }
  },

  /**
  * 生命周期函数--监听页面显示
  */
  onShow: function () {
	  //头部导航标题
	  uni.setNavigationBarTitle({
	      title:this.i18n.pickUpOrderList
	  });

    this.loadStationOrderData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    if(this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.current < this.pages) {
      this.current += 1
			this.loadStationOrderData();
		} else {
			this.setData({
				loadAll: true
			})
		}
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    /**
     * 加载自提点订单数据
     */
    loadStationOrderData: function () {
      uni.showLoading(); //加载自提点订单列表
      var params = {
        url: "/p/myStationOrder/getOrderBystationId",
        method: "GET",
        data: {
          stationId: this.stationId,
          current: this.current,
          size: this.size
        },
        callBack: (res) => {
          uni.hideLoading()
          var list = [];
					if (res.current == 1) {
						list = res.records;
					} else {
						list = this.stationList.concat(res.records)
					}
          this.stationList = list
          this.pages = res.pages
          this.current = res.current
          // 清空选中的订单编号
          this.orderNumbers = ''
        },
				errCallBack: err => {
          uni.hideLoading();
					console.log(err)
					uni.showModal({
						title: this.i18n.tips,
						content: this.i18n.stationNotFound,
						confirmText: this.i18n.confirm,
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								uni.switchTab({
									url: '/pages/user/user'
								})
							}
						}
					})
          // 清空列表
					this.stationList = []
          // 清空选中的订单编号
          this.orderNumbers = ''
				}
      };
      http.request(params);
    },

    /**
     * 查看订单详情
     */
    toOrderDetailPage: function (e) {
      // uni.navigateTo({
      //   url: '/pages/order-detail/order-detail?orderNum=' + e.currentTarget.dataset.ordernum
      // });
      this.$Router.push({
        path:'/pages/order-detail/order-detail', query: {orderNum:e.currentTarget.dataset.ordernum}
      })
    },

    /**
     * 订单选择
     */
    orderRadioChange: function(shopIndex) {
      var stationList = this.stationList
      let checked = stationList[shopIndex].checked
      stationList[shopIndex].checked = !checked; // 改变状态
      let orderNumbers = []
      this.stationList.forEach((el) => {
        if(el.checked) {
          orderNumbers.push(el.orderNumber)
        }
      })
      this.orderNumbers = orderNumbers.join()
    },

    /**
     * 一键核销（整单）
     */
    confirmRemoval: function() {
      var orderNumbers = this.orderNumbers
      if (!orderNumbers) {
        uni.showToast({
          title: this.i18n.SelectOrderPickup,
          icon: 'none',
          duration: 1500,
        })
        return
      }
      uni.showModal({
        title: this.i18n.tips,
        content: this.i18n.confirmPickUpTips,
        cancelText: this.i18n.cancel,
        confirmText: this.i18n.confirm,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading();
            var params = {
              url: "/p/myStationOrder/orderStationByOrderNumber",
              method: "PUT",
              data: {
                orderNumbers,
                stationId: this.stationId
              },
              callBack: () => {
                uni.hideLoading()
                uni.showToast({
                  title: this.i18n.pickUpSuccessfully,
                  icon: 'none',
                  duration: 1500,
                })
                this.timer = setTimeout(() => {
                  this.current = 1
                  this.loadStationOrderData()
                }, 500)
              }
            };
            http.request(params);
          } else if (res.cancel) {
            // console.log('用户点击取消');
          }
        }
      });
    },

  }
};
</script>
<style>
@import "./stationOrderList.css";
</style>

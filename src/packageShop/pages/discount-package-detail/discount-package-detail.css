page{
 background: #F4F4F4;
}
.combo-detail {
  height: 100%;
}

.main-prod {
  margin-bottom: 20rpx;
}

.combo-prod-item {
  padding: 30rpx;
  background: #fff;
  display: flex;
  box-sizing: border-box;
}
.prod-info {
  display: flex;
  width: 100%;
}
.info-row {
  width: 100%;
  display: flex;
  align-items: center;
}
.info-row .btn {
  width: 36rpx;
}
.info-row .pic {
  margin: 0 24rpx 0 20rpx;
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
}

.info-row .pic image {
  width: 100%;
  height: 100%;
}

.info-row .opt {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.info-row .opt .prod-name {
  display: -webkit-box;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.info-row .opt .prod-sku-con {
  font-size: 24rpx;
}

.info-row .opt .prod-sku-con .prod-sku-text {
  color: #999;
}

.info-row .opt .prod-sku-con .prod-sku-text.arrow {
  background: #F9F9F9;
  padding: 6rpx 40rpx 6rpx 10rpx;
  border-radius: 30rpx;
  position: relative;
}

.info-row .opt .prod-sku-con .prod-sku-text.arrow::after {
  content: "";
  width: 10rpx;
  height: 10rpx;
  border: solid #999;
  border-width: 2rpx 0 0 2rpx;
  -webkit-transform: translate(-50%, -50%) rotate(225deg);
  transform: translate(-50%, -50%) rotate(225deg);
  position: absolute;
  right: 10rpx;
  top: 22rpx;
}
.info-row .opt .price-count {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.info-row .opt .price-count  .combo-count {
  font-size: 26rpx;
  color: #999999;
}

.match-prod-con {
  background: #fff;
}

/* 底部 */
.combo-footer {
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  width: 100%;
  background-color: #fff;
  box-shadow: 0px -8rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.combo-price-reduce {
  flex: 3;
  display: flex;
  flex-direction: column;
}

.combo-price-reduce .combo-price {
  font-family: PingFang SC;
  font-weight: bold;
}
.combo-price-reduce .combo-price .combo-price-title {
  color: #000;
  font-size: 24rpx;
}
.combo-price-reduce .combo-price .price {
  font-size: 24rpx;
  color: #E43130;
}
.combo-price-reduce .combo-price .price .big {
  font-size: 32rpx;
}
.combo-price-reduce .combo-reduce {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #999999;
}

.btn-con {
  flex: 5;
  display: flex;
  align-items: center;
}

.btn-con .btn {
  flex: 1;
  border-radius: 50rpx;
  text-align: center;
  color: #fff;
  font-size: 26rpx;
  padding: 24rpx 0;
}

.btn-con .btn.add-cart {
  background-color: #564E5F;
  margin-right: 10rpx;
}

.btn-con .btn.buy-now {
  background-color: #D2423A;
  margin-left: 10rpx;
}

/* 套装整体信息弹窗 */
.pup-sku {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.pup-sku-header {
  position: relative;
  font-size: 16px;
  color: #333;
  padding: 20rpx 20rpx;
  background-color: #fff;
  padding-top: 35rpx;
	border-radius: 20rpx 20rpx 0 0;
}

.close {
  color: #aaa;
  border-radius: 12px;
  line-height: 20px;
  text-align: center;
  height: 20px;
  width: 20px;
  font-size: 18px;
  padding: 1px;
  top: 10px;
  right: 10px;
  position: absolute;
}

.close::before {
  content: '\2716';
}

.pup-sku-main {
  position: absolute;
  bottom: 0;
  padding-bottom: calc(110rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
  width: 100%;
  background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
}
.pup-sku-body {
  height: 424rpx;
}
/* 套装商品 */
.pup-sku-body .combo-prod-list {
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
}

.combo-prod-list .plus-icon {
  display: inline-flex;
  align-items: center;
}

.pup-sku-body .combo-prod-list .pup-combo-item {
  position: relative;
  width: 160rpx;
  display: inline-flex;
  flex-direction: column;
  font-size: 22rpx;
  font-weight: 400;
  color: #000000;
  padding-right: 40rpx;
}
.pup-sku-body .combo-prod-list .pup-combo-item:last-child {
  overflow: hidden;
  padding: 0;
}

.pup-sku-body .combo-prod-list .plus-icon {
  display: inline-flex;
}

.pup-combo-item .pup-combo-name {
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  margin-bottom: 10rpx;
  text-align: center;
}

.pup-combo-item .pup-combo-pic {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  padding-right: 40rpx;
  position: relative;
}
.pup-combo-item .pup-combo-pic::after {
  content: ' + ';
  font-size: 32rpx;
  color: #999;
  font-weight: bold;
  position: absolute;
  top: 60rpx;
  right: 10rpx;
}

.pup-combo-item .pup-combo-pic:last-child::after {
  display: none;
}

.pup-combo-item .pup-combo-pic image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
}


.pup-combo-item  .pup-combo-sku {
  color: #999;
  overflow: hidden;
  position: relative;
}
.pup-combo-item  .pup-combo-sku::after {
  content: ' ';
  position: absolute;
  right: 0;
  top: 14rpx;
  width: 10rpx;
  height: 10rpx;
  border: solid #999;
  border-width: 2rpx 0 0 2rpx;
  -webkit-transform: translate(-50%, -50%) rotate(225deg);
  transform: translate(-50%, -50%) rotate(225deg);
}

.pup-combo-item  .pup-combo-sku .pup-combo-sku-text {
  width: calc(100% - 24rpx);
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-align: center;
}

/* 套装数量 */
.pup-sku-count {
  padding: 0 10px 13px;
  font-size: 12px;
  margin-top: 20rpx;
}

.pup-sku-count .count-name {
  height: 31px;
  line-height: 31px;
  width: 100rpx;
  color: #000;
  font-weight: bold;
}

.pup-sku-count .num-wrap {
  position: relative;
  z-index: 0;
  width: 110px;
  float: right;
  vertical-align: middle;
  display: flex;
}


.num-wrap .minus,
.num-wrap .plus {
  position: relative;
  max-width: 30px;
  min-width: 30px;
  height: 30px;
  line-height: 30px;
  background: #f7f7f7;
  text-align: center;
}

.num-wrap .minus {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.num-wrap .plus {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.num-wrap .row {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -7px;
  margin-top: -1px;
  width: 14px;
  height: 2px;
  background-color: #ccc;
}

.num-wrap .col {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -1px;
  margin-top: -7px;
  width: 2px;
  height: 14px;
  background-color: #999;
}

.pup-sku-count .text-wrap {
  position: relative;
  width: 45px;
  z-index: 0;
  margin: 0 1px;
}

.pup-sku-count .text-wrap input {
  height: 30px;
  width: 100%;
  color: #333;
  background: #fff;
  font-size: 12px;
  text-align: center;
  border: none;
  background: #f7f7f7;
}

.pup-sku-footer {
  position: fixed;
  bottom: 0;
  bottom: constant(safe-area-inset-bottom);
  bottom: env(safe-area-inset-bottom);
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  align-items: center;
  height: 110rpx;
  z-index: 999;
  /* box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05); */
}

.pup-sku-footer .btn {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  font-size: 30rpx;
  flex-flow: column;
  border-radius: 60rpx;
  height: 2.8em;
  width: 45%;
  margin: 0 15rpx;
}


.pup-sku-footer .btn.buy {
  background: #e43130;
  color: #fff;
}

<template>
  <view v-if="loaded" class="combo-detail">
    <view class="main-prod">
      <view class="combo-prod-item">
        <view class="prod-info">
          <view class="info-row">
            <view class="btn">
              <label>
                <checkbox
                  :checked="mainProd.type===1 || mainProd.checked"
                  :disabled="mainProd.type===1"
                  :color="mainProd.type===1 ? '#E89F9B' : '#E43130'">
                </checkbox>
              </label>
            </view>
            <view class="pic">
              <image :src="mainProd.pic" mode="aspectFit"></image>
            </view>
            <view class="opt">
              <view class="prod-name">{{mainProd.prodName}}</view>
              <view class="prod-sku-con">
                <text :class="['prod-sku-text', mainProd.defaultSku.skuName ? 'arrow' : '' ]" @tap.stop="handleShowSkuPop(mainProd)">{{ mainProd.defaultSku.skuName || '' }}</text>
              </view>
              <view class="price-count">
                <view class="price">
                  <text class="symbol">￥</text>
                  <text class="big-num">{{parsePrice(mainProd.defaultSku.matchingPrice)[0]}}</text>
                  <text class="small-num">.{{parsePrice(mainProd.defaultSku.matchingPrice)[1]}}</text>
                </view>
                <!-- 套装内的商品的数量 -->
                <view class="combo-count"> x {{ mainProd.leastNum * comboNum }}</view>
              </view>
            </view>
          </view>
          <!-- 赠品展示 -->
          <!-- <view v-if="prod.giveaway" class="gift-con">
            <view v-for="(giveawayItem, giveawayIndex) in prod.giveaway.giveawayProds" class="gift-item" @tap.stop="toProdPage(giveawayItem.prodId)">
              <view class="gift-name">【赠品】{{ giveawayItem.prodName }}</view>
              <view class="gift-count">x {{ giveawayItem.giveawayNum }} </view>
            </view>
          </view> -->
        </view>
      </view>
    </view>

    <view class="match-prod-con">
      <view class="combo-prod-item" v-for="(matchingProd, matchingProdIndex) in comboInfo.matchingProds" :key="matchingProdIndex">
        <view class="prod-info">
          <view class="info-row">
            <view class="btn">
              <label>
                <checkbox @tap.stop="handleCheckProdItem(matchingProd, matchingProdIndex)"
                  :value="toString(matchingProd.prodId)"
                  :checked="matchingProd.checked || matchingProd.required===1"
                  :disabled="matchingProd.required===1"
                  :color="matchingProd.type===1 ? '#E89F9B' : '#E43130'">
                </checkbox>
              </label>
            </view>
            <view class="pic" v-if="matchingProd">
              <image :src="matchingProd.pic" mode="aspectFit"></image>
            </view>
            <view class="opt">
              <view class="prod-name">{{matchingProd.prodName}}</view>
              <view class="prod-sku-con">
                <text :class="['prod-sku-text', matchingProd.defaultSku.skuName ? 'arrow' : '' ]" @tap.stop="handleShowSkuPop(matchingProd)">{{ matchingProd.defaultSku.skuName || '' }}</text>
              </view>
              <view class="price-count">
                <view class="price">
                  <text class="symbol">￥</text>
                  <text class="big-num">{{parsePrice(matchingProd.defaultSku.matchingPrice)[0]}}</text>
                  <text class="small-num">.{{parsePrice(matchingProd.defaultSku.matchingPrice)[1]}}</text>
                </view>
                <!-- 套装内的商品的数量 -->
                <view class="combo-count"> x {{ matchingProd.leastNum * comboNum }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="combo-footer">
      <view class="combo-price-reduce">
        <view class="combo-price">
          <text class="combo-price-title">{{i18n.packagePrice}}: </text>
          <text class="price" decode="true">￥
            <text class="big">{{parsePrice(comboAmountInfo.matchingPrice)[0]}}.</text>
            {{parsePrice(comboAmountInfo.matchingPrice)[1]}}
          </text>
        </view>
        <view class="combo-reduce">
          {{i18n.savesYou}}: ￥{{parsePrice(comboAmountInfo.reduceAmount)[0]}}.{{parsePrice(comboAmountInfo.reduceAmount)[1]}}
        </view>
      </view>
      <view class="btn-con">
        <view class="btn add-cart" @tap="handleShowComboInfo(0)">{{i18n.addShoppingCart}}</view>
        <view class="btn buy-now" @tap="handleShowComboInfo(1)">{{i18n.buyNow}}</view>
      </view>
    </view>

    <!-- 套餐整体信息弹窗 -->
    <view v-if="showComboInfo" class="pup-sku">
      <view class="pup-sku-main">
        <view class="pup-sku-header">
				  <view class="close" @tap="handleHideComboInfo"></view>
			  </view>
        <view class="pup-sku-body">
          <!-- 商品 -->
          <scroll-view class="combo-prod-list" scroll-x :show-scrollbar="false">
            <view class="pup-combo-item" v-if="mainProd">
              <view class="pup-combo-pic">
                <image :src="mainProd.pic" mode="aspectFit"></image>
              </view>
              <view class="pup-combo-name">{{mainProd.prodName}}</view>
              <view class="pup-combo-sku" @tap.stop="handleShowSkuPop(mainProd)">
                <view class="pup-combo-sku-text">{{mainProd.defaultSku.skuName}}</view>
              </view>
            </view>
            <block v-for="(prodItem, prodIndex) in matchingProdsList" :key="prodIndex">
              <view v-if="prodItem.checked" class="pup-combo-item">
                <image class="pup-combo-pic" :src="prodItem.pic" mode="aspectFit"></image>
                <view class="pup-combo-name">{{prodItem.prodName}}</view>
                <view class="pup-combo-sku" @tap.stop="handleShowSkuPop(prodItem)">
                  <view class="pup-combo-sku-text">{{prodItem.defaultSku.skuName || ''}}</view>
                </view>
              </view>
            </block>
          </scroll-view>
          <!-- 数量 -->
          <view class="pup-sku-count">
            <view class="num-wrap">
              <view class="minus" @tap="onCountMinus">
                <text class="row"></text>
              </view>
              <view class="text-wrap">
                <input type="number" v-model="comboNum"></input>
              </view>
              <view class="plus" @tap="onCountPlus">
                <text class="row"></text>
                <text class="col"></text>
              </view>
            </view>
            <view class="count-name">{{i18n.numberOfPackages}}</view>
          </view>
        </view>
        <view class="pup-sku-footer">
          <view class="btn buy" @tap="handleComboInfoPopComfirm">{{i18n.confirm}}</view>
        </view>
      </view>
    </view>

    <!-- 套餐sku弹窗 -->
    <prod-sku-select
      v-if="showSkuPop && currentProdItem"
      :pic="currentProdItem.pic"
      :least-num="currentProdItem.leastNum"
      :sku-id="currentProdItem.defaultSku.skuId"
      :sku-name="currentProdItem.defaultSku.skuName"
      :sku-list="currentProdItem.skuList"
      :is-main="currentProdItem.type===1"
      @setSku="handleSetSku"
      @closeSkuPop="handleCloseSkuPop"
    > </prod-sku-select>
  </view>
</template>

<script>
import prodSkuSelect from '@/components/prodSkuSelect/prodSkuSelect'
import util from '@/utils/util'
import http from '@/utils/http'
export default {
  components: { prodSkuSelect },
  data(){
    return {
      loaded: false,
      shopId: 0,
      // 套装的数量
      comboNum: 1,
      // sku弹窗显隐
      showSkuPop: false,
      // 套装信息
      comboInfo: {},
      // 套装主商品
      mainProd: {},

      // 套装商品列表
      matchingProdsList: [],

      // 当前操作的商品项
      currentProdItem: {},
      // 请求套餐价格的数据
      comboAmountParam: {},
      // 套餐的价格信息
      comboAmountInfo: {},
      // 套餐整体信息弹窗显隐
      showComboInfo: false,
      // 套餐购买方式: 0加入购物车 1立即购买
      comboBuyType: 0
    }
  },
  computed: {
    i18n() {
      return this.$t('index')
    },
  },
  onLoad(options) {
    this.shopId = options.shopId
    this.comboId = options.comboId
    this.getComboDetailById()
  },
  onShow() {
    uni.setNavigationBarTitle({
      title: this.i18n.discountPackage
    })
  },
  methods: {
    /**
     * 获取套餐详情
     */
    getComboDetailById() {
      var params = {
        url: '/combo/getComboByComboId',
        data: {
          comboId: this.comboId
        },
        method: "GET",
        callBack: res => {
          this.comboInfo = res
          res.mainProd.defaultSku = this.setDefaulProdSku(res.mainProd.skuList, res.mainProd.comboPrice)
          this.mainProd = res.mainProd
          res.matchingProds.forEach(matchingPordItem => {
            matchingPordItem.checked = matchingPordItem.required === 1
            matchingPordItem.defaultSku = this.setDefaulProdSku(matchingPordItem.skuList, matchingPordItem.comboPrice)
          })
          this.matchingProdsList = res.matchingProds
          this.loaded = true
          this.setComboParam()
        },
        errCallBack: err => {
          if (err.statusCode === 400) {
            uni.showToast({
              title: err.data,
              icon: 'none'
            })
            uni.navigateBack()
          }
        }
      }
      http.request(params)
    },

    /**
     * 初始化商品的默认sku
     */
    setDefaulProdSku(skuList, comboPrice, isMainProd) {
      if (isMainProd) {
        for (let i = 0; i < skuList.length; i++) {
          if (skuList[i].matchingPrice === comboPrice) {
            return skuList[i]
          }
        }
      } else {
        let defaultSku = null
        let flag = false
        for (let i = 0; i <skuList.length; i++) {
          if (comboPrice === skuList[i].matchingPrice && skuList[i].stocks ) {
            defaultSku = skuList[i]
            flag = true
            break
          }
        }
        if (!flag && skuList.length) {
         defaultSku = skuList[0]
        } else {
         defaultSku = defaultSku
        }
        return defaultSku
      }
    },

    /**
     * 修改后重设商品的sku
     */
    handleSetSku(sku, isMain) {
      if (isMain) {
        this.mainProd.defaultSku = sku
        this.$forceUpdate()
      } else {
        const matchingProds = this.matchingProdsList
        for (let i = 0; i < matchingProds.length; i++) {
          if (matchingProds[i].comboProdId === sku.comboProdId) {
            matchingProds[i].defaultSku = sku
            this.$forceUpdate()
            break
          }
        }
      }
      this.handleCloseSkuPop()
      this.setComboParam()
    },

    /**
     * 勾选/取选套装的商品
     */
    handleCheckProdItem(matchingProd, matchingProdIndex) {
      if (matchingProd.required) return
      let checked = matchingProd.checked
      const matchingProdsList = this.matchingProdsList
      matchingProdsList[matchingProdIndex].checked = !checked
      this.setComboParam()
    },

    /**
     * 生成请求套餐金额的参数
     */
    setComboParam() {
      const matchingSkuIds = []
      this.matchingProdsList.forEach(matchingProd=> {
        if (matchingProd.checked) {
          matchingSkuIds.push(matchingProd.defaultSku.skuId)
        }
      })

      let comboAmountParam = {
        comboId: this.comboId,
        comboNum: this.comboNum,
        matchingSkuIds,
        prodId: this.mainProd.prodId,
        skuId: this.mainProd.defaultSku.skuId
      }
      this.comboAmountParam = comboAmountParam
      this.getComboAmount(comboAmountParam)
    },

    /**
     * 展示套装信息弹窗
     * @param {Number} openType: 0加入购物车  1立即购买
     */
    handleShowComboInfo(openType) {
      this.showComboInfo = true
      this.comboBuyType = openType
    },

    /**
     * 关闭套装信息弹窗
     */
    handleHideComboInfo() {
      this.showComboInfo = false
    },

    /**
     * 减数量
     */
    onCountMinus() {
      util.tapLog(3)
      let comboNum = this.comboNum;
      if (comboNum > 1) {
        this.comboNum =  this.comboNum - 1
      }
      this.setComboParam()
    },

    /**
     * 加数量
     */
    onCountPlus() {
      const matchingProdsList = this.matchingProdsList.filter(el=> el.checked)
      // 套餐内是否只有一个主商品
      const isOnlyMain = !matchingProdsList.length
      let minStocksProd = {}
      if (isOnlyMain) {
        minStocksProd = this.mainProd
      } else {
        minStocksProd = matchingProdsList[0]
        matchingProdsList.forEach(el=> {
          if (el.defaultSku.stocks < minStocksProd.defaultSku.stocks) {
            minStocksProd = el
          }
        })
      }
      if (!isOnlyMain && this.mainProd.defaultSku.stocks < minStocksProd.defaultSku.stocks) {
        minStocksProd = this.mainProd
      }
      let comboNum = Number(this.comboNum)
      if (comboNum < minStocksProd.defaultSku.stocks) {
        this.comboNum = comboNum + 1
      } else {
        uni.showToast({
          title: minStocksProd.prodName + this.i18n.insufficientStock,
          icon: 'none',
          duration: 1000
        });
      }
      this.setComboParam()
    },

    /**
     * 套装信息弹窗确认按钮
     */
    handleComboInfoPopComfirm() {
      if (this.comboNum < 1) {
        uni.showToast({
          title: this.i18n.leastTips,
          icon: 'none'
        })
        this.comboNum = 1
        return
      }
      if (this.comboBuyType) {
        // console.log('立即购买');
        util.checkAuthInfo(this.buyNow)
      } else {
        // console.log('加入购物车');
        util.checkAuthInfo(this.addToCart)
      }
    },

    /**
     * 加入购物车
     */
    addToCart() {
      const paramData = {
        ...this.comboAmountParam,
        basketId: 0,
        shopId: this.shopId,
        count: this.comboNum
      }
      const params = {
        url: '/p/shopCart/changeItem',
        method: 'POST',
        data: paramData,
        callBack: res => {
          uni.showToast({
            title: this.i18n.successfullyAddedCart,
            icon: "none"
          })
          this.handleHideComboInfo()
          uni.redirectTo({
            url: '/pages/basket/basket'
          })
        },
        errCallBack: errMsg => {
          uni.showToast({
            title: errMsg.data,
            icon: 'none'
          })
          this.handleHideComboInfo()
        }
      }
      http.request(params)
    },

    /**
     * 立即购买
     */
    buyNow() {
      const paramData = {
        ...this.comboAmountParam,
        basketId: 0,
        shopId: this.shopId,
        prodCount: this.comboNum
      }
      uni.setStorageSync("orderItem", JSON.stringify(paramData))
      uni.redirectTo({
        url: '/pages/submit-order/submit-order?orderEntry=1&mold=' + ''
      })
    },

    /**
     * 请求套餐金额
     */
    getComboAmount(comboAmountParam) {
      const params = {
        url: '/combo/calculateComboAmount',
        method: 'PUT',
        data: comboAmountParam,
        callBack: res => {
          this.comboAmountInfo = res
        },
        errCallBack: errMsg => {
          console.log(errMsg)
        }
      }
      http.request(params)
    },

    /**
     * 显示sku弹窗
     */
    handleShowSkuPop(item) {
      this.currentProdItem = item
      this.showSkuPop = true
    },

    /**
     * 隐藏sku弹窗
     */
    handleCloseSkuPop() {
      this.showSkuPop = false
    },


  }
}
</script>

<style>
 @import './discount-package-detail.css';
</style>

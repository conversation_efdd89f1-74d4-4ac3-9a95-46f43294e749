/* packageShop/pages/afterSales/afterSales.wxss */
/* 清除浮动 */
.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
image {
  width: 100%;
  height: 100%;
}
.page {
  display: block;
  width: 100%;
  min-height: 100vh;
  height: auto;
  overflow: auto;
  box-sizing: border-box;
  background: #f7f7f7;
}

.goods-item {
  background: #fff;
  margin-bottom: 20rpx;
}
.goods-item:first-child {
  margin-top: 20rpx;
}
/* 店铺 */
.shop-box {
  display: block;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.shop-img {
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  vertical-align: middle;
  margin-right: 10rpx;
}
.shop-img > iamge {
  display: block;
  width: 100%;
  height: 100%;
}
.shopname {
  display: inline-block;
  font-size: 28rpx;
  line-height: 1em;
  vertical-align: middle;
}
/* 商品信息 */
.goods-msg-box {
  background: #fff;
  padding: 25rpx 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.img-box {
  display: inline-block;
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  background: #eee;
  vertical-align: top;
}
.goods-text {
  display: inline-block;
  vertical-align: top;
  width: 75%;
}
.goods-title {
  font-size: 28rpx;
   display: -webkit-box;
  -webkit-line-clamp: 2; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}
.goods-quantity {
  font-size: 26rpx;
  color: #888888;
  margin-top: 25rpx;
}

/* 提示 */
.goods-item-tips {
  padding: 20rpx;
  padding-bottom: 25rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f2f2f2;
  line-height: 1em;
}
.rmb-icon {
  display: inline-block;
  width: 35rpx;
  height: 35rpx;
  vertical-align: middle;
  margin-right: 15rpx;
}
.refund-type {
  display: inline-block;
  vertical-align: middle;
  color: #888888;
  margin-right: 15rpx;
}
.refund-process {
  display: inline-block;
  vertical-align: middle;
  color: #555555;
}
.item-cont {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  display: -webkit-flex;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  background: #f7f7f7;
  justify-content: space-between;
}

.item-cont .categories {
  white-space: nowrap;
}
.item-cont .categories .prod-pic {
  display: inline-block;
  width: 180rpx;
  height: 180rpx;
  margin-right: 20rpx;
}

.item-cont .prod-pic image {
  display: block;
  width: 100%;
  height: 100%;
}


/* 查看详情 */
.item-bottom {
  /* padding: 15rpx 20rpx; */
  /* text-align: right; */
  /* margin-top: 20rpx; */
  line-height: 1em;
}
.view-details {
  float: right;
  /* display: inline-block; */
  font-size: 25rpx;
  vertical-align: middle;
  border: 1rpx solid #e4e4e4;
  padding: 10rpx 25rpx;
  border-radius: 8rpx;
}

/* .empty{
  font-size: 30rpx;
  text-align: center;
  height:200rpx;
  line-height: 200rpx;
  color: #666;
} */
/* 列表为空 */
.empty {
  margin-top: 150rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 2em;
}




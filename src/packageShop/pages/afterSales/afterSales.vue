<template>
<!-- 退款/售后 -->
<!--packageShop/pages/afterSales/afterSales.wxml-->
<view class="page">
  <view class="goods-list">

    <view class="goods-item" v-for="(item, index) in list" :key="index">
      <!-- 店铺 -->
      <view class="shop-box clearfix">
        <view class="shop-img"><image :src="`${staticPicDomain}images/icon/shop.png`"></image></view>
        <view class="shopname">{{i18n.shop}}: {{item.shopName}}</view>
      </view>
      <view class="goods-msg-box" v-if="item.orderItems.length==1">
        <!-- 图片 -->
        <view class="img-box">
          <image :src="item.orderItems[0].pic"></image>
        </view>
        <!-- 信息 -->
        <view class="goods-text">
          <view class="goods-title">{{item.orderItems[0].prodName}}</view>
          <view class="goods-quantity">{{i18n.quantity}}：{{item.orderItems[0].prodCount}}{{i18n.piece}}</view>
          <!-- 查看详情btn -->
          <view class="item-bottom clearfix">
            <text class="view-details" :data-refundsn="item.refundSn" @tap="toRefundDetails">{{i18n.seeDetails}}</text>
          </view>
        </view>
      </view>
      <view class="goods-msg-box" v-else>
        <block>
          <view class="item-cont" @tap="toRefundDetails" :data-refundsn="item.refundSn">
            <scroll-view scroll-x="true" scroll-left="0" scroll-with-animation="false" class="categories">
              <block v-for="(prod, index2) in item.orderItems" :key="index2">
                <view class="prod-pic">
                  <image :src="prod.pic"></image>
                </view>
              </block>
            </scroll-view>
          </view>
        </block>
      </view>

      <!-- 退款进度提示 -->
      <view class="goods-item-tips">
        <view class="rmb-icon">
          <!-- 仅退款 -->
          <image :src="`${staticPicDomain}images/icon/rmb-icon.png`" v-if="item.applyType == 1"></image>
          <!-- 退货退款 -->
          <image :src="`${staticPicDomain}images/icon/refunds.png`" v-if="item.applyType == 2"></image>
        </view>
        <!-- applyType  申请类型:1,仅退款,2退款退货 -->
        <!-- 处理退款状态:(1.买家申请 2.卖家接受 3.买家发货 4.卖家收货 5.退款成功 6.买家撤回申请 7.商家拒绝 -1.退款关闭) -->
        <view class="refund-type" v-if="item.applyType == 1">{{i18n.refund01}}</view>
        <view class="refund-type" v-if="item.applyType == 2">{{i18n.refund02}}</view>
        <view class="refund-process" v-if="item.returnMoneySts == 1">{{i18n.refund1}}</view>
        <view class="refund-process" v-if="item.returnMoneySts == 2  && item.applyType == 1">{{i18n.refund21}}</view>
        <view class="refund-process" v-if="item.returnMoneySts == 2 && item.applyType == 2">{{i18n.refund22}}</view>
        <view class="refund-process" v-if="item.returnMoneySts == 3 && item.applyType == 2">{{i18n.refund3}}</view>
        <view class="refund-process" v-if="item.returnMoneySts == 4 && item.applyType == 2">{{i18n.refund4}}</view>
        <view class="refund-process" v-if="item.returnMoneySts == 5">{{i18n.refund5}}</view>
        <view class="refund-process" v-if="item.returnMoneySts == 6">{{i18n.refund6}}</view>
        <view class="refund-process" v-if="item.returnMoneySts == 7">{{i18n.refund7}}</view>
        <view class="refund-process" v-if="item.returnMoneySts == -1">{{i18n.refund_1}}</view>
      </view>

    </view>

    <!-- <view class="empty" wx:if="{{!list.length}}">您还没有退款/售后订单哦~</view> -->
    <!-- 空 -->
    <view class="empty" v-if="!list.length">
      <view class="empty-icon">
        <image :src="`${staticPicDomain}images/icon/empty.png`"></image>
      </view>
      <view class="empty-text">{{i18n.refundEmpty}}</view>
    </view>

  </view>
</view>
</template>

<script>
// packageShop/pages/afterSales/afterSales.js
var http = require("@/utils/http.js");

export default {
  data() {
    return {
      list: [],
      current: 1,
      pages: 0,
      startTime: '',
      //开始时间
      endTime: '' //结束时间
      //orderItems: '',  //订单项

    };
  },

  components: {},
  props: {},
  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.myRefundOrder(); //退款订单列表
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
	  //头部导航标题
	  uni.setNavigationBarTitle({
	      title:this.i18n.refundAfterSale
	  });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.current < this.pages) {
      this.setData({
        current: this.current + 1
      })
      this.myRefundOrder()
    } else {
      this.setData({
        loadAll: true
      })
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    /**
     * 我的退款订单列表
     */
    myRefundOrder: function (e) {
      uni.showLoading();
      var params = {
        url: "/p/orderRefund/list",
        method: "GET",
        data: {
          current: this.current,
          size: 20,
          startTime: this.startTime,
          endTime: this.endTime
        },
        callBack: res => {
          var list = []
          if(res.current == 1 ) {
            list = res.records
          }else {
            list = this.list
            list = list.concat(res.records)
          }
          this.list = list
          this.pages = res.pages
          this.current = res.current
          uni.hideLoading()
        }
      };
      http.request(params);
    },

    /**
     * 点击查看详情跳转页面
     */
    toRefundDetails: function (e) {
      var refundSn = e.currentTarget.dataset.refundsn;
      uni.navigateTo({
        url: '/packageShop/pages/DetailsOfRefund/DetailsOfRefund?refundSn=' + refundSn
      });
    }
  }
};
</script>
<style>
@import "./afterSales.css";
</style>

<template>
	<view class="shop-feature" style="padding-bottom: 80rpx;">
		<feature ref="featureIndex" @pageLoaded="pageLoaded" :pageLoad="pageLoad" :pageId="renovationId" :shopId="shopId" :pageScorllTop="pageScorllTop"></feature>
    <BottomLoginNav v-if="!isLogin" />
    <WenetAccountTip />
	</view>
</template>

<script>
	// const http = require('../../../utils/http')
  import http from '../../../utils/http'
	import feature from '../../../components/feature/index/index'
  import BottomLoginNav from '@/components/bottom-login-nav'
  import WenetAccountTip from '@/components/wenet-account-tip'
	export default {
		data() {
			return {
				pageLoad: false,
				renovationId: '', // 页面id
				shopId: '',
				pageScorllTop: 0, // 页面滚动距离
			}
		},
		components: {
			feature,
      BottomLoginNav
		},
    computed: {
      isLogin () {
        return this.$store.state.isLogin
      },
    },
		mounted() {
			this.getShopInfo()
		},
    onShareAppMessage: function () {},
		onLoad: function(options) {
			uni.showLoading()
			if (options.activityid) {
				this.renovationId = options.activityid
			}
			if (options.shopid) {
				this.shopId = options.shopid
			}
		},
		onShow:function() {},
		onPageScroll: function(e) {
			this.pageScorllTop = e.scrollTop
		},
		onPullDownRefresh: function() {
			setTimeout(()=> {
				this.$nextTick(() => {
					this.$refs.featureIndex.getPageInfoById()
				})
				uni.stopPullDownRefresh(); //停止下拉刷新
			}, 100);
		},
		methods: {

			/**
			 * 获取店铺信息
			 */
			getShopInfo() {
				var params = {
					url: "/shop/headInfo",
					method: "GET",
					data: {
						shopId: this.shopId
					},
					callBack: (res) => {
						uni.setStorageSync("shopInfo", res);
						// 进入页面时判断店铺状态
						this.handleShopStatus(res)
					}
				};
				http.request(params);
			},

			/**
			 * 店铺状态处理
			 */
			handleShopStatus(res) {
				const shopStatus = res.shopStatus
				// shopStatus店铺状态(-1:未开通 0: 停业中 1:营业中 2:平台下线 3:平台下线待审核)，可修改
				if (shopStatus === -1) {
					this.handleTipsModal(this.i18n.storeStatusTips2)
					return
				}
				if (shopStatus === 0) {
					const contractStartTime = new Date(data.contractStartTime).getTime()
					const contractEndTime = new Date(data.contractEndTime).getTime()
					const today = new Date().getTime()
					// 1、店铺状态为0(停业中)时，当店铺未到签约开始时间，用户进入店铺提示：商家尚未营业
					if (today < contractStartTime) {
						this.handleTipsModal(this.i18n.storeStatusTips4)
						return
					}
					// 2、店铺状态为0(停业中)时，当店铺超过签约有效期，用户进入店铺提示：商家已暂停未营业
					if (today > contractEndTime) {
						this.handleTipsModal(this.i18n.storeStatusTips5)
						return
					}
				}
				if (shopStatus === 2 || shopStatus === 3) {
					this.handleTipsModal(this.i18n.storeStatusTips3)
					return
				}
			},
			handleTipsModal(tips) {
				uni.showModal({
					title: this.i18n.tips,
					content: tips,
					showCancel: false,//是否显示取消按钮
					cancelText: this.i18n.cancel,
					confirmText: this.i18n.confirm,
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack({
								delta: 1
							})
						}
					}
				})
			},

			// 页面加载回调
			pageLoaded(e) {
				uni.setNavigationBarTitle({
					title: e.detail.title
				})
				// this.pageLoaded = false
				setTimeout(() => {
					uni.hideLoading()
					// this.pageLoaded = true
				}, 1)
			}
		}
	}
</script>

<style>

</style>

/* pages/shopPage/shopPage.wxss */
/* 颜色记录：背景色：#f8f8f8;店铺热卖背景色：#efefef；商品列表背景色：#fafafa */

page {
  padding-bottom: 90rpx;
  background: #f8f8f8;
}

.shop-container {
  width: 100%;
  height: 100%;
  background: #f8f8f8;
}

/* 搜索框 */

.head-bg {
  width: 100%;
  height: 400rpx;
  background: linear-gradient(#102b6a, #1b2f5d, #f8f8f8);
  /* 标准的语法（必须放在最后） */
}

.navtop {
  position: fixed;
  top: 0rpx;
  z-index: 99;
  width: 100%;
  padding: 12px 0;
  background: linear-gradient(
    to top right,
    #151d2c,
    #102b6a
  ); /* 标准的语法（必须放在最后） */
}

.bg-sear .section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70rpx;
  background: #fff;
  z-index: 1;
  border-radius: 50rpx;
  width: 92%;
  margin: auto;
  left: 4%;
  background: #f7f7f7;
}

.bg-sear .section .placeholder {
  display: block;
  font-size: 26rpx;
  color: #777;
}

.bg-sear .section .search-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

/* 内容大盒子 */
.prod-wrapper {
  position: absolute;
  top: 270rpx;
  box-sizing: border-box;
  display: block;
  margin-top: 15rpx;
  margin-bottom: 100rpx;
  border-radius: 12rpx;
  background: transparent;
  width: 100%;
}

/* 导航分类 轮播图 */

.content {
  border-radius: 30rpx;
  background: transparent;
  width: 95%;
  padding: 0 2.5%;
}

swiper {
  width: 100%;
  height: 350rpx;
  overflow: hidden;
}

swiper.pic-swiper {
  background: #fff;
  height: 780rpx;
  border-radius: 15rpx 15rpx 0 0;
}

swiper-item {
  font-size: 26rpx;
  font-weight: bold;
}

swiper.pic-swiper .img-box {
  font-size: 0;
}

.wx-swiper-dots {
  margin-bottom: 15rpx;
}

.banner-item {
  box-sizing: border-box;
}

swiper.pic-swiper .banner {
  position: absolute;
  width: 100%;
  height: 780rpx;
  display: inline-block;
}

.tit {
  padding: 40rpx 0;
  text-align: center;
  background: #fff;
  font-size: 28rpx;
  width: 95%;
  margin: auto;
  border-radius: 14rpx;
}
.hotsell-tit {
  font-size: 27rpx;
  color: #000;
  font-weight: bold;
  vertical-align: middle;
}
.hotsell {
  display: inline-block;
  width: 55rpx;
  height: 50rpx;
  margin-right: 0.2em;
  vertical-align: middle;
}
.hotsell > image {
  width: 100%;
  height: 100%;
}

/* 门店公告 */
.message-play {
  position: relative;
  height: 90rpx;
  background: #fff;
  width: 95%;
  margin: auto;
  padding: 0 60rpx 0 100rpx;
  box-sizing: border-box;
  border-top: 1px solid #f9f9f9;
  margin-bottom: 20rpx;
  border-radius: 0 0 14rpx 14rpx;
}
.message-play .hornpng {
  width: 77rpx;
  height: 36rpx;
  position: absolute;
  left: 20rpx;
  top: 27rpx;
  margin-right: 8rpx;
}
.message-play .swiper-cont {
  height: 90rpx;
  line-height: 90rpx;
  margin-top: 0;
}
.message-play .swiper-cont .items {
	width: 90%;
  text-overflow: ellipsis;
  text-align: left;
}
.arrow {
  width: 15rpx;
  height: 15rpx;
  border-top: 3rpx solid #686868;
  border-right: 3rpx solid #686868;
  transform: rotate(45deg);
  position: absolute;
  right: 30rpx;
  top: 34rpx;
}

/* 商品列表 */
.prod-list {
  width: 100%;
	padding-bottom: 120rpx;
}
.prodlist-item-cont {
  padding: 0 2.5%;
  padding-top: 20rpx;
}

.prod-list .title {
  position: relative;
  height: 64rpx;
  line-height: 64rpx;
  font-size: 32rpx;
  padding: 30rpx 0 10rpx 30rpx;
  color: #333;
  background: #fff;
}

.title .more-prod-cont {
  color: #999;
  display: inline-block;
  text-align: right;
}

.title .more-prod-cont .more {
  position: absolute;
  right: 30rpx;
  top: 48rpx;
  color: #666;
  font-size: 24rpx;
  padding: 0 20rpx;
  height: 44rpx;
  line-height: 44rpx;
}

.title .more-prod-cont .arrow {
  width: 15rpx;
  height: 15rpx;
  transform: rotate(45deg);
  position: absolute;
  top: 58rpx;
  right: 30rpx;
  border-top: 2rpx solid #666;
  border-right: 2rpx solid #666;
}

.prod-list .prod-items {
  width: 345rpx;
  display: inline-block;
  background: #fff;
  padding-bottom: 20rpx;
  box-sizing: border-box;
  /* box-shadow: 0rpx 5rpx 15rpx rgba(58,134,185,0.2); */
  box-shadow: 0rpx 3rpx 12rpx rgba(23, 24, 26, 0.2);
  border-radius: 14rpx;
}

.prod-list .prod-items:nth-child(2n-1) {
  margin: 0 12rpx 20rpx 0rpx;
}

.prod-list .prod-items:nth-child(2n) {
  margin: 0 0rpx 20rpx 11.5rpx;
}

.prod-items .hot-imagecont .hotsaleimg {
  width: 100%;
  height: 341rpx;
  border-radius: 14rpx 14rpx 0 0;
}

.prod-items .hot-text .prod-name {
  font-size: 28rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.prod-items .hot-imagecont {
  font-size: 0;
  text-align: center;
}

.prod-items .hot-text {
  margin-top: 20rpx;
  padding: 0 10rpx;
}

.prod-items .hot-text .prod-info,
.more-prod .prod-text-right .prod-info {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 36rpx;
  line-height: 36rpx;
}
.brief {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 22rpx;
  color: #999;
}

.prod-items .hot-text .prod-text-info {
  position: relative;
  height: 70rpx;
  line-height: 70rpx;
  font-family: Arial;
}

.prod-items .hot-text .prod-text-info .hotprod-price {
  display: inline;
  font-size: 26rpx;
  color: #eb2444;
}

.prod-items .hot-text .prod-text-info .basket-img {
  width: 50rpx;
  height: 50rpx;
  position: absolute;
  right: 0;
  bottom: 2rpx;
  padding: 8rpx;
}

.singal-price {
  display: inline;
  font-size: 20rpx;
  text-decoration: line-through;
  color: #777;
  margin-left: 15rpx;
}

/* 查看更多 */

.view-more {
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  color: #999;
  padding: 20rpx 0;
  font-size: 24rpx;
  border-radius: 12rpx;
  /* margin-bottom: 100rpx; */
}

.display {
  display: none;
}

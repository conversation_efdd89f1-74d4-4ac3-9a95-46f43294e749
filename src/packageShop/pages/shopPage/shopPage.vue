<template>
<!-- pages/shopPage/shopPage.wxml -->
<view class="shop-container">

  <!-- 渐变背景 -->
  <view class="head-bg shop-index">
    <shopHeader style="color: #fff;" :shopId="shopId"></shopHeader>
    <!-- 搜索框 -->
    <view class="bg-sear">
      <view :class="'bg-search ' + (topFlag? 'navtop' : '')">
        <view class="section" @tap="toShopSearchPage" :data-shopid="shopId">
          <image :src="`${staticPicDomain}images/icon/search.png`" class="search-img"></image>
          <text class="placeholder">{{i18n.searchForItems}}</text>
        </view>
      </view>
    </view>
  </view>
  <!-- /渐变背景 -->

  <!-- 内容大盒子 -->
  <view :class="'prod-wrapper'">
    <!-- 轮播图 -->
    <view v-if="indexImgs" :class="'content ' + (indexImgs.length? '' : 'display')">
      <swiper :autoplay="autoplay" :indicator-color="indicatorColor" :interval="interval" :duration="duration" :indicator-active-color="indicatorActiveColor" circular="true" class="pic-swiper" indicator-dots>
        <block v-for="(item, index) in indexImgs" :key="index">
          <swiper-item class="banner-item">
            <view class="img-box">
              <image :src="item.imgUrl" @tap="bannerToProdPage(item)" class="banner"></image>
            </view>
          </swiper-item>
        </block>
      </swiper>
    </view>
		<!-- 店内公告 -->
		<view class="message-play" @tap="onNewsPage" v-if="news.length">
			<image :src="`${staticPicDomain}images/icon/horn.png`" class="hornpng"></image>
			<swiper vertical="true" autoplay="true" duration="1000" circular="true" class="swiper-cont">
				<block v-for="(item, id) in news" :key="id">
					<swiper-item class="items">{{item.title}}</swiper-item>
				</block>
			</swiper>
			<text class="arrow"></text>
		</view>
    <!-- 商品列表 -->
    <view class="prod-list">
      <view class="tit">
        <view class="hotsell">
          <image :src="`${staticPicDomain}images/icon/hotsell03.png`"></image>
        </view>
        <text class="hotsell-tit">{{i18n.shopHotSale}}</text>
      </view>

      <view  class="prodlist-item-cont">
        <block v-for="(prod, index) in shopProdList" :key="index">
          <view class="prod-items" @tap="toProdPage(prod.prodId)" >
            <view class="hot-imagecont">
              <image :src="prod.pic" class="hotsaleimg"></image>
            </view>
            <view class="hot-text">
              <view class="prod-name">{{prod.prodName}}</view>
              <view class="prod-info">
                <view class="brief">{{prod.brief || ''}}</view>
              </view>
              <view class="prod-text-info">
                <view class="price">
                  <text class="symbol">￥</text>
                  <text class="big-num">{{prod.price}}</text>
                </view>
                <image src="/static/images/tabbar/basket-sel.png`" class="basket-img"></image>
              </view>
            </view>
          </view>
        </block>

        <!-- 查看更多商品 -->
        <view class="view-more" v-if="shopProdList.length>=6" @tap="toShopProds">{{i18n.viewMoreProducts}}</view>
				<view class="view-more" v-if="!shopProdList.length">{{i18n.noData}}</view>
      </view>

    </view>
  </view>

  <!-- /内容大盒子 -->
  <shop-tabbar style="position:fixed;bottom:0;width:100%;left:0;right:0;" :shopId="shopId"></shop-tabbar>

  <!-- 回到顶部 -->
  <back-top-btn v-if="showBacktop"></back-top-btn>
</view>
</template>

<script>
// pages/shopPage/shopPage.js
var http = require("../../../utils/http.js");
import shopTabbar from '@/components/shop-tabbar/shop-tabbar';
import shopHeader from '@/components/shopHeader/shopHeader';
import backTopBtn from "../../../components/backTopBtn/backTopBtn";

export default {
  components: {
    shopTabbar,
    shopHeader,
    backTopBtn
  },
  data() {
    return {
      shopId: 0,
      shopInfo: {},
      indicatorDots: true,
      indicatorColor: '#d1e5fb',
      indicatorActiveColor: '#1b7dec',
      autoplay: true,
      interval: 2000,
      duration: 1000,
      indexImgs: null,
      topFlag: false,
      isCollection: false,
      shopProdList: [],
      news: [],
      scrollTop: "",
      showBacktop: false
    };
  },
  props: {},
  computed: {
    i18n() {
      return this.$t('index')
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    uni.showLoading({
      title: '',
      // #ifndef MP-TOUTIAO
      mask: true
      // #endif
    });

    uni.setNavigationBarTitle({
      title: this.i18n.myShop
    });
		this.setData({
			shopId: options.shopId
		});
    this.getIndexImgs();
    this.getShopProds();
    this.getShopInfo()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () { },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getNoticeList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () { },
  methods: {
		/**
		 * 轮播图跳转商品详情
		 */
		bannerToProdPage(item) {
			if (item.type !==0 ) {
				return
			}
			let prodId = item.relation
			http.request({
				url: '/prod/isStatus',
				method: 'GET',
				data: {
					prodId : prodId
				},
				callBack: res => {
					if (res) {
						this.$Router.push({
							path: '/pages/prod/prod',
							query: {
								prodid: prodId,
								bannerEnter: '1'
							}
						})
					}
				}
			})
		},

    // 加载公告
    getNoticeList() {
      var params = {
        url: "/shop/notice/topNoticeList/" + this.shopId,
        method: "GET",
        data: {},
        callBack: res => {
          this.setData({
            news: res
          });
          uni.hideLoading();
        }
      };
      http.request(params);
    },

    //跳转公告列表页面
    onNewsPage: function () {
      uni.navigateTo({
        url: '/packageUser/pages/recent-news/recent-news?shopId=' + this.shopId
      });
    },


    //加载轮播图
    getIndexImgs() {
      var shopId = this.shopId; //加载轮播图
      var params = {
        url: `/indexImgs/${shopId}`,
        method: "GET",
        data: {},
        callBack: res => {
          this.setData({
            indexImgs: res // seq: res
          });
          uni.hideLoading()
        }
      };
      http.request(params);
    },

    // 跳转店铺详情页
    toShopInfo(e) {
      const shopId = e.currentTarget.dataset.shopid;
      uni.navigateTo({
        url: '/pages/shopInfo/shopInfo?shopId=' + shopId
      });
    },

    // 跳转店内搜索页
    toShopSearchPage: function (e) {
      uni.navigateTo({
        url: 'pages/shopSearch/shopSearch?shopId=' + e.currentTarget.dataset.shopid
      });
    },

    // 跳转所有商品页
    toShopProds(e) {
      uni.navigateTo({
        url: 'pages/shopProds/shopProds?shopId=' + e.currentTarget.dataset.shopid
      });
    },


    // 跳转商品详情页
    toProdPage: function (prodId) {
			this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodId } })
    },

    // 获取店铺信息
    getShopInfo() {
      var params = {
        url: "/shop/headInfo",
        method: "GET",
        data: {
          shopId: this.shopId || uni.getStorageSync('shopInfo').shopId
        },
        callBack: (res) => {
          this.setData({
            shopInfo: res
          });
					uni.setStorageSync("shopInfo", res);
					// 进入页面时判断店铺状态
          this.handleShopStatus(res)
        }
      };
      http.request(params);
    },

    /**
     * 店铺状态处理
     */
    handleShopStatus(res) {
      const shopStatus = res.shopStatus
      // shopStatus店铺状态(-1:未开通 0: 停业中 1:营业中 2:平台下线 3:平台下线待审核)，可修改
      if (shopStatus === -1) {
        this.handleTipsModal(this.i18n.storeStatusTips2)
        return
      }
      if (shopStatus === 0) {
        const contractStartTime = new Date(data.contractStartTime).getTime()
        const contractEndTime = new Date(data.contractEndTime).getTime()
        const today = new Date().getTime()
        // 1、店铺状态为0(停业中)时，当店铺未到签约开始时间，用户进入店铺提示：商家尚未营业
        if (today < contractStartTime) {
          this.handleTipsModal(this.i18n.storeStatusTips4)
          return
        }
        // 2、店铺状态为0(停业中)时，当店铺超过签约有效期，用户进入店铺提示：商家已暂停未营业
        if (today > contractEndTime) {
          this.handleTipsModal(this.i18n.storeStatusTips5)
          return
        }
      }
      if (shopStatus === 2 || shopStatus === 3) {
        this.handleTipsModal(this.i18n.storeStatusTips3)
        return
      }
    },
    handleTipsModal(tips) {
      uni.showModal({
        title: this.i18n.tips,
        content: tips,
        showCancel: false,//是否显示取消按钮
        cancelText: this.i18n.cancel,
        confirmText: this.i18n.confirm,
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack({
              delta: 1
            })
          }
        }
      })
    },

    // 获取店铺商品
    getShopProds() {
      var ths = this;
      var params = {
        // url: '/search/searchProdPage',
        url: '/search/page',
        method: 'GET',
        data: {
          shopId: this.shopId,
					current: 1,
					size: 20,
          isActive: 1 // 过滤掉活动商品
        },
        callBack: res => {
					console.log(res)
          uni.hideLoading()
          this.setData({
            shopProdList: res.records[0].products
          });
        }
      };
      http.request(params);
    },

    // 设置页面标题
    setNavTitle() {
      uni.setNavigationBarTitle({
        title: this.shopInfo.shopName
      });
    },

    // 页面滚动事件
    onPageScroll: function (e) {
      var _this = this;
      if (e.scrollTop > 76) {
        _this.setData({
          topFlag: true,
          showBacktop: true
        });
      } else {
        _this.setData({
          topFlag: false,
          showBacktop: false
        });
      }
    }
  }
};
</script>
<style>
@import "./shopPage.css";
</style>
<style scoped>
</style>

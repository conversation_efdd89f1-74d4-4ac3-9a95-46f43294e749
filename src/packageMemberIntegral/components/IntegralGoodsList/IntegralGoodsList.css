/* components/IntegralGoodsList/IntegralGoodsList.wxss */

.integral-item {
  margin-top: 30rpx;
  width: 340rpx;
  background: #fff;
  box-sizing: border-box;
  border-radius: 20rpx;
}

.bottom-info{
	padding: 20rpx;
}

.integral-item:nth-child(2n) {
  margin-right: 0;
}

.integral-item .img {
  width: 340rpx;
  height: 340rpx;
  border-radius: 20rpx 20rpx 0 0;
  font-size: 0;
}

.integral-item .img image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx 20rpx 0 0;
}

.integral-item .name {
  font-size: 28rpx;
  margin: 16rpx 0 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.integral-item .price {
  position: relative;
  height: 20px;
  line-height: 20px;
  font-size: 24rpx;
  background: var(--primary-color);
  padding:0 20rpx;
  border-radius: 20px;
  margin-top: 16rpx;
}

.integral-item .price .icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 28rpx;
  height: 28rpx;
}

.integral-item .red-word {
  color: #fff;
}

.integral-item .old-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  font-family: arial;
}
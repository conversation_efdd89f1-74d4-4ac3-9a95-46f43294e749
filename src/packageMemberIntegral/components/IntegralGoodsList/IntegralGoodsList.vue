<template>
<!-- 积分商品列表组件 -->
<!--components/IntegralGoodsList/IntegralGoodsList.wxml-->
<!-- <view class="con-box"> -->
  <view class="integral-item" @tap="toScoreProdDet" :data-scoreprodid="prod.prodId">
    <view class="img">
      <image :src="prod.pic" mode="aspectFit"></image>
    </view>
	<view class="bottom-info">
		<view class="name">{{prod.prodName}}</view>
		<!-- 销售价大于等于市场价 市场价不展示 -->
		<view v-if="prod.oriPrice > prod.price" class="old-price">￥{{prod.oriPrice}}</view>
		<view class="price">
		  <!-- <image :src="`${staticPicDomain}images/icon/integral-icon.png`" class="icon"></image> -->
		  <text class="red-word">{{prod.scorePrice}}{{i18n.integral}}<text v-if="prod.price > 0" decode="true">+ ￥{{prod.price}}</text></text>
		</view>
	</view>
  </view>
<!-- </view> -->
</template>

<script>

export default {
  data() {
    return {};
  },

  components: {},
  props: {
    prod: Object
  },
  
  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },
  
  methods: {
	  /**
	   * 跳转积分商品详情
	   */
	  toScoreProdDet:function(e) {
	    const prodId = this.prod.prodId;
	    uni.navigateTo({
	      url: '/packageMemberIntegral/pages/convertProdDet/convertProdDet?prodId=' + prodId
	    });
	  },
  }
};
</script>
<style>
@import "./IntegralGoodsList.css";
</style>
<template>
  <view class="vip-intro-item-title">
    <text class="vip-intro-item-title-text">
      <slot></slot>
    </text>
  </view>
</template>

<script>
export default {
  name: 'vip-intro-item-title',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
    }
  },
  methods: {

  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
.vip-intro-item-title {
  font-size: 28rpx;
  color: #000000;
  letter-spacing: 0;
  font-weight: 700;
  display: flex;
  flex-direction: column;
  margin-bottom: 32rpx;
}
.vip-intro-item-title-text {
  padding-left: 10rpx;
}
.vip-intro-item-title::after {
  content: '';
  background: linear-gradient(.25turn, #FF364D, #ffffff);
  width: fit-content;
  min-width: 100rpx;
  height: 8rpx;
}
</style>
<template>
  <view class="container">
    <ShopCategory ref="shopCategoryRef" v-if="isLogin && shopId" :shop-id="shopId"></ShopCategory>
  </view>
</template>

<script>
import ShopCategory from '@/pages/shopCategory/shopCategory'
export default {
  data () {
    return {
      shopId: '',
    }
  },
  components: {
    ShopCategory,
  },
  onShow(options) {
    this.$nextTick(() => {
      if (this.isLogin) {
        this.$refs.shopCategoryRef.parentOnShow(options);
      }
    })
  },
  onReachBottom(options) {
    if (this.isLogin) {
      this.$refs.shopCategoryRef.parentOnReachBottom(options);
    }
  },
  onLoad(options) {
    this.shopId = options.shopId;
    this.$nextTick(() => {
      if (this.isLogin) {
        this.$refs.shopCategoryRef.parentOnLoad(options);
      }
    })
  },
  methods: {

  },
  computed: {
    isLogin () {
      return this.$store.state.isLogin
    },
  },
  mounted () {

  },
}
</script>

<style scoped>
.container {
  width: 100%;
  height: auto;
  min-height: 100vh;
}
</style>
/* pages/integralProdDet/integralProdDet.wxss */
page {
  background: #f4f4f4;
  height: 100%;
  overflow: visible;
}
image {
  width: 100%;
  height: 100%;
}

.container {
  height: auto;
  padding-bottom: 160rpx;
}

.price-info{
	width: 100%;
	height: 132rpx;
	background: linear-gradient(90deg, var(--light-background-2) 0%, var(--gradient-ramp-deep) 100%);
	background: -webkit-linear-gradient(90deg, var(--light-background-2) 0%, var(--gradient-ramp-deep) 100%);
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;
	padding: 0 10px;
}

.left-info{
	display: flex;
	flex-direction: column;
}

.price-now{
	font-size: 30rpx;
	color: #fff;
	margin-bottom: 10rpx;
}

.price-old{
	font-size: 28rpx;
	color: #fff;
	text-decoration: line-through;
}

.unit{
	font-size: 24rpx;
}

.add{
	margin: 0 10rpx;
}

.right-text{
	font-size: 28rpx;
	color: #fff;
}

.swiper-con {
	position: relative;
}

swiper {
  height: 750rpx;
  width: 100%;
}

swiper image {
  height: 750rpx;
  width: 100%;
}

.video-container {
	position: relative;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 750rpx;
	justify-content: center;
	align-items: center;
	background: #000;
	z-index: 10;
}

.video-container video {
	display: block;
	width: 100%;
}

.play-btn {
	position: absolute;
	left: 50%;
	bottom: 12%;
	padding: 2rpx;
	background: rgba(255, 255, 255, 0.75);
	border-radius: 50rpx;
	color: #000;
	font-size: 24rpx;
	text-align: center;
	transform: translateX(-50%);
	display: flex;
	justify-content: space-between;
	align-items: center;
	z-index: 15;
}

.play-icon {
	width: 50rpx;
	height: 50rpx;
}

.play-text {
  padding-right: 10rpx;
  margin: 0 10rpx;
}

.video-stop {
  padding: 2rpx 8rpx;
}

/** 商品信息 */

.prod-info {
  padding: 30rpx 30rpx 0 30rpx;
  position: relative;
  background: #fff;
}

.tit-wrap {
  position: relative;
  line-height: 1.5em;
  /* padding-right: 104rpx; */
  text-align: justify;
}

.prod-tit {
  font-size: 32rpx;
  color: #333;
  padding-right: 20rpx;
	word-break: break-all;
}

.tit-wrap .col {
  position: absolute;
  top: 0;
  right: 0;
  width: 80rpx;
  color: #666;
  font-size: 24rpx;
  padding-left: 20rpx;
  text-align: center;
}

.tit-wrap .col image {
  display: block;
  margin: auto;
  width: 40rpx;
  height: 40rpx;
}

.tit-wrap .col::after {
  content: "";
  display: block;
  width: 1px;
  height: auto;
  background: #f1f1f1;
  position: absolute;
  top: 0;
  bottom: 5px;
  left: 0;
}

.sales-p {
  background: #fff;
  color: #999;
  font-size: 28rpx;
  padding: 20rpx 0;
  line-height: 1.5em;
  margin-right: 104rpx;
  text-align: justify;
}

.prod-price {
  font-size: 30rpx;
  height: 100rpx;
  line-height: 100rpx;
}

.price {
  color: #e43130;
  font-size: 26rpx;
  font-weight: 600;
  margin-right: 50rpx;
}

.price-num {
  font-size: 46rpx;
  font-weight: 400;
}

.sales {
  color: #999;
}

.share-icon {
  position: absolute;
  right: 50rpx;
  top: 50rpx;
  background: none;
  line-height: 40rpx;
  border: none;
  outline: none;
  box-shadow: 0;
  padding: 0;
}

.share-icon::after {
  border: none;
}

.share-icon image {
  width: 60rpx;
  height: 60rpx;
}

.share-text {
  font-size: 26rpx;
  color: #999;
  line-height: 30rpx;
}

/* 积分商品价格 */
.integral-prod-price {
  display: inline-block;
  padding: 10rpx 0 40rpx;
  color: #e43130;
  font-size: 30rpx;
}

/** end 商品信息 */


.more {
  position: absolute;
  right: 20rpx;
  width: 60rpx;
  top: 10rpx;
  text-align: right;
  font-size: 40rpx;
  color: #999;
  letter-spacing: 1px;
}

/* 已选 */

.sku {
  padding: 20rpx;
  background: #fff;
  margin-top: 20rpx;
  position: relative;
  line-height: 48rpx;
	display: flex;
}

.sku-tit {
  display: inline-block;
  width: auto;
  font-size: 25rpx;
  color: #999;
	padding-right: 20rpx;
}

.sku-con {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 28rpx;
  font-weight: bold;
}


/** 商品详情 */

.prod-detail {
  /* width: 100%!important; */
  background: #fff;
  margin-top: 20rpx;
  position: relative;
  line-height: 48rpx;
}
rich-text {
  display: block!important;
  width: 100%!important;
}

.det-tit {
  width: 300rpx;
}

.detail-tit {
  font-size: 32rpx;
  position: relative;
  border-bottom: 1px solid #ddd;
  padding: 20rpx;
}

.prod-detail image {
  width: 750rpx !important;
  display: block;
}

rich-text image {
  width: 100% !important;
}

img {
  width: 100% !important;
  display: block;
}

/** end 商品详情 */


/* 积分 底部按钮 */
.foot-btn-box {
  display: block;
  margin-top: 20px;
}
.ex-integral-foot {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  color: #fff;
  z-index: 9999;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
  background: #fff;
  margin-top: 20px;
  padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: constant(safe-area-inset-bottom);


}
.ex-integral-btn {
  box-sizing: border-box;
  width: 100%;
  margin: 20rpx;
  height: 88rpx;
  background: var(--primary-color);
  color: #fff;
  text-align: center;
  font-size: 30rpx;
  line-height: 88rpx;
  border-radius: 28px;
}


/* 购物车数量显示 */
.badge {
  position: absolute;
  top: 10rpx;
  left: 65rpx;
  display: inline-block;
  width: 28rpx;
  height: 28rpx;
  border-radius: 14rpx;
  background-color: #e43130;
  text-align: center;
  line-height: 28rpx;
  font-size: 18rpx;
  color: #fff;
}
/* .badge-1 {
  width: 36rpx;
} */
.badge-2 {
  width: 48rpx;
  left: 52rpx;
}
/** end  底部按钮 */


/** 规格弹窗**/

.pup-sku {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.pup-sku-main {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 375px;
  max-height: 475px;
  background-color: #fff;
}

.pup-sku-header {
  position: relative;
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  height: 70px;
  padding: 0 0 10px 110px;
  background-color: #fff;
  border: 1rpx solid #f2f2f2;
}

.pup-sku-img {
  position: absolute;
  left: 10px;
  top: 0px;
  border-radius: 2px;
  width: 90px;
  height: 90px;
  border: 0 none;
  vertical-align: top;
  background-color: #fff;
}

.prod-title {
  font-size: 28rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
  margin: 15rpx 0;
}

.pup-sku-price {
  display: inline-block;
  height: 1.5em;
  line-height: 1.5em;
  color: #e43130;
  font-size: 26rpx;
}

.pup-sku-price-int {
  font-size: 34rpx;
}

.pup-sku-prop {
  display: block;
  font-size: 26rpx;
  color: #333;
  line-height: 1.4em;
  padding-right: 10px;
  margin-top: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pup-sku-prop text:first-child {
  color: #999;
}

.pup-sku-body {
  box-sizing: border-box;
  max-height: 379px;
  padding-bottom: 100px;
  overflow: auto;
}
/*ipad适应样式 */
@media screen and (min-width: 500px) {
	/* 购买弹窗 */
	.pup-sku-body {
		max-height: 600px;
		padding-bottom: 120px;
	}
	.pup-sku-main {
		max-height: 775px;
	}
	/* 评论字体大小 */
	.cmt-tag text,.cmt-user,.cmt-cnt{
		font-size: 16px;
	}
}

.pup-sku-area .sku-kind {
  font-size: 12px;
  color: #999;
  margin: 0 10px;
  height: 40px;
  line-height: 40px;
}

.pup-sku-area .sku-choose {
  overflow: hidden;
  margin-bottom: 3px;
}

.sku-choose-item {
  display: inline-block;
  padding: 0 10px;
  min-width: 20px;
  max-width: 270px;
  overflow: hidden;
  height: 30px;
  line-height: 30px;
  text-align: center;
  margin-left: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  border: 1px solid #E9EBED;
}

.sku-choose-item.active {
  background-color: #fff;
  color: var(--primary-color);
  border: 1px solid var(--primary-color) !important;
}

.sku-choose-item.gray {
  background-color: #f9f9f9;
  color: #ddd;
}
.sku-choose-item.dashed {
  border:1px dashed #333;
}

.pup-sku-count {
  padding: 0 10px 13px;
  font-size: 12px;
  margin-top: 20rpx;
}

.pup-sku-count .count-name {
  color: #999;
  height: 31px;
  line-height: 31px;
  width: 100rpx;
}

.pup-sku-count .num-wrap {
  position: relative;
  z-index: 0;
  width: 110px;
  float: right;
  vertical-align: middle;
  display: flex;
}

.num-wrap .minus, .num-wrap .plus {
  position: relative;
  max-width: 30px;
  min-width: 30px;
  height: 30px;
  line-height: 30px;
  background: #f7f7f7;
  text-align: center;
}

.num-wrap .minus {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.num-wrap .plus {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.num-wrap .row {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -7px;
  margin-top: -1px;
  width: 14px;
  height: 2px;
  background-color: #ccc;
}

.num-wrap .col {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -1px;
  margin-top: -7px;
  width: 2px;
  height: 14px;
  background-color: #999;
}

.pup-sku-count .text-wrap {
  position: relative;
  width: 45px;
  z-index: 0;
  margin: 0 1px;
}

.pup-sku-count .text-wrap input {
  height: 30px;
  width: 100%;
  color: #333;
  background: #fff;
  font-size: 12px;
  text-align: center;
  border: none;
  background: #f7f7f7;
}

.pup-sku-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  height: 100rpx;
  z-index: 999;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.pup-sku-footer .btn {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  width: 0;
  background-color: #fff;
  font-size: 30rpx;
  flex-flow: column;
}

.pup-sku-footer .btn.cart {
  background: #584e61;
  color: #fff;
}

.pup-sku-footer .btn.buy {
  background: #e43130;
  color: #fff;
}
.pup-sku-footer.gray .btn.cart{
  background: #ddd;
}
.pup-sku-footer.gray .btn.buy {
background: #ddd;
}


.promo-con{
  position: fixed;
  bottom: 200rpx;
  right:40rpx;
}
.promo-con .earn{
  background:rgba(230, 55, 58,0.9);
  box-shadow: 1px 2px 6px rgba(78, 78, 78, 0.4);
  color:#fff;
  border-radius: 50%;
  width:70rpx;
  height:70rpx;
  font-size: 30rpx;
  margin: auto;
  text-align: center;
  line-height: 70rpx;
}

/*分享弹窗 */
.promo-share{
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height:100%;
z-index: 9999;
}
.promo-tit {
 padding-top: 40rpx;
  font-size: 25rpx;
  text-align: center;
  margin-left: 80rpx;


}
.promo-main{
  background: #fff;
  position: absolute;
  bottom: 0rpx;
  width:100%;
  height:500rpx;
  z-index: 9999;
}
.promo-icons-close image{
  width: 45rpx;
  height: 45rpx;
  /* border-radius: 55%; */
  float: right;
  padding-right: 20rpx;
  padding-top: 20rpx;
}
.promo-desc{
  margin-left: 73rpx;
   width: 600rpx;
  text-align: center;
}

.promo-desc text{
  padding:20rpx;
font-size: 20rpx;
color: #999;
line-height: 34rpx;
display: inline-block;

}
.promo-icons{
  font-size: 20rpx;
  color: #666;
  display: flex;
  justify-content: space-around;
  padding:10rpx 150rpx;

}
.promo-icons image{
  width: 75rpx;
  height: 75rpx;
  border-radius: 50%;

}
.promo-img1 {
  display: flex;
  flex-flow: column;
  align-items: center;
  line-height: 75rpx;
  font-size: 22rpx;
  background: none;
  border: 0;
  margin: 0;
  padding: 0;
}

.promo-img1::after {
  border: 0;
}

.promo-btn{
  font-size: 28rpx;
  width:90%;
  height: 80rpx;
  border: 1px #e5e5e5 solid;
  text-align: center;
  line-height: 80rpx;
  margin-left: 40rpx;
}

/** 二维码弹窗 */

.code-popup {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.code-main {
  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  position: fixed;
  width: 500rpx;
  height: 700rpx;
}

.close {
  color: #aaa;
  border-radius: 12px;
  line-height: 20px;
  text-align: center;
  height: 20px;
  width: 20px;
  font-size: 18px;
  padding: 1px;
  top: 10px;
  right: 10px;
  position: absolute;
}

.close::before {
  content: "\2716";
}

.close-png {
  width: 50rpx;
  height: 50rpx;
  margin-top: -10rpx;
}

.code-v {
  padding: 20rpx;
  border-radius: 6rpx;
  background: #fff;
}

.wx-code {
  width: 460rpx;
  height: 460rpx;
}

.close-v {
  text-align: right;
  height: 60rpx;
}

.code-txt {
  margin-top: 20rpx;
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  background: #3a86b9;
  border-radius: 6rpx;
  height: 80rpx;
  line-height: 80rpx;
}

/** 二维码弹窗 end */



/* 倒计时栏 */
.countdown-box {
  width: 100%;
  height: 85rpx;
  line-height: 85rpx;
  /* background: #e92121; */
  background: #e43130;
  color: #fff;
  padding: 0 30rpx;
  box-sizing: border-box;
}
.second-kill {
  font-size: 33rpx;
}
.countdown-content {
  float: right;
  font-size: 28rpx;
  height: 85rpx;
  line-height: 85rpx;
  text-align: left;
}
.countdown-tips {
  padding: 10rpx 0;
  font-size: 25rpx;
  opacity: 0.8;
  margin-right: 10rpx;
}
.countdown-time {
  font-size: 25rpx;
  padding-top: 5rpx;
}

/* 价格 */
.goods-price {
  padding: 15rpx 0;
  border-bottom: 1px solid #f3f3f3;
}
.current-price {
  color: #e43130;
  font-size: 34rpx;
  margin-right: 15rpx;
  vertical-align: middle;
}
.original {
  font-size: 26rpx;
  color: #888888;
  margin-top: 15rpx;
}
.original-price {
  font-size: 26rpx;
  text-decoration: line-through;
  color: #999999;
  margin-left: 10rpx;
}

/* 列表为空 */
.empty {
  margin-top: 100rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  
  color: #999;
  line-height: 2em;
}

/*弹窗解决遮罩层移动问题*/
.contenta{
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
.page-hidden{
  height: 100%;
  overflow: hidden
}

/* 引导蒙版 end */


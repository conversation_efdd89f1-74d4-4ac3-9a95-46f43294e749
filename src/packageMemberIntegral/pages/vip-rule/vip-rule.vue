<template>
  <view class="vip-rule-container">
    <view class="vip-rule-header">
      <view class="vip-rule-header-text">
        <view class="vip-rule-header-text-line1">
          <text>会员规则</text>
        </view>
        <view class="vip-rule-header-text-line2">
          <n-i icon="benifits-header-icon" width="24rpx" height="20rpx"></n-i>
          <text>会员等级与成长值说明</text>
        </view>
      </view>
      <view class="vip-rule-header-img">
        <n-i icon="vip-rule-bg01" ext="png" width="340rpx" height="340rpx"></n-i>
      </view>
    </view>

    <view class="vip-rule-intro">
      <VipIntroItemTitle style="margin-bottom: 16rpx;">等级介绍</VipIntroItemTitle>
      <view class="normal-text">
        wenet会员中心共包含2个等级，根据用户的成长值进行评估，成长值越高，享有的权益越丰厚。
      </view>

      <view class="table-container">
        <!-- 表格 -->
        <view class="table">
          <!-- 表头 -->
          <view class="table-row">
            <view class="table-cell">等级</view>
            <view class="table-cell highlighted">超级会员</view>
            <view class="table-cell">普通会员</view>
          </view>

          <!-- 表格内容 -->
          <view class="table-row">
            <view class="table-cell">成长值</view>
            <view class="table-cell highlighted">158以上</view>
            <view class="table-cell">0~158</view>
          </view>
        </view>
      </view>

      <view class="normal-text" style="margin-bottom: 32rpx;">
        等级有效期为6个月，以学期为依据划分。春季学期为每年的2月1日至7月31日，秋季学期为每年的8月1日至次年1月31日。等级到期将根据届时的成长值重新匹配等级。
      </view>

      <VipIntroItemTitle style="margin-bottom: 16rpx;">等级权益介绍</VipIntroItemTitle>
      <view class="normal-text">
        等级越高，享有权益越丰富。用户达成特定等级，可获得校园wenet提供的权益，详细权益内容见下图。部分权益仅支持在已开通相关服务的学校提供，请您谅解。权益将不定期做出优化、调整，实际可使用权益以页面展示为准。
      </view>

      <view class="table-container">
        <!-- 表格 -->
        <view class="table">
          <!-- 表头 -->
          <view class="table-row">
            <view class="table-cell">权益</view>
            <view class="table-cell highlighted">超级会员</view>
            <view class="table-cell">普通会员</view>
          </view>

          <!-- 表格内容 -->
          <view class="table-row">
            <view class="table-cell">会员商城</view>
            <view class="table-cell highlighted">158以上</view>
            <view class="table-cell">0~158</view>
          </view>
          <view class="table-row">
            <view class="table-cell">游戏下载加速</view>
            <view class="table-cell highlighted"><n-i icon="vip-rule-exit" width="32rpx" height="32rpx"></n-i></view>
            <view class="table-cell">—</view>
          </view>
          <view class="table-row">
            <view class="table-cell">学习平台</view>
            <view class="table-cell highlighted"><n-i icon="vip-rule-exit" width="32rpx" height="32rpx"></n-i></view>
            <view class="table-cell">—</view>
          </view>
          <view class="table-row">
            <view class="table-cell">无感知认证</view>
            <view class="table-cell highlighted"><n-i icon="vip-rule-exit" width="32rpx" height="32rpx"></n-i></view>
            <view class="table-cell">—</view>
          </view>
        </view>
      </view>

      <VipIntroItemTitle style="margin-bottom: 16rpx;">成长值说明</VipIntroItemTitle>
      <view class="normal-text" style="margin-bottom: 32rpx;">
        成长值是根据用户在校园wenet小程序本学期内的消费行为计算得出的分值，每消费1元，成长值+1分。 
        <view>注：购买会员及会员商城商品均不计入成长值计算</view>
        <view class="normal-text red">更新与有效期：</view>
        <view class="normal-text">（1）消费分在订单确认收货后，会在次日9:00前进行更新（若遇系统延迟，请耐心等待）。</view>
        <view class="normal-text">（2）新获得的消费分将统一于下一学期的1日零点失效。例如2025年5月1日获得的消费分或，将在2025年8月1日零点失效。</view>
      </view>

      <VipIntroItemTitle>如何获得会员权益</VipIntroItemTitle>
      <view class="upgrade-way-title">
        <view class="upgrade-way-index">
          <text class="upgrade-way-index-text">方法一</text>
          <n-i icon="sp-rectangle" width="160rpx" height="41rpx" class="corner"></n-i>
        </view>
        <view class="upgrade-way-desc">累计实付满159，成长值达到159，免费赠送超级会员。</view>
      </view>
      <view class="upgrade-way-title">
        <view class="upgrade-way-index">
          <text class="upgrade-way-index-text">方法二</text>
          <n-i icon="sp-rectangle" width="160rpx" height="41rpx" class="corner"></n-i>
        </view>
        <view class="upgrade-way-desc">以9.9元/月的价格付费升级为超级会员。</view>
      </view>

      <view class="normal-text notice">
        <view>
          注：若同时通过以上两种方法获得会员权益，则权益将同时生效。例如3月1日购买了6个月超级会员，4月1日成长值达到159，免费升级为超级会员，则会员权益将在7月31日到期。
        </view>
        <view>
          超级会员一经开通进入当期服务有效期，整套会员应用及权益产品即时交付生效，并持续供给权益内容，具有不可拆分性。若您已经使用权益，会员费用不支持退还。若出现以下情形造成超级会员服务中止或终止，不支持退还会员服务费用：
        </view>
        <view>
          1. 您因自身原因未及时使用相应权益或主动放弃使用权益。
        </view>
        <view>
          2. 您因超级会员业务运营过程中出现的权益内容、服务、功能正常变动而主张主动退出超级会员。
        </view>
      </view>
      
    </view>

  </view>
</template>

<script>
import NI from '@/components/n-i'
import VipIntroItemTitle from '../../components/vip-intro-item-title'

export default {
  name: 'vip-rule',
  components: {
    VipIntroItemTitle,
    NI
  },
  data () {
    return {
    }
  },
  methods: {

  },
  computed: {

  },
  mounted () {

  },
}
</script>

<style scoped>
.vip-rule-container {
  background-color: var(--primary-color);
  width: 100%;
  padding: 48rpx;
  box-sizing: border-box;
  min-height: 100vh;
}

.vip-rule-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 170rpx;
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

.vip-rule-header-text {
  display: flex;
  flex-direction: column;
  z-index: 10;
}
.vip-rule-header-text-line1 {
  font-family: Helvetica;
  font-size: 64rpx;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.vip-rule-header-text-line1 .text.bold {
  font-weight: 500;
}
.vip-rule-header-text-line2 {
  font-family: Helvetica;
  font-size: 32rpx;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.vip-rule-header-img {
  position: absolute;
  top: 0;
  right: 0;
}

.vip-rule-intro {
  border-radius: 16px;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  padding: 32rpx 16rpx;
}
.normal-text {
  font-size: 28rpx;
  color: #000;
  line-height: 44rpx;
  font-weight: 200;
}

.normal-text.red {
  color: var(--primary-color);
}

/* 表格容器 */
.table-container {
  position: relative;
  width: 100%;
  margin-top: 26rpx;
  margin-bottom: 32rpx;
}

/* 表格主体 */
.table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

/* 表格行 */
.table-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color:  rgba(217,217,217,0.20);
}

.table-row:nth-child(2n) {
  background-color: #fff;
}

/* 表格单元格 */
.table-cell {
  flex: 1;
  padding: 24rpx 16rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  z-index: 1;
}


/* 高亮列 */
.highlighted {
  background-color: #ffe8c2;
  color: #d97a00;
  font-weight: bold;
  z-index: 10;
  position: relative;
  margin-right: -20rpx; /* 让高亮列超出右边 */
  padding-left: 32rpx;
  padding-right: 32rpx;
  border: none;
  position: relative;
}

.table .table-row:first-child .table-cell.highlighted::before {
  content: '';
  width: 100%;
  box-sizing: border-box;
  background-color: #ffe8c2;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  position: absolute;
  top: -30rpx;
  left: 0;
  height: 30rpx;
}
.table .table-row:last-child .table-cell.highlighted::after {
  content: '';
  width: 100%;
  box-sizing: border-box;
  background-color: #ffe8c2;
  position: absolute;
  bottom: -30rpx;
  left: 0;
  height: 30rpx;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
}

.upgrade-way-title {
  width: 100%;
  position: relative;
  font-size: 28rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
}

.upgrade-way-index {
  width: fit-content;
  box-sizing: border-box;
  color: var(--primary-color);
  font-weight: 500;
  position: relative;
  height: 40rpx;
  width: 100%;
}

.upgrade-way-desc {
  width: 100%;
  padding: 10rpx 18rpx;
  color: #000;
  box-sizing: border-box;
  background-color: #FFE3E6;
}
.upgrade-way-index-text {
  z-index: 1;
  position: absolute;
  left: 0;
  padding: 0 22rpx;
  width: fit-content;
}
.corner {
  position: absolute;
  top: 0;
  left: 0;
}

.notice {
  padding: 16rpx;
  background-color: #F4F4F4;
}
</style>
/* 会员首页 */
page{
	background: #fff;
}
.member-index {
  position: relative;
}

.background-box {
  width: 100%;
  display: flex;
  justify-content: center;
}

.jf-bg{
	width: 710rpx;
	height: 260rpx;
	margin: 30px auto;
}

/* 会员信息 */
.member-index .member-msg {
  position: relative;
  width: 750rpx;
  height: 350rpx;
}

.member-index .member-msg .m-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.member-index .member-msg .member-info {
  padding: 50rpx 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.member-index .member-msg .member-info .head-portrait {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
}

.member-index .member-msg .member-info .head-portrait image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.member-index .member-msg .member-info .text-box {
  margin: 0 10rpx;
  flex: 1;
}

.member-index .member-msg .member-info .text-box .name-box {
  display: flex;
  align-items: center;
}

.member-index .member-msg .member-info .text-box .name-box .name {
	width: 160rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  /* max-width: 196rpx; */
  /* display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1; */
}

.member-index .member-msg .member-info .text-box .name-box .level {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40rpx;
  padding: 0 14rpx;
  margin-left: 10rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 32rpx;
}

.member-index .member-msg .member-info .text-box .name-box .level .l-bg {
  width: 28rpx;
  height: 28rpx;
}

.member-index .member-msg .member-info .text-box .name-box .level .text {
  color: #7c4733;
  font-size: 18rpx;
  font-family: arial;
  position: relative;
  line-height: 1.4em;
  margin-left: 12rpx;
}

.member-index .member-msg .member-info .text-box .date {
  margin-top: 14rpx;
  font-size: 24rpx;
  color: #fff;
}

.member-index .member-msg .member-info .buy-btn {
  width: 170rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #7c4733;
  background: #fbe3b4;
  box-shadow: 0 0 5px rgba(0,0,0,.1);
  border-radius: 70rpx;
  font-weight: 600;
}

.member-index .member-msg .grow-box {
  position: absolute;
  left: 0;
  bottom: 26rpx;
  color: #fff;
  display: flex;
  align-items: center;
  z-index: 1;
  width: 100%;
  padding: 0 70rpx;
  box-sizing: border-box;
}

.member-index .member-msg .grow-box .item {
  flex: 1;
  text-align: center;
}

.member-index .member-msg .grow-box .item .text {
  font-size: 24rpx;
}

.member-index .member-msg .grow-box .item .number-box {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.member-index .member-msg .grow-box .item .number-box .number {
  font-family: arial;
  font-size: 40rpx;
}


.member-index .member-msg .grow-box .item .number-box .arr {
  font-size: 0;
  margin-left: 10rpx;
  margin-bottom: 4rpx;
}

.member-index .member-msg .grow-box .item .number-box .arr image {
  width: 12rpx;
  height: 20rpx;
}

/* 会员信息 end */

.member-tit {
  display: flex;
  align-items: center;
}

.member-tit .text {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  position: relative;
  padding-left: 16rpx;
}

.member-tit .text::before {
  position: absolute;
  left: 0;
  top: 20%;
  display: block;
  width: 6rpx;
  height: 60%;
  content: " ";
  background: var(--primary-color);
  border-radius: 6rpx;
}

.member-tit .more {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 每日签到、积分活动、积分任务 */

.member-index .sign-in,
.member-index .activity,
.member-index .mission {
  margin-top: 50rpx;
  padding: 0 30rpx;
}

.member-index .sign-in .con-box {
  display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}

.member-index .sign-in .con-box .item {
  width: 140rpx;
  height: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ebebeb;
  border-radius: 20rpx;
  margin-top: 12px;
  box-sizing: border-box;
  padding-top: 20rpx;
  margin-right: 40rpx;
}

.member-index .sign-in .con-box  .active-item{
	background-color: #1B77F8;
}

.member-index .sign-in .con-box .item:nth-child(7n), .member-index .sign-in .con-box .item:nth-child(4n) {
  margin-right: 0;
}

.member-index .sign-in .con-box .item .number {
/*  min-width: 80rpx;
  height: 80rpx;
  border-radius: 50%; */
/*  text-align: center;
  line-height: 80rpx; */
  /* background: #ffeee6; */
  color: #2A82E4;
  font-family: arial;
  font-size: 24rpx;
}

.member-index .sign-in .con-box .active-item .number {
  color: #fff;
}

.jinbi{
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 16rpx;
}

.member-index .sign-in .con-box .item .number.num-text {
  color: #FFB13F;
  margin-bottom: 10px;
}

.member-index .sign-in .con-box .item .number.active {
  background: -webkit-gradient(linear,left top,right top,from #f73200,to(#fe730c));
  background: -o-linear-gradient(to right,#fe730c,#f73200);
  background: linear-gradient(to right,#fe730c,#f73200);
  background: -webkit-linear-gradient(to right,#fe730c,#f73200);
  color: #fff;
}

.member-index .sign-in .con-box .item .day {
  font-size: 24rpx;
  margin-bottom: 10rpx;
  color: #2A82E4;
}

.member-index .sign-in .con-box .active-item .day {
  color: #fff;
}

.member-index .activity .con-box {
  display: flex;
  margin-top: 30rpx;
}

.member-index .activity .con-box .item {
  width: 330rpx;
  height: 168rpx;
  position: relative;
}

.member-index .activity .con-box .item:first-child {
  margin-right: 30rpx;
}

.member-index .activity .con-box .item .bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 10rpx rgba(255,121,1,.5);
  border-radius: 16rpx;
}

.member-index .activity .con-box .item .b-text,
.member-index .activity .con-box .item .s-text {
  position: relative;
  color: #fff;
  margin-left: 20rpx;
}

.member-index .activity .con-box .item .b-text {
  font-size: 28rpx;
  font-weight: 600;
  margin-top: 48rpx;
}

.member-index .activity .con-box .item .s-text {
  font-size: 24rpx;
  margin-top: 6rpx;
  opacity: 0.8;
}

.member-index .mission .con-box .item {
  margin-top: 30rpx;
  display: flex;
  align-items: center;
  padding-bottom: 30rpx;
  border-bottom: 1px solid #eee;
}

.member-index .mission .con-box .item:last-child {
  border: 0;
}

.member-index .mission .con-box .item .mission-box {
  flex: 1;
  margin-right: 30rpx;
}

.member-index .mission .con-box .item .mission-box .name {
  font-size: 28rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-index .mission .con-box .item .mission-box .mission-des {
  color: #999;
  font-size: 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 10rpx;
}

.member-index .mission .con-box .item .btn {
  background: #e43130;
  color: #fff;
  padding: 10rpx;
  border-radius: 60rpx;
  font-size: 28rpx;
  text-align: center;
}

.member-index .mission .con-box .item .btn.done {
  background: #ccc;
}

/* 每日签到、积分活动、积分任务 end */

/* 积分兑换 */

.integral-list {
  padding: 30rpx 30rpx 30rpx;
  background: #fff;
}

.integral-list .con-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.integral-list .con-box .integral-item {
  box-sizing: border-box;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.08);
}
/* .integral-list .con-box .item {
  margin: 30rpx 30rpx 0 0;
  width: 330rpx;
  background: #fff;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
}

.integral-list .con-box .item:nth-child(2n) {
  margin-right: 0;
}

.integral-list .con-box .item .img {
  width: 290rpx;
  height: 290rpx;
  font-size: 0;
}

.integral-list .con-box .item .img image {
  width: 100%;
  height: 100%;
}

.integral-list .con-box .item .name {
  font-size: 28rpx;
  margin: 16rpx 0 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.integral-list .con-box .item .price {
  position: relative;
  padding-left: 34rpx;
  height: 28rpx;
  line-height: 28rpx;
  font-size: 24rpx;
}

.integral-list .con-box .item .price .icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 28rpx;
  height: 28rpx;
}

.integral-list .con-box .item .old-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  font-family: arial;
} */

/* 积分兑换 end */



.signIn {
  position: relative;
  width: 100%;
  z-index: 0;
  background: linear-gradient(var(--primary-color) 58%, #fff 90%);
}

.signIn .gift-img {
  position: absolute;
  width: 342rpx;
  height: 342rpx;
  right: 0rpx;
  z-index: -1;
}
.signIn-info {
  width: 100%;
  padding: 80rpx 57rpx;
  padding-bottom: 0rpx;
  box-sizing: border-box;
}

.signIn-info .score {
  display: inline-block;
  color: #FFF;
  font-size: 80rpx;
  font-weight: 600;
  margin-right: 16rpx;
}
.signIn-info .title {
  color: #FFF;
  font-size: 36rpx;
  font-weight: 400;
  margin-bottom: 11rpx;
}
.signIn-info .sub {
  color: #FFF;
  font-size: 24rpx;
  font-weight: 400;
}
.signIn-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-sizing: border-box;
  gap: 38rpx;
  padding: 32rpx;
  margin: 48rpx;
  margin-bottom: 0rpx;
  border-radius: 16rpx;
  background: #FFF;
  box-shadow: 0px 0px 40rpx 0px rgba(0, 0, 0, 0.08);
}

.signIn-box .signIn-list {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.signIn-list .item{
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 72rpx;
  height: 132rpx;
  gap: 16rpx;
  border-radius: 98rpx;
  background: #FAFAFA;
}
.signIn-list.item .coin {
  width: 30rpx;
  height: 30rpx;
  flex-shrink: 0;
}
.signIn-list .number, .signIn-list .day{
  color: #999;
  text-align: center;
  font-size: 24rpx;
  font-weight: 600;
}
.signIn-list .actived-item {
  background: #FCEAF3;
}
.signIn-list .actived-item .number{
  color: var(--primary-color);
}

.signIn-list .active-item {
  background: var(--primary-color);
}
.signIn-list .active-item .number{
  color: #FCEAF3;
}
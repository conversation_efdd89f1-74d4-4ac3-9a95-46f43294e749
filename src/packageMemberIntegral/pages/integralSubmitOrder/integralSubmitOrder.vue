<template>
	<!-- 积分商品提交订单页面 -->
	<!--pages/integralSubmitOrder/integralSubmitOrder.wxml-->
	<view class="container">
		<view class="submit-order">
			<!-- 收货地址 -->
			<view class="delivery-addr" @tap="toAddrListPage">
				<view class="addr-bg" v-if="!userAddr">
					<view class="add-addr">
						<view class="plus-sign-img">
							<image :src="`${staticPicDomain}images/icon/plus-sign.png`"></image>
						</view>
						<text>{{i18n.addNewAddress}}</text>
					</view>
					<view class="arrow empty"></view>
				</view>
				<view class="addr-bg whole" v-if="userAddr">
					<view class="addr-icon">
						<image :src="`${staticPicDomain}images/icon/addr.png`"></image>
					</view>
					<view class="user-info">
						<text class="item">{{userAddr.receiver}}</text>
						<text class="item">{{userAddr.mobile}}</text>
					</view>
					<view class="addr">{{userAddr.province}}{{userAddr.city}}{{userAddr.area}}{{userAddr.addr}}</view>
					<view class="arrow"></view>
				</view>
			</view>
			<!-- 店铺商品明细 -->
			<view class="prod-item">
				<view class="item-box">
					<!-- 商品信息 -->
					<view class="item-cont">
						<view class="prod-pic">
							<image :src="productItemDto.pic"></image>
						</view>
						<view class="prod-info">
							<view class="prodname">{{productItemDto.prodName}}</view>
							<view class="prod-info-cont">{{productItemDto.skuName}}</view>
							<view class="show-price">
								<view class="price-nums">
									<text class="prodprice"><text class="symbol" v-if="productItemDto.price">￥</text>
										<text class="big-num" v-if="productItemDto.price">{{parsePrice(productItemDto.price)[0]}}</text>
										<text class="small-num" v-if="productItemDto.price">.{{parsePrice(productItemDto.price)[1]}}</text>
										<text class="small-num" decode="true" v-if="productItemDto.price">&ensp;+&ensp;</text>
									</text>
									<text class="prodcount">x{{productItemDto.prodCount}}</text>
								</view>
								<!-- 积分 -->
								<view class="integral-num">
									<text decode="true">{{productItemDto.scorePrice / productItemDto.prodCount}} {{i18n.integral}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- /店铺商品明细 -->

			<!-- 店铺优惠券和买家留言 -->
			<view class="order-msg">
				<view class="msg-item">
					<view class="item">
						<text>{{i18n.storeNotes}}：</text>
						<input :placeholder="i18n.storeNotesTips" maxlength="100" :value="remarks" @input="onRemarkIpt"></input>
					</view>
				</view>
			</view>
			<!-- /店铺优惠券和买家留言 -->

			<!-- 总金额计算 -->
			<view class="order-msg">
				<view class="msg-item">
					<!-- 商品总额 -->
					<view class="item">
						<view class="item-tit">{{i18n.comTotal}}：</view>
						<view class="item-txt price">
							<text class="symbol">￥</text>
							<text class="big-num">{{parsePrice(total)[0]}}</text>
							<text class="small-num">.{{parsePrice(total)[1]}}</text>
						</view>
					</view>
					<!-- 应付运费 -->
					<view class="item">
						<view class="item-tit">{{i18n.freightPayable}}：</view>
						<view class="price black item-txt">
							<text class="symbol">￥</text>
							<text class="big-num">{{parsePrice(transfee)[0]}}</text>
							<text class="small-num">.{{parsePrice(transfee)[1]}}</text>
						</view>
					</view>
					<!-- 平台开启会员包邮(运费减免) -->
					<view class="item" v-if="freeTransfee">
						<view class="item-tit">{{i18n.memberPackage}}：</view>
						<view class="price item-txt">
							<text class="symbol">-￥</text>
							<text class="big-num">{{parsePrice(freeTransfee)[0]}}</text>
							<text class="small-num">.{{parsePrice(freeTransfee)[1]}}</text>
						</view>
					</view>
					<!-- 优惠金额 -->
					<view class="item">
						<view class="item-tit">{{i18n.preferentialAmount}}：</view>
						<view class="item-txt price">
							<text class="symbol">-￥</text>
							<text class="big-num">{{parsePrice(orderReduce)[0]}}</text>
							<text class="small-num">.{{parsePrice(orderReduce)[1]}}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- /总金额计算 -->
		</view>


		<!-- 底部栏 -->
		<view class="submit-order-footer">
			<view class="sub-order">
				<view class="item-txt">{{i18n.total}}:<view class="price">
						<text class="symbol" v-if="actualTotal">￥</text>
						<text class="big-num" v-if="actualTotal">{{parsePrice(actualTotal)[0]}}</text>
						<text class="small-num" v-if="actualTotal">.{{parsePrice(actualTotal)[1]}}</text>
						<text class="small-num" decode="true" v-if="actualTotal">&ensp;+&ensp;</text>
					</view>
					<!-- 积分 -->
					<view class="integral-num">
						<text decode="true">{{scorePrice}} {{i18n.integral}}</text>
					</view>
				</view>
			</view>
			<view class="footer-box" @tap="toPay">{{i18n.submitOrders}}</view>
		</view>
    <PayComponent ref="payRef" />
	</view>
</template>

<script>
	var http = require("../../../utils/http.js")
	var big = require("../../../utils/big.min.js")
	import Pay from "../../../utils/pay.js";
  import PayComponent from '@/components/pay/pay'

	export default {
		data() {
			return {
				popupShow: false,
				// 订单入口 0购物车 1立即购买
				orderEntry: "0",
				userAddr: null,
				orderItems: [],
				//所有店铺的数据
				productItemDto: [],
				//商品信息
				actualTotal: 0,
				total: 0,
				totalCount: 0,
				transfee: 0,
				freeTransfee: 0, // 用户等级免运费金额
				reduceAmount: 0,
				couponIds: [],
				orderReduce: 0,
				platformCoupons: [],
				//整个订单可以使用的优惠券列表
				scorePrice: 0,
				remarks: '',
				//留言
				isPurePoints: false // 是否纯积分商品
			}
		},
		props: {},
    components: {
      PayComponent
    },
		computed:{
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.setData({
				orderEntry: options.orderEntry
			})
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			var pages = getCurrentPages()
			var currPage = pages[pages.length - 1]
			if (currPage.selAddress == "yes") {
				this.setData({
					//将携带的参数赋值
					userAddr: currPage.item
				})
			}
			this.loadOrderData()
		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},

		methods: {
			//加载订单数据
			loadOrderData: function() {
				var addrId = 0
				if (this.userAddr != null) {
					addrId = this.userAddr.addrId
				}
				uni.showLoading({
					// #ifndef MP-TOUTIAO
					mask: true
					// #endif
				})
				var params = {
					url: "/p/score/confirm",
					method: "POST",
					data: {
						addrId: addrId,
						orderItem: this.orderEntry === "1" ? JSON.parse(wx.getStorageSync("orderItem")) : undefined,
						basketIds: this.orderEntry === "0" ? JSON.parse(wx.getStorageSync("basketIds")) : undefined,
						couponIds: this.couponIds,
						userChangeCoupon: 1
					},
					callBack: res => {
						uni.hideLoading()
						// 运费
						let transfee = Number(new big(res.totalTransfee).minus(new big(res.freeTransfee)).valueOf())
						this.setData({
							productItemDto: res.productItemDto,
							//商品信息
							actualTotal: res.actualTotal,
							//实际总值
							total: res.total,
							//商品总值
							totalCount: res.totalCount,
							//商品总数
							userAddr: res.userAddr,
							//地址Dto
							// 用户等级免运费金额
							freeTransfee: res.freeTransfee,
							// 运费
							transfee: transfee >= 0 ? transfee : 0,
							scorePrice: res.productItemDto.scorePrice //商品所需积分
						})
					},
					errCallBack: res => {
						uni.hideLoading()
					}
				}
				http.request(params)
			},

			/**
			 * 提交订单
			 */
			toPay: function() {
				if (!this.userAddr) {
					uni.showToast({
						title: this.i18n.pleaseSelectSddress,
						icon: "none"
					})
					return
				}
				this.submitOrder()
			},

			/**
			 * 提交订单
			 */
			submitOrder: function() {
				var isPurePoints = this.actualTotal > 0 ? '' : 1 // 是否纯积分: 1是
				if (isPurePoints) {
					uni.showModal({
						title: this.i18n.prompt,
						content: this.i18n.confirmSpend + this.scorePrice + this.i18n.scoreToPay,
						confirmText: this.i18n.confirm,
						cancelText: this.i18n.cancel,
						success: (modalRes) => {
							if (modalRes.confirm) {
								var params = {
									url: "/p/score/submit",
									method: "POST",
									data: {
										remarks: this.remarks
									},
									callBack: res => {
										uni.hideLoading()
										Pay.toOrderPay(0,res.orderNumbers, isPurePoints)
									}
								}
								http.request(params)
							}
						}
					})
					return
				}
				var params = {
					url: "/p/score/submit",
					method: "POST",
					data: {
						remarks: this.remarks
					},
					callBack: res => {
						uni.hideLoading()
            this.$refs.payRef.init({
              orderNumbers:res.orderNumbers,
              isPurePoints
            })
					}
				}
				http.request(params)
			},

			/**
			 * 去地址页面
			 */
			toAddrListPage: function() {
				uni.navigateTo({
					url: '/packageUser/pages/delivery-address/delivery-address?order=0'
				})
			},

			/**
			 * 输入备注
			 */
			onRemarkIpt: function(e) {
				this.remarks = e.detail.value
			}
		}
	}
</script>
<style>
	@import "./integralSubmitOrder.css";
</style>

	<template>
	<!--pages/buyVip/buyVip.wxml-->
	<!-- 购买会员页面 -->
	<view class="buy-vip-container">
    <view class="buy-vip-header">
      <view class="buy-vip-header-text">
        <view class="buy-vip-header-text-line1">
          <text>升级</text>
          <text class="text bold">超级会员</text>
        </view>
        <view class="buy-vip-header-text-line2">
          <n-i icon="benifits-header-icon" width="24rpx" height="20rpx"></n-i>
          <text>可享多项会员特权</text>
        </view>
      </view>
      <view class="buy-vip-header-img">
        <n-i icon="vip-rule-bg002" ext="png" width="340rpx" height="340rpx"></n-i>
      </view>
    </view>

    <view class="buy-vip-intro">
      <view class="buy-vip-intro-title">
        <n-i icon="benifits-title-border-left" width="176rpx" height="16rpx"></n-i>
        <text class="buy-vip-title-text">校园wenet会员</text>
        <n-i icon="benifits-title-border-right" width="176rpx" height="16rpx"></n-i>
      </view>

      <view>
        <VipIntroItemTitle>购物权益</VipIntroItemTitle>
        <view class="vip-intro-card1">
          <view class="vip-intro-card1-img">
            <n-i icon="benifits-1" width="112rpx" height="112rpx"></n-i>
          </view>
          <view class="vip-intro-card1-text">
            <view class="vip-intro-card1-text__line1">低价会员商城</view>
            <view>多种聚惠商品、学生的自由私域</view>
          </view>
        </view>

        <VipIntroItemTitle>联网权益</VipIntroItemTitle>
        <view class="vip-intro-card2-list">
          <view class="vip-intro-card2" v-for="(item) in internetBenifits" :key="item.id">
            <view class="vip-intro-card2-img">
              <n-i :icon="item.icon" width="112rpx" height="112rpx"></n-i>
            </view>
            <view class="vip-intro-card2-text">
              {{ item.title }}
            </view>
          </view>
        </view>
        <view class="vip-rights-tips">
          部分权益仅支持在已开通相关服务的学校使用，实际权益以会员中心首页展示为准。
        </view>
      </view>

      <view>
        <VipIntroItemTitle>升级方法</VipIntroItemTitle>
        <view class="upgrade-way-title">
          <view class="upgrade-way-index">方法一</view>
          <view class="upgrade-way-desc">成长值&gt;158 即可免费升级为超级会员</view>
        </view>
        <view class="recommends-prods">
          <HorizontalCard
            @tap-goods="goodsFun"
            v-for="item in recommendsProdList"
            :goods-item="item"
            :key="item.prodId"
            cardSize="large"
            :showOriPrice="false"
          />
        </view>

        <view class="upgrade-way-title">
          <view class="upgrade-way-index">方法二</view>
          <view class="upgrade-way-desc">每月单独付费购买超级会员</view>
        </view>
        <view class="vip-prod">
          <view>
            <HorizontalCard
              @tap-goods="buyNow"
              :goods-item="{ prodId: 0, prodName: '超级会员', price: 9.9, pic: `${staticPicDomain}/images/icon/vip-product-hd.png` }"
              cardSize="large"
              :showOriPrice="false"
            />
          </view>
          <view class="vip-prod-btn-wrap">
            <view class="vip-prod-btn" @tap="buyNow">立即购买</view>
          </view>
        </view>
      </view>

    </view>
		
		<!-- 购买会员弹出层:支付类型选择 -->
		<view class="popup-hide" v-if="popupShow">
			<view class="popup-box radius">
				<view class="popup-tit radius">
					<text>{{i18n.choosePaymentMethod}}</text>
					<text class="close" @tap="closePopup"></text>
				</view>
				<view class="popup-cnt pay-way">
					<!-- 支付方式选择器组件 -->
					<PaymentMethodSelector :showFriendPay="false" :actualTotal="selectPremiumVip.needAmount" :pay-type-str="payTypeStr" @setPayType="setPayType" :totalBalance="totalBalance"/>
					<view class="sure-pay" @tap="toPay">{{i18n.determinePayment}}</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script>
	// pages/buyVip/buyVip.js
  import NI from '@/components/n-i'
	import PaymentMethodSelector from '../../../components/paymentMethodSelector/index'
  import VipIntroItemTitle from '../../components/vip-intro-item-title'
	var http = require("../../../utils/http.js");
	var util = require("../../../utils/util.js");
	import { PayType	} from "../../../utils/constant.js";
	import Pay from "../../../utils/pay.js";
  import GoodsContainer from '@/components/goods-card/goods-container'
  import HorizontalCard from '@/components/goods-card/horizontal-card'
	export default {
		components: {
			PaymentMethodSelector,
      NI,
      VipIntroItemTitle,
      GoodsContainer,
      HorizontalCard
		},
		data() {
			return {
				payTypeStr: 'aliPay',
				payType: PayType.ALIPAY_H5,  //支付类型，默认支付宝
				PayTypeConstant: PayType,  //支付类型常量
				popupShow: false,
				duration: 1000,
				totalBalance: null,
				userInfo: {},
				userLevelInfo: {},
				// 当前会员信息
				premiumVipList: [],
				// 付费会员等级列表
				currentLevelId: 0,
				selectPremiumVip: {},
				currentGrowth: "",
				selectPremiumVipIndex: '',
				// 当前选择的付费会员id
				selectPremiumVipId: 0,
        internetBenifits: [
          { id: 1, icon: 'benifits-2', title: '游戏下载加速' },
          { id: 2, icon: 'benifits-3', title: '学习资源平台' },
          { id: 3, icon: 'benifits-4', title: '无感知认证' },
        ],
        recommendsProdList: []
			};
		},
		props: {},

		computed:{
			i18n() {
				return this.$t('index')
			},
      currentShop() {
        return this.$store.state?.currentShop
      },

		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			// 初始化支付类型数据
			this.payType = util.initPayType().payType
			this.payTypeStr = util.initPayType().payTypeStr
      this.getVipCenterPageConfig(this.currentShop.shopId)
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {
			this.setData({});
		},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			//头部导航标题
			uni.setNavigationBarTitle({
				title:this.i18n.membershipCentre
			});

			// 获取会员信息
			this.getUserLevelInfo();
			// 获取年费会员列表
			this.getPayMemberList();
			// 获取用户信息
			this.queryUserInfo();
			// 获取用户余额
			this.queryCurrentBalance()

		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
      goodsFun(prod) {
        if (prod.prodId) {
          uni.navigateTo({
            url: '/pages/prod/prod?prodid=' + prod.prodId
          })
        }
			},
      getProductsByIds(ids) {
        if (!ids || !ids.length) {
          return
        }
        return new Promise((resolve, reject) => {
          http.request({
            url: '/prod/listProdByIdsAndType',
            method: 'GET',
            data: {
              prodIds: ids.join(','),
              prodType: ''
            },
            callBack: res => {
              resolve(res)
            },
            errCallBack() {
              reject('')
            }
          })
        })
      },
      getShopVipConfig(shopId) {
        if (!shopId) {
          return
        }
        return new Promise((resolve, reject) => {
          http.request({
            url: '/shop/getMemberShopConfig',
            method: 'GET',
            data: {
              shopId
            },
            callBack: res => {
              const configJson = res.config
              if (configJson) {
                const configObj = JSON.parse(configJson)
                resolve(configObj)
              }
            },
            errCallBack() {
              reject('')
            }
          })
        })
      },
      async getVipCenterPageConfig(shopId) {
        const currentShopConfig = await this.getShopVipConfig(shopId)
        if (currentShopConfig) {
          const currentShopVipBenefitsPage = currentShopConfig.vipBenefitsPage
          if (!currentShopVipBenefitsPage) {
            return
          }
          const prodIds = currentShopVipBenefitsPage.recommendsProducts.map(el => el.prodId);
          const prodList = await this.getProductsByIds(prodIds)
          if (prodList) {
            this.recommendsProdList = prodList
          }
        }
      },
			/**
			 * 设置支付类型
			 */
			setPayType(paymentInfo) {
				this.payType = paymentInfo.payType
				this.payTypeStr = paymentInfo.payTypeStr
			},

			/**
			 * 关闭支付类型选择弹窗
			 */
			closePopup: function() {
				this.popupShow = false
			},

			/**
			 * 获取用户信息
			 */
			queryUserInfo : function() {
			  uni.showLoading();
			  var params = {
			    url: "/p/user/userInfo",
			    method: "GET",
			    data: {},
			    callBack: (res) => {
			      uni.hideLoading();
			      // this.nickName =  res.nickName,  //用户昵称
			      // this.pic = res.pic
				  this.setData({
					  userInfo:res
				  })
			    }
			  };
			  http.request(params);
			},

			/**
			 * 获取用户当前余额
			 */
			queryCurrentBalance: function() {
				var params = {
					url: "/p/user/getUserInfo",
					method: "GET",
					data: {},
					callBack: res => {
						this.totalBalance = res.totalBalance
					}
				};
				http.request(params);
			},

			/**
			 * 获取当前会员信息
			 */
			getUserLevelInfo() {
				var params = {
					url: '/p/score/scoreLevel/page',
					method: 'GET',
					data: {
						levelType: 0
					},
					callBack: res => {
						if (res.endTime) {
							res.endTime = res.endTime.split(' ')[0]
						}
						this.setData({
							userLevelInfo: res, // 非付费会员等级
							currentLevelId: res.userLevel.id,
							currentGrowth: res.growth
						});
					}
				};
				http.request(params);
			},

			/**
			 * 获取年费会员列表
			 */
			getPayMemberList() {
				var params = {
					url: '/p/score/scoreLevel/page',
					method: 'GET',
					data: {
						levelType: 1
					},
					callBack: res => {
						this.premiumVipList = res.userLevels
						let flag = false
						for (let i = 0; i < res.userLevels.length; i++) {
							const el = res.userLevels[i];
							if (el.id == this.selectPremiumVipId) {
								this.selectPremiumVip = el
								this.selectPremiumVipId = el.id
								this.selectPremiumVipIndex = i
								flag = true
								break
							}
						}
						if (!flag) {
							this.selectPremiumVip = res.userLevels[0]
							this.selectPremiumVipId = res.userLevels[0].id
							this.selectPremiumVipIndex = 0
							this.popupShow = false
						}
					}
				};
				http.request(params);
			},

			/**
			 * 立即购买- 根据当前付费会员id
			 */
			buyNow() {
				// 成长值不足
				if (this.currentGrowth < this.selectPremiumVip.needGrowth) {
					uni.showToast({
						title: this.i18n.growthValueTips,
						icon: 'none'
					});
					return
				}
				// 已有付费会员时购买低级付费会员
				if (this.userLevelInfo.levelType == 1 && this.userLevelInfo.userLevel.level > this.selectPremiumVip.level) {
					uni.showToast({
						title: this.i18n.cannotBuyLower,
						icon: 'none'
					});
					return
				}

				// 已有付费会员时购买(同级或更高级)付费会员
				if (this.userLevelInfo.levelType == 1 && this.userLevelInfo.userLevel.level < this.selectPremiumVip.level) {
					uni.showModal({
						title: this.i18n.tips,
						content: this.i18n.upgradeMemberTips1 + this.userLevelInfo.userLevel.levelName + ',' + this.i18n.upgradeMemberTips2 + this.selectPremiumVip.levelName + ',' +  this.i18n.upgradeMemberTips3,
						cancelText: this.i18n.cancel,
						confirmText: this.i18n.confirm,
						success: res => {
							if (res.confirm) {
								this.popupShow = true
							} else {}
						}
					});
					return;
				}
				this.popupShow = true
			},



			// 去支付 - 购买会员
			toPay: function() {
				if(this.payType == 9) {
					uni.showModal({
						title: this.i18n.tips,
						content: this.i18n.confirmBalancePay,
						showCancel: true,//是否显示取消按钮
						cancelText: this.i18n.cancel,
						confirmText: this.i18n.confirm,
						success: (res) => {
							if (res.confirm) {
								//点击确定
								Pay.toOrderPay(this.payType,'','','',this.selectPremiumVip.id)
							} else if (res.cancel) {
								console.log('用户点击取消');
								uni.showToast({
									title: this.i18n.cancelBalancePay,
									icon: 'none',
									duration: 1500
								});
							}
						},
					})
					return
				}
				Pay.toOrderPay(this.payType,'','','',this.selectPremiumVip.id)
			},

			/**
			 * 切换轮播图
			 */
			selectVip(e) {
				const cutIndex = e.currentTarget.dataset.index
				this.setData({
					selectPremiumVipIndex: cutIndex,
					selectPremiumVip: this.premiumVipList[cutIndex],
					selectPremiumVipId : this.premiumVipList[cutIndex].id
				});
			}

		}
	};
</script>
<style lang="scss" scoped>
.buy-vip-container {
  background-color: $wenet-color-legacy-primary;
  width: 100%;
  padding: 48rpx;
  box-sizing: border-box;
  min-height: 100vh;
}

.buy-vip-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 170rpx;
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

.buy-vip-header-text {
  display: flex;
  flex-direction: column;
  z-index: 10;
}
.buy-vip-header-text-line1 {
  font-family: Helvetica;
  font-size: 64rpx;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.buy-vip-header-text-line1 .text.bold {
  font-weight: 500;
}
.buy-vip-header-text-line2 {
  font-family: Helvetica;
  font-size: 32rpx;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.buy-vip-header-img {
  position: absolute;
  top: 0;
  right: 0;
}

.buy-vip-intro {
  border-radius: 16px;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  padding: 32rpx 16rpx;
}
.buy-vip-intro-title {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
}
.buy-vip-title-text {
  margin: 0 26rpx;
  font-size: 28rpx;
  color: $wenet-color-legacy-primary;
  letter-spacing: 0;
  font-weight: 600;
}

.vip-intro-card1 {
  background-color: #FFEDC2;
  padding: 18rpx 26rpx;
  display: flex;
  align-items: center;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}
.vip-intro-card1-text {
  font-size: 24rpx;
  margin-left: 28rpx;
  font-weight: 400;
  letter-spacing: 0;
}
.vip-intro-card1-text__line1 {
  color: #FB560D;
  line-height: 44rpx;
}
.vip-intro-card1-text__line2 {
  color: #333333;
}

.vip-intro-card2-list {
  display: flex;
  flex-wrap: nowrap;
  column-gap: 20rpx;
  margin-bottom: 48rpx;
}
.vip-intro-card2 {
  width: 198rpx;
  height: 220rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 16rpx;
  background-color: #FFEDC2;
}
.vip-intro-card2-text {
  font-family: Helvetica;
  font-size: 24rpx;
  color: #FB560D;
  letter-spacing: 0;
  text-align: center;
  line-height: 44rpx;
  font-weight: 400;
}

.upgrade-way-title {
  width: 100%;
  position: relative;
  font-size: 28rpx;
}

.upgrade-way-index {
  width: fit-content;
  padding: 3px 22rpx 0;
  height: 107rpx;
  background-color: $wenet-color-legacy-primary;
  box-sizing: border-box;
  color: #fff;
  border-radius: 16rpx;
  font-weight: 500;
}

.upgrade-way-desc {
  border-radius: 16rpx;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 10rpx 18rpx;
  color: #000;
  background-color: #FFE3E6;
  box-sizing: border-box;
}

.recommends-prods {
  margin-bottom: 34rpx;
}

.vip-prod {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.vip-prod-btn {
  border-radius: 6.6rpx;
  padding: 8rpx 16rpx;
  color: #fff;
  background: $wenet-color-legacy-primary;
  font-size: 20rpx;
  width: fit-content;
  flex-shrink: 0;
}

/**
 * 支付类型选择弹窗
 */
 .popup-hide {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}
.popup-box {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 330px;
  overflow: hidden;
  background-color: #fff;
}

.popup-tit {
  position: relative;
  height: 46px;
  line-height: 46px;
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  background-color: #f7f7f7;
}

.close {
  color: #aaa;
  border-radius: 12px;
  line-height: 20px;
  text-align: center;
  height: 20px;
  width: 20px;
  font-size: 18px;
  padding: 1px;
  top: 10px;
  right: 10px;
  position: absolute;
}

.close::before {
  content: "\2716";
}

.popup-cnt {
  max-height: 429px;
  overflow: auto;
  padding: 0 10px;
}

/* 圆角样式 */
.radius {
  border-radius: 26rpx 26rpx 0 0;
}
.box-radius {
  border-radius: 20rpx;
}


.pay-way .ways {
	padding: 0 20rpx;
}

.pay-way .ways .item {
	display: flex;
	align-items: center;
	border-bottom: 1px solid #eee;
	padding: 20rpx 0;
}

.pay-way .ways .item .pay-name {
	flex: 1;
	display: flex;
	align-items: center;
}

.pay-way .ways .item .pay-name .img {
	width: 44rpx;
	height: 44rpx;
	font-size: 0;
}
image{
	width: 100%;
	height: 100%;
}

.pay-way .ways .item .pay-name .name {
	font-size: 24rpx;
	margin-left: 20rpx;
}

.pay-way .sure-pay {
	position: fixed;
	bottom: 40rpx;
	left: 20rpx;
	right: 20rpx;
	width: auto;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 80rpx;
	text-align: center;
	background: $wenet-color-error;
	font-size: 24rpx;
	font-weight: 600;
	color: #fff;
}
.vip-rights {
  margin-bottom: 34rpx;
}

.vip-rights-title {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.vip-rights-list {
  display: flex;
  flex-wrap: nowrap;
  column-gap: 20rpx;
}

.vip-rights-tips {
  font-size: 20rpx;
  color: #999;
  line-height: 1.5;
  padding: 12rpx 0 32rpx;
  text-align: left;
  display: flex;
  align-items: flex-start;
}
.vip-rights-tips::before {
    content: '*';
    color: #999;
    margin-right: 4rpx;
    flex-shrink: 0;
  }
</style>

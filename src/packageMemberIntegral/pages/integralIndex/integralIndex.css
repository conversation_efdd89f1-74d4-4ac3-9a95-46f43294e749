/* 积分中心 */

@import "../memberIndex/memberIndex.css";

page {
  background: #f6f7f9;
}

.integral-index {
  position: relative;
}

.integral-index .integral-msg {
  width: 704rpx;
  height: 282rpx;
  position: relative;
  border-radius: 20rpx;
  margin: 36px auto 0;
}

.member-tit1{
	display: flex;
	justify-content: center;
}

.integral-index .integral-msg .bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.integral-index .integral-msg .my-integral {
  display: flex;
  padding: 40rpx 0rpx 0 46rpx;
  color: #fff;
  position: relative;
  align-items: center;
}

.integral-index .integral-msg .my-integral .number-box .text {
  font-size: 32rpx;
}

.integral-index .integral-msg .my-integral .number-box .number {
  font-size: 40rpx;
  font-family: arial;
  margin-top: 10rpx;
}

.integral-index .integral-msg .my-integral .det {
  font-size: 24rpx;
  margin-left: 20rpx;
  margin-top: 76rpx;
	display: flex;
	align-items: center;
}

.integral-index .integral-msg .my-integral .det image {
  width: 12rpx;
  height: 20rpx;
	margin-left: 10rpx;
	padding-top: 4rpx;
}

.integral-index .integral-msg .make {
  position: absolute;
  right: 30rpx;
  top: 44rpx;
	display: flex;
	flex-direction: column;
}

.make-item{
	min-width: 70px;
	height: 24px;
	border-radius: 20px;
	box-sizing: border-box;
	border: 2rpx solid #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #fff;
	margin-bottom: 20rpx;
  padding: 20rpx;
}

.integral-index .integral-msg .make image {
  width: 42rpx;
  height: 42rpx;
  vertical-align: top;
  margin:0 8rpx 0 4rpx;
}

/* 积分中心 */

/* 加载完成 */
.loadall {
  margin: 20rpx 0;
  line-height: 2em;
  font-size: 28rpx;
  color: #aaa;
  text-align: center;
}

/* 空 */
.empty {
  display: block;
  height: 200rpx;
  line-height: 200rpx;
  font-size: 30rpx;
  color: #888;
  text-align: center;
}
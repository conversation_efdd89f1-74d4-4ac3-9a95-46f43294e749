<template>
<!-- 积分商城 -->
<view class="integral-index">
  <view class="integral-msg">
    <image :src="`${staticPicDomain}images/icon/integral-bg.png`" class="bg"></image>
    <view class="my-integral">
      <view class="number-box">
        <view class="text">{{i18n.myIntegral}}</view>
        <view class="number">{{scoreInfo.score}}</view>
      </view>
    <!--  <view class="det" >{{i18n.detailed}}<image :src="`${staticPicDomain}images/icon/white-arr.png`"></image>
      </view> -->
    </view>
	<view class="make">
      <!-- <image :src="`${staticPicDomain}images/icon/make-integral.png`"></image>{{i18n.earnPoints}} -->
	  <view class="make-item" @tap="navigateTo" data-path="scoreDet">积分明细</view>
	  <view class="make-item" @tap="navigateTo" data-path="scoreIndex">积分签到</view>
	</view>
  </view>
  <view class="integral-list">
    <view class="member-tit1">
      <view class="text">{{i18n.pointsExchange}}</view>
    </view>
    <view class="con-box">
      <block v-for="(prod, prodId) in scoreProdList" :key="prodId">
        <goodsitem :prod="prod"></goodsitem>
      </block>
    </view>
    <view class="loadall" v-if="scoreProdList.length > 10 && loadAll">{{i18n.endTips}}</view>
    <view class="empty" v-if="!scoreProdList.length">{{i18n.noData}}</view>
  </view>
</view>
</template>

<script>
// pages/integralIndex/integralIndex.js
var http = require("../../../utils/http.js");
import goodsitem from "../../components/IntegralGoodsList/IntegralGoodsList";

export default {
  data() {
    return {
      current: 1,
      size: 20,
      scoreProdList: [],
      scoreInfo: "",
      pages: "",
      loadAll: false, // 已加载全部
    };
  },

  components: {
    goodsitem
  },
  props: {},
  
  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getScoreProdList();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    uni.setNavigationBarTitle({
      title: this.i18n.pointCenter
    })
    // 获取当前积分信息
    this.getScoreInfo();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.current < this.pages) {
      this.current = this.current + 1
      this.getScoreProdList()
		}else {
      this.setData({
        loadAll: true
      })
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    /**
     * 跳转
     */
    navigateTo(e) {
      const path = e.currentTarget.dataset.path;

      if (path == 'scoreDet') {
        uni.navigateTo({
          url: '/packageMemberIntegral/pages/integralDetail/integralDetail'
        });
      } else if (path == 'scoreIndex') {
        uni.navigateTo({
          url: '/packageMemberIntegral/pages/memberIndex/memberIndex'
        });
      }
    },

    /**
     * 获取当前积分信息
     */
    getScoreInfo() {
      var params = {
        url: '/p/score/scoreInfo',
        method: 'GET',
        data: {},
        callBack: res => {
          this.setData({
            scoreInfo: res
          });
        }
      };
      http.request(params);
    },

    /**
     * 获取积分商品列表
     */
    getScoreProdList() {
      var param = {
        url: "/p/score/prodScorePage",
        method: "GET",
        data: {
          current: this.current,
          size: this.size,
        },
        callBack: res => {
          uni.hideLoading()          
					var scoreProdList = [];

          if (this.current == 1) {
            this.setData({
              scoreProdList: res.records,
              pages: res.pages,
              current: res.current
            });
          } else {
            scoreProdList = this.scoreProdList;
            scoreProdList.push(...res.records);
            this.setData({
              scoreProdList
            });
          }
        }
      };
      http.request(param);
    }
  }
};
</script>
<style>
@import "./integralIndex.css";
</style>
import Vue from 'vue';
import App from './App';
import wechat from './utils/wechat.js';
import {router,RouterMount} from './router/index.js'
import store from './store'
import config from './utils/config'
// 国际化模块
import VueI18n from 'vue-i18n'
import uma from './uma'
import NLoading from '@/components/n-loading/n-loading'

Vue.use(uma);
Vue.use(VueI18n)
Vue.use(router)

// #ifdef H5
if (wechat.isWechat()) {
	Vue.prototype.$wechat = wechat;
}
// #endif
Vue.config.productionTip = false;

Vue.component('n-loading', NLoading)

Vue.mixin({
	methods: {
		setData: function (obj, callback) {
			let that = this;
			let keys = [];
			let val, data;
			Object.keys(obj).forEach(function (key) {
				keys = key.split('.');
				val = obj[key];
				data = that.$data;
				keys.forEach(function (key2, index) {
					if (index + 1 == keys.length) {
						that.$set(data, key2, val);
					} else {
						if (!data[key2]) {
							that.$set(data, key2, {});
						}
					}
					data = data[key2];
				})
			});
			callback && callback();
		},

	},
	data(){
		return {
			staticPicDomain: config.iconDomain
		}
	},
});

if (!uni.getStorageSync('lang')) {
	// 设置默认语言
	uni.getSystemInfo({
		success: (res) => {
			uni.setStorageSync('lang', (res.language.indexOf('zh') != -1) ? 'zh_CN' : 'en')
		}
	})
}

const i18n = new VueI18n({
	locale: uni.getStorageSync('lang') || 'zh_CN', // 默认使用中文
	messages: {
		'en': require('utils/lang/en.js'), // 英文语言包
		'zh_CN': require('utils/lang/zh.js')  // 中文简体语言包
	}
})

// 导出国际化
export default i18n

Vue.prototype._i18n = i18n

Vue.prototype.$i18nMsg = function () {
	return i18n.messages[i18n.locale]
}


Vue.prototype.parsePrice = function (val) {
	if (!val) {
	  val = 0
	}
	return val.toFixed(2).split(".")
}
Vue.prototype.toPrice = function (val, fixed = 2) {
	if (!val) {
	  val = 0
	}
	return val.toFixed(fixed)
}


// 解决四舍五入精度问题
Number.prototype.toFixxed=function (d) { 
	var s=this+""; 
	if(!d)d=0; 
	if(s.indexOf(".")==-1)s+="."; 
	s+=new Array(d+1).join("0"); 
	if(new RegExp("^(-|\\+)?(\\d+(\\.\\d{0,"+(d+1)+"})?)\\d*$").test(s)){
		 var s="0"+RegExp.$2,pm=RegExp.$1,a=RegExp.$3.length,b=true;
		 if(a==d+2){
				 a=s.match(/\d/g); 
				 if(parseInt(a[a.length-1])>4){
						 for(var i=a.length-2;i>=0;i--){
								 a[i]=parseInt(a[i])+1;
								 if(a[i]==10){
										 a[i]=0;
										 b=i!=1;
								 }else break;
						 }
				 }
				 s=a.join("").replace(new RegExp("(\\d+)(\\d{"+d+"})\\d$"),"$1.$2");

		 }if(b)s=s.substr(1); 
		 return (pm+s).replace(/\.$/,"");
} return this+"";

};

Vue.prototype.toFixxed = val => {
	if (!val) {
	  val = 0
	}
	return val.toFixxed(2)
}

Vue.prototype.$store = store

App.mpType = 'app';

const app = new Vue({
	i18n,  // 国际化
  store,
	...App
});


//v1.3.5起 H5端 你应该去除原有的app.$mount();使用路由自带的渲染方式
// #ifdef H5
RouterMount(app, router, '#app');
// #endif

// #ifndef H5
app.$mount(); //为了兼容小程序及app端必须这样写才有效果
// #endif

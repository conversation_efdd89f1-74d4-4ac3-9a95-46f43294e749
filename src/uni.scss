/* 颜色变量 */
$wenet-color-legacy-primary: #FF2442;
$wenet-color-brand: #2F54D4;
$wenet-color-success: #52C41A;
$wenet-color-warning: #FAAD14;
$wenet-color-error: #FF2900;
$wenet-color-info: #888888;
$wenet-color-disabled: #d0d0d0;

//渐变终止颜色
$wenet-color-gradient-end: #5C8DEC;

// 文字颜色
$wenet-color-text-primary: #373737;
$wenet-color-text-secondary: #888888;

$vip-level-1-color: #4E76AB;
$vip-level-2-color: #C26D0C;

$page-background: #f4f4f4;

// 主色增加透明度
@function wenet-color-brand-opacity($opacity) {
  @return rgba(47,84,212,$opacity);
}

.text-primary {
  color: $wenet-color-text-primary;
}
.text-secondary {
  color: $wenet-color-text-secondary;
}
.text-brand {
  color: $wenet-color-brand;
}

.text-size-10 {
  font-size: 20rpx;
}
.text-size-12 {
  font-size: 24rpx;
}
.text-size-14 {
  font-size: 28rpx;
}
.text-size-16 {
  font-size: 32rpx;
}
.text-size-18 {
  font-size: 36rpx;
}
.text-size-20 {
  font-size: 40rpx;
}

.font-weight-400 {
  font-weight: 400;
}
.font-weight-500 {
  font-weight: 500;
}
.font-weight-600 {
  font-weight: 600;
}
.font-weight-700 {
  font-weight: 700;
}
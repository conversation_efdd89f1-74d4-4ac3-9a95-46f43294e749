import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
import http from '@/utils/http';
import { getWenetAccountInfo, cacheWenetAccountInfo } from '@/utils/login-processor';

const store = new Vuex.Store({
  state: {
    isLogin: !!uni.getStorageSync('token'),
    userInfo: null,
    prodCommentSwitch: false,
    shopCustomerSwitch: false,
    shopVipSwitch: false,
    currentShop: uni.getStorageSync('currentShop'),
    requestNum: 0,
    simpleWenetAccountInfo: uni.getStorageSync('simpleWenetAccount'),
    originalWenetAccountInfo: uni.getStorageSync('originalWenetAccount'),
  },
  mutations: {
    setIsLogin (state, payload) {
      state.isLogin = payload.isLogin
    },
    refreshUserInfo (state, payload = { forceRefresh: false }) {
      if (!state.isLogin) {
        return
      }
      const userInfoInStorage = uni.getStorageSync('userInfo');
      const simpleWenetAccountInfoInStorage = uni.getStorageSync('simpleWenetAccount');
      const originalWenetAccountInfoInStorage = uni.getStorageSync('originalWenetAccount');
      // 为了满足tabbar首页直达店铺的需求，用户信息接口增加了店铺信息；因此为兼容之前的逻辑，需要判断是否有店铺信息，如果没有则重新获取用户信息
      if (!userInfoInStorage || !userInfoInStorage.shop || payload.forceRefresh) {
        var params = {
          url: "/p/user/userInfo",
          method: "GET",
          data: {},
          dontTrunLogin: true,
          callBack: (res) => {
            state.userInfo = res
            state.isLogin = true
            uni.setStorageSync('userInfo', res)
            const currentShop = uni.getStorageSync('currentShop');
            const ou = res.ou;
            // 未绑定学校的用户，忽略店铺，直接访问平台首页
            if (!ou) {
              state.currentShop = null
              uni.removeStorageSync('currentShop')
              return
            }
            if (!currentShop) {
              const targetShop = res?.shops?.find(item => !!item.renovationId)
              uni.setStorageSync('currentShop', targetShop)
              state.currentShop = targetShop
            } else {
              const currentShopId = currentShop.shopId
              const newCurrentShop = res?.shops?.find(item => item.shopId === currentShopId)
              uni.setStorageSync('currentShop', newCurrentShop)
              // 店铺的首页信息可能会变化，所以需要更新
              state.currentShop = newCurrentShop
            }
            payload && payload.callBack && payload.callBack(res)
          }
        };
        http.request(params);
        getWenetAccountInfo().then(res => {
          state.originalWenetAccountInfo = res
          state.simpleWenetAccountInfo = cacheWenetAccountInfo(res)
        })
      } else {
        state.userInfo = userInfoInStorage
        state.simpleWenetAccountInfo = simpleWenetAccountInfoInStorage
        state.originalWenetAccountInfo = originalWenetAccountInfoInStorage
        if (state.userInfo) {
          state.isLogin = true
          return
        }
      }
    },
    clearUserInfo (state) {
      state.userInfo = null
      state.isLogin = false
      state.currentShop = null
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('token')
    },
    getAppSwitch(state) {
      const params = {
        url: '/sys/config/info/getAppSwitch',
        method: 'GET',
        callBack: res => {
          state.prodCommentSwitch = res.prodCommentSwitch
          state.shopCustomerSwitch = res.shopCustomerSwitch
          state.shopVipSwitch = res.shopVipSwitch
        },
      }
      http.request(params)
    },
    setCurrentShop(state, payload) {
      const shopId = payload.shopId
      const shopList = state.userInfo?.shops || []
      const shop = shopList.find(item => item.shopId === shopId)
      state.currentShop = shop || shopList[0]
      uni.setStorageSync('currentShop', shop)
    },
  },
  actions: {
    reduceRequestNum() {
      if (this.state.requestNum > 0) {
        this.state.requestNum = this.state.requestNum - 1
      }
    },
    addRequestNum() {
      this.state.requestNum = this.state.requestNum + 1
    }

  }
})

export default store
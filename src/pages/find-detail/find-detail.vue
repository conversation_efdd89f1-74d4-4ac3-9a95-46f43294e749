<template>
	<view class="find">
		<scroll-view class="scroll" scroll-y="true">
			<view class="find-title">发现中心</view>
			<view class="user-data" v-if="isLogin">
				<image class="user-head" :src="info.user.pic"/>
				<text class="user-name">{{info.user.nickName}}</text>
				<view class="user-vip">{{levelInfo.levelName}}</view>
			</view>
			<div class="find-banner" v-if="info.pics.split(',').length == 1" :style="'background-image: url(' + info.pics.split(',')[0] + ');'"/>
			<swiper class="find-banner" v-if="info.pics.split(',').length > 1" :circular="true" :indicator-dots="indicatorDots" :autoplay="video ? false : autoplay"
			 :indicator-color="indicatorColor" :interval="interval" :duration="duration" :indicator-active-color="indicatorActiveColor">
				<block v-for="(item, imgIndex) in info.pics.split(',')" :key="imgIndex">
					<swiper-item>
						<div class="find-banner" :style="'background-image: url(' + item + ');'"/>
					</swiper-item>
				</block>
			</swiper>
			<view class="words">{{info.content}}</view>
			<view class="time">发布时间:{{info.recTime}}</view>
			<view class="prod-box" @tap="goProddetail">
				<image class="prod-picture" :src="info.product.pic ? info.product.pic :`${staticPicDomain}images/icon/head04.png`"/>
				<view class="right-prod">
					<text class="prod-title">{{info.product.prodName}}</text>
					<text class="prod-price">
						¥{{info.product.price}}
						<!-- <text class="line-price">¥{{info.product.oriPrice}}</text> -->
					</text>
					<text class="prod-eval">{{info.prodCommNum || 0}}条评论</text>
				</view>
			</view>
		</scroll-view>
		<view class="bottom">
			<view class="dz-btn" @tap="thumbsUp(info.starStatus)">{{info.starStatus ? '取消点赞' : '点赞'}}</view>
		</view>
	</view>
</template>

<script>
	// pages/category/category.js
	var http = require("../../utils/http.js");
	var config = require("../../utils/config.js");
	var util = require("../../utils/util.js");

	export default {
		data() {
			return {
				info: {},
				levelInfo: {},
				commflag: false, // 默认点赞按钮
				prodCommId: '',
				indicatorDots: true,
				indicatorColor: '#f2f2f2',
				indicatorActiveColor: '#eb2444',
				isAuthInfo: true,
				autoplay: true,
				interval: 3000,
				duration: 1000,
				isLogin: uni.getStorageSync('token')
			};
		},

		components: {},
		props: {},
		computed:{
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.init(options)
			if (uni.getStorageSync('token')) {
				this.level()
			}
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {

		},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			// 加载导航标题
			uni.setNavigationBarTitle({
				title:this.i18n.find
			});

		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			thumbsUp: function(commflag) {
				var ths = this; //加载分类列表
				
				var params = {
					url: commflag ? "/p/prodComm/unStar" : "/p/prodComm/star",
					method: "POST",
					data: {
						prodCommId: this.prodCommId
					},
					callBack: function(res) {
						ths.setData({
							commflag: !commflag,
						});
						ths.init({prodCommId: ths.prodCommId})
					}
				};
				http.request(params);
			},
			init: function(options) {
				var ths = this; //加载分类列表
				
				var params = {
					url: uni.getStorageSync('token') ? "/p/prodComm/getProdCommentDetail" : "/center/prodComm/getProdCommentDetail",
					method: "GET",
					data: {
						prodCommId: options.prodCommId
					},
					callBack: function(res) {
						ths.setData({
							info: res,
							prodCommId: options.prodCommId
						});
					}
				};
				http.request(params);
			},
			level: function() {
				var ths = this; //加载分类列表
				
				var params = {
					url: '/p/score/scoreLevel/page',
					method: 'GET',
					data: {
						levelType: 0
					},
					callBack: function(res) {
						ths.setData({
							levelInfo: res,
						});
					}
				};
				http.request(params);
			},
			gotoZhibo: function() {
				// #ifdef MP-WEIXIN
				uni.navigateTo({
					url: '/pages/liveBroadcast/liveBroadcast'
				});
				return
				// #endif
				uni.showToast({
					title: '当前环境不支持，请到小程序中访问',
					duration: 2000,
					icon: 'none',
				})
				
			},
			/**
			 * 分类点击事件，获取子分类
			 */
			onMenuTab: function(e) {

			},
			// 跳转搜索页
			toSearchPage: function() {
				util.tapLog(3)
				uni.navigateTo({
					url: '/pages/search-page/search-page'
				});
			},
			goProddetail: function() {
				uni.navigateTo({
					url: `/pages/prod/prod?prodid=${this.info.prodId}`
				});
			},
		}
	};
</script>
<style>
	@import "./find-detail.css";
</style>

/* pages/category/category.wxss */

page {
  height: 100%;
  background: #f5f5f5;
}
.find{
	width: 100%;
}

.scroll{
	height: calc(100vh - 140rpx);
}

.find-title{
	width: 100%;
	height: 90rpx;
	background: linear-gradient(90deg, var(--light-background-2) 0%, var(--gradient-ramp-light) 100%);
	background: -webkit-linear-gradient(90deg, var(--light-background-2) 0%, var(--gradient-ramp-light) 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #fff;
}
.find-banner{
	width: 100%;
	height: 586rpx;
	background-size: 100% auto;
	background-repeat: no-repeat;
}
.user-data{
	display: flex;
	flex-direction: row;
	align-items: center;
	margin: 10px 0;
	padding-left: 40px;
}
.user-data .user-head{
	width: 50rpx;
	height: 50rpx;
	border-radius: 100%;
	margin-right: 10rpx;
}
.user-data .user-name{
	font-size: 24rpx;
	color: #888;
	margin-right: 10rpx;
}
.user-data .user-vip{
	padding: 0 20rpx;
	height: 16px;
	background: rgba(53, 53, 53, 1);
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #F0CB77;
}
.words{
	width: 100%;
	background: #fff;
	box-sizing: border-box;
	padding: 20rpx 10rpx;
	font-size: 28rpx;
	color: #333;
	margin-top: 12rpx;
	border-radius: 20rpx;
}
.time{
	font-size: 24rpx;
	color: #666;
	padding: 30rpx; 
}
.prod-box{
	width: 100%;
	background: #fff;
	box-sizing: border-box;
	padding: 12px 17px;
	display: flex;
	flex-direction: row;
}
.prod-picture{
	width: 200rpx;
	height: 200rpx;
	min-width: 200rpx;
	min-height: 200rpx;
	border-radius: 20rpx;
	margin-right: 12px;
}
.right-prod{
	display: flex;
	flex-direction: column;
}
.prod-title{
	display: -webkit-box;
	-webkit-line-clamp: 2; /*设定显示行数*/
	-webkit-box-orient: vertical;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 12px;
}
.prod-price{
	font-size: 28rpx;
	color: #FF5733;
	margin-bottom: 12px;
}
.line-price{
	font-size: 28rpx;
	color: #A6A6A6;
	text-decoration: line-through;
}
.prod-eval{
	font-size: 28rpx;
	color: #A6A6A6;
}

.bottom{
	width: 100%;
	padding: 10px 20px;
	box-sizing: border-box;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	border: 2rpx solid #fff;
}
.dz-btn{
	width: 672rpx;
	height: 70rpx;
	background-color: var(--primary-color);
	border-radius: 20px;
	font-size: 32rpx;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
}
<template>
  <!--pages/pay-result/pay-result.wxml-->
  <view class="container">
    <!-- 失败 -->
    <view v-if="sts == 0">
      <view class="pay-sts">

        <view class="pay-result">
          <n-i icon="close" width="40rpx" height="40rpx" ext="png" />
          <view>{{i18n.paymentFailed}}</view>
        </view>

        <view class="tips">
          <view>
            {{i18n.paymentFailedTips1}}
            <text class="warn">{{i18n.paymentFailedTips2}}</text>
            {{i18n.paymentFailedTips3}}
          </view>
          {{i18n.paymentFailedTips4}}
        </view>
      </view>

      <view class="btns">
        <text class="button checkorder" @tap="toOrderList">{{i18n.checkOrder}}</text>
        <text class="button payagain" @tap="payAgain">{{i18n.payAgain}}</text>
      </view>
    </view>
    <!-- 成功 -->
    <view v-if="sts == 1">
      <view class="pay-sts">
        <view class="pay-result">
          <n-i icon="isDone" width="40rpx" height="40rpx" />
          <view>{{i18n.orderCompleted}}</view>
        </view>
      </view>
      <view class="btns">
        <text class="button checkorder" @tap="toOrderList">{{i18n.checkOrder}}</text>
        <text class="button shopcontinue" @tap="toIndex">{{i18n.continueShopping}}</text>
      </view>
      <feature v-if="shopId && renovationId" ref="featureIndex" :pageId="renovationId" :shopId="shopId"></feature>
    </view>
    <PayComponent ref="payRef" />
  </view>
</template>

<script>
import Feature from '@/components/feature/index/index'
import NI from '@/components/n-i'
import util from "../../utils/util.js";
import http from '@/utils/http.js'
import PayComponent from '@/components/pay/pay'

export default {
  data () {
    return {
      sts: 0,
      orderNumbers: '',
      selPayType: '',
      orderType: '', // 订单类型 1团购 2秒杀
			hadUpload: false,
      ordermold: '', // 1虚拟商品
      renovationId: undefined,
      shopId: undefined,
      hideBalancePay: false,
    };
  },
  components: {
    Feature,
    NI,
    PayComponent
  },
  props: {},
  computed: {
    i18n () {
      return this.$t('index')
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      sts: options.sts,
      orderNumbers: options.orderNumbers,
      orderType: options.orderType,
      ordermold: options.ordermold,
      hideBalancePay: options.hideBalancePay === 'true',
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () { },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 设置头部导航标题
    uni.setNavigationBarTitle({
      title: this.i18n.paymentResults
    });
		if (this.sts == 1) {
			if (!this.hadUpload) {
				util.tapLog(null, {
          orderNumbers: this.orderNumbers,
          isPayRes: true,
        })
				this.hadUpload = true
			}
		}
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
		this.hadUpload = false
	},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () { },
  mounted() {
    if (this.orderType == 1) { // 拼团订单
      const groupOrderItem = JSON.parse(wx.getStorageSync("groupOrderItem"));
      const prodId = groupOrderItem.prodId
      const shopId = groupOrderItem.shopId
      this.getRewardsRenovation(shopId, prodId)
    } else if (this.orderType == 2) {
      const seckillOrderItem = wx.getStorageSync("secKillObj");
      const prodId = seckillOrderItem.prodId
      const shopId = seckillOrderItem.shopId
      this.getRewardsRenovation(shopId, prodId)
    } else {
      const orderItem = JSON.parse(wx.getStorageSync("orderItem"));
      const prodId = orderItem.prodId
      const shopId = orderItem.shopId
      this.getRewardsRenovation(shopId, prodId)
    }
  },
  methods: {
    toOrderList: function () {
      let paySts = this.sts == 0 ? 1 : 2
      if (this.orderType == 1 || this.ordermold == 1) { // 1团购 || orderMold=1虚拟商品
        paySts = 0
      }
      this.$Router.replace({
        path: '/pages/orderList/orderList', query: { sts: paySts }
      })
    },
    toIndex: function () {
      this.$Router.pushTab('/pages/index/index')
    },
    payAgain: function () {
      this.$refs.payRef.init({
        orderNumbers: this.orderNumbers,
        ordermold: this.ordermold,
        hideBalancePay: this.hideBalancePay
      })
    },
    getRewardsRenovation(shopId, prodId) {
      if (shopId && prodId) {
        http.request({
          url: `/p/order/incentive/${shopId}/${prodId}`,
          method: 'GET',
          callBack: res => {
            this.shopId = shopId
            this.renovationId = res.renovationId
          },
        })
      }
    }
  }
};
</script>
<style>
@import "./pay-result.css";
</style>

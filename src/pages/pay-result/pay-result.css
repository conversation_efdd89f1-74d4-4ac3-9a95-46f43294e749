/* pages/pay-result/pay-result.wxss */

.pay-sts {
  padding: 48rpx 50rpx 60rpx;
  color: #000;
  font-size: 40rpx;
  font-weight: 600;
  line-height: 40rpx; 
}
.pay-result {
  display: flex;
  gap: 20rpx;
}

.btns {
  margin-top: 50rpx;
  text-align: center;
}

.tips {
  color: #999;
  font-size: 24rpx;
  font-weight: 400;
  margin-top: 16rpx;
  margin-left: 60rpx;
  line-height: 1.5;
}

.tips .warn {
  color: var(--primary-color);
}

.btns {
  display: flex;
  gap: 30rpx;
  padding: 0rpx 50rpx;
  justify-content: center;
  margin: 0rpx 0rpx 48rpx;
}
.btns .button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 310rpx;
  height: 70rpx;
  border-radius: 70rpx;
  background: var(--primary-color);
}

.btns .button.checkorder {
  background: var(--primary-color);
  color: #fff;
  margin-bottom: 20rpx;
  border: 2rpx solid var(--primary-color);
}

.btns .button.payagain {
  background: #fff;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btns .button.shopcontinue {
  background: #fff;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

/* pages/category/category.wxss */

page {
  height: 100%;
}
uni-page-wrapper{
	height: 0!important;
}

.container {
  display: flex;
  flex-direction: row;
  height: 100%;
}

.main {
  position: relative;
  display: flex;
  width: 100%;
  margin-top: 100rpx;
  height: calc(100vh - 100rpx);
  overflow: hidden;
}

/* 搜索栏 */

.search-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  color: #777;
  background: #fff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.07);
  z-index: 3;
  padding: 20rpx 0;
}

.search-bar .arrow {
  width: 20rpx;
  height: 20rpx;
  border-bottom: 2rpx solid #777;
  border-left: 2rpx solid #777;
  transform: rotate(45deg);
  position: absolute;
  left: 30rpx;
  top: 41rpx;
}

.search-bar .search-box {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  background: #f7f7f7;
  z-index: 999;
  width: 92%;
  border-radius: 50rpx;
  text-align: center;
  margin: auto;
}

.sear-input {
  font-size: 28rpx;
}

.search-bar .search-hint {
  font-size: 28rpx;
  position: absolute;
  right: 30rpx;
  top: 32rpx;
}

.search-bar .search-box .search-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

/* 左侧菜单栏 */

.leftmenu {
  width: fit-content;
  max-width: 25%;
	padding: 20rpx 20rpx 120rpx;
  box-sizing: border-box;
  background-color: #f5f6f7;
  overflow: scroll;
  z-index: 2;
}

.menu-item {
	height: 90rpx;
	text-align: center;
	border-bottom: 2rpx silid #e3e3e3;
	position: relative;
	color: #777;
	font-size: 28rpx;
	/* word-break: break-word; */
	display: -webkit-box;
	display: flex;
	align-items:center;
	justify-content: center;
	
}
.menu-text {
	text-overflow:ellipsis;
	white-space: nowrap;
	overflow: hidden;
}
.menu-item.active {
	width: 120rpx;
	height: 48rpx;
	background: linear-gradient(122.88deg, var(--gradient-ramp-light) 0%, var(--gradient-ramp-deep) 100%);
	background: -webkit-linear-gradient(122.88deg, var(--gradient-ramp-light) 0%, var(--gradient-ramp-deep) 100%);
	border-radius: 20px;
	margin-left: 15rpx;
	margin-top: 20rpx;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  position: relative;
}

.menu-item.active:before {
/*  position: absolute;
  left: 0;
  content: "";
  width: 8rpx;
  height: 32rpx;
  top: 29rpx;
  background: #E43130; */
}

.menu-item text.tips-num {
  position: absolute;
  top: 20rpx;
  right: 15rpx;
  border-radius: 15rpx;
  width: 30rpx;
  height: 30rpx;
  background: red;
  color: #fff;
  font-size: 25rpx;
  line-height: 30rpx;
}

/* 右侧商品栏 */

.rightcontent {
  width: 75%;
	padding-bottom: 120rpx;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1;
}

.rightcontent .adver-map {
  width: auto;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  margin: 30rpx 20rpx 0;
}

.rightcontent .adver-map .item-a {
  display: block;
  font-size: 0;
  width: 100%;
  height: 160rpx;
}

.rightcontent .adver-map .item-a image {
  max-width: 100%;
  max-height: 100%;
  width: 1220px;
}

.rightcontent .sub-category {
  /* display: flex;
  flex-wrap: wrap; */
}

/* 二级分类 */
.sub-category-con{
  margin: 20rpx;
}
.empty-tips {
	font-size: 26rpx;
	color: #aaa;
	margin: 30rpx;
	/* text-align: center; */
}
.sub-cate-title{
  display: flex;
  align-items: center;
  font-weight: 800;
}
.view-all{
  color: #999;
  font-weight: normal;
  font-size: 22rpx;
}
.sub-cate-text{
  flex: 1;
  font-size: 28rpx;
}
.th-cate-con{
  display: flex;
  flex-wrap: wrap;
}

.sub-category-item{
  width: 33.33%;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;
  align-items: center;
}
.sub-category-item .sub-category-item-pic{
  width: 120rpx !important;
  height: 120rpx !important;
  padding-bottom:10rpx;
}
.sub-category-item text{
  font-size: 25rpx;
}


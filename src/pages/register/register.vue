<template>
  <LoginLayout>
    <view v-if="step === 1">
      <NInput name="username" v-model="registerInfo.username" placeholder="学号" />
      <NInput name="realname" v-model="registerInfo.realname" placeholder="姓名" />
      <NInput type="password" name="passowrd" v-model="registerInfo.password" placeholder="密码" />
      <NInput type="password" name="cfmPassword" v-model="registerInfo.cfmPasword" placeholder="确认密码" />
      <NButton @tap="handleClickConfirm">信息无误,立即注册</NButton>
      <NButton style="margin-top: 24rpx;" @tap="handleClickPrev" type="">上一步</NButton>
    </view>
    <GetUserMobile
      v-if="step === 2"
      submit-name="注册"
      @verifyMobileSuccess="verifyMobileSuccess"
      page-name="注册页"
      wx-btn-text="手机号快捷注册"
      :privacy-checked="true"
    />
    <n-loading :show="loading" />

    <Dialog ref="duplicateUsernameDialog" @ok="handleDuplicateUsernameDialogOk" ok-text="去登录">
      <text>该手机号已绑定学号{{ occupantUsername}}，请通过手机号登录！</text>
    </Dialog>
    <Dialog ref="duplicateMobileDialog" @ok="handleDuplicateUsernameDialogOk" ok-text="去登录">
      <text>该学号已绑定手机号{{ occupantMobile}}，请通过学号登录！</text>
    </Dialog>

  </LoginLayout>
</template>

<script>
import utils from '@/utils/util'
import LoginLayout from '@/components/login-layout/login-layout'
import GetUserMobile from '@/components/get-user-mobile'
import NInput from '@/components/form/n-input';
import NButton from '@/components/n-button';
import Dialog from '@/components/popup/dialog';
import { getWenetAccountInfo, isExtendInfoAllFilled, isWenetAccountAvaliable, getBasOrgList, getWenetToken, getExtendInfoListConfig, registerAccountToWenet } from '@/utils/login-processor'

export default {
  data() {
    return {
      loading: false,
      step: 1,
      registerInfo: {
        username: '',
        realname: '',
        password: '',
        cfmPasword: '',
        ou: '',
        prefix: '',
      },
      orgList: [],
      occupantUsername: '',
      occupantMobile: '',
    }
  },
  computed: {
  },
  components: {
    GetUserMobile,
    LoginLayout,
    NInput,
    NButton,
    Dialog
  },
  mounted() {
  },
  onLoad(query) {
    getBasOrgList().then((res) => {
      this.orgList = res
    })
    this.registerInfo.username = query.username || ''
    this.registerInfo.realname = decodeURIComponent(query.realname || '')
    this.registerInfo.ou = query.ou || ''
    this.registerInfo.prefix = query.prefix || ''
  },
  methods: {
    async handleClickConfirm() {
      if (!this.checkForm()) {
        return
      }
      const fullUsername = this.registerInfo.prefix + this.registerInfo.username
      const usernameAvaliable = await isWenetAccountAvaliable(fullUsername);
      if (!usernameAvaliable) {
        const res = await utils.getWenetAnotherAccount({
          username: fullUsername
        })
        this.occupantMobile = res.mobile
        this.$refs.duplicateMobileDialog.show()
        return 
      }
      this.step = 2
    },
    checkForm() {
      // 所有项都必填，确认密码和密码相等
      const { cfmPasword, password, realname, username} = this.registerInfo
      if (!cfmPasword || !password || !realname || !username) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return false
      }
      if (cfmPasword !== password) {
        uni.showToast({
          title: '两次密码不一致',
          icon: 'none'
        })
        return false
      }
      return true
    },

    async registerAndLogin(inputMobile) {
      try {
        this.startLoading()
        await registerAccountToWenet({
          studentId: this.registerInfo.username,
          mobile: inputMobile,
          org: this.registerInfo.ou,
          password: this.registerInfo.password,
          realname: this.registerInfo.realname
        })
        const fullUsername = this.registerInfo.prefix + this.registerInfo.username;
        let token = await getWenetToken(fullUsername, this.registerInfo.password);
        uni.setStorageSync('wenetToken', token);
        const wenetAccountInfo = await getWenetAccountInfo(token)
        const orgExtendedInfo = getExtendInfoListConfig(wenetAccountInfo.org.ou, this.orgList)
        const isAllFilledRes = isExtendInfoAllFilled(wenetAccountInfo, orgExtendedInfo)
        const ou = wenetAccountInfo.org.ou || this.registerInfo.ou;
        const uid = wenetAccountInfo.person.uid;
        const mobile = wenetAccountInfo.person.mobile;
        if (isAllFilledRes.result) {
          // 如果信息已经填写完整，表示注册成功
          this.$umaTracker.setUserBasicInfo({
            userId: uid,
            schoolName: this.selectedOrg.ou
          })
          this.$umaTracker.RegisterSuc({
            Um_Key_RegisterType: '学号注册'
          }) 
        }
        await utils.loginWithWenet({
          ou,
          uid,
          mobile
        }, {
          needImproveExtendInfo: !isAllFilledRes.result,
        })
      } catch (error) {
        console.dir(error);
      } finally {
        this.stopLoading()
      }
    },

    verifyMobileSuccess(mobile) {
      if (mobile) {
        this.startLoading()
        isWenetAccountAvaliable(mobile).then(async (res) => {
          if (res) {
            uni.setStorageSync('registerMobile', mobile)
            this.registerAndLogin(mobile)
          } else {
            const res = await utils.getWenetAnotherAccount({
              mobile
            })
            this.occupantUsername = res.username
            this.$refs.duplicateUsernameDialog.show()
            return 
          }
        }).finally(() => {
          this.stopLoading()
        })
      }
    },
    handleDuplicateUsernameDialogOk() {
      uni.redirectTo({
        url: '/pages/login/login'
      })
    },

    startLoading() {
      this.loading = true;
    },
    stopLoading() {
      this.loading = false;
    },
    handleClickPrev() {
      uni.navigateBack({
        delta: 1
      })
    },
  },
}
</script>

<style scoped>
@import "./register.css"
</style>
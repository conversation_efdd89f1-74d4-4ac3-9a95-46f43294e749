<template>
<view class="container">
  <contact-button/>
  <view class="fixed-header">
    <!-- 头部搜索区 -->
    <view class="search-bar">
      <SearchBar :dataField="searchBarDataField" :shop-id="shopId" />
    </view>
    <view class="tabs">
      <view class="tab-item complete" :class="{'text-brand': sortType==''}" @tap="onStsTap('')"">{{i18n.default}}</view>
      <view :class="'tab-item ' + (sortType=='sales' && (sts==2?'down':'up'))" @tap="onStsTap('sales')">{{i18n.sales}}</view>
      <view :class="'tab-item ' + (sortType=='price' && (sts==4?'down':'up'))" @tap="onStsTap('price')">{{i18n.price}}</view>
    </view>
  </view>
  <!-- 滚动内容区 -->
  <view class="main">
    <!-- 左侧菜单start -->
    <view class="leftmenu">
      <block v-for="(item, index) in shopCategoryList" :key="index">
        <view :class="'menu-item ' + (selIndex==index?'active':'')" :data-index="index" :data-categoryid="item.categoryId" @tap="onMenuTab">
          <text class="menu-text">{{item.categoryName}}</text>
        </view>
      </block>
    </view>
    <!-- 左侧菜单end -->

    <!-- 右侧内容start -->
    <view class="rightcontent" lower-threshold="200" @scrolltolower="loadNextPage" :scroll-top="prodListScrollTop">
      <view class="cont-item">
        <HorizontalCard 
          :show-ori-price="false" 
          @tap-goods="toProdPage(prod)" 
          v-for="prod in prodList" 
          :key="prod.prodId" 
          :goodsItem="getGoodsItem(prod)"
        ></HorizontalCard>
        <view class="empty-tips" v-if="prodList.length==0">{{i18n.noProductsTips}}</view>
      </view>
    </view>
    <!-- 右侧内容end -->

  </view>
  <n-loading />
</view>
</template>

<script>
import http from '@/utils/http'
import SearchBar from '@/components/feature/all-feature/search-bar/search-bar'
import LabelsBelowHeadline from '@/components/labels-below-headline'
import LabelsOverImage from '@/components/labels-over-image'
import HorizontalCard from '@/components/goods-card/horizontal-card.vue';
import ContactButton from '@/components/contact-button'
export default {
  data() {
    return {
      shopCategoryList: [],
      selIndex: 0,
      prodList: [],
      categoryLength: 0,
      shopCategoryId: 0,
      current: 1,
      pages: 0,
      prodListScrollTop: 0,
      searchBarDataField: {
        "title": "搜索",
        "position": 1,
        "textAlgin": "start",
        "boxHeight": 28,
        "boxColor": "#FFFFFF",
        "textColor": "#999999",
      },
      curLang: 'en',
      sortType: '',
      sts: 21,
      orderBy:1,//状态点击事件
      categoryId: '', // 存储传入的分类 ID
      categoryName: '', // 新增 categoryName 用于存储传入的分类名称
    };
  },

  components: {
    LabelsBelowHeadline,
    LabelsOverImage,
    SearchBar,
    HorizontalCard,
    ContactButton
  },
  props: {
    shopId: {
      type: [Number, String],
      default: ''
    }
  },
  computed: {
    i18n() {
      return this.$t('index')
    },
  },
  
  methods: {
    getGoodsItem(prod) {
      return {
       ...prod,
        price: prod.activityPrice || prod.price
      };
    },
    parentOnReachBottom() {
      this.loadNextPage()
    },
    parentOnLoad(options) {
      this.setData({
        curLang: uni.getStorageSync('lang'),
        categoryId: options.categoryId || '', // 获取传入的 categoryId
        categoryName: options.categoryName || '' // 获取传入的 categoryName
      });
      // 如果既没有 categoryId 也没有 categoryName，直接获取商品列表
      if (!options.categoryId && !options.categoryName) {
        this.getProdListByCategoryId();
      }
    },
    parentOnShow() {
      this.getShopCategory();
      //头部导航标题
      uni.setNavigationBarTitle({
        title: this.i18n.commodityClassification
      });
    },
    // 加载下一页
    loadNextPage() {
      if (this.current < this.pages) {
        this.current = this.current + 1
        this.getProdListByCategoryId('loadNextPage')
      }
    },
    // 跳转店内搜索页
    toShopSearchPage: function () {
      uni.navigateTo({
        url: '/packageShop/pages/shopSearch/shopSearch?shopId=' + this.shopId
      });
    },
    // 分类点击事件
    onMenuTab: function (e) {
      let categoryId = e.currentTarget.dataset.categoryid;
      const {
        index
      } = e.currentTarget.dataset;
      this.setData({
        selIndex: index,
        shopCategoryId: categoryId,
        current: 1,
        pages: 0,
        prodListScrollTop: 0
      });
      this.getProdListByCategoryId();
    },
    // 获取店内分类列表
    getShopCategory: function () {
      let shopId = this.shopId;
      var params = {
        url: '/category/categoryInfo',
        method: 'GET',
        data: {
          shopId
        },
        callBack: res => {
          // 添加长度标识
          res.forEach((item, index) => {
            item.categoryLength = item.categoryName.length
          })
          
          let selectedIndex = 0;
          // 优先使用 categoryId 查找
          if (this.categoryId) {
            const foundIndex = res.findIndex(item => item.categoryId == this.categoryId);
            if (foundIndex !== -1) {
              selectedIndex = foundIndex;
            }
          } 
          // 如果没有找到对应的 categoryId 且提供了 categoryName，则使用 categoryName 查找
          else if (this.categoryName) {
            const foundIndex = res.findIndex(item => {
              // 微信小程序需要两次 decodeURIComponent 解码
              // #ifdef MP-WEIXIN
              return item.categoryName.toLowerCase() === decodeURIComponent(decodeURIComponent(this.categoryName)).toLowerCase()
              // #endif
              // 非微信小程序
              // #ifndef MP-WEIXIN
              return item.categoryName.toLowerCase() === this.categoryName.toLowerCase()
              // #endif
            });
            if (foundIndex !== -1) {
              selectedIndex = foundIndex;
            }
          }
          console.log(selectedIndex, 'selectedIndex')

          this.setData({
            shopCategoryList: res,
            shopCategoryId: this.shopCategoryId || res[selectedIndex].categoryId,
            selIndex: selectedIndex,
          });
          this.getProdListByCategoryId();
        }
      };
      http.request(params);
    },

    // 根据店铺分类id获取商品
    getProdListByCategoryId(type) {
      var params = {
        // url: '/search/searchProdPage',
        url: '/search/page',
        method: 'GET',
        data: {
          shopCategoryId: this.shopCategoryId,
          shopId: this.shopId,
          current: this.current || 1,
          // 新增参数
          isAllProdType: true,
          size: 20,
          sort: this.sts,
          orderBy: this.orderBy,
          isActive: 1 // 过滤掉活动商品
        },
        callBack: res => {
          var list = []
          if (params.data.current == 1) {
            list = res.records[0].products
          }
          else if (type === 'loadNextPage') {
            list = this.prodList.concat(res.records[0].products)
          }

          this.setData({
            prodList: list,
            pages: res.pages
          });
        }
      };
      // 如果是第一页或加载下一页，请求数据
      // 如果不是第一页且不加载下一页，不请求数据, 防止重复请求
      if (params.data.current === 1 || type === 'loadNextPage'){
        http.request(params);
      }
    },

    // 跳转商品详情页
    toProdPage: function (prod) {
      var prodid = prod.prodId;
      this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodid } })
    },
    /**
     * 状态点击事件
     * @param {String} sortType 排序类型
     */
    onStsTap: function(sortType) {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      })
      this.sortType = sortType
      if (!sortType) {
        this.sts = 21
      } else if (sortType=='sales') {
        this.sts = this.sts == 3 ? 2 : 3
      } else if (sortType=='price') {
        this.sts = this.sts == 5 ? 4 : 5
      }
      this.setData({
        current: 1,
        pages: 1
      });
      this.getProdListByCategoryId();
    },
  }
};
</script>
<style lang="scss" scoped>
page {
  height: 100%;
}

.fixed-header {
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 999;
  background-image: linear-gradient(180deg, #BCDAFF 35%, #FFFFFF 100%);
}

/* 搜索栏 */
.search-bar {
  width: 100%;
  color: #777;
  z-index: 3;
  padding: 0rpx 0rpx 24rpx;
}

.main {
  position: relative;
  display: flex;
  width: 100%;
  /* height: calc(100vh - 300rpx - constant(safe-area-inset-bottom));
  height: calc(100vh - 300rpx - env(safe-area-inset-bottom)); */
  /* overflow: hidden; */
}

/* 左侧菜单栏 */
.leftmenu {
  width: fit-content;
  max-width: 214rpx;
  padding: 0 12rpx 200rpx 0;
  box-sizing: border-box;
  background-color: #fff;
  overflow: scroll;
  z-index: 2;
  border-right: 1px solid #f8f8f8;
  position: sticky;
  top: 195rpx;
  height: calc(100vh - 300rpx - constant(safe-area-inset-bottom));
  height: calc(100vh - 300rpx - env(safe-area-inset-bottom));
}

.menu-item {
  height: 90rpx;
  max-width: 200rpx;
  text-align: center;
  position: relative;
  color: #999;
  font-size: 28rpx;
  font-weight: 400;
  /* word-break: break-word; */
  display: -webkit-box;
  display: flex;
  align-items:center;
  padding: 0 0 0 24rpx;
}
.menu-text {
	text-overflow:ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.menu-item-top {
  display: inline-block;
  padding-top: 7rpx;
}

.menu-item.active {
  color: #fff;
  font-weight: 600;
  background-image: linear-gradient(270deg, $wenet-color-gradient-end 0%, $wenet-color-brand 100%);
  box-shadow: 0 0 4px 0 rgba(235,235,235,0.50);
  border-radius: 0 4px 4px 0;
}

.menu-item text.tips-num {
  position: absolute;
  top: 20rpx;
  right: 15rpx;
  border-radius: 15rpx;
  width: 30rpx;
  height: 30rpx;
  background: $wenet-color-brand;
  color: #fff;
  font-size: 25rpx;
  line-height: 30rpx;
}

/* 右侧商品栏 */

.rightcontent {
  width: 550rpx;
  box-sizing: border-box;
  background-color: #fff;
  padding-bottom: 20rpx;
  z-index: 1;
  flex: 1;
}

.rightcontent .adver-map {
  width: auto;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  margin: 30rpx 20rpx 0;
}

.rightcontent .adver-map .item-a {
  display: block;
  font-size: 0;
  width: 100%;
}

.rightcontent .adver-map .item-a image {
  max-width: 100%;
}

.rightcontent .cont-item {
  padding-top: 0;
  padding-left: 12rpx;
  padding-right: 20rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 50px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 50px);
}



.rightcontent .cont-item .empty-tips {
  width: 100%;
  color: #999;
  text-align: center;
  font-size: 26rpx;
  line-height: 100rpx;
}

.tabs {
  position: relative;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  z-index: 999;
  border-bottom: 1rpx solid #f8f8f8;
}

.tabs .tab-item {
  position:relative;
  display: inline-block;
  width: 33.33%;
  text-align: center;
  font-size: 28rpx;
}

.tabs .tab-item.on {
  color: $wenet-color-brand;
}

.tab-item.up, .tab-item.down {
  color: $wenet-color-brand;
}
.tab-item.down::after,.tab-item.up::after {
  content: " ";
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  left: 172rpx;
  top: 40rpx;
  border: 10rpx solid transparent;
}

.en.tab-item.down:first-child::after, .en.tab-item.up:first-child::after{
	left: 176rpx;
}

.tab-item.down::after {
  border-top: 10rpx solid $wenet-color-brand;
} 

.tab-item.up::after {
  top: 26rpx;
  border-bottom: 10rpx solid $wenet-color-brand;
}
</style>

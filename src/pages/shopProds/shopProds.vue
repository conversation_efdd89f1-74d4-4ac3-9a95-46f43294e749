<template>
	<!--pages/allGoods/allGoods.wxml-->
	<view class="shop-container">

	<!-- 店铺信息 -->
  <shopHeader :shopId="shopId"></shopHeader>

		<!-- 搜索框 && 排序tab -->
		<view class="head-bg">
			<view class="bg-sear">
				<view :class="topFlag? 'navtop' : ''">
					<view class="section" @tap="toShopSearchPage" :data-shopid="shopId">
						<image class="search-img" :src="`${staticPicDomain}images/icon/search.png`"></image>
						<text class="placeholder">{{i18n.searchForItems}}</text>
					</view>

					<view class="tab-tit">
						<view :class="(curLang=='en'? 'en ': '') + 'tab-item'" @tap="onStsTap('')" :style="{color:sortType==''?'#e43130':''}">
							<text>{{i18n.default}}</text>
						</view>
						<view :class="'tab-item ' + (sortType=='sales' && (sort==2?'down':'up'))" @tap="onStsTap('sales')">
							<text>{{i18n.sales}}</text>
						</view>
						<view :class="'tab-item ' + (sortType=='price' && (sort==4?'down':'up'))" @tap="onStsTap('price')">
							<text>{{i18n.price}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 商品列表 -->
		<view class="prodlist-item-cont">
			<block v-for="(prod, prodId) in shopProdList" :key="prodId">
				<!-- <view class='prod-items' bindtap='toProdPage' data-prodid="{{prod.prodId}}">
				<view class='hot-imagecont'>
					<image src='{{prod.pic}}' class='hotsaleimg'></image>
				</view>
				<view class='hot-text'>
					<view class='hotprod-text'>{{prod.prodName}}</view>
					<view class='prod-info'>{{prod.brief}}</view>
					<view class='prod-text-info'>
					<view class='price'>
						<text class='symbol'>￥</text>
						<text class='big-num'>{{prod.price}}</text>
					</view>
					<image src='../../images/tabbar/basket-sel.png' class='basket-img'></image>
					</view>
				</view>
				</view> -->
				<prod-item :prod="prod"></prod-item>
			</block>
			<view class="tips" v-if="current === pages && current>1">{{i18n.allLoaded}}</view>
		</view>

		

    <!-- 回到顶部 -->
    <back-top-btn v-if="showBacktop"></back-top-btn>
	</view>
</template>

<script>
import http from "@/utils/http.js";
import prodItem from "@/components/prodListItem/prodListItem";
import shopHeader from '@/components/shopHeader/shopHeader';
import backTopBtn from "@/components/backTopBtn/backTopBtn";

export default {
  data() {
    return {
      topFlag: false,
      shopProdList: [],
      sort: 21,
      orderBy: 1, //状态点击事件
      current: 1,
      // 当前页
      pages: 0,
      // 总页码
      isAll: false,
      currentTab: true,
      curLang: '',
			scrollTop: "",
			showBacktop: false,
			sortType: ''
    };
  },

  components: {
    prodItem,
    shopHeader,
    backTopBtn
  },
  props: {
    shopId: {
      type: String,
      default: ''
    }
  },

  computed: {
    i18n() {
      return this.$t('index')
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () { },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    this.getNextPage();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () { },

  methods: {
    parentOnShow() {
      //头部导航标题
      uni.setNavigationBarTitle({
        title: this.i18n.allCommodities
      });
    },
    parentOnLoad(options) {
      this.setData({
        curLang: uni.getStorageSync('lang')
      });
      this.getShopProds(options);
    },
	// 页面滚动到指定位置指定元素固定在顶部
	onPageScroll: function (e) {
		this.scrollTop = e.scrollTop
		if (this.scrollTop > 80) {
			this.setData({
				topFlag: true,
				showBacktop: true
			})
		} else if (this.scrollTop < 80) {
			this.setData({
				topFlag: false,
				showBacktop: false
			})
		}
	},

    /**
     * 切换排序
		 * @param {String} sortType 排序类型
     */
    onStsTap: function (sortType) {
			this.sortType = sortType
			if (!sortType) {
				this.sort = 21
			} else if (sortType=='sales') {
				this.sort = this.sort == 3 ? 2 : 3
			} else if (sortType=='price') {
				this.sort = this.sort == 5 ? 4 : 5
			}
      uni.pageScrollTo({
        scrollTop: 0, //距离页面顶部的距离
        duration: 0
      })
      this.setData({
        currentTab: false,
        current: 1,
        pages: 0,
        isAll: false
      });
      this.getShopProds();
    },

    /**
     * 跳转搜索页
     */
    toShopSearchPage: function (e) {
      const shopId = e.currentTarget.dataset.shopid;
      uni.navigateTo({
        url: '/packageShop/pages/shopSearch/shopSearch?shopId=' + shopId
      });
    },

    // 获取店铺商品
    getShopProds(options) {
      uni.showLoading();
      var params = {
        // url: '/search/searchProdPage',
				url: '/search/page',
        method: 'GET',
        data: {
          shopId: options.shopId || this.shopId,
          sort: this.sort,
          current: this.current,
          orderBy: this.orderBy,
          size: 10,
          isAllProdType: true,
          isActive: 1 // 过滤掉活动商品
        },
        callBack: res => {
          uni.hideLoading()
          var shopProdList = [];
          if (this.current == 1) {
            this.setData({
              shopProdList: res.records[0].products,
              pages: res.pages,
            });
          } else {
            shopProdList = this.shopProdList;
            shopProdList.push(...res.records[0].products);
            this.setData({
              shopProdList
            });
          }
					if (res.pages == res.current) {
						this.isAll = true
					}
        }
      };
      http.request(params);
    },

    // 触底加载下一页
    getNextPage() {
      if (this.pages > this.current) {
        this.setData({
          current: this.current + 1
        });
        this.getShopProds();
      } else {
        this.setData({
          isAll: true
        });
      }
    },

    // 跳转商品详情
    toProdPage: function (e) {
      var prodid = e.currentTarget.dataset.prodid;

      if (prodid) {
        uni.navigateTo({
          url: '/pages/prod/prod?prodid=' + prodid
        });
      }
    }
  }
};
</script>
<style>
@import "./shopProds.css";
</style>

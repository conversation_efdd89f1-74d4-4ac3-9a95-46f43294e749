/**index.wxss**/

page {
  background: #f7f7f7;
  height: auto;
}

/* 骨架屏 */
.screen{
	position: fixed;
	z-index: 1000;
	width: 100%;
	height: 100vh;
	top: 0;
	background-color: #FFFFFF;
}
.screen .screen-bg-sear {
  height: 80rpx;
  /* z-index: 1; */
  border-radius: 50rpx;
  width: 100%;
  margin: 20rpx 0;
	/* padding: 0 20rpx; */
	background-color: #EEEEEE;
	display: block;
	align-items: center;
}
.screen .screen-swiper{
	width: 100%;
	height: 300rpx;
	overflow: hidden;
	margin-top: 40rpx;
	background-color: #EEEEEE;
}
.screen .screen-cat-item {
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: 20rpx 10rpx 30rpx;
}
.screen .screen-cat-item .item {
  text-align: center;
  /* width: 25%; */
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	background-color: #EEEEEE;
  display: flex;
  flex-direction: column;
  margin: auto;
  align-items: center;
}
.screen .screen-more{
	width: 100%;
	height: 200rpx;
	margin-top: 10rpx;
	margin-bottom: 40rpx;
	background-color: #EEEEEE;
}
/* 骨架屏end */

/* 轮播图及搜索框 */
swiper {
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}
swiper.pic-swiper {
  margin-top: 101rpx;
  padding: 10rpx 0;
  background: #fff;
  height: 400rpx;
}
swiper-item {
  font-size: 26rpx;
}
swiper.pic-swiper .img-box {
  font-size: 0;
}
.wx-swiper-dots {
  margin-bottom: 15rpx;
}
.banner-item {
  box-sizing: border-box;
}
.banner-item .img-box {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20rpx 10rpx;
}
.banner-item .img-box image {
  border-radius: 8rpx;
  box-shadow: 0 4px 10px 0 rgba(83, 83, 83, 0.288);
}
swiper.pic-swiper .banner {
  position: absolute;
  width: 690rpx;
  margin: 0 10rpx;
  height: 388rpx;
  border-radius: 8rpx;
  display: inline-block;
  box-shadow: 0 4px 10px 0 rgba(83, 83, 83, 0.288);
}
.container .bg-sear {
  position: sticky;
  z-index: 999;
  width: 100%;
  line-height: 56rpx;
  background: #fff;
  padding: 20rpx 0 30rpx 0;
  text-align: center;
  top: -1rpx;
}
.bg-sear .section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70rpx;
  background: #fff;
  z-index: 1;
  border-radius: 50rpx;
  width: 92%;
  margin: auto;
  left: 4%;
  background: #eeeeee;
  margin-top: 24rpx;
}
.bg-sear .section .placeholder {
  display: block;
  font-size: 28rpx;
  color: #999;
}
.bg-sear .section .search-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

/* 分类栏目 */
.content {
  background: #fff;
}
.cat-item {
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: 20rpx 10rpx 30rpx;
}
.cat-item .item {
  text-align: center;
  width: 25%;
  display: flex;
  flex-direction: column;
  margin: auto;
  align-items: center;
}
.cat-item .item image {
  width: 86rpx;
  height: 86rpx;
}
.cat-item .item text {
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 消息播放 */
.message-play {
  position: relative;
  height: 90rpx;
  background: #fff;
  margin: auto;
  padding: 0 60rpx 0 100rpx;
  box-sizing: border-box;
  border-top: 1px solid #f9f9f9;
}
.message-play .hornpng {
  width: 77rpx;
  height: 36rpx;
  position: absolute;
  left: 20rpx;
  top: 27rpx;
  margin-right: 8rpx;
}
.message-play .swiper-cont {
  height: 90rpx;
  line-height: 90rpx;
  margin-top: 0;
}
.message-play .swiper-cont .items {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-align: left;
}
.arrow {
  width: 15rpx;
  height: 15rpx;
  border-top: 3rpx solid #686868;
  border-right: 3rpx solid #686868;
  transform: rotate(45deg);
  position: absolute;
  right: 30rpx;
  top: 34rpx;
}

/* 直播列表入口 */
.live-enter {
  display: block;
  width: 100%;
  height: 250rpx;
  padding: 10rpx;
  padding-top: 20rpx;
  box-sizing: border-box;
}
.live-enter image {
  display: block;
  width: 100%;
  height: 100%;
}

/* 秒杀 */
.snap-up,
.abulk {
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.more-icon > image,
.img-box > image {
  width: 100%;
  height: 100%;
}
.snap-up-head {
  padding: 20rpx;
  padding-bottom: 10rpx;
}
.snap-up-tit {
  font-size: 32rpx;
  font-weight: bold;
}
.snap-up-time {
  padding: 5rpx 10rpx;
  padding-right: 1rpx;
  font-size: 24rpx;
  background: red;
  border-radius: 30rpx;
  margin: 0 15rpx;
}
.white {
  color: white;
}
.snap-up-countdown {
  margin-right: 2rpx;
  margin-left: 10rpx;
  background: #fff;
  color: red;
  border-radius: 0 15rpx 15rpx 0;
  padding: 5rpx 10rpx 0 8rpx;
}
.snap-up-time {
  display: inline-block;
}
.more-goods {
  float: right;
  font-size: 25rpx;
  color: #777;
  padding-top: 0.5em;
  line-height: 1em;
  display: flex;

}
.more-goods-txt {
  vertical-align: middle;
  color: #aaa;
}
.more-icon {
  display: inline-block;
  width: 20rpx;
  height: 20rpx;
  vertical-align: middle;
  padding-left: 5rpx;
}
/* 清除浮动 */
.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
/* 列表 */
.snap-up-goods-show {
  box-sizing: border-box;
  padding: 20rpx;
  width: 100%;
  height: auto;
  overflow-y: hidden;
  overflow-x: auto;
  white-space: nowrap;
  background: #fff;
  border-radius: 10rpx;
  margin-top: 10rpx;
}
.snap-up-goods-show::-webkit-scrollbar{
  display: none;
}

.snap-up-goods-box {
  position: relative;
  display: inline-block;
  text-align: center;
  margin-right: 20rpx;
	overflow:-moz-scrollbars-none;
	scrollbar-width: none;
}
.snap-up-goods-box .img-box {
  display: block;
  width: 210rpx;
  height: 210rpx;
  font-size: 0;
}
.purchase-conditions {
  margin-top: 10rpx;
  line-height: 1.5em;
}
.group-number {
font-size: 18rpx;
color: white;
background: #e43130;
padding: 6rpx 12rpx;
border-radius: 20rpx;
line-height: 20rpx;
position: absolute;
top: 10rpx;
left: 10rpx;
}
.snap-up-price {
  display: inline-block;
  font-size: 28rpx;
  color: #e43130;
  line-height: 1.5em;
  text-align: center;
  padding-top: 8rpx;
  font-family: arial;
}

.snap-up-price .symbol {
  font-size: 24rpx;
}

.original-price {
  text-align: center;
  font-size: 25rpx;
  line-height: 1em;
  color: #999;
  text-decoration: line-through;
}
/* 查看更多 */
.view-more {
  display: inline-block;
  width: 30rpx;
  padding: 0 10rpx;
  height: 146px;
  line-height: 30rpx;
  writing-mode: vertical-lr;
  font-size: 23rpx;
  color: #888;
  background: #eee;
  vertical-align: top;
  text-align: center;
  letter-spacing: 3px;
}
.view-more-icon {
  display: inline-block;
  width: 20rpx;
  height: 20rpx;
  text-align: center;
  vertical-align: middle;
  margin-right: 9rpx;
}



/* 每日上新 */
.title {
  position: relative;
  height: 1.5em;
  line-height: 1.5em;
  font-size: 32rpx;
  padding: 20rpx 40rpx;
  color:#333;
  font-weight: bold;
  margin-top: 20rpx;
}

.up-to-date .title{
  color: #fff;
  background: none;
}

.title .more-prod-cont {
  color: #999;
  display: inline-block;
  text-align: right;
}

.up-to-date .title .more-prod-cont .more {
  position:absolute;
  right:30rpx;
  top:48rpx;
  color:#fff;
  font-size:24rpx;
  background:#65addf;
  border-radius:30rpx;
  padding:0 30rpx;
  height:44rpx;
  line-height:44rpx;

}

.title .more-prod-cont .more{
   position:absolute;
  right:30rpx;
  top:48rpx;
  color:#666;
  font-size:24rpx;
  padding:0 20rpx;
  height:44rpx;
  line-height:44rpx;
}
.title .more-prod-cont .arrow {
  top:58rpx;
  right: 30rpx;
  border-top: 2rpx solid #666;
  border-right: 2rpx solid #666;
}
.up-to-date {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUAAAABxCAYAAACkwXoWAAABS2lUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxMzggNzkuMTU5ODI0LCAyMDE2LzA5LzE0LTAxOjA5OjAxICAgICAgICAiPgogPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIi8+CiA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgo8P3hwYWNrZXQgZW5kPSJyIj8+IEmuOgAAAZBJREFUeJzt1DEBwCAAwLAxYfhEGXJABkcTBb065trnAwj6XwcAvGKAQJYBAlkGCGQZIJBlgECWAQJZBghkGSCQZYBAlgECWQYIZBkgkGWAQJYBAlkGCGQZIJBlgECWAQJZBghkGSCQZYBAlgECWQYIZBkgkGWAQJYBAlkGCGQZIJBlgECWAQJZBghkGSCQZYBAlgECWQYIZBkgkGWAQJYBAlkGCGQZIJBlgECWAQJZBghkGSCQZYBAlgECWQYIZBkgkGWAQJYBAlkGCGQZIJBlgECWAQJZBghkGSCQZYBAlgECWQYIZBkgkGWAQJYBAlkGCGQZIJBlgECWAQJZBghkGSCQZYBAlgECWQYIZBkgkGWAQJYBAlkGCGQZIJBlgECWAQJZBghkGSCQZYBAlgECWQYIZBkgkGWAQJYBAlkGCGQZIJBlgECWAQJZBghkGSCQZYBAlgECWQYIZBkgkGWAQJYBAlkGCGQZIJBlgECWAQJZBghkGSCQZYBAlgECWQYIZBkgkGWAQJYBAlkGCGRdKykDj9OUNYkAAAAASUVORK5CYII=");
  background-position: top;
  background-size: 100% 332rpx;
  background-repeat: no-repeat;
  background-color: #fff;
}
.up-to-date .item-cont {
  margin: auto;
  height: auto;
  width: calc(100% - 40rpx);
  padding:0 20rpx;
  display: flex;
  flex-wrap:wrap;
  justify-content: space-around;
}

.goods-item-cont {
  padding-bottom: 20rpx;
}

.up-to-date .item-cont::before {
  clear: both;
  height: 0;
  overflow: hidden;
}

.up-to-date .item-cont .prod-item {
  width: 345rpx;
  display: inline-block;
  background: #fff;
  padding: 20rpx;
  box-sizing: border-box;
  margin-left: 20rpx;
  margin-top: 20rpx;
  border-radius: 10rpx;
}

.up-to-date .item-cont .prod-item .imagecont {
  width: 100%;
  font-size: 0;
}

.up-to-date .item-cont .prod-item .imagecont .prodimg {
  width: 220rpx;
  height: 220rpx;
  vertical-align: middle;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
  font-size:0;
}
.up-to-date .item-cont .prod-item .prod-text {
  font-size: 28rpx;
  overflow: hidden;
  margin: 10rpx 0;
  min-height: 60rpx;
  display: -webkit-box;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #000;
  padding: 0 10rpx;
}
.up-to-date .item-cont .prod-item .prod-price {
  font-size: 25rpx;
  color: #eb2444;
  font-family: Arial;
  padding: 0 10rpx;
}
.more.prod-price {
  position: absolute;
  bottom: 20rpx;
}

/* 商城热卖&更多宝贝 */

.goods-list {
  margin-top: 20rpx;
}

.goods-list .prod-items {
  width: 345rpx;
  display: inline-block;
  background: #fff;
  padding: 20rpx;
  box-sizing: border-box;
  margin-left: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.prod-items .goods-item-imagecont .goods-item-img {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.prod-items .goods-item-text .goods-item-prod-text {
  font-size: 28rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;  /* 固定行数：2行  */
  -webkit-box-orient: vertical;  /* 方向：垂直  */
  word-break: break-all;  /* word-break换行方式：break-all允许在单词内换行  */
  overflow: hidden;
  text-overflow: ellipsis;
}
.prod-items .goods-item-imagecont {
  font-size: 0;
  text-align: center;
    width: 305rpx;
    height: 305rpx;
}
.prod-items .goods-item-text {
  margin-top: 20rpx;
}
.prod-items .goods-item-text .prod-info, .more-prod .prod-text-right .prod-info {
  font-size: 24rpx;
  color: #888888;
  line-height: 1em;
  margin-top: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 24rpx;
}

.prod-items .goods-item-text .prod-text-info {
  position: relative;
  height: 40rpx;
  line-height: 40rpx;
  font-family: Arial;
  margin-top: 20rpx;
}
.prod-items .goods-item-text .prod-text-info .goods-item-prod-price {
  display: inline;
  font-size: 26rpx;
  color: #eb2444;
}
.prod-items .goods-item-text .prod-text-info .basket-img {
  width: 50rpx;
  height: 50rpx;
  position: absolute;
  right: 10rpx;
  bottom: 10rpx;
  /* border: 2rpx solid #eb2444;
  border-radius: 50%; */
  /* padding: 8rpx; */
}
.singal-price {
  display: inline;
  font-size: 20rpx;
  text-decoration: line-through;
  color: #777;
  margin-left: 15rpx;
}
.tips {
  margin-top: 50rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}
.index-stytle .container {
  padding: 0;
}

.index-stytle >>> .bottom-dialog-body {
  width: 270px;
}

.loop-top {
  margin-top: 110rpx
}

.popup-close {
  display: flex;
  justify-content: flex-end;
}

.popup-close image {
  width: 18rpx;
  height: 18rpx;
  display: block;
}

.login-phone {
  width: 100%;
  margin: 50rpx auto 30rpx auto;
}

.authorized-btn {
  width: 88%;
  margin: 0 auto;
  text-align: center;
  background-color: #0ab906;
  border: 1rpx solid #0ab906;
  color: #fff;
  border-radius: 6rpx;
  font-size: 26rpx;
}

.authorized-tip {
  font-size: 24rpx;
  color: #999999;
  margin-top: 20rpx;
  margin-bottom: 24rpx;
  text-align: center;
}

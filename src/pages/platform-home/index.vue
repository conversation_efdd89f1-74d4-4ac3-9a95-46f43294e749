<template>
	<!--index.wxml-->
	<view class="index-stytle">
		<!-- 骨架屏 -->
		<view class="screen" v-if="showScreen">
			<view class="screen-bg-sear"></view>
			<view class="screen-swiper"></view>
			<view class="screen-cat-item">
				<view class="item" v-for="item in 5" :key="item"></view>
			</view>
			<view class="screen-more" v-for="item in 4"  :key="item"></view>
		</view>
		<!-- 骨架屏 end -->
		<view class="container" v-if="!renovationId">
			<!-- 搜索框 -->
			<view class="bg-sear" id="bgsear">
        <view class="scrolltop">
					<view class="section" @tap="toSearchPage">
						<image :src="`${staticPicDomain}images/icon/search.png`" class="search-img"></image>
						<text class="placeholder">{{i18n.search}}</text>
					</view>
				</view>
			</view>
			<!-- 搜索框end -->

			<!-- 导航&公告 -->
			<view :class="['content', indexImgs.length? '':'loop-top']">
				<!-- swiper -->
				<swiper circular="true" :autoplay="autoplay" :indicator-color="indicatorColor" :interval="interval" :duration="duration"
				 :indicator-active-color="indicatorActiveColor" class="card-swiper" indicator-dots previous-margin="20rpx"
				 next-margin="20rpx"
				 v-if="indexImgs.length"
				 >
					<block v-for="(item, seq) in indexImgs" :key="seq">
						<swiper-item class="banner-item">
							<view class="img-box">
								<image :src="item.imgUrl" @tap="toIndexImgContent(item)" class="banner"></image>
							</view>
						</swiper-item>
					</block>
				</swiper>
				<!-- end swiper -->

				<!-- 板块 -->
				<view class="cat-item">
					<view class="item" @tap="toClassifyPage" data-sts="1">
						<image :src="`${staticPicDomain}images/icon/newProd.png`"></image>
						<text>{{i18n.newProduct}}</text>
					</view>
					<view class="item" @tap="toSpecialDiscount" data-sts="2">
						<image :src="`${staticPicDomain}images/icon/timePrice.png`"></image>
						<text>{{i18n.limitedTimeOffer}}</text>
					</view>
					<view class="item" @tap="toAbulkPage">
						<image :src="`${staticPicDomain}images/icon/group.png`"></image>
						<text>{{i18n.groupDiscount}}</text>
					</view>
					<view class="item" @tap="toSecKillPage" data-sts="3">
						<image :src="`${staticPicDomain}images/icon/neweveryday.png`"></image>
						<text>{{i18n.spikeSpecial}}</text>
					</view>
					<view class="item" @tap="toCouponCenter">
						<image :src="`${staticPicDomain}images/icon/newprods.png`"></image>
						<text>{{i18n.couponCenter}}</text>
					</view>
				</view>
				<!-- 板块end -->

				<!-- 消息播放 -->
				<view class="message-play" @tap="onNewsPage" v-if="news.length">
					<image :src="`${staticPicDomain}images/icon/horn.png`" class="hornpng"></image>
					<swiper vertical="true" autoplay="true" duration="1000" circular="true" class="swiper-cont">
						<block v-for="(item, id) in news" :key="id">
							<swiper-item class="items">{{item.title}}</swiper-item>
						</block>
					</swiper>
					<text class="arrow"></text>
				</view>
				<!-- 消息播放end -->

			</view>
			<!-- 导航&公告end -->

			<!-- 直播列表入口 -->
			<!-- #ifdef MP-WEIXIN -->
			<!-- <view class="live-enter" @tap="toLiveListPage">
			</view>	 -->
			<!-- #endif -->

			<!-- 秒杀 -->
			<view class="snap-up clearfix" v-if="snapUpList.length">
				<!-- 头部 -->
				<view class="snap-up-head clearfix">
					<text class="snap-up-tit">{{i18n.spike}}</text>
					<!-- 倒计时 -->
					<!-- <view class="snap-up-time">
					<text class="white">12点场</text>
					<text class="snap-up-countdown">00:14:53</text>
				</view> -->
					<!-- 更多 -->
					<view class="more-goods" @tap="toSnapUpPage">
						<text class="more-goods-txt">{{i18n.moreSpikes}}</text>
						<view class="more-icon">
							<image :src="`${staticPicDomain}images/icon/more.png`"></image>
						</view>
					</view>
				</view>

				<!-- 列表 -->
				<view class="snap-up-goods-show">
					<!-- 商品盒子 -->
					<view class="snap-up-goods-box" v-for="(item, index) in snapUpList" :key="index" @tap="toSnapUpListPage(item.seckillSearchVO.seckillId)">
						<view class="img-box">
							<image :src="item.pic"></image>
						</view>
						<view class="snap-up-price"><text class="symbol">￥</text>{{toPrice(item.activityPrice)}}</view>
						<view class="original-price">￥{{toPrice(item.price)}}</view>
					</view>
					<!-- 查看更多 -->
					<view class="view-more" @tap="toSnapUpPage" v-if="snapUpList.length > 3">{{i18n.seeMore}}
						<image class="view-more-icon" :src="`${staticPicDomain}images/icon/more-icon.png`"></image>
					</view>
				</view>

			</view>
			<!-- 秒杀end -->

			<!-- 团购 -->
			<view class="abulk clearfix" v-if="aBulkList.length">
				<!-- 头部 -->
				<view class="snap-up-head clearfix">
					<text class="snap-up-tit">{{i18n.groupBuy}}</text>
					<!-- 倒计时 -->
					<!-- <view class="snap-up-time">
					<text class="white">12点场</text>
					<text class="snap-up-countdown">00:14:53</text>
				</view> -->
					<!-- 更多 -->
					<view class="more-goods" @tap="toAbulkPage">
						<text class="more-goods-txt">{{i18n.moreGroupBuy}}</text>
						<view class="more-icon">
							<image :src="`${staticPicDomain}images/icon/more.png`"></image>
						</view>
					</view>
				</view>

				<!-- 列表 -->
				<view class="snap-up-goods-show">
					<!-- 商品盒子 -->
					<view class="snap-up-goods-box" v-for="(item, index) in aBulkList" :key="index" @tap="toAbulkListPage"
					 :data-groupactivityid="item.groupActivityId" :data-prodid="item.prodId">
						<view class="img-box">
							<image :src="item.pic"></image>
						</view>
						<view class="purchase-conditions">
							<view class="group-number">{{item.groupActivitySearchVO.groupNumber}}{{i18n.join}}</view>
							<view class="snap-up-price"><text class="symbol">￥</text>{{toPrice(item.activityPrice)}}</view>
						</view>
						<view class="original-price">￥{{toPrice(item.price)}}</view>
					</view>
					<!-- 查看更多 -->
					<view class="view-more" @tap="toAbulkPage" v-if="aBulkList.length > 3">{{i18n.seeMore}}
						<image class="view-more-icon" :src="`${staticPicDomain}images/icon/more-icon.png`"></image>
					</view>
				</view>

			</view>
			<!-- 团购end -->

			<!-- 商城热卖 -->
			<view class="goods-list">
				<view class="title">
					<text>{{i18n.hotSale}}</text>
				</view>
				<view class="goods-item-cont">
					<block v-for="(prod, prodId) in hotSalesList" :key="prodId">
						<view class="prod-items" @tap="toProdPage" :data-prodid="prod.prodId">
							<view class="goods-item-imagecont">
								<image :src="prod.pic" class="goods-item-img"></image>
							</view>
							<view class="goods-item-text">
								<view class="goods-item-prod-text">{{prod.prodName}}</view>
								<view class="prod-info" v-if="prod.brief" >{{prod.brief}}</view>
								<view class="prod-text-info">
									<view class="price">
										<text class="symbol">￥</text>
										<text class="big-num">{{parsePrice(prod.price)[0]}}</text>
										<text class="small-num">.{{parsePrice(prod.price)[1]}}</text>
									</view>
								</view>
							</view>
						</view>
					</block>
					<view class="tips" >{{i18n.allLoaded}}</view>
				</view>
			</view>

			<!-- 回到顶部 -->
			<back-top-btn v-if="showBacktop"></back-top-btn>

		</view>
		<view v-if="renovationId">
			<feature ref="featureIndex" @pageLoaded="pageLoaded" :pageLoad="pageLoad" :pageId="renovationId" :shopId="0"
			 :pageScorllTop="pageScorllTop"></feature>
		</view>
		<privacy-pop v-if="showPop" @hidePop="hidePop" />

	</view>
</template>

<script>
	//index.js
	//获取应用实例
	var http = require("../../utils/http.js");
	var util = require("../../utils/util.js");
	import backTopBtn from "../../components/backTopBtn/backTopBtn";
	import feature from '../../components/feature/index/index'
	import privacyPop from '../../components/privacyPop/index'
	import popup from '../../components/popup/popup'
  
	export default {
		data() {
			return {
				indicatorDots: true,
				indicatorColor: '#d1e5fb',
				indicatorActiveColor: '#1b7dec',
				autoplay: true,
				interval: 3000,
				duration: 1000,
				indexImgs: [],
				seq: 0,
				news: [],
				hotSalesList: [],
				sts: 0,
				current: 1,
				sort: 1,
				isAll: false,
				snapUpList: [],
				//秒杀列表
				aBulkList: [], //团购列表
				pages: "",
				scrollTop: "",
				showBacktop: false,
				isShow: false,
				targetDiffHours: 1,
				loading: false,

				// 装修
				pageLoad: false,
				renovationId: '', // 页面id
				shopId: '',
				pageScorllTop: 0, // 页面滚动距离
				platformHomePage: '',
				uniLoginLogoImg: "", // Logo
				showScreen: true, // 骨架屏的显隐
				showPop: false,
			};
		},

		components: {
			backTopBtn,
			feature,
			privacyPop,
			popup,
    },
		props: {},
		computed: {
			i18n() {
				return this.$t('index')
			},
      // userInfo() {
      //   return this.$store.state.userInfo
      // }
		},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			this.getNextPage();
		},
		onShareAppMessage: function(e) {
			return {
				path: "pages/index/index"
			};
		},

		// 页面滚动到指定位置指定元素固定在顶部
		onPageScroll: function(e) {
			this.scrollTop = e.scrollTop
			this.pageScorllTop = e.scrollTop
			if (this.scrollTop > 800) {
				this.setData({
					showBacktop: true
				})
			} else if (this.scrollTop < 800) {
				this.setData({
					showBacktop: false
				})
			}
		},

		methods: {
      parentOnShow() {
        // #ifndef H5
        var logData = uni.getStorageSync('flowAnalysisLogDto')
        uni.setStorageSync('step', uni.getStorageSync('step') / 1 + 1)
        if (logData && logData.pageId != 1 || uni.getStorageSync('step') == 1) {
          logData.pageId = 1
          logData.step = uni.getStorageSync('step')
          uni.setStorageSync('flowAnalysisLogDto', logData)
          http.saveLog(logData, 1)
        }
        // #endif
        // http.getCartCount(); //重新计算购物车总数量
      },
      parentOnLoad(options) {
        uni.showNavigationBarLoading()
        // 获取装修数据
        this.getFeatureIndex()
        this.getUniWebConfig(); //初始加载获取配置
        // 缓存用户uid、学校ou
        // this.saveWenetInfo(options)
      },
      parentPullDownRefresh: function() {
        //模拟加载
        var ths = this;
        this.setData({ //下拉刷新
          current: 1
        })
        setTimeout(function() {
          // ths.getAllData(); // uni.hideNavigationBarLoading() //完成停止加载
          if (ths.renovationId) {
            ths.$refs.featureIndex.getPageInfoById()
          }
          wx.stopPullDownRefresh(); //停止下拉刷新
        }, 100);
      },

			hidePop() {
				this.showPop = false
				uni.showTabBar()
			},

			/**
			 * 获取装修页面数据
			 */
			getFeatureIndex() {
				http.request({
					url: '/shopRenovation/getHomePage',
					method: 'GET',
					callBack: res => {
						if (res.homeStatus === 1) {
							this.renovationId = res.renovationId
							this.shopId = res.shopId
						} else {
							this.getAllData();
							uni.setNavigationBarTitle({
								title: this.i18n.wenetMultiStore
							});
						}
						this.showScreen = false;
					}
				})
				uni.hideNavigationBarLoading()
			},

			/**
			 * 获取uni-app相关配置
			 */
			getUniWebConfig: function() {
				var params = {
					url: "/webConfig/getUniWebConfig",
					method: "GET",
					data: {},
					callBack: res => {
						this.setData({
							uniLoginLogoImg: res.uniLoginLogoImg
						});
						uni.setStorageSync("uniLoginLogoImg",this.uniLoginLogoImg)
					}
				};
				http.request(params);
			},

			// 页面加载回调
			pageLoaded(e) {
				uni.setNavigationBarTitle({
					title: e.detail.title
				})
				// this.pageLoaded = false
				setTimeout(() => {
					uni.hideLoading()
					// this.pageLoaded = true
				}, 1)
			},

			/**
			 * 跳转到商品详情页
			 */
			toProdPage: function(e) {
				util.tapLog(3)
				var prodid = e.currentTarget.dataset.prodid;
				if (prodid) {
					// uni.navigateTo({
					//   url: '/pages/prod/prod?prodid=' + prodid
					// });
					this.$Router.push({
						path: '/pages/prod/prod',
						query: {
							prodid: prodid
						}
					})
				}
			},
			// 点击轮播图跳转相应页面
			toIndexImgContent: function(item) {
				util.tapLog(3)
				if (item.type !== 0) {
					return
				}
				let prodId = item.relation
				http.request({
					url: '/prod/isStatus',
					method: 'GET',
					data: {
						prodId : prodId
					},
					callBack: res => {
						if (res) {
							this.$Router.push({
								path: '/pages/prod/prod',
								query: {
									prodid: prodId,
									bannerEnter: '1'
								}
							})
						}
					}
				})
			},
			toCouponCenter: function() {
				util.checkAuthInfo(() => {
					uni.navigateTo({
						url: '/packageActivities/pages/couponCenter/couponCenter'
					})
				})
			},

			/**
			 * 跳转到直播列表
			 */
			toLiveListPage: function() {
				util.tapLog(3)
				uni.navigateTo({
					url: '/pages/liveBroadcast/liveBroadcast',
				})
			},

			/**
			 * 秒杀
			 */
			getSnapUpList: function() {
				uni.showLoading();
				var params = {
					// url: "/seckill/pageProd",
					url: "/search/page",
					method: "GET",
					data: {
						size: 20,
						current: 1,
						prodType: 2,
						isActive: 1 // 过滤掉活动商品
					},
					callBack: res => {
						uni.hideLoading();
						let result = res.records[0].products.filter(item => util.dateToTimestamp(item.seckillSearchVO.endTime) > new Date().getTime()); //过滤掉秒杀时间已结束的商品

						this.setData({
							snapUpList: result
						});
					}
				};
				http.request(params);
			},

			/**
			 * 跳转秒杀列表页
			 */
			toSnapUpPage: function() {
				util.tapLog(3)
				uni.navigateTo({
					url: '/packageActivities/pages/snapUpList/snapUpList'
				});
			},
			toSnapUpListPage: function(seckillId) {
				util.tapLog(3)
				this.$Router.push({
					path: '/packageActivities/pages/snapUpDetail/snapUpDetail',
					query: {
						seckillId: seckillId
					}
				})
			},

			/**
			 * 团购
			 */
			getAbulk: function() {
				uni.showLoading();
				var param = {
					// url: "/groupProd/indexList",
					url: "/search/page",
					method: "GET",
					data: {
						current: 1,
						size: 5,
						prodType: 1,
						isActive: 1 // 过滤掉活动商品
					},
					callBack: res => {
						uni.hideLoading();
						this.setData({
							aBulkList: res.records[0].products
						});
					}
				};
				http.request(param);
			},

			/**
			 * 跳转团购列表页
			 */
			toAbulkPage: function() {
				util.tapLog(3)
				uni.navigateTo({
					url: '/packageActivities/pages/aBulkList/aBulkList'
				});
			},
			toAbulkListPage: function(e) {
				util.tapLog(3)
				var prodId = e.currentTarget.dataset.prodid;
				var groupActivityId = e.currentTarget.dataset.groupactivityid;
				this.$Router.push({
					path: '/pages/prod/prod',
					query: {
						prodid: prodId,
						groupActivityId: groupActivityId
					}
				})
			},
			// 跳转搜索页
			toSearchPage: function() {
				util.tapLog(3)
				uni.navigateTo({
					url: '/pages/search-page/search-page'
				});
			},
			//跳转商品活动页面
			toClassifyPage: function(e) {
				util.tapLog(3)
				var url = '/pages/prod-classify/prod-classify?sts=' + e.currentTarget.dataset.sts;
				var id = e.currentTarget.dataset.id;
				var title = e.currentTarget.dataset.title;

				if (id) {
					url += "&tagid=" + id + "&title=" + title;
				}

				uni.navigateTo({
					url: url
				});
			},
			toSecKillPage: function() {
				uni.navigateTo({
					url: '/packageActivities/pages/snapUpList/snapUpList'
				});
			},
			toSpecialDiscount: function() {
				this.$Router.push('/packageActivities/pages/specialDiscount/specialDiscount')
			},
			//跳转公告列表页面
			onNewsPage: function() {
				uni.navigateTo({
					url: '/packageUser/pages/recent-news/recent-news'
				});
			},

			getAllData() {
				uni.showLoading();
				// http.getCartCount(); //重新计算购物车总数量

				this.getIndexImgs();
				this.getNoticeList();
				this.getHotSalesProds();
				this.getSnapUpList();
				this.getAbulk();
			},

			//加载轮播图
			getIndexImgs() {
				//加载轮播图
				var params = {
					url: "/indexImgs/0",
					method: "GET",
					data: {},
					callBack: res => {
						this.setData({
							indexImgs:res || [],
							seq: res
						});
						uni.hideLoading();
					}
				};
				http.request(params);
			},

			getNoticeList() {
				// 加载公告
				var params = {
					url: "/shop/notice/topNoticeList/0",
					method: "GET",
					data: {},
					callBack: res => {
						this.setData({
							news: res || []
						});
						uni.hideLoading();
					}
				};
				http.request(params);
			},

			/**
			 * 加载热销商品列表
			 */
			getHotSalesProds() {
				uni.showLoading();
				var param = {
					// url: "/search/searchProdPage",
					url: "/search/page",
					method: "GET",
					data: {
						current: this.current,
						size: 20,
						sort: 2,
						orderBy: 1,
						isActive: 1 // 过滤掉活动商品
					},
					callBack: res => {
						uni.hideLoading();
						var hotSalesList = [];

						if (this.current == 1) {
							this.setData({
								hotSalesList: res.records[0].products,
								pages: res.pages
							});
						} else {
							hotSalesList = this.hotSalesList;
							hotSalesList.push(...res.records[0].products);
							this.setData({
								hotSalesList
							});
						}
					}
				};
				http.request(param);
			},

			// 触底加载下一页
			getNextPage() {
				if (this.pages > this.current) {
					this.setData({
						current: this.current + 1
					});
					this.getHotSalesProds();
				} else {
					this.setData({
						isAll: true
					});
				}
			},

		}
	};
</script>
<style>
	@import "./index.css";
</style>

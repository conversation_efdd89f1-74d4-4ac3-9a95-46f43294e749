<template>
  <div>
    <!-- TODO shop-id 和 装修id 动态获取 -->
    <ShopProds ref="shopProdsRef" v-if="isLogin" :shop-id="35"></ShopProds>
    <GoodStore ref="goodStoreRef" v-else></GoodStore>
  </div>
</template>

<script>
import GoodStore from '@/pages/good-store/good-store'
import ShopProds from '@/pages/shopProds/shopProds'
export default {
  data () {
    return {
    }
  },
  components: {
    GoodStore,
    ShopProds
  },
  onShow(options) {
    this.$nextTick(() => {
      if (this.isLogin) {
        this.$refs.shopProdsRef.parentOnShow(options);
      } else {
        this.$refs.goodStoreRef.parentOnShow(options);
      }
    })
  },
  onLoad(options) {
    this.$nextTick(() => {
      if (this.isLogin) {
        this.$refs.shopProdsRef.parentOnLoad(options);
      } else {
        this.$refs.goodStoreRef.parentOnLoad(options);
      }
    })
  },
  methods: {

  },
  computed: {
    isLogin () {
      return this.$store.state.isLogin
    }
  },
  mounted () {

  },
}
</script>

<style scoped>
</style>
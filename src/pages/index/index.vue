<template>
  <view :class="wrapClassName">
    <shop-switcher v-if="isLogin"></shop-switcher>
    <ShopHome ref="shopHomeRef" v-if="isLogin && currentShop" :shop-id="currentShop.shopId" :renovation-id="currentShop.renovationId"></ShopHome>
    <PlatformHome ref="platformHomeRef" v-else></PlatformHome>

    <PopupAd :isShow="showPopupAd" @hidePopupAd="hidePopupAd" ref="popupAdRef"/>
    <BottomLoginNav v-if="!isLogin" />
    <WenetAccountTip />
    <n-loading />
    <!-- #ifdef MP-WEIXIN -->
    <WxPrivacyPopup ref="wxPrivacyPopupRef" @agree="handleWxPrivacyPopupAgree" />
    <!-- #endif -->
  </view>
</template>

<script>
import PlatformHome from '@/pages/platform-home/index'
import ShopHome from '@/pages/shop-feature-index/shop-feature-index0'
import PopupAd from '@/components/popup-ad/popup-ad'
import ShopSwitcher from '@/components/shop-switcher'
import util from '@/utils/util'
import WenetAccountTip from '@/components/wenet-account-tip'
import BottomLoginNav from '@/components/bottom-login-nav'
import WxPrivacyPopup from '@/components/wx-privacy-popup'
var http = require("../../utils/http.js");
import { AppType } from '@/utils/constant';

let harassGen;
let isInitProcessRunning = false;  // 添加状态变量
let newCustomerZoneLocked = true; // 新人专区锁，如果为true，不跳转

export default {
  data () {
    return {
      showPopupAd: false,
      popupAdInfo: {},
      isMini: false,
    }
  },
  components: {
    PlatformHome,
    ShopHome,
    PopupAd,
    ShopSwitcher,
    BottomLoginNav,
    WenetAccountTip,
    WxPrivacyPopup
  },
  onShow(options) {
    this.$nextTick(() => {
      if (this.isLogin && this.currentShop) {
        this.$refs.shopHomeRef?.parentOnShow(options);
        this.goToNewCustomerZone();
        this.showMineTabBarBadge();
      } else {
        this.$refs.platformHomeRef?.parentOnShow(options);
      }
    })
  },
  onLoad(options) {
    this.$nextTick(() => {
      if (this.isLogin && this.currentShop) {
        this.$refs.shopHomeRef?.parentOnLoad(options);
      } else {
        this.$refs.platformHomeRef?.parentOnLoad(options);
      }
    })
    harassGen = this.initProcess();
    this.runInitProcessStep();  // 修改为使用新的函数
  },
  onPullDownRefresh() {
    setTimeout(() => {
      this.$nextTick(() => {
        if (this.isLogin && this.currentShop) {
          this.$refs.shopHomeRef?.parentPullDownRefresh();
        } else {
          this.$refs.platformHomeRef?.parentPullDownRefresh();
        }
      })
      uni.stopPullDownRefresh();
    }, 100);
  },
  mounted() {
    const appType = uni.getStorageSync("appType");
    this.isMini = appType === AppType.MINI
  },
  onShareAppMessage: function () {},
  methods: {
    // 一系列骚扰程序
    *initProcess() {
      yield this.checkWxPrivacy();
      yield this.checkImprovedInfo();
      yield this.checkPopUpAd();
      newCustomerZoneLocked = false;
      this.goToNewCustomerZone();
    },
    runInitProcessStep() {
      if (isInitProcessRunning) return;
      isInitProcessRunning = true;
      const { value, done } = harassGen.next();
      if (done) {
        isInitProcessRunning = false;
        return;
      }
      Promise.resolve(value).then(() => {
        isInitProcessRunning = false;
        this.runInitProcessStep();
      }).catch(() => {
        isInitProcessRunning = false;
        this.runInitProcessStep();
      });
    },
    checkPopUpAd() {
      return new Promise((resolve) => {
        http.request({
          url: '/api/popup/get',
          method: 'get',
          params: {},
          callBack: (res) => {
            if (res) {
              this.showPopupAd = true;
              this.$nextTick(() => {
                this.$refs.popupAdRef.init(res);
                console.log('弹出弹窗广告');
                resolve();
              });
            } else {
              console.log('无需弹出广告');
              resolve();
            }
          },
          errCallBack: () => {
            console.log('获取广告出错');
            resolve();
          }
        });
      });
    },
    checkImprovedInfo() {
      return new Promise((resolve, reject) => {
        const isUnImprovedInfo = uni.getStorageSync('unImprovedInfo');
        if (isUnImprovedInfo) {
          console.log('前往补充信息页');
          uni.redirectTo({
            url: '/packageUser/pages/improve-info/improve-info'
          });
          reject('需要前往补充信息页');
        } else {
          console.log('无需前往补充信息页');
          resolve();
        }
      });
    },
    checkWxPrivacy() {
      return new Promise((resolve) => {
        if (this.$refs.wxPrivacyPopupRef) {
          this.$refs.wxPrivacyPopupRef.checkIsAgree().then((res) => {
            if (res?.needAuthorization) {
              console.log('弹出《微信小程序隐私保护指引》');
              this.$refs.wxPrivacyPopupRef.show();
              this.$umaTracker.TriggerHideTabbar({
                reason: '弹出《微信小程序隐私保护指引》'
              });
              uni.hideTabBar();
            } else {
              console.log('用户已同意《微信小程序隐私保护指引》');
              resolve();
            }
          });
        } else {
          console.log('h5无需判断《微信小程序隐私保护指引》');
          resolve();
        }
      });
    },
    handleWxPrivacyPopupAgree() {
      if (this.$refs.wxPrivacyPopupRef) {
        this.$refs.wxPrivacyPopupRef.close();
        this.$umaTracker.TriggerShowTabbar({
          reason: '同意《微信小程序隐私保护指引》'
        });
        uni.showTabBar();
        console.log('同意《微信小程序隐私保护指引》');
        this.runInitProcessStep();
      }
    },
    hidePopupAd() {
      this.showPopupAd = false;
    },
    goToNewCustomerZone() {
      if (this.currentShop && this.currentShop.newPageId) {
        const autoRedirectToNewCustomerZone = uni.getStorageSync('autoRedirectToNewCustomerZone');
        if (!autoRedirectToNewCustomerZone) {
          return
        }
        util.checkNewUser(this.currentShop.shopId).then(isNewUser => {
          if (isNewUser) {
            console.log('新用户：前往新人专区');
            uni.navigateTo({
              url: `/packageShop/pages/new-customer-zone/new-customer-zone`
            });
            console.log('新用户：已自动跳转过新人专区，此次不跳转');
          } else {
            console.log('非新用户，不跳转新人专区');
          }
        });
      }
    },
     // 加载订单数据
     loadOrderCountFun() {
      return new Promise((resolve) => {
        http.request({
          url: "/p/user/centerInfo",
          method: "GET",
          dontTrunLogin: true,
          data: {},
          callBack: (res) => {
            if (uni.getStorageSync("token")) {
              resolve(res?.orderCountData?.unPay || 0);
            }
          },
          errCallBack: (err) => {
            reject(err);
          },
        });
      });
    },
    /**
     *  获取各个状态下优惠券数量
     */
     couponCount() {
      const that = this;
      return new Promise((resolve) => {
        http.request({
          url: "/p/myCoupon/getMyCouponsStatusCount",
          method: "GET",
          data: {},
          callBack: function (res) {
            const unUse = res?.unUseCount || 0
            resolve(unUse);
            that.setData({
              couponNum: unUse
              })
          },                            
          errCallBack: (err) => {
            reject(err);
          },
        });
      });
    },
    async showMineTabBarBadge(){
      const unpay = await this.loadOrderCountFun();
      const couponCount = await this.couponCount();
      let showCouponCount = 0;
      let sum = 0;
      if(uni.getStorageSync("unUseCouponCount") && uni.getStorageSync("unUseCouponCount")>=couponCount){
        showCouponCount = 0
      }else{
        showCouponCount = couponCount
      }
      sum = unpay + showCouponCount;
      if(sum>0){
        uni.setTabBarBadge({
    			  index: 2,
    			  text: sum > 99 ? "99+" : sum + ""
    		  })
      }else{
        uni.removeTabBarBadge({index:2})
      }
    }
  },
  computed: {
    isLogin() {
      return this.$store.state.isLogin;
    },
    currentShop() {
      return this.$store.state?.currentShop;
    },
    wrapClassName() {
      if (!this.isLogin) {
        return this.isMini ? 'mini-bottompadding' : 'h5-bottompadding';
      }
      return '';
    }
  },
  watch: {
    currentShop() {
      if (!newCustomerZoneLocked) {
        this.goToNewCustomerZone();
      }
      if (this.isLogin && this.currentShop) {
        this.showMineTabBarBadge();
      }
    }
  }
}
</script>

<style scoped>
.mini-bottompadding {
  padding-bottom: 64px;
  box-sizing: border-box;
}

.h5-bottompadding {
  padding-bottom: 64px;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 64px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 64px);
  box-sizing: border-box;
  transition: all .3s;
}
</style>

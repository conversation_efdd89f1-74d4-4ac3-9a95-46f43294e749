/* pages/liveBroadcast/liveBroadcast.wxss */
page {
  height: 100%;
  background: #f8f8f8;
}
image {
  width: 100%;
  height: 100%;
}
.top-bg {
  display: block;
  width: 100%;
  height: 45vw;
background: url('https://m.wenet.com.cn/resources/shop-static/images/other/fa7b549e9387424a9f06664003c14c5b.png') no-repeat;
background-size:100% auto ;
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
	z-index: -1;
}
.search-bar {
  display: flex;
  box-sizing: border-box;
  padding: 30rpx 20rpx;
  align-items: center;
  justify-content: space-between;
}
.search {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 90%;
  margin: 0 auto;
  background: rgba(255,255,255,.2);
  border-radius: 60rpx;
  padding: 18rpx 20rpx;
  z-index: 1;
}
image.search-icon {
  display: block;
  width: 41rpx;
  height: 36rpx;
  opacity: .5;
}
.select {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20%;
  box-sizing: border-box;
  padding-right: 10rpx;
  z-index: 1;
}
.select .key {
  font-size: 30rpx;
  color: #fff;
  font-weight: bold;
}
.select image.arrow {
  width: 30rpx;
  height: 30rpx;
}
.search-int {
  padding: 0 14rpx;
  font-size: 28rpx;
  color: #fff;
  width: 100%;
}
.placeholder {
  color: rgba(255,255,255,.5);
  font-size: 28rpx;
}


/* 列表 */
.broadcast-list {
  display: block;
  padding: 24rpx;
  box-sizing: border-box;
  /* margin-top: 10rpx; */
}
.broadcast-list .item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 10rpx;
  background: #fff;
  width: 100%;
  margin-bottom: 15px;
}
.b-video {
  position: relative;
  display: inline-block;
  width: 45vw;
  height: 42vw;
  overflow:hidden;
}
.b-situation {
  position: absolute;
  display: block;
  top: 20rpx;
  left: 20rpx;
  color: #fff;
}
.b-processing {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: auto;
  font-size: 20rpx;
  background: #F82E4A;
  padding: 6rpx;
  padding-right: 7rpx;
  border-radius: 4rpx;
}
.dot {
  font-size: 12rpx;
  padding-right: 4rpx;
}
.people-num {
  display: inline-block;
  font-size: 20rpx;
  background: rgba(0,0,0,.4);
  padding: 5rpx 6rpx;
  border-radius: 4rpx;
}
.b-video .img-box {
  position: absolute;
  top: 0;
  left: 0;
  width:100%;
  height:100%;
  overflow:hidden;
  text-align:center;
}
.b-img {
  /* display: inline-block;
  position: absolute;
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;  */
  border-radius: 10rpx 0 0 10rpx;
}
.like {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  color: #fff;
  text-align: center;
}
.like-icon {
  display: block;
  width: 50rpx;
  height: 50rpx;
  margin: 0 auto;
}
.like-num {
  font-size: 20rpx;
}
.b-content {
  width: 55vw;
  display: inline-block;
  padding: 20rpx 10rpx;
  box-sizing: border-box;
  padding-left: 20rpx;
  padding-right: 20rpx;
  z-index: 11;
  background: #ffff;
  height: 42vw;
}
.b-tit {
  max-width: 100%;
  font-size: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.b-name {
  font-size: 28rpx;
  color: #555;
  padding: 22rpx 0;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}
.b-prod-list {
  display: flex;
  width: 100%;
  height: 15vw;
  justify-content: flex-start;
  align-items: center;
  margin-top: 5rpx;
}
.b-prod-list .b-prod {
  display: inline-block;
  width: 15vw;
  height: 15vw;
  margin-right: 12rpx;
  border-radius: 12rpx;
}
.b-prod-img image {
  display: block;
  border-radius: 12rpx;
}
.b-prod-list .b-prod.b-prod-num {
  position: relative;
  display: block;
  background: #fef4f3;
  text-align: center;
  margin-right: 0;
}
.b-num {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.prod-num {
  display: block;
  width: 100%;
  font-size: 30rpx;
  color: #e43130;
  font-weight: bold;
}
.b-more {
  display: block;
  width: 100%;
  font-size: 26rpx;
  color: rgba(228, 49, 48,.7);
}

/* 列表 end */

/* 空 */
.empty,
.loadall {
  display: block;
  font-size: 28rpx;
  color: #aaa;
  line-height: 2em;
  margin-top: 250rpx;
  text-align: center;
}
.loadall {
  margin-top: 20rpx;
  margin-bottom: 30rpx;
}


/* pages/prod-classify/prod-classify.wxss */

page {
  background: #f4f4f4;
}

.tit-background {
  width: 100%;
  height: 20rpx;
  background: #f4f4f4;
}

.coupon-condition {
  padding: 20rpx 20rpx 0;
  font-size: 24rpx;
}

/* 加载完成 */
.loadall {
  margin: 20rpx 0;
  line-height: 2em;
  font-size: 28rpx;
  color: #aaa;
  text-align: center;
}


/* 列表为空 */
.empty {
  margin-top: 100rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 2em;
}
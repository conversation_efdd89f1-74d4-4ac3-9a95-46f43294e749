<template>
	<!--pages/login/login.wxml-->
  <view class="container">
    <view class="headline">
      <view class="headline-title">WeNet商城</view>
      <view class="headline-msg">网络产品一站服务</view>
    </view>
    <view class="login-container">
      <view style="text-align: center; font-size: 36rpx;padding-bottom: 32rpx;font-weight: bold;">注册账号</view>
      <view class="con">
        <view class="login-form">
          <view :class="['item',errorTips==1 || MobileEmpty? 'error':'']">
            <!-- 手机号 -->
            <view class="account">
              <text class="input-item">+86</text>
              <input @focus="handleInpFocus" type="number" v-model="mobile" placeholder-class="inp-palcehoder" :placeholder="i18n.enterMobileNumber"
                maxlength="11"/>
            </view>
            <view class="error-text" v-if="errorTips==1"><text class="warning-icon">!</text>{{i18n.enterValidPhone}}</view>
            <view class="error-text" v-if="MobileEmpty"><text class="warning-icon">!</text>{{i18n.enterMobileNumber}}</view>
          </view>
          <view :class="['item',errorTips==2 || errorTips==6? 'error':'']">
            <!-- 验证码 -->
            <view class="account">
              <input @focus="handleInpFocus" type="text" class="int-yzm" v-model="validCode" placeholder-class="inp-palcehoder" :placeholder="i18n.enterCode" maxlength="6"
              />
              <text class="input-item" @click="getCode" v-if="show">{{i18n.getCode}}</text>
              <text class="input-item getcode" v-if="!show">{{count}} s</text>
            </view>
            <view class="error-text" v-if="errorTips==2"><text class="warning-icon">!</text>{{i18n.enterCode}}</view>
            <view class="error-text" v-if="errorTips==6"><text class="warning-icon">!</text>{{i18n.enterCodeFirst}}</view>
          </view>
        </view>
        <!-- 按钮 -->
        <NButton @click="handleClickRegister" block>注册</NButton>
        <PrivacyTip :checked="privacyChecked" @change="handlePrivacyTipChange"  />
		  </view>
    </view>
    <NLoading></NLoading>
  </view>
</template>

<script>
	import http from "@/utils/http";
	import util from "@/utils/util.js";
  import PrivacyTip from '@/components/privacy-tip'
  import { AppType } from '@/utils/constant.js'
  import {sendRegisterCode, registerAccountToWenet2, getWenetAccountInfo, cacheWenetAccountInfo, getExtendInfoListConfig, isExtendInfoAllFilled } from '@/utils/login-processor'
  import NButton from '@/components/n-button'


	export default {
		data() {
			return {
				errorTips: 0, // 输入错误提示:  1手机号输入错误  2验证码输入错误  3账号输入错误  4密码输入错误  5验证密码输入错误
				mobile: '',
				password: '',
        ou: '',
				// 验证码相关
				validCode: '',
				show: true,
				count: '',
				timer: null,
				MobileEmpty: false, // 手机号是否为空
        loading: false,
        privacyChecked: false,
			};
		},
    components: {
      PrivacyTip,
      NButton
    },
		props: {
    },

		computed: {
			i18n() {
				return this.$t('index')
			},
			appType() {
				return uni.getStorageSync('appType')
			}
		},

    onLoad(query) {
      this.ou = query.ou      
    },
		methods: {
			/**
			 * 输入框聚焦
			 */
			handleInpFocus() {
				this.errorTips = 0
			},

			/**
			 * 获取验证码
			 */
			async getCode() {
				if (!this.mobile) {
					this.MobileEmpty = true
					this.errorTips = 0
					return
				}
				if (!util.checkPhoneNumber(this.mobile)) {
					this.MobileEmpty = false
					this.errorTips = 1
					return
				}
				this.MobileEmpty = false
				this.errorTips = 0
        try {
          await sendRegisterCode(this.mobile)
          const timeCount = 60;
          if (!this.timer) {
            this.count = timeCount
            this.show = false;
            this.timer = setInterval(() => {
              if (this.count > 0 && this.count <= timeCount) {
                this.count--;
              } else {
                clearInterval(this.timer);
                this.timer = null
                this.show = true
              }
            }, 1000)
          }
        } catch (error) {
          if (error.data?.code === 4003) {
            uni.showToast({
              title: '手机号已注册',
              icon: 'none'
            })
          } else if (error.data?.code || error.data?.description) {
            uni.showToast({
              title: `${error.data?.code || ''} ${error.data?.description || ''}`,
              icon: 'none'
            })
          }
        }
        
			},

			async handleClickRegister() {
				if (!this.privacyChecked) {
					uni.showToast({
						title: '请先同意隐私策略和服务条款',
						icon: 'none'
					})
					return
				}
        if (!this.validCode || !this.mobile) {
          uni.showToast({
            title: '请输入手机号和验证码',
            icon: 'none'
          })
          return
        }
        try {
          const res = await registerAccountToWenet2({
            ou: this.ou,
            verifyCode: this.validCode,
            mobile: this.mobile,
          })
          const wenetToken = `Bearer ${res.accountToken}`
          uni.setStorageSync('wenetToken', wenetToken)
          const accountInfo = await getWenetAccountInfo();
          cacheWenetAccountInfo(accountInfo)
          const uid = accountInfo.person.uid;
          this.loginToShop({
            ou: this.ou,
            uid,
            basMobile: this.mobile,
          })
        } catch (error) {
          if (error.data?.code === 4003) {
            uni.showToast({
              title: '验证码错误或过期',
              icon: 'none'
            })
          } else if (error.data?.code || error.data?.description) {
            uni.showToast({
              title: `${error.data?.code || ''} ${error.data?.description || ''}`,
              icon: 'none'
            })
          }
        }
			},

      getWxCode() {
        const appType = uni.getStorageSync('appType')
        return new Promise((resolve) => {
          if (appType === AppType.MINI) {
            wx.login({
              success: (res) => {
                resolve(res.code)
              }
            })
          } else {
            resolve('')
          }
        })
      },

      loginToShop({ uid, ou, basMobile}) {
        this.getWxCode().then((code) => {
          http.request({
            url: '/self/login',
            methods: 'POST',
            data: {
              basUsername: basMobile,
              uid,
              ou,
              mobile: basMobile,
              code
            },
            callBack: async (res) => {
              const accountInfo = await getWenetAccountInfo();
              cacheWenetAccountInfo(accountInfo)
              const extendInfoList = getExtendInfoListConfig(ou, this.basOrgList);
              const allFilled = await isExtendInfoAllFilled(accountInfo, extendInfoList)
              if (res) {
                util.loginSuccess(res, false, {
                  needImproveExtendInfo: !allFilled.result
                })
              } else {
                // 没有绑定手机号
              }
            },
            errCallBack: (error) => {
              console.log(error);
            }
          })
        })
      },

      handlePrivacyTipChange() {
        this.privacyChecked = !this.privacyChecked
      }
		},
  }
</script>
<style lang="scss" scoped>
page {
  position: relative;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background-color: $wenet-color-brand;
}

.headline {
  width: 100%;
  height: calc(100vh - 1228rpx);
  min-height: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.headline-title {
  color: #fff;
  font-size: 48rpx;
  font-weight: 600;
  line-height: 64rpx; 
  letter-spacing: 1rpx;
}
.headline-msg {
  color: #fff;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 44rpx;
}

.login-container {
  position: absolute;
  bottom: 0rpx;
  width: 100%;
  height: 1228rpx;
  max-height: 80%;
  box-sizing: border-box;
  padding: 94rpx 48rpx;
  border-radius: 60rpx 60rpx 0rpx 0rpx;
  background-color: #fff;
}

.account{
  display: flex;
  box-sizing: border-box;
  width: 100%;
  height: 84rpx;
  padding-left: 32rpx;
  align-items: center;
  border-radius: 16rpx;
	font-size: 30rpx;
  background: #F5F5F5;
  margin-bottom: 48rpx;
}
.account input{
  padding-left: 20rpx;
  width:70%;
}
.inp-palcehoder{
  font-size: 26rpx;
}
.account input.int-yzm {
  width: 380rpx;
}
.input-item {
  font-size: 28rpx;
	color: $wenet-color-brand;
}
.input-item.getcode {
  width: 120rpx;
  text-align: right;
}

button::after{
  border: 0 !important;
}
.successful {
	display: block;
	width: 100%;
	text-align: center;
	font-size: 36rpx;
}
.item {
	display: block;
	margin-bottom: 40rpx;
}
.error .error-text {
	display: block;
	width: 100%;
	font-size: 28rpx;
	color: $wenet-color-error;
  text-align: left;
  margin-top: 10rpx;
}
.error .error-text .warning-icon {
  display: inline-block;;
  color: #fff;
  width: 26rpx;
  height: 26rpx;
  line-height: 26rpx;
  background: $wenet-color-error;
  border-radius: 50%;
  text-align: center;
  margin-right: 12rpx;
  font-size: 22rpx;
}
</style>

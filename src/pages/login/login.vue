<template>
	<!--pages/login/login.wxml-->
  <view class="container">
    <view class="headline">
      <view class="headline-title">WeNet商城</view>
      <view class="headline-msg">网络产品一站服务</view>
    </view>
    <view class="login-container">
      <template v-if="step === 1">
        <SchoolSelector v-model="selectedOrg" @change="handleSchoolSelectorChange" :basOrgList="basOrgList"/>
        <NButton @click="handleClickNext" block>下一步</NButton>
      </template>
      <template v-else>
        <tabs
          :tabs="tabs"
          :active-tab="activeTab"
          @change="changeTab"
        />
        <LoginWithWenet
          v-if="activeTab === 'STUDENT_ID'"
          :privacy-checked="privacyChecked"
          @getBasUserinfoSuccess="handleGetBasUserinfoSuccess"
          @getUserMobileSuccess="handleGetUserMobileSuccess"
          ref="loginWithWenetRef"
          :basOrgList="basOrgList"
          :selected-ou="selectedOrg.ou"
          :selected-ou-prefix="selectedOrg.telexNumber"
          :student-id-placeholder="studentIdPlaceholder"
          :password-placeholder="passwordPlaceholder"
          :show-register="showRegister"
        />
        <LoginWithMobile
          v-else
          :privacy-checked="privacyChecked"
          :basOrgList="basOrgList"
          :selected-ou="selectedOrg.ou"
          :selected-ou-prefix="selectedOrg.telexNumber"
          :student-id-placeholder="studentIdPlaceholder"
          :password-placeholder="passwordPlaceholder"
          :show-register="showRegister"
        />
        <PrivacyTip :checked="privacyChecked" @change="handlePrivacyTipChange"  />
      </template>
    </view>
    <NLoading />
    <Dialog ref="settingPasswordDialogRef" @ok="handlePasswordDialogOk">
      <view class="password-tips">为了您账号的安全，请立即设置您的密码</view>
      <NInput type="password" v-model="password" placeholder="密码" />
      <NInput type="password" v-model="cfmPassword" placeholder="确认密码" />
    </Dialog>

    <Dialog ref="forceAppendMobileDialogRef" @ok="forceAppendMobileDialogOk">
      <view >该手机号已和学号<text style="font-weight: bold;">{{ oldStudentId }}</text>绑定,是否绑定到学号<text style="font-weight: bold;">{{currentStutendId}}</text></view>
    </Dialog>
  </view>
</template>

<script>
  import PrivacyTip from '@/components/privacy-tip'
  import http from '@/utils/http';
  import util from '@/utils/util';
  import Tabs from '@/components/tabs/tabs'
  import LoginWithWenet from '@/components/login-with-wenet';
  import LoginWithMobile from '@/components/login-with-mobile';
  import AlreadyBindTip from '@/components/wenet-account-tip/already-bind-tip';
  import { AppType } from '@/utils/constant.js'
  import {appendMobileToWenet, getWenetAccountInfo, cacheWenetAccountInfo, getExtendInfoListConfig, isExtendInfoAllFilled, updateWenetPassword, getWenetAnotherAccount} from '@/utils/login-processor'
  import Dialog from '@/components/popup/dialog'
  import config from '@/utils/config.js'
  import NInput from '@/components/form/n-input'
  import NButton from '@/components/n-button'
  import SchoolSelector from '@/components/login-with-wenet/SchoolSelector'
  import NLoading from '@/components/n-loading/n-loading'

	export default {
		data() {
			return {
        step: 1,
				uniLoginLogoImg: '', // Logo
        loading: false,
        hasWenetAccount: false,
        basOrgList: [],
        functionQueue: [],
        password: '',
        cfmPassword: '',
        privacyChecked: false, // 是否同意了隐私协议
        tabs: [
          { key: 'STUDENT_ID', label: '学号登录' },
          { key: 'TELEPHONE', label: '手机号登录'},
        ],
        activeTab: 'STUDENT_ID',
        studentIdPlaceholder: '学号',
        passwordPlaceholder: '密码',
        selectedOrg: {},
        showRegister: false,
        currentStutendId: '',
        oldStudentId: '',
			};
		},

		components: {
      LoginWithWenet,
      LoginWithMobile,
      AlreadyBindTip,
      Dialog,
      NInput,
      NButton,
      Tabs,
      PrivacyTip,
      SchoolSelector,
      NLoading
    },
		props: {},
		computed:{
			i18n() {
				return this.$t('index')
			}
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			var uniLoginLogoImg = uni.getStorageSync("uniLoginLogoImg");
			if (uniLoginLogoImg) {
				this.uniLoginLogoImg = uniLoginLogoImg
			} else {
				// 获取uni-app相关配置
				this.getUniWebConfig()
			}
      this.getBasOrgList();
		},
    mounted() {
      // this.$refs.settingPasswordDialogRef.show();
    },

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			uni.setNavigationBarTitle({
				title: 'WeNet'
			});
		},
		methods: {
      handleClickNext() {
        if (!this.selectedOrg.ou) {
          uni.showToast({
            title: '请选择学校',
            icon: 'none'
          })
        } else {
          this.step += 1
        }
      },
      handleSchoolSelectorChange(school) {
        const studentIdPlaceholder = school.postalAddress || '学号'
        const passwordPlaceholder = school.postalCode || '密码'
        this.studentIdPlaceholder = studentIdPlaceholder
        this.passwordPlaceholder = passwordPlaceholder
        this.showRegister = school.registration?.includes('app')
      },
      handlePrivacyTipChange(newValue) {
        this.privacyChecked = newValue
      },
      changeTab(key) {
        this.activeTab = key;
      },
      getBasOrgList() {
        var params = {
          url: "/v1/projection/org/visible",
          domain: config.wenetUrl, 
          method: "GET",
          callBack: (res) => {
            this.basOrgList = res
          }
        };
        http.request(params);
      },
      handlePasswordDialogOk() {
        if (this.checkPassowrdForm()) {
          updateWenetPassword(this.password, this.cfmPassword).then(() => {
            if (this.functionQueue.length) {
              // 执行方法队列中的方法
              this.functionQueue.forEach(fn => fn());
              this.functionQueue = [];
            }
          })
        }
        
      },
      loginToShop({basUsername, uid, ou, basMobile}) {
        this.getWxCode().then((code) => {
          http.request({
            url: '/self/login',
            methods: 'POST',
            data: {
              basUsername,
              uid,
              ou,
              mobile: basMobile,
              code
            },
            callBack: async (res) => {
              const accountInfo = await getWenetAccountInfo();
              cacheWenetAccountInfo(accountInfo)
              const homePostalAddress = accountInfo?.person?.homePostalAddress;
              const extendInfoList = getExtendInfoListConfig(ou, this.basOrgList);
              const allFilled = await isExtendInfoAllFilled(accountInfo, extendInfoList)
              // 暂时去掉强制修改
              // if (String(homePostalAddress) === '0') {
              //   this.$refs.settingPasswordDialogRef.show();
              //   this.functionQueue.push(() => {
              //     util.loginSuccess(res, false, {
              //       needImproveExtendInfo: !allFilled.result
              //     })
              //   })
              //   return
              // }
              if (res) {
                util.loginSuccess(res, false, {
                  needImproveExtendInfo: !allFilled.result
                })
              } else {
                // 没有绑定手机号
              }
            },
            errCallBack: () => {
            }
          })
        })
      },

      handleGetBasUserinfoSuccess(wenetAccountInfo) {
        if (!wenetAccountInfo) {
          return
        }
        const { basUsername, uid, ou, basMobile} = wenetAccountInfo
        if (!basMobile) {
          return
        }
        this.loginToShop({basUsername, uid, ou, basMobile});
      },

      handleGetUserMobileSuccess(wenetAccountInfo) {
        const { basUsername, uid, ou, basMobile} = wenetAccountInfo
        //这里的username是手机号
        appendMobileToWenet({ uid, username: basMobile})
        .then(() => {
          this.loginToShop({basUsername, uid, ou, basMobile});  
        })
        .catch(async (err) => {
          const res = await getWenetAnotherAccount({ mobile: basMobile })
          this.oldStudentId = res.username
          this.currentStutendId = basUsername
          if (!res.username) {
            uni.showToast({
              title: '手机号已被占用，请联系客服',
              icon: 'none',
              duration: 5000,
            })
            return
          }
          if (err.data.code === 1023) {
            // 弹窗提示用户，是否强制绑定
            this.$refs.forceAppendMobileDialogRef.show({
              basUsername, uid, ou, basMobile
            })
            // uni.showToast({
            //   title: '手机号已被使用',
            //   icon: 'none'
            // })
          }
        })
      },
      forceAppendMobileDialogOk(context) {
        const {basUsername, uid, ou, basMobile} = context
        appendMobileToWenet({ uid, username: basMobile, force: true}).then(() => {
          this.loginToShop({basUsername, uid, ou, basMobile});  
        })
      },

      getWxCode() {
        const appType = uni.getStorageSync('appType')
        return new Promise((resolve) => {
          if (appType === AppType.MINI) {
            wx.login({
              success: (res) => {
                resolve(res.code)
              }
            })
          } else {
            resolve('')
          }
        })
      },

			/**
			 * 获取uni-app相关配置
			 */
			getUniWebConfig: function() {
				var params = {
					url: "/webConfig/getUniWebConfig",
					method: "GET",
					data: {},
					callBack: res => {
						this.setData({
							uniLoginLogoImg: res.uniLoginLogoImg
						});
						uni.setStorageSync("uniLoginLogoImg",this.uniLoginLogoImg)
					}
				};
				http.request(params);
			},
      checkPassowrdForm() {
      // 检查password 和 cfmpassowrd
        if (!this.password || !this.cfmPassword) {
          uni.showToast({
            title: '请填写完整信息',
            icon: 'none'
          })
          return false
        }
        if (this.password !== this.cfmPassword) {
          uni.showToast({
            title: '两次密码不一致',
            icon: 'none'
          })
          return false
        }
        return true;
      }
		}
	};
</script>
<style lang="scss" scoped>
page {
  position: relative;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background-color: $wenet-color-brand;
}

.headline {
  width: 100%;
  height: calc(100vh - 1228rpx);
  min-height: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.headline-title {
  color: #fff;
  font-size: 48rpx;
  font-weight: 600;
  line-height: 64rpx; 
  letter-spacing: 1rpx;
}
.headline-msg {
  color: #fff;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 44rpx;
}

.login-container {
  position: absolute;
  bottom: 0rpx;
  width: 100%;
  height: 1228rpx;
  max-height: 80%;
  box-sizing: border-box;
  padding: 94rpx 48rpx;
  border-radius: 60rpx 60rpx 0rpx 0rpx;
  background-color: #fff;
}
.content {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
  padding: 48rpx 0;
}


.password-tips {
  text-align: center;
  font-size: 28rpx;
  width: 100%;
  padding: 24rpx;
  box-sizing: border-box;
}
</style>

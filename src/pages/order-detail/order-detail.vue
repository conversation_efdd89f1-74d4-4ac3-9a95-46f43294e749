<template>
	<!--pages/order-detail/order-detail.wxml-->

	<view class="container">
    <view class="order-detail">
			<!-- 订单状态 -->
			<view class="order-status">
				<view class="status-bg">
					<!-- <image :src="`${staticPicDomain}images/icon/status-bg.png`"></image> -->
				</view>
				<view class="status-text">{{['',i18n.waitingBuyerPay,i18n.waitingForDelivery,i18n.waitingBuyerReceipt,i18n.orderCompleted,i18n.orderCompleted,i18n.orderCancelled,i18n.itInaGroup][status]}}</view>
	
			</view>

			<!-- 配送地址(虚拟商品不展示) -->
			<view class="address-box" v-if="dvyType !== 2 && userAddrDto && orderMold !== 1">
				<view class="user-info">
					<view class="img">
						<image :src="`${staticPicDomain}images/icon/addr.png`"></image>
					</view>
					<view class="name">{{userAddrDto.receiver}}</view>
					<view class="phone">{{userAddrDto.mobile}}</view>
				</view>
				<view class="address-detail">{{userAddrDto.province}}{{userAddrDto.city}}{{userAddrDto.area}}{{userAddrDto.addr}}</view>
			</view>

			<!-- 自提地址(虚拟商品不展示) -->
			<view class="delivery-certificate" v-if="dvyType == 2">
				<view class="tit"><text v-if="status == 2 ">{{i18n.deliveryVoucher}}</text><text v-if="status > 2">{{i18n.pickup}}</text></view>
				<view class="address">
					<view class="text">{{stationAddress}}</view>
					<view class="icon-box">
						<view class="icon" @tap="callStation">
							<image :src="`${staticPicDomain}images/icon/phone.png`"></image>
						</view>
						<view class="icon bl" @tap="openMap">
							<image :src="`${staticPicDomain}images/icon/addr.png`"></image>
						</view>
					</view>
				</view>
				<!-- 没付款时 -->
				<view class="code-box no-code" v-if="status == 1">
					<view class="text-box">
						<view class="item">
							<view class="i-lable">{{i18n.pickingCode}}：</view>
							<view class="i-text fw">{{stationCode?stationCode:i18n.deliveryCodeTips}}</view>
						</view>
						<view class="item">
							<view class="i-lable">{{i18n.picker}}：</view>
							<view class="i-text">
								<text>{{stationUserName}}</text>
								<text>{{stationUserMobile}}</text>
							</view>
						</view>
					</view>
				</view>
				<!-- 已付款时 -->
				<view class="code-box code-det" @tap="viewCertificate" v-if="status == 2">
					<view class="code">
						<image :src="stationQrCode"></image>
						<!-- <canvas class="qrcode-img" canvas-id="orderQrcode"></canvas> -->
					</view>
					<view class="text-box">
						<view class="item">
							<view class="i-lable">{{i18n.pickingCode}}：</view>
							<view class="i-text fw">{{stationCode}}</view>
						</view>
						<view class="item">
							<view class="i-lable">{{i18n.picker}}：</view>
							<view class="i-text">
								<text>{{stationUserName}}</text>
								<text>{{stationUserMobile}}</text>
							</view>
						</view>
						<view class="item">
							<view class="i-lable">{{i18n.appointmentTime}}：</view>
							<view class="i-text">{{stationTime}}</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 商品信息 -->
			<view class="prod-item">
				<!-- 店铺 -->
				<view class="shop-box">
					<view class="shop-icon">
						<image :src="`${staticPicDomain}images/icon/shop.png`"></image>
					</view>
					<view class="shop-name">{{shopName}}</view>
				</view>
				<!-- /店铺 -->
				<view v-for="(item, prodId) in orderItemDtos" :key="prodId" class="prod-box">
					<view class="item-cont" @tap="toProdPage(item.prodId)">
						<view class="prod-pic">
							<image :src="item.pic"></image>
						</view>
						<!-- 拼团商品展示icon -->
						<view class="prod-info">
							<view class="prodname">
								<view class="text">{{item.prodName}}</view>
							</view>
							<view class="suk-con">
								<view class="a-icon" v-if="orderType && orderMold !== 1">{{['',i18n.aGroup,i18n.spike,i18n.integral][orderType]}}</view>
								<view class="sku-name">{{item.skuName || ''}}</view>
							</view>
              <!-- 价格 -->
							<view class="price-nums">
								<view class="prodprice"><text class="symbol">￥</text>
									<text class="big-num">{{parsePrice(item.price)[0]}}</text>
									<text class="small-num">.{{parsePrice(item.price)[1]}}</text>
									<text class="small-num" v-if="orderType==3">{{`&nbsp;+&nbsp;`}}</text>
									<text class="small-num" v-if="orderType==3">{{item.useScore / item.prodCount}} {{i18n.integral}}</text>
								</view>
								<view class="number">{{item.prodCount}}{{i18n.piece}}</view>
							</view>
						</view>
						<!--
              申请退款：
                1、orderMold==1虚拟商品(支持买家申请退款时)
                1.1 需要核销且还有未核销的券码
                1.2 无需核销
            -->
						<view
              v-if="!item.refundSn && canRefund && orderType <1 &&
                    item.actualTotal &&
                    (orderMold !=1 ||
                    (orderMold == 1 && ((writeOffNum != 0 && unusedCount) || writeOffNum == 0)))"
              class="apply-refund-btn"
              @tap.stop="applyRefund(item,2)"
            >{{i18n.requestRefund}}</view>
						<view class="apply-refund-btn" @tap.stop="viewRefund" :data-refundsn="item.refundSn" v-if="item.refundSn && item.returnMoneySts != -1">{{i18n.checkRefund}}</view>
						<!-- 申请退款btn -->
					</view>
          <!-- 赠品：多个商品时展示 -->
          <view v-if="orderItemDtos.length > 1 && item.giveawayList && item.giveawayList.length" class="gift-prods">
            <view
              v-for="giftItem in item.giveawayList"
              :key="giftItem.orderItemId"
              class="gift-item"
               @tap="toProdDetail(giftItem.prodId)"
            >
              <text class="name">{{'【' + i18n.gift + '】'}}{{ giftItem.prodName }}</text>
              <text class="num">×{{ giftItem.prodCount }}</text>
            </view>
          </view>

          <!-- 实物商品留言 -->
          <view v-if="orderMold !== 1">
            <view class="physical-remark-list">
              <template v-for="(remarkItem) in virtualRemark">
                <view class="physical-remark-item" v-if="remarkItem.prodId === item.prodId">
                  <view class="item-con weak">{{remarkItem.name}}</view>
                  <view class="item-con">{{remarkItem.value}}</view>
                </view>
              </template>
            </view>
          </view>
				</view>
        <!-- 赠品：单个商品时展示 -->
        <block v-if="orderItemDtos.length === 1 && orderItemDtos[0].giveawayList && orderItemDtos[0].giveawayList.length">
          <view v-for="(item, prodId) in orderItemDtos[0].giveawayList" :key="item.orderItemId" class="prod-box giveaway-list">
            <view class="item-cont" @tap="toProdPage(item.prodId)">
              <view class="prod-pic">
                <image :src="item.pic.indexOf('http') === -1 ? picDomain + item.pic : item.pic"></image>
              </view>
              <view class="prod-info">
                <view class="prodname">
                  <view class="text">{{item.prodName}}</view>
                </view>
                <view class="suk-con">
                  <view class="a-icon" v-if="orderType && orderMold !== 1">{{['',i18n.aGroup,i18n.spike,i18n.integral][orderType]}}</view>
                  <view class="sku-name">
                    <text class="gift-icon">{{i18n.gift}}</text>
                    <text>{{item.skuName || ''}}</text>
                  </view>
                </view>
                <view class="price-nums">
                  <view v-if="!item.giveawayOrderItemId" class="prodprice"><text class="symbol">￥</text>
                    <text class="big-num">{{parsePrice(item.price)[0]}}</text>
                    <text class="small-num">.{{parsePrice(item.price)[1]}}</text>
                    <text v-if="orderType==3" class="small-num">+</text>
                    <text class="small-num" v-if="orderType==3">{{item.useScore}} {{i18n.integral}}</text>
                  </view>
                  <view v-if="item.giveawayOrderItemId" class="prodprice"><text class="symbol">￥</text>
                    <text class="big-num">{{parsePrice(item.giveawayAmount)[0]}}</text>
                    <text class="small-num">.{{parsePrice(item.giveawayAmount)[1]}}</text>
                  </view>
                  <view class="number">{{item.prodCount}}{{i18n.piece}}</view>
                </view>
              </view>
            </view>
          </view>
        </block>
        <!-- / 赠品 -->
			</view>


			<!-- 订单信息 -->
			<view class="order-msg">
				<view class="msg-item">
					<view class="item">
						<text class="item-tit">{{i18n.orderNumber}}：</text>
						<view class="item-txt" style="display: flex;column-gap: 32rpx;">
              <click-to-copy :copy-text="orderNumber" @success="handleOrderNumberCopied">
                {{orderNumber}}
              </click-to-copy>
            </view>
						<!--  #ifdef  APP-PLUS -->
						<text class="copy-btn" @tap="copyBtn">{{i18n.copy}}</text>
						<!--  #endif -->
					</view>
					<view class="item" v-for="(outerShipItem, index) in outerShipInfos" :key="index">
            <template v-if="outerShipItem.doubleCheck">
              <text class="item-tit">激活码：</text>
              <view class="item-txt">
                <click-to-copy :copy-text="outerShipItem.saleCode" @success="handleActiveCodeCopied">
                  {{outerShipItem.saleCode}}
                </click-to-copy>
              </view>
              <!--  #ifdef  APP-PLUS -->
              <text class="copy-btn" @tap="copyBtn">{{i18n.copy}}</text>
              <!--  #endif -->
            </template>
					</view>
					<view class="item">
						<text class="item-tit">{{i18n.orderTime}}：</text>
						<text class="item-txt">{{createTime}}</text>
					</view>
				</view>
				<view class="msg-item">
          <!-- 支付方式 -->
					<view v-if="status>1&&status!=6" class="item">
						<text class="item-tit">{{i18n.paymentMethod}}：</text>
						<text class="item-txt">{{[i18n.integralPayment,i18n.payWithWeChat,i18n.PayWithAli,i18n.payWithWeChat,i18n.payWithWeChat,i18n.payWithWeChat,i18n.PayWithAli,i18n.PayWithAli,i18n.payWithWeChat,i18n.balancePay,i18n.paypalPay][payType]}}</text>
					</view>
          <!-- 配送方式（虚拟商品不展示） -->
					<view v-if="orderMold !== 1" class="item">
						<text class="item-tit">{{i18n.deliveryMethod}}：</text>
						<!-- 1卖家配送 2到店自提 3无需快递 4同城配送 -->
						<text class="item-txt">{{['',i18n.sellerDelivery,i18n.pickStore,i18n.noNeedDelivery,i18n.sameDelivery][dvyType]}}</text>
					</view>
          <!-- 全部留言：虚拟商品 && 留言不为空时显示 -->
          <view v-if="orderMold===1 && virtualRemarkList" class="item all-msg" @tap="showViewMsgPopup">
						<view class="item-tit">{{i18n.allMsg}}：</view>
						<view class="item-txt" :class="{'pd': isExtraLong}">{{virtualRemarkList}}</view>
            <view v-if="isExtraLong" class="more-msg"></view>
					</view>
				</view>
        <!-- 订单备注 -->
				<view class="msg-item" v-if="remarks">
					<view class="item">
						<text class="item-tit">{{i18n.OrderNotes}}：</text>
						<text class="item-txt">{{remarks?remarks:''}}</text>
					</view>
				</view>
			</view>

      <!--
        虚拟商品-券码列表显隐：
        1、订单状态为status==1“待支付” 时隐藏
        2、订单状态为status==6“已取消”时
          2.1 含有退款编号（即退款成功）时显示
          2.2 不含退款编号（即取消支付）时隐藏
        3、订单状态为status==7“拼团中” 时隐藏
        4、writeOffNum!==0 需要核销的虚拟商品 显示
      -->
      <view
        v-if="orderMold === 1 &&
              status != 1 &&
              status != 7 &&
              ((status == 6 && orderItemDtos[0].refundSn) || status != 6) &&
              writeOffNum !== 0"
        class="order-msg voucher-code-con"
      >
        <view class="msg-item">
          <view class="item" @tap="toViewVoucherCodePage">
						<view class="item-left">
              <!-- 待使用/未使用 (unusedCount待使用的核销券) -->
              <!-- 暂时不显示：“待使用(xx张)” -->
              <!-- <view
                v-if="unusedCount && status != 6"
                class="txt strong"
              >{{ (isExpired?i18n.expired:i18n.toBeUsed) + '（' + unusedCount + i18n.sheets + '）' }}</view> -->
              <!-- 确认收货(已完成) & 没有待核销的券码 -->
              <!-- <view
                v-if="status == 5 && !unusedCount"
                class="txt strong"
              >{{ i18n.used + '（' + orderItemDtos[0].prodCount + i18n.sheets + '）' }}</view> -->
              <view class="txt strong">
                核销码
              </view>
              <!-- 退款成功 -->
              <view
                v-if="status == 6 && orderItemDtos[0].refundSn"
                class="txt strong"
              >{{ i18n.used + '（' +  (orderItemDtos[0].prodCount - unusedCount) + i18n.sheets + '）' }}</view>
              <!-- 有效期限 -->
              <view class="txt weak flex-item">
                <view class="lf">{{i18n.expirationDate}}：</view>
                <view class="rg">
                  <text v-if="writeOffStart && !writeOffEnd">{{i18n.longTermValidity}}</text>
                  <text v-if="writeOffStart && writeOffEnd">{{writeOffStart + i18n.to + writeOffEnd}}</text>
                </view>
              </view>
              <!-- 核销次数 -->
              <!-- 暂时不展示 -->
              <!-- <view class="txt weak flex-item">
                <view class="lf">{{i18n.numberOfWriteOffs}}：</view>
                <view class="rg">
                  <text v-if="writeOffNum === -1">{{i18n.multipleWriteOffs}}</text>
                  <text v-if="writeOffNum === 1">{{i18n.singleWriteOffs}}</text>
                  <text v-if="writeOffNum === 0">{{i18n.noWriteOffRequired}}</text>
                </view>
              </view> -->
            </view>
            <!-- 核销码条码/二维码： 非取消状态 && 仍有待使用的核销券码时显示icon -->
						<view v-if="status != 6 && unusedCount" class="item-right">
              <image :src="`${staticPicDomain}images/icon/write-off-code.png`"></image>
            </view>
					</view>
        </view>
        <!-- 券码列表 -->
        <!-- <view v-if="virtualInfoList && virtualInfoList.length" class="msg-item voucher-code-list">
          <view
            v-for="(item, index) in virtualInfoList"
            :key="index"
            class="item"
            :class="{'used': item.isWriteOff === 1 || status == 6}"
          >
            <view class="code-row">
              <text>{{i18n.voucherCode}}</text>
              <text v-if="virtualInfoList.length > 1">{{index+1}}</text>：
              <text class="code">{{item.writeOffCode}}
            </text>
            </view>
            <view v-if="item.isWriteOff === 1" class="tips">{{i18n.used}}</view>
            <view v-if="item.isWriteOff === 0 && status == 6 && orderItemDtos[0].refundSn" class="tips">{{i18n.refunded}}</view>
            <view v-if="item.isWriteOff === 0 && status != 6" class="copy-btn2" @tap="copyText(item.writeOffCode)">{{i18n.copy}}</view>
          </view>
        </view> -->
      </view>

      <!-- 商品总额与优惠明细 -->
			<view class="order-msg payment">
				<view class="msg-item">
          <!-- 商品总额 -->
					<view class="item">
						<view class="item-tit">{{i18n.comTotal}}：</view>
						<view class="item-txt">￥{{parsePrice(productTotalAmount)[0]}}.{{parsePrice(productTotalAmount)[1]}}</view>
					</view>
          <!-- 商品运费(虚拟商品不展示) -->
					<view v-if="orderMold !== 1" class="item">
						<view class="item-tit">{{i18n.prodTransfee}}：</view>
						<view class="item-txt">￥{{parsePrice(transfee)[0]}}.{{parsePrice(transfee)[1]}}</view>
					</view>
          <!-- 运费减免 -->
          <view v-if="freeTransfee" class="item">
            <view class="item-tit">{{i18n.shippingDiscount}}：</view>
            <view class="item-txt gray">-￥{{ parsePrice(freeTransfee)[0] }}.{{ parsePrice(freeTransfee)[1] }}</view>
          </view>
          <!-- 平台优惠券 -->
          <view v-if="platformCouponAmount" class="item">
            <view class="item-tit">{{i18n.platformCoupons}}：</view>
            <view class="item-txt gray">-￥{{ parsePrice(platformCouponAmount)[0] }}.{{ parsePrice(platformCouponAmount)[1] }}</view>
          </view>
          <!-- 积分抵扣 -->
          <view v-if="scoreAmount" class="item">
            <view class="item-tit">{{i18n.pointsDeduction}}：</view>
            <view class="item-txt gray">-￥{{ parsePrice(scoreAmount)[0] }}.{{ parsePrice(scoreAmount)[1] }}</view>
          </view>
          <!-- 会员折扣 -->
          <view v-if="memberAmount" class="item">
            <view class="item-tit">{{i18n.memberDiscountAmount}}：</view>
            <view class="item-txt gray">-￥{{ parsePrice(memberAmount)[0] }}.{{ parsePrice(memberAmount)[1] }}</view>
          </view>
          <!-- 店铺优惠券 -->
          <view v-if="shopCouponMoney" class="item">
            <view class="item-tit">{{i18n.storeCoupons}}：</view>
            <view class="item-txt gray">-￥{{ parsePrice(shopCouponMoney)[0] }}.{{ parsePrice(shopCouponMoney)[1] }}</view>
          </view>
          <!-- 促销满减 -->
          <view v-if="discountMoney" class="item">
            <view class="item-tit">{{i18n.promotionOffer}}：</view>
            <view class="item-txt gray">-￥{{ parsePrice(discountMoney)[0] }}.{{ parsePrice(discountMoney)[1] }}</view>
          </view>
          <!-- 套餐优惠 -->
          <view v-if="shopComboAmount" class="item">
            <view class="item-tit">{{i18n.packageOffer}}：</view>
            <view class="item-txt gray">-￥{{ parsePrice(shopComboAmount)[0] }}.{{ parsePrice(shopComboAmount)[1] }}</view>
          </view>
          <!-- 团购/秒杀优惠 -->
          <view v-if="orderType == 1 || orderType == 2 && shopAmount" class="item">
            <view class="item-tit">{{ orderType === 1 ? i18n.aGroup : i18n.spike }}{{i18n.preferential}}：</view>
            <view class="item-txt ">-￥{{ parsePrice(shopAmount)[0] }}.{{ parsePrice(shopAmount)[1] }}</view>
          </view>
          <!-- 商家改价(只允许减价) -->
          <view v-if="shopChangeFreeAmount" class="item">
            <view class="item-tit">{{i18n.merchantsModifyPrices}}：</view>
            <view class="item-txt "> -￥{{ parsePrice(Math.abs(shopChangeFreeAmount))[0] }}.{{ parsePrice(Math.abs(shopChangeFreeAmount))[1] }}</view>
          </view>
          <!-- 订单总额 -->
					<view class="item price">
						<view class="item-txt">
							<text class="gray">{{i18n.orderTotal}}：</text>
							<text class="symbol" >￥</text>
							<text class="big-num" >{{parsePrice(actualTotal)[0]}}</text>
							<text class="small-num">.{{parsePrice(actualTotal)[1]}}</text>
							<text class="small-num" v-if="orderType==3">+</text>
							<text class="big-num" v-if="orderType==3">{{orderScore}}<text class="small-num">{{i18n.integral}}</text></text>
						</view>
					</view>
				</view>
			</view>
      <!-- 商品总额与优惠明细 end -->

			<!-- 底部栏 -->
      <!-- v-if="canAllRefund && orderType!=3" -->
    </view>
		<view class="order-detail-footer" >
			<view style="display: flex;align-items: center;"><view class="more-text" v-if="isShowMore" @tap="handleMore">{{i18n.more}}</view></view>
			<view class="footer-btn-box">
				<view v-if="!isShowMore && shopCustomerSwitch" class="group-det" @tap="handleCustomService">{{i18n.contactCustomerService1}}</view>
				<view
				class="group-det"
				@tap="viewIinvoice(orderNumber,orderInvoiceId)"
				v-if="isViewInvoice&&!isShowMore"
				>{{ i18n.invoice.viewInvoice }}</view>
				<!-- <view
				class="group-det"
				@tap="showInvoicePopup(shopId ,orderNumber)"
				v-if="isInvoice&&!isShowMore">{{ i18n.invoice.InvoicingRequest }}
        </view> -->
        <!-- 申请退款 -->
				<view
          v-if="canAllRefund && orderType!=3 && orderMold !=1 && (actualTotal > 0 || orderScore > 0)"
          class="refund-full blue-full"
          data-refundtype="1"
          @tap.stop="applyRefund(null,1)"
        >
          <text v-if="orderMold !=1">{{orderType < 1 ? i18n.wholeOrderRefund : i18n.requestRefund}}</text>
          <text v-if="orderMold == 1">{{ i18n.requestRefund }}</text>
        </view>
				<view  v-if="orderType==1&& status>1 && status !== 6" class="group-det" @tap="toGroupDetails" :data-ordernumber="orderNumber">{{i18n.viewGroupDetails}}</view>
			</view>
		</view>
		<view v-if="showMore" class="popup" :class="{ show: showMore }">
		  <view class="more-popup-mask" @tap="closePopup" />
		  <view class="more-box">
			<!-- <view class="more-box-mark" /> -->
			<view class="more-popup-text" v-if="shopCustomerSwitch" @tap="handleCustomService">{{i18n.contactCustomerService1}}</view>
			<view
				class="more-popup-text"
				@tap="viewIinvoice(orderNumber,orderInvoiceId)"
				v-if="isViewInvoice"
				>{{ i18n.invoice.viewInvoice }}</view>
				<!-- <view
				class="more-popup-text"
				@tap="showInvoicePopup(shopId ,orderNumber)"
				v-if="isInvoice">{{ i18n.invoice.InvoicingRequest }}</view> -->
		  </view>
		</view>
		<invoiceEdit v-if="isShowInvoicePopup" :shop-id="invoiceShopId" :order-number="invoiceOrderNumber" @closePopup="closePopup" @getData="getOrderDataList" />


    <!-- 查看留言弹窗（虚拟商品） -->
		<view class="popup-hide" :hidden="!showViewMsg">
			<view class="popup-box virtual-goods-msg-pop">
				<view class="con-tit">
					<view class="tit-text">{{i18n.viewMsg}}</view>
					<view class="close" @tap="closeMsgPopup"></view>
				</view>
				<view class="msg-pop-con">
					<view class="msg-list">
						<view v-for="(item, index) in virtualRemark" :key="index" class="msg-item">
							<view class="item-con weak">{{item.name}}</view>
							<view class="item-con">{{item.value}}</view>
						</view>
					</view>
					<view class="pop-foot">
						<view class="foot-btn" @tap="closeMsgPopup">{{i18n.gotIt}}</view>
					</view>
				</view>
			</view>
		</view>

    <CopySuccessTip ref="copySuccessTipRef" />

  </view>
</template>

<script>
	// pages/order-detail/order-detail.js
	var http = require("../../utils/http.js");
	var Qr = require("../../utils/wxqrcode");
	var util = require("../../utils/util.js");
	import invoiceEdit from '../../components/invoiceEdit/index'
	// var qrCode = require("../../utils/weapp-qrcode.js")
  import config from "../../utils/config.js";
  import ClickToCopy from '@/components/click-to-copy';
  import CopySuccessTip from '@/components/click-to-copy/copy-success-tip';

export default {
  data () {
    return {
      //图片地址
      picDomain: config.picDomain,
      orderItemDtos: [],
      remarks: "",
      actualTotal: 0,
      userAddrDto: null,
      shopId: "", //店铺id
      orderNumber: "",
      createTime: "",
      // 订单状态status 1:待付款 2:待发货 3:待收货 4:待评价 5:成功 6:失败 7:待成团
      status: 0,
      productTotalAmount: '', // 商品总额
      transfee: '', // 运费
			freeTransfee: '', // 运费减免
      reduceAmount: '', // 总优惠
      prodid: '',
      orderType: 0,
      shopName: '', //店铺名称
      canRefund: false, // 能否退款
      canAllRefund: false, // 能否整单退款
      isLastProd: false, //最后一款商品
      irrevocable: false, //不可撤销
      sum: [], //本单已申请单个退款的商品数组
      dvyType: 1, //配送类型 1:快递 2:自提 3：无需快递

      id: 0, // 自提信息id
      stationAddress: '', // 自提点的地址
      stationCode: '', // 提货码
      stationId: 0, // 自提点id
      stationPhone: '', // 自提点的联系电话
      stationTime: '', // 自提时间(用户下单时选择)
      stationUserMobile: '', // 自提人的手机
      stationUserName: '', // 自提人的名字
      stationQrCode: '', // 提货码二维码
      stationName: '', // 自提点名称

      payType: '', //订单支付类型
      orderScore: 0, // 整单使用积分
      lat:'', //经度
      lng:'', //纬度
	    // 当前订单可退金额
	    canRefundAmount: 0,
	    seckillId: '',orderInvoiceId: '',
	    invoiceShopId: '',
	    isShowInvoicePopup: false,
	    invoiceOrderNumber: '', // 当前选择发票的id
	    refundStatus: null,

      // 优惠明细
      platformCouponAmount: 0, // 平台优惠券优惠金额
      scoreAmount: 0, // 积分抵扣
      memberAmount: 0, // 会员折扣
      shopCouponMoney: 0, // 店铺优惠券
      discountMoney: 0, // 促销满减
      shopComboAmount: 0, // 套餐优惠
      shopAmount: 0, // 店铺优惠总额(包括秒杀/团购优惠)
      shopChangeFreeAmount: 0, // 商家改价
	    showMore: false,
      // 虚拟商品
      orderMold: null,  // 订单类型 1虚拟商品
      virtualRemark: [], // 留言
      virtualRemarkList: '',
      virtualInfoList: [], // 券码
      writeOffNum: 0,
      writeOffStart: null,
      writeOffEnd: null,
      isExpired: null, //判断有无超过有效时间
      unusedCount: 0, // 待使用的核销券

      // 虚拟商品留言是否超长
      isExtraLong: false,
      // 查看全部留言弹窗
      showViewMsg: false,
      outerShipInfos: [],
    };
  },

  components: { invoiceEdit, ClickToCopy, CopySuccessTip },
  props: {},
  computed: {
    i18n () {
      return this.$t('index')
    },
    isViewInvoice() {
      return ((this.orderInvoiceId && this.status !== 6) ||
        (this.orderInvoiceId && (this.status === 6 && this.refundStatus))) && this.status !== 1 && this.orderType !== 3
    },
    isInvoice() {
      return !this.orderInvoiceId && this.status !== 6 && this.status !== 1 && this.orderType !== 3
    },
    isShowMore() {
      return (this.isViewInvoice || this.isInvoice) && this.canAllRefund && this.orderType!=3 && this.orderType==1 && this.status>1 && this.status !== 6
    },
    shopCustomerSwitch() {
        return this.$store.state.shopCustomerSwitch
      }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
		if (options.orderNum) {
			this.orderNumber = options.orderNum
		}
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () { },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 加载导航标题
    uni.setNavigationBarTitle({
      title: this.i18n.orderDetails
    });
    this.loadOrderDetail(this.orderNumber)  //请求订单详情数据

    this.setData({
      isLastProd: false
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () { },
  methods: {
    handleActiveCodeCopied() {
      this.$refs.copySuccessTipRef.showPopup({
        title: '激活码已复制',
        desc: '前往wenet兑换套餐',
      })
    },
    handleOrderNumberCopied() {
      uni.showToast({
        title: '订单编号已复制',
        icon: 'success',
      })
    },
	  handleMore() {
		this.showMore = true
	  },
	  showInvoicePopup (shopId,invoiceOrderNumber) {
	    this.invoiceShopId = shopId
	    this.invoiceOrderNumber = invoiceOrderNumber
	    this.isShowInvoicePopup = true
		this.showMore = false
	  },
	  // 关闭弹窗
	  closePopup() {
	    this.isShowInvoicePopup = false
		this.showMore = false
	  },
	  getOrderDataList() {
	  	this.loadOrderDetail(this.orderNumber);
	  },
	  // 查看发票
	  viewIinvoice (orderNumber, orderInvoiceId) {
	  	uni.navigateTo({
	  		url: `/packageUser/pages/invoice-detail/invoice-detail?orderNumber=${orderNumber}&orderInvoiceId=${orderInvoiceId}`
	  	})
	  },
    //跳转商品详情页
    toProdPage: function (prodid) {
      util.tapLog(3, { shopId: this.shopId })

      if (this.orderType == 3) {
        uni.navigateTo({
          url: '/packageMemberIntegral/pages/convertProdDet/convertProdDet?prodId=' + prodid
        });
      } else if (this.orderType == 2) {
        if (this.seckillId) {
        	this.$Router.push({ path: '/packageActivities/pages/snapUpDetail/snapUpDetail', query: { seckillId: this.seckillId } })
        } else {
        	uni.showToast({
        		title: this.i18n.secondKillClosed,
        		icon: "none",
        		duration: 1000,
        	})
        }
      } else {
        // uni.navigateTo({
        // 	url: '/pages/prod/prod?prodid=' + prodid
        // });
        this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodid } })
      }
    },
    toProdDetail (prodid) {
      this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodid } })
    },

    /**
   * 是否最后一个商品在执行单个商品退款事件
   */
    lastProdApplyRefund: function () {
      this.isLastProd = false
      if (this.status == 2) { //待发货状态下
        // 遍历商品list
        if (this.orderItemDtos.length > 1) {  //如果商品列表长度大于1
          let sum = []
          this.orderItemDtos.forEach((el, index) => {
            if (el.refundSn) { //如果拥有退款单号
              sum.push(el)
            }
          })
          if (sum.length == this.orderItemDtos.length - 1) {
            // 如果拥有退款单号的item等于商品列表数据长度-1，那么点击的这件商品就是最后一件商品
            this.setData({
              isLastProd: true
            })
          } else if (sum.length == this.orderItemDtos.length) {
            // 如果拥有退款单号的item等于商品列表数据长度，那么不可撤销申请
            this.setData({
              irrevocable: true  //不可撤销
            })
          }
        };
      }
    },

    /**
     * 申请退款
     */
    applyRefund: util.debounce(function (item, refundtype) {
      util.tapLog(3, { shopId: this.shopId })
      // refundType 退款类型 1整单  2单个物品
      let status = this.status
      var type = this.status == 2 ? '1' : '2'
      this.loadOrderDetail(this.orderNumber)  //请求订单详情数据
      if (refundtype == 1) { //整单退款
        var items = {}
        items.orderNumber = this.orderNumber;
        items.actualTotal = this.actualTotal; // 订单总额
        items.transfee = this.transfee; // 订单运费
				items.freeTransfee = this.freeTransfee; //运费减免
        items.status = this.status; //订单状态
        items.orderItemDtos = this.orderItemDtos;
        items.orderScore = this.orderScore;  // 整单积分
				items.canRefundAmount = this.canRefundAmount;
				// 将数据存储到本地
				wx.setStorageSync("refundItem", items);
      } else if (refundtype == 2) { //单个商品退款
        this.lastProdApplyRefund() //是否最后一个商品执行单个商品退款
        if (this.orderItemDtos.length == 1) { // 是否只有一件商品
          item.isOnlyProd = true
        } else {
          item.isOnlyProd = false
        }
        // 将数据存储到本地
        item.orderNumber = this.orderNumber;
        item.transfee = this.transfee; // 本单运费
				item.freeTransfee = this.freeTransfee, //运费减免
        item.status = this.status; //订单状态
        item.isLastProd = this.isLastProd // 是否最后一款商品
				item.canRefundAmount = this.canRefundAmount
        // 虚拟商品
        if (this.orderMold == 1) {
          // 1.无需核销：允许用户选择退款数量（writeOffNum 0无需核销 -1多次核销 1单次核销）
          item.needWriteOffs = this.writeOffNum !== 0
          // 2.需要核销：展示最大可退款件数，不允许选择（最大可退款件数 = 待使用的核销券数）
          item.maxRefundPieces = this.unusedCount
        }
        //拿到存储在本地的订单项数据
        wx.setStorageSync("refundItem", item);
      }
      /**  判断跳转页面（根据订单是否已发货）*/
      if (status == 2) {
        // 待发货，跳转到申请退款页面
        uni.navigateTo({
          url: `/packageShop/pages/applyRefund/applyRefund?type=${type}&refundType=${refundtype}`,
        })
      } else {  //已发货，跳转到选择退货方式页
        // orderMold: 1虚拟商品订单（下单成功后订单状态status=3待收货）
        // 虚拟商品，直接跳转到申请页，不需要选择退货方式（默认为仅退款（type:1））
        let url = this.orderMold == 1
                  ? `/packageShop/pages/applyRefund/applyRefund?type=1&refundType=${refundtype}&orderMold=${this.orderMold}`
                  : `/pages/chooseRefundWay/chooseRefundWay?refundType=${refundtype}`
        uni.navigateTo({
          // url: '/pages/chooseRefundWay/chooseRefundWay?refundType=' + refundtype
          url: url
        })
      }
    }, 600),

    /**
     * 查看退款
     */
    viewRefund: function (e) {
      util.tapLog(3, { shopId: this.shopId })
      var refundSn = e.currentTarget.dataset.refundsn;
      this.lastProdApplyRefund() //是否最后一个商品执行单个商品退款
      uni.navigateTo({
        url: '/packageShop/pages/DetailsOfRefund/DetailsOfRefund?refundSn=' + refundSn + '&irrevocable=' + this.irrevocable
      })
    },

    /**
     * 加载订单数据
     */
    loadOrderDetail: function (orderNum) {
      var ths = this;
      uni.showLoading(); //加载订单详情

      var params = {
        url: "/p/myOrder/orderDetail",
        method: "GET",
        data: {
          orderNumber: orderNum
        },
        callBack: function (res) {
          ths.setData({
            shopId: res.shopId,
            orderNumber: orderNum,
            actualTotal: res.actualTotal,
            //实际支付总额（商品总额+运费)
            userAddrDto: res.userAddrDto,
            remarks: res.remarks,
            orderItemDtos: res.orderItemDtos,
            createTime: res.createTime,
            status: res.status,
            productTotalAmount: res.total, // 商品总额
            transfee: res.transfee, // 运费
						// freeTransfee: res.freeTransfee,//运费减免
            reduceAmount: res.reduceAmount, // 总优惠
            orderType: Number(res.orderType),
            shopName: res.shopName,
            canRefund: res.canRefund,
            canAllRefund: res.canAllRefund,
            payType: res.payType,
            dvyType: res.dvyType, // 配送类型 1:快递 2:自提 3：无需快递
            orderScore: res.orderScore, // 整单使用积分
			      // 当前订单可退款金额
			      canRefundAmount: res.canRefundAmount,
			      seckillId: res.seckillId,
			      orderInvoiceId: res.orderInvoiceId,
			      refundStatus: res.refundStatus,

            // 优惠明细
            freeTransfee: res.platformFreeFreightAmount, // 运费减免
            platformCouponAmount: res.platformCouponAmount,  // 平台优惠券优惠金额
            scoreAmount: res.scoreAmount, // 积分抵扣
            memberAmount: res.memberAmount, // 会员折扣
            shopCouponMoney: res.shopCouponMoney, // 店铺优惠券
            discountMoney: res.discountMoney, // 促销满减
            shopComboAmount: res.shopComboAmount, // 套餐优惠
            shopAmount: res.shopAmount, // 店铺优惠总额
            shopChangeFreeAmount: res.shopChangeFreeAmount, // 商家改价

            orderMold: res.orderMold, // 1虚拟商品
            virtualInfoList: res.virtualInfoList, // 券码
            virtualRemark: res.virtualRemark ? JSON.parse(res.virtualRemark) : [], // 留言
            writeOffNum: res.writeOffNum, // 核销次数 -1多次 0无需核销 1单次核销
            writeOffStart: res.writeOffStart, // 核销有效期开始时间
            writeOffEnd: res.writeOffEnd, // 核销有效期结束时间（仅有开始时间没有结束时间标识长期有效）
            outerShipInfos: res.outerShipInfos,
          });
          if (res.writeOffEnd) {
            let writeOffEndTime = new Date(res.writeOffEnd).getTime()
            let nowTime = new Date().getTime()
            if(nowTime>writeOffEndTime) { ths.isExpired = true}
          }
          if (res.dvyType == 2) {
            ths.loadStationDetail(orderNum)
          }

          // 虚拟商品-待使用的券总数
          ths.handleUnusedVirtualCode(res.virtualInfoList)

          // 虚拟商品留言处理
          ths.handlevirtualRemark()
          uni.hideLoading()
        }
      };
      http.request(params);
    },

    // 虚拟商品-待使用的券总数
    handleUnusedVirtualCode(list) {
      if (list && list.length) {
        let unusedCount = 0
        list.forEach(el => {
          if (el.isWriteOff === 0) {
            unusedCount = unusedCount + 1
          }
        })
        this.unusedCount = unusedCount
      }
    },

    // 虚拟商品-留言列表处理
    handlevirtualRemark() {
      if (this.virtualRemark && this.virtualRemark.length) {
        // 过滤掉没有填写的留言
        this.virtualRemark = this.virtualRemark.filter(el => el.value)
        let virtualRemarkList = ''
        let virtualRemarks = []
        // 如果留言总数大于3条，先展示前一条（完整留言在弹窗中展示）
        if (this.virtualRemark.length > 3) {
          const list = JSON.parse(JSON.stringify(this.virtualRemark))
          virtualRemarks = list.splice(0, 1)
          this.isExtraLong = true
        } else {
          virtualRemarks = this.virtualRemark
          this.isExtraLong = false
        }
        // 全部留言拼接
        virtualRemarks.forEach((el, index) => {
          if (index < virtualRemarks.length - 1) {
            virtualRemarkList = virtualRemarkList + el.name + ' ' + el.value + '，'
          } else {
            virtualRemarkList = virtualRemarkList + el.name + ' ' + el.value
          }
        })
        if (this.isExtraLong) {
          this.virtualRemarkList = virtualRemarkList + '，......'
        } else {
          this.virtualRemarkList = virtualRemarkList
        }
      }
    },

    // 查看留言弹窗
    showViewMsgPopup: function() {
      if (this.isExtraLong) {
        this.showViewMsg = true
      }
    },
    closeMsgPopup: function() {
      this.showViewMsg = false
    },

    /**
     * 加载订单详情自提信息
     */
    loadStationDetail: function (orderNum) {
      uni.showLoading(); //加载订单详情
      var params = {
        url: "/p/myStationOrder/stationDetail",
        method: "GET",
        data: {
          orderNumber: orderNum
        },
        callBack: (res) => {
          uni.hideLoading()
          this.id = res.id, // 自提信息id
            this.stationAddress = res.stationAddress, // 自提点的地址
            this.stationCode = res.stationCode, // 提货码
            this.stationId = res.stationId, // 自提点id
            this.stationPhone = res.stationPhone, // 自提点的联系电话
            this.stationTime = res.stationTime, // 自提时间(用户下单时选择)
            this.stationUserMobile = res.stationUserMobile, // 自提人的手机
            this.stationUserName = res.stationUserName, // 自提人的名字
            this.stationName = res.stationName, // 自提点名称
            this.lat = this.bMapTransQQMap(res.lng, res.lat).lat, // 纬度
            this.lng = this.bMapTransQQMap(res.lng, res.lat).lng, // 经度
            console.log(this.lat,this.lng,'sda')
            this.getQrcode(this.stationCode)
        }
      };
      http.request(params);
    },

    /**
     * 拨打自提点电话
     */
    callStation: function () {
      util.tapLog(3, { shopId: this.shopId })
      uni.makePhoneCall({
        // 手机号
        phoneNumber: this.stationPhone,
        // 成功回调
        success: (res) => {
        },
        // 失败回调
        fail: (res) => {
        }
      });
    },

    // 将百度地图经纬度转换为腾讯/高德地图经纬度
    bMapTransQQMap: function (lng, lat) {
      let x_pi = 3.14159265358979324 * 3000.0 / 180.0;
      let x = lng - 0.0065;
      let y = lat - 0.006;
      let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
      let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
      let lngs = z * Math.cos(theta);
      let lats = z * Math.sin(theta);
      return {
        lng: lngs,
        lat: lats
      }
    },

    /**
     * 打开地图
     */
    openMap: function () {
      util.tapLog(3, { shopId: this.shopId })
      var ths = this
      console.log('success',ths.lat,ths.lng);
      uni.openLocation({
        latitude: Number(ths.lat),
        longitude: Number(ths.lng),
        success: function () {
        }
      });
    },

    /**
     * 生成二维码
     */
    getQrcode: function (stationCode) {
      this.stationQrCode = Qr.createQrCodeImg(stationCode)
    },

    // #ifdef  APP-PLUS
    /**
     * 一键复制事件
     */
    // 设置系统剪贴板的内容。
    copyBtn () {
      util.tapLog(3, { shopId: this.shopId })
      // 除了h5以外都可用的复制方法
      uni.setClipboardData({
        data: this.orderNumber,
        success: function () {
          uni.showToast({
            title: this.i18n.copySucceeded,
            icon: 'none',
            duration: 1000
          })
        }
      });
    },
    // #endif

    /**
     * 兼容h5的复制方法
     */
    copyText (text) {
      let result
      // #ifndef H5
      uni.setClipboardData({
        data: text,
        success() {
          uni.showToast({
            title: this.i18n.copySucceeded,
            icon: 'none',
            duration: 1000
          })
          result = true
        },
        fail() {
          uni.showToast({
            title: this.i18n.copyFailure,
            icon: 'none',
            duration: 1000
          })
          result = false
        }
      });
      // #endif

      // #ifdef H5
      let textarea = document.createElement("textarea")
      textarea.value = text
      textarea.readOnly = "readOnly"
      document.body.appendChild(textarea)
      textarea.select() // 选中文本内容
      textarea.setSelectionRange(0, text.length)
      result = document.execCommand("copy")
      if (result) {
        uni.showToast({
          title: this.i18n.copySucceeded,
          icon: 'none',
          duration: 1000
        })
      }
      textarea.remove()
      // #endif
      return result
    },

    // 查看提货凭证
    viewCertificate () {
      util.tapLog(3, { shopId: this.shopId })
      var cont = {
        stationCode: this.stationCode, // 提货码
        stationAddress: this.stationAddress, // 自提点的地址
        stationPhone: this.stationPhone, // 自提点的联系电话
        stationTime: this.stationTime, // 自提时间(用户下单时选择)
        orderNumber: this.orderNumber, // 订单编号
        stationName: this.stationName
      }
      uni.setStorageSync('stationCont', cont)
      uni.navigateTo({
        url: '/pages/deliveryCertificate/deliveryCertificate'
      })
    },
    /**
     * 查看团购详情
     */
    toGroupDetails: function (e) {
      util.tapLog(3, { shopId: this.shopId })
      uni.navigateTo({
        url: '/packageActivities/pages/spellGroupDetails/spellGroupDetails?orderNumber=' + e.currentTarget.dataset.ordernumber,
      });
    },

    /**
     * 咨询客服
     */
    handleCustomService() {
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageUser/pages/chat/chatIm?shopid=' + (this.shopId?this.shopId:'0') + '&orderNumber=' + this.orderNumber
        });
      })
    },

    /**
     * 查看券码（虚拟商品）
     */
    toViewVoucherCodePage () {
      if (this.isExpired) {
        uni.showToast({
          title: this.i18n.voucherCode + this.i18n.expired,
          icon: 'none'
        })
        return
      }
      uni.navigateTo({
        url: `/pages/voucherCode/voucherCode?orderNum=${this.orderNumber}`,
      });
    }

  }
};
</script>
<style>
@import "./order-detail.css";
</style>

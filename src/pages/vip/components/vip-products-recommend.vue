<template>
  <view class="recommend-container" v-if="!!vipCenterPage">
    <view v-for="(item) in vipCenterPage.recommends" :key="item.id" class="recommend-item">
      <view class="recommend-item-head">
        <view class="recommend-item-title">
          <text class="recommend-item-title-text">{{item.title}}</text>
          <view class="recommend-item-title-tag">
            超级会员专享
          </view>
        </view>
        <view class="recommend-item-title-text" @tap=handleTapMore(item)>
          更多&gt;
        </view>
      </view>
      <view class="recommend-item-body">
        <view v-for="(prodItem) in item.products" :key="prodItem.prodId" class="recommend-item-product" @click="handleClickProd(prodItem.prodId)">
          <view>
            <image lazy-load class="recommend-item-product-img" mode='scaleToFill' v-if="prodItem.pic" :src="prodItem.pic"></image>
            <view class="recommend-item-product-name">
              {{ prodItem.prodName }}
            </view>
          </view>
          <view v-if="!isMiniInIos" class="recommend-item-product-price">¥{{prodItem.price}} 开通</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import http from '@/utils/http.js'
import { AppType } from '@/utils/constant.js'

export default {
  name: 'vip-products-recommend',
  data () {
    return {
      vipCenterPage: null,
    }
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  methods: {
    getShopVipConfig(shopId) {
      if (!shopId) {
        return
      }
      return new Promise((resolve, reject) => {
        http.request({
          url: '/shop/getMemberShopConfig',
          method: 'GET',
          data: {
            shopId
          },
          callBack: res => {
            const configJson = res.config
            if (configJson) {
              const configObj = JSON.parse(configJson)
              resolve(configObj)
            }
          },
          errCallBack() {
            reject('')
          }
        })
      })
    },
    async getVipCenterPageConfig(shopId) {
      const currentShopConfig = await this.getShopVipConfig(shopId)
      if (currentShopConfig) {
        const currentShopVipCeterPage = currentShopConfig.vipCenterPage
        if (!currentShopVipCeterPage) {
          return
        }
        if (currentShopVipCeterPage.reuse) {
          const resuseShopId = currentShopVipCeterPage.reuseShopId
          this.getVipCenterPageConfig(resuseShopId)
        } else {
          // 这里真正获取到了需要的 vip 配置信息，需要用商品 id再查询最新的商品信息
          currentShopVipCeterPage.recommends.forEach(async (item, index) => {
            const prodIds = item.products.map(el => el.prodId);
            const prodList = await this.getProductsByIds(prodIds)
            if (prodList) {
              item.products = prodList
            }
          })
          this.vipCenterPage = currentShopVipCeterPage
        }
      }
    },
    getProductsByIds(ids) {
      if (!ids || !ids.length) {
        return
      }
      return new Promise((resolve, reject) => {
        http.request({
          url: '/prod/listProdByIdsAndType',
          method: 'GET',
          data: {
            prodIds: ids.join(','),
            prodType: ''
          },
          callBack: res => {
            resolve(res)
          },
          errCallBack() {
            reject('')
          }
        })
      })
    },
    handleClickProd(prodId) {
      if (this.isMiniInIos && this.vipCenterPage.iosMpPath) {
        uni.navigateTo({
          url: this.vipCenterPage.iosMpPath
        })
      } else if (prodId) {
        uni.navigateTo({
          url: '/pages/prod/prod?prodid=' + prodId
        })
      }
    },
    handleTapMore(item) {
      if (this.isMiniInIos && this.vipCenterPage.iosMpPath) {
        uni.navigateTo({
          url: this.vipCenterPage.iosMpPath
        })
      } else if (item.moreBtnPath) {
        uni.navigateTo({
          url: item.moreBtnPath
        })
      }
    }
  },
  computed: {
    currentShop() {
      return this.$store.state?.currentShop
    },
    appType() {
      return uni.getStorageSync('appType')
    },
    // 是否是 IOS 小程序
    isMiniInIos() {
      const res = this.appType === AppType.MINI && uni.getSystemInfoSync().platform === 'ios'
      return res
    }
  },
  mounted () {
    this.getVipCenterPageConfig(this.currentShop.shopId)
  },
  watch: {
    currentShop: {
      handler(newVal) {
        this.getVipCenterPageConfig(newVal.shopId)
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.recommend-container {
  width: 100%;;
  padding: 42rpx 32rpx;
  box-sizing: border-box;
}
.recommend-item {
  width: 100%;
  padding: 16rpx 16rpx 32rpx;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px 0px rgba(221,221,221,0.5);box-shadow: 0px 2px 4px 0px rgba(245,245,245,0.5);
  border-radius: 8px;
  margin-bottom: 48rpx;
}
.recommend-item-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.recommend-item-title {
  display: flex;
  align-items: center;
}
.recommend-item-title-text {
  font-size: 28rpx;
  color: #333333;
  letter-spacing: 0;
  font-weight: 400;
}
.recommend-item-title-tag {
  font-size: 20rpx;
  color: var(--primary-color);
  letter-spacing: 0;
  text-align: center;
  border-radius: 2px;
  border: 1px solid var(--primary-color);
  padding: 1rpx 8rpx;
  margin-left: 12rpx;
}

.recommend-item-body {
  width: 100%;
  display: flex;
  column-gap: 16rpx;
  row-gap: 24rpx;
  margin-top: 16rpx;
  flex-wrap: wrap;
}
.recommend-item-product {
  width: 208rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}
.recommend-item-product-img {
  width: 208rpx;
  height: 208rpx;
  border-radius: 8px;
}
.recommend-item-product-name {
  font-size: 24rpx;
  color: #000;
  font-weight: 500;
  margin: 16rpx;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -moz-box;
  -moz-line-clamp: 1;
  -moz-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
}
.recommend-item-product-price {
  margin-top: 5rpx;
  background-color: #FFC676;
  border-radius: 16rpx;
  padding: 4rpx 40rpx;
  font-size: 24rpx;
  color: #000000;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}
</style>
<template>
  <view class="vip-rights-container">
    <view class="vip-right-title" :class="{'level2': !isCommonPerson}">
      <text class="vip-right-title-text">超级会员专属权益</text>
    </view>
    <view class="rights-item-con">
      <block v-for="(item, rightsId) in rights" :key="rightsId" >
        <view class="rights-item" @tap="handleTap(item)">
          <view class="rights-img">
            <image mode="heightFix" :src="item.icon.startsWith('http') ? item.icon : resourcesUrl + item.icon"></image>
          </view>
            <view class="rights-tit">{{item.rightsName}}</view>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
import config from '@/utils/config.js'
import { AppType } from '@/utils/constant.js'

export default {
  name: 'vip-rights',
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  data () {
    return {
      resourcesUrl: config.picDomain,
    }
  },
  props: {
    userRights: {
      type: Array,
      default: () => {
        return []
      }
    },
    allRights: {
      type: Array,
      default: () => {
        return []
      }
    },
    userLevelInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  methods: {
    handleTap(rightsItem) {
      console.log(rightsItem);
      if (!rightsItem.targetPath) {
        return
      }
      if (rightsItem.targetPath.startsWith('http')) {
        // #ifdef H5
        window.open(rightsItem.targetPath)
        // #endif
        return
      } else {
        uni.navigateTo({
          url: rightsItem.targetPath
        })
      }
    }
  },
  computed: {
    isCommonPerson() {
      if (this.userLevelInfo.levelType !== 0) {
        return false
      } else if (!this.userLevelInfo.nextGrowth) {
        return false
      }
      return true
    },
    currentShop() {
      return this.$store.state?.currentShop
    },
    rights() {
      const res = [];
      const userRightsIds = this.userRights.map(item => item.rightsId);
      this.allRights.forEach(item => {
        const {config, ...rest} = item;
        const processedItem = {...rest}
        const rightConfigObj = JSON.parse(config)
        const inactiveIcon = rightConfigObj.inactiveIcon;
        const targetPath = rightConfigObj.targetPath
        const visibleInShops = rightConfigObj.visibleInShops
        const hasSpecial = rightConfigObj.hasSpecial;
        const specialClickConfig = rightConfigObj.specialClickConfig || []
        // 权益在当前店铺不显示
        if (!visibleInShops.includes(this.currentShop.shopId)) {
          return
        }
        // 用户没有该权益，icon 设置为 inactiveIcon
        if (!userRightsIds.includes(item.rightsId)) {
          processedItem.icon = inactiveIcon;
        }
        if (hasSpecial) {
          const targetClickConfig = specialClickConfig.find(item => item.shopId === this.currentShop.shopId);
          if (this.isMiniInIos) {
            processedItem.targetPath = targetClickConfig?.iosMpPath || '';
          } else {
            processedItem.targetPath = targetClickConfig?.path || '';
          }
        } else {
          if (this.isMiniInIos) {
            processedItem.targetPath = rightConfigObj.iosMpTargetPath || '';
          } else {
            processedItem.targetPath = targetPath || '';
          }
        }
        res.push(processedItem)
      });
      return res;
    },
    appType() {
      return uni.getStorageSync('appType')
    },
    // 是否是 IOS 小程序
    isMiniInIos() {
      const res = this.appType === AppType.MINI && uni.getSystemInfoSync().platform === 'ios'
      return res
    }
  },
  mounted () {

  },
}
</script>

<style scoped>
.vip-rights-container {
  width: 100%;
  border-radius: 14rpx 14rpx 0 0;
  position: relative;
  padding: 0 30rpx;
  box-sizing: border-box;
  overflow-y: auto;
  margin-top: 32rpx;
}

.vip-right-title {
  display: flex;
  justify-content: center;
  align-items: center;
}

.vip-right-title-text {
  font-family: Helvetica;
  font-size: 32rpx;
  color: #333333;
  font-weight: 700;
}

.vip-right-title::before, .vip-right-title::after {
  content: '';
  width: 156rpx;
  height: 4rpx;
}

.vip-right-title::before {
  background: linear-gradient(.25turn, #ffffff, #3497e2);
  margin-right: 17rpx;
}
.vip-right-title::after {
  background: linear-gradient(.25turn, #3497e2, #ffffff);
  margin-left: 17rpx;
}
.vip-right-title.level2::before {
  background: linear-gradient(.25turn, #ffffff, #F6A64A );
  margin-right: 17rpx;
}
.vip-right-title.level2::after {
  background: linear-gradient(.25turn, #F6A64A, #ffffff);
  margin-left: 17rpx;
}

.rights-item-con {
  display: flex;
  box-sizing: border-box;
  justify-content: flex-start;
  width: 100%;
  flex-wrap: wrap;
  margin-top: 52rpx;
}

.rights-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 15rpx;
  box-sizing: border-box;
  flex-shrink: 0;
}

.rights-img {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
}
.rights-img image{
  width: 100%;
  height: 100%;
  display: block;
}

.rights-tit {
  font-family: Helvetica;
  font-size: 24rpx;
  color: #999999;
  font-weight: 400;
  margin-top: 8rpx;
}
</style>
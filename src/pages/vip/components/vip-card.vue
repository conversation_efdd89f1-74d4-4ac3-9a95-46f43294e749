<template>
  <view class="vip-card-wrap">
    <view class="vip-card" :class="{ 'vip-level-2': !isCommonPerson }">
      <view class="vip-card__content">
        <view class="vip-card__basic-info">
          <image class="vip-card__avatar" :src="userInfo.pic?userInfo.pic:`${staticPicDomain}images/icon/head04.png`"></image>
          <view class="vip-card__level-info">
            <view class="vip-card__level-info__block1">
              <view class="vip-card__username">{{ userInfo.nickName }}</view>
              <view class="vip-card__level-placement">
                <view class="vip-card__level-sign">
                  <image class="vip-card__level-img" width="40rpx" height="40rpx" v-if="userLevelInfo && userLevelInfo.userLevel" :src="userLevelInfo.userLevel.img" />
                  <view class="vip-card__level-name" v-if="userLevelInfo.userLevel">{{userLevelInfo.userLevel.level==0?i18n.ordinaryMembership:userLevelInfo.levelName}}</view>
                </view>
                <view class="vip-card__buy-vip-btn" v-if="isCommonPerson">
                  <view @tap="toBuyVip">去升级</view>
                </view>
              </view>
              <view class="vip-card__expire-date" v-if="!isCommonPerson">
                <view class="vip-card__expire-date__text">会员卡有效期至{{ expireTime }}</view>
                <view @tap="toVipRule" class="vip-card__expire-date__btn">查看规则&gt;</view>
              </view>
            </view>
          </view>

        </view>

        <view class="vip-card__progress">
          <view class="vip-card__progress-tip" v-if="isCommonPerson">
            <view class="vip-card__progress-tip__text">还差{{ upgradeNeedGrowth }}成长值成为{{ userLevelInfo.nextLevelName }}</view>
            <view @tap="toVipRule" class="vip-card__progress-tip__btn">查看规则&gt;</view>
          </view>

          <view class="vip-card__progress-bar">
            <progress class="level-progress" :percent="percent" backgroundColor="#feeee0" :activeColor="isCommonPerson ? '#4E76AB' : '#F6A64A' " active="true" border-radius="8" stroke-width="4"></progress>
          </view>

          <view class="vip-card__progress-footer">
            <view>
              <view class="vip-card__growth" >
                <n-i :icon="isCommonPerson ? 'growth-level-1' : 'growth-level-2'" width="26rpx" height="26rpx"></n-i>
                <view class="vip-card__growth-text">
                  成长值 {{userLevelInfo.growth}}
                  <text v-if="userLevelInfo.nextGrowth">
                    / {{userLevelInfo.nextGrowth}}
                  </text>
                </view>
              </view>
            </view>
            <view v-if="isCommonPerson && !userLevelInfo.totalSaveMoney" class="vip-card__save-money">
              <text>成为超级会员预计可节省 163.5 元</text>
            </view>
            <view class="vip-card__save-money" v-else-if="!!userLevelInfo.totalSaveMoney">
              <text>
                累计已节省
              </text>
              <text class="vip-card__saved-money" :class="{gold: !isCommonPerson}">
                {{userLevelInfo.totalSaveMoney}}
              </text>
              <text>元</text>
            </view>
          </view>
        </view>

      </view>
      <!-- 背景图片 -->
      <image
        class="vip-card__bg"
        :src="isCommonPerson ? `${staticPicDomain}/images/other/vipcard-bg1.png`: `${staticPicDomain}/images/other/vipcard-bg2.png`"
        mode="aspectFit"
      />
    </view>
  </view>
</template>

<script>
import NI from '@/components/n-i'
export default {
  name: 'vip-card',
  data () {
    return {
    }
  },
  components: {
    NI,
  },
  options: {
    multipleSlots: true,
    virtualHost: true, // 加上此配置，可以避免小程序多出一级盒子造成的影响
  },
  props: {
    userInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    userLevelInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  methods: {
    toVipRule() {
      uni.navigateTo({
        url: '/packageMemberIntegral/pages/vip-rule/vip-rule'
      })
    },
    toBuyVip() {
      uni.navigateTo({
        url: '/packageMemberIntegral/pages/buyVip/buyVip'
      })
    }

  },
  computed:{
    i18n() {
      return this.$t('index')
    },
    currentShop() {
      return this.$store.state?.currentShop
    },
    percent() {
      if (!this.userLevelInfo) {
        return 0
      }
      return this.userLevelInfo.nextGrowth > 0 ? this.userLevelInfo.growth * 100 / this.userLevelInfo.nextGrowth : (this.userLevelInfo.nextGrowth === 0 && this.userLevelInfo.growth === 0) || (this.userLevelInfo.growth > this.userLevelInfo.nextGrowth) ? 100 : 0
    },
    currentGrowthFull() {
      return this.userLevelInfo.growth >= this.userLevelInfo.nextGrowth
    },
    upgradeNeedGrowth() {
      return this.userLevelInfo.nextGrowth ? this.userLevelInfo.nextGrowth - this.userLevelInfo.growth : 0
    },
    isCommonPerson() {
      if (this.userLevelInfo.levelType !== 0) {
        return false
      } else if (!this.userLevelInfo.nextGrowth) {
        return false
      }
      return true
    },
    expireTime() {
      if (this.userLevelInfo.levelType !== 0) {
        return this.userLevelInfo.endTime
      }
      return this.userLevelInfo.growthExpireTime
    }
  },
  mounted () {
  },
}
</script>

<style scoped lang="scss">
.vip-card-wrap {
  width: 100%;
  padding: 44rpx 48rpx;
  box-sizing: border-box;
}
.vip-card {
  position: relative;
  width: 100%;
  border-radius: 8px;
  box-shadow: 5px 5px 10px 5px rgba(0, 0, 0, 0.04);
  color: $vip-level-1-color;
  background: linear-gradient(106deg, rgba(255, 255, 255, 0.60) 0.89%, rgba(255, 255, 255, 0.00) 98.18%), linear-gradient(104deg, #E2EBF8 0%, #9FC6F9 100%);
}

.vip-card.vip-level-2 {
  color: $vip-level-2-color;
  background: linear-gradient(106deg, rgba(255, 255, 255, 0.60) 0.89%, rgba(255, 255, 255, 0.00) 98.18%), linear-gradient(104deg, #F9ECC2 0%, #FCCA40 100%);
}

.vip-card__bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0
}
.vip-card__content {
  position: relative;
  z-index: 10;
}

.vip-card__basic-info {
  padding-left: 42rpx;
  padding-top: 26rpx;
  display: flex;
  column-gap: 24rpx;
}

.vip-card__avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}


.vip-card__level-info {
  flex: 1;
  display: flex;
  padding-right: 20rpx;
}

.vip-card__level-info__block1 {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.vip-card__username {
  font-family: Saira;
  font-size: 38rpx;
  font-weight: 500;
  line-height: 58rpx;
}
.vip-card__level-placement {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.vip-card__level-sign {
  display: flex;
  align-items: center;
  column-gap: 8rpx;  

}
.vip-card__level-img {
  width: 40rpx;
  height: 40rpx;
}
.vip-card__level-name {
  font-size: 28rpx;
  font-weight: 400;
}
.vip-card__buy-vip-btn {
  width: 100rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 8px;
  border: 1px solid rgba(17,85,153,1);
  padding: 26rpx 6rpx;
  box-sizing: border-box;
  font-size: 10px;
  color: #115599;
}

.vip-card__expire-date {
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
  margin-top: 14rpx;
}

.vip-card__expire-date__text {
  font-weight: 500;
}


.vip-card__progress {
  padding: 22rpx 18rpx 16rpx 8rpx;
}

.vip-card__progress-tip {
  display: flex;
  font-size: 24rpx;
  letter-spacing: 0;
  font-weight: 400;
  justify-content: space-between;
}

.vip-card__progress-tip__text {
  font-weight: 500;
}

.vip-card__progress-bar {
  margin-top: 10rpx;
}

.vip-card__progress-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 24rpx;
  padding: 0 24rpx;
}

.vip-card__growth {
  display: flex;
  justify-content: center;
  align-items: center;
}

.vip-card__growth-text {
  font-size: 24rpx;
  font-weight: 400;
  margin-left: 10rpx;
}

.vip-card__save-money {
  font-size: 20rpx;
  font-weight: 400;
}

.vip-card__saved-money {
  margin: 0 8rpx;
}
.vip-card__saved-money.gold {
  color: #A05800;
  margin: 0 8rpx;
}

</style>
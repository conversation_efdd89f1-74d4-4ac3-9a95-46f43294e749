<template>
	<view v-if="ou != 10427" class="buy-vip-container">
    <VipCard v-if="isLogin" :user-level-info="userLevelInfo" :user-info="userInfo" />
    <VipRights v-if="isLogin" :user-rights="userRights" :all-rights="allRights" :user-level-info="userLevelInfo" />
    <VipProductsRecommend v-if="isLogin" />
	</view>
	<view v-else class="feature-container">
		<feature ref="featureIndex" @pageLoaded="pageLoaded" :pageLoad="pageLoad" :pageId="renovationId" :shopId="shopId" :pageScorllTop="pageScorllTop"></feature>
	</view>
</template>

<script>
	var http = require("@/utils/http.js");
	var util = require("@/utils/util.js");
	import config from '@/utils/config.js'
	import Pay from "@/utils/pay.js";
  import VipCard from './components/vip-card.vue'
  import VipRights from "./components/vip-rights.vue";
  import VipProductsRecommend from "./components/vip-products-recommend.vue";
  import feature from '@/components/feature/index/index'

	export default {
		components: {
      VipCard,
      VipRights,
      VipProductsRecommend,
      feature,
		},
		data() {
			return {
				duration: 1000,
				totalBalance: null,
				userInfo: {},
				userLevelInfo: {},
				// 当前会员信息
				premiumVipList: [],
				// 付费会员等级列表
				currentLevelId: 0,
				selectPremiumVip: {},
				currentGrowth: "",
				selectPremiumVipIndex: '',
				// 当前选择的付费会员id
				selectPremiumVipId: 0,
        percent: 0,
        currentGrowthFull: false, // 当前成长值已满
        upgradeNeedGrowth: 0, // 升级仍需成长值
				vipCenterRenovationId: null,
        ou: '',
				pageLoad: false, // feature组件需要的数据
        renovationId: config.ujnVipRenovationId, // 装修页面ID
        shopId: config.ujnVipShopId, // 店铺ID
        pageScorllTop: 0, // 页面滚动距离
			};
		},
		props: {},

		computed:{
			i18n() {
				return this.$t('index')
			},
      currentShop() {
        return this.$store.state?.currentShop
      },
      userRights() {
        return this.userLevelInfo.userLevel?.userRights || []
      },
      allRights() {
        return this.selectPremiumVip.userRights || []
      },
      isLogin() {
        return this.$store.state.isLogin
      }
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			// 初始化支付类型数据
			this.payType = util.initPayType().payType
			this.payTypeStr = util.initPayType().payTypeStr

			// 获取ou信息
			const originalWenetAccount = wx.getStorageSync('originalWenetAccount')
			const ou = originalWenetAccount?.org?.ou;
			this.ou = ou;
			
			if (ou == 10427) {
				uni.showLoading()
				this.pageLoad = false
				this.$nextTick(() => {
					this.$refs.featureIndex?.getPageInfoById()
				})
			}
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {
			this.setData({});
		},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			//头部导航标题
			uni.setNavigationBarTitle({
				title:this.i18n.membershipCentre
			});

			// 获取会员信息
			this.getUserLevelInfo();
			// 获取年费会员列表
			this.getPayMemberList();
			// 获取用户信息
			this.queryUserInfo();
			// 获取用户余额
			this.queryCurrentBalance()
      this.vipCenterRenovationId = 1247

			// 获取ou信息
			const originalWenetAccount = wx.getStorageSync('originalWenetAccount')
			const ou = originalWenetAccount?.org?.ou;
			this.ou = ou;
			
			if (ou == 10427) {
				uni.showLoading()
				this.pageLoad = false
				this.$nextTick(() => {
					this.$refs.featureIndex?.getPageInfoById()
				})
			}
		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		onPageScroll: function(e) {
			this.pageScorllTop = e.scrollTop
		},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			/**
			 * 页面加载回调（用于feature组件）
			 */
			pageLoaded(e) {
				if (this.ou == 10427) {
					setTimeout(() => {
						uni.hideLoading()
						this.pageLoad = true
					}, 1)
				}
			},

			/**
			 * 设置支付类型
			 */
			setPayType(paymentInfo) {
				this.payType = paymentInfo.payType
				this.payTypeStr = paymentInfo.payTypeStr
			},

			/**
			 * 关闭支付类型选择弹窗
			 */
			closePopup: function() {
				this.popupShow = false
			},

			/**
			 * 获取用户信息
			 */
			queryUserInfo : function() {
			  uni.showLoading();
			  var params = {
			    url: "/p/user/userInfo",
			    method: "GET",
			    data: {},
          dontTrunLogin: true,
			    callBack: (res) => {
			      uni.hideLoading();
			      // this.nickName =  res.nickName,  //用户昵称
			      // this.pic = res.pic
				  this.setData({
					  userInfo:res
				  })
			    }
			  };
			  http.request(params);
			},

			/**
			 * 获取用户当前余额
			 */
			queryCurrentBalance: function() {
				var params = {
					url: "/p/user/getUserInfo",
					method: "GET",
					data: {},
          dontTrunLogin: true,
					callBack: res => {
						this.totalBalance = res.totalBalance
					}
				};
				http.request(params);
			},

			/**
			 * 获取当前会员信息
			 */
			getUserLevelInfo() {
				var params = {
					url: '/p/score/scoreLevel/page',
					method: 'GET',
					data: {
						levelType: 0
					},
					callBack: res => {
						if (res.endTime) {
							res.endTime = res.endTime.split(' ')[0]
						}
            const percent = res.nextGrowth > 0 ? res.growth * 100 / res.nextGrowth : (res.nextGrowth === 0 && res.growth === 0) || (res.growth > res.nextGrowth) ? 100 : 0
            const currentGrowthFull = res.growth >= res.nextGrowth
            const upgradeNeedGrowth = res.nextGrowth ? res.nextGrowth - res.growth : 0
						this.setData({
							userLevelInfo: res, // 非付费会员等级
							currentLevelId: res.userLevel.id,
							currentGrowth: res.growth,
              percent,
              currentGrowthFull,
              upgradeNeedGrowth
						});
					}
				};
				http.request(params);
			},

			/**
			 * 获取付费会员列表
			 */
			getPayMemberList() {
				var params = {
					url: '/p/score/scoreLevel/page',
          dontTrunLogin: true,
					method: 'GET',
					data: {
						levelType: 1
					},
					callBack: res => {
						this.premiumVipList = res.userLevels
						let flag = false
						for (let i = 0; i < res.userLevels.length; i++) {
							const el = res.userLevels[i];
							if (el.id == this.selectPremiumVipId) {
								this.selectPremiumVip = el
								this.selectPremiumVipId = el.id
								this.selectPremiumVipIndex = i
								flag = true
								break
							}
						}
						if (!flag) {
							this.selectPremiumVip = res.userLevels[0]
							this.selectPremiumVipId = res.userLevels[0].id
							this.selectPremiumVipIndex = 0
							this.popupShow = false
						}
					}
				};
				http.request(params);
			},

			/**
			 * 立即购买- 根据当前付费会员id
			 */
			buyNow() {
				// 成长值不足
				if (this.currentGrowth < this.selectPremiumVip.needGrowth) {
					uni.showToast({
						title: this.i18n.growthValueTips,
						icon: 'none'
					});
					return
				}
				// 已有付费会员时购买低级付费会员
				if (this.userLevelInfo.levelType == 1 && this.userLevelInfo.userLevel.level > this.selectPremiumVip.level) {
					uni.showToast({
						title: this.i18n.cannotBuyLower,
						icon: 'none'
					});
					return
				}

				// 已有付费会员时购买(同级或更高级)付费会员
				if (this.userLevelInfo.levelType == 1 && this.userLevelInfo.userLevel.level < this.selectPremiumVip.level) {
					uni.showModal({
						title: this.i18n.tips,
						content: this.i18n.upgradeMemberTips1 + this.userLevelInfo.userLevel.levelName + ',' + this.i18n.upgradeMemberTips2 + this.selectPremiumVip.levelName + ',' +  this.i18n.upgradeMemberTips3,
						cancelText: this.i18n.cancel,
						confirmText: this.i18n.confirm,
						success: res => {
							if (res.confirm) {
								this.popupShow = true
							} else {}
						}
					});
					return;
				}
				this.popupShow = true
			},



			// 去支付 - 购买会员
			toPay: function() {
				if(this.payType == 9) {
					uni.showModal({
						title: this.i18n.tips,
						content: this.i18n.confirmBalancePay,
						showCancel: true,//是否显示取消按钮
						cancelText: this.i18n.cancel,
						confirmText: this.i18n.confirm,
						success: (res) => {
							if (res.confirm) {
								//点击确定
								Pay.toOrderPay(this.payType,'','','',this.selectPremiumVip.id)
							} else if (res.cancel) {
								console.log('用户点击取消');
								uni.showToast({
									title: this.i18n.cancelBalancePay,
									icon: 'none',
									duration: 1500
								});
							}
						},
					})
					return
				}
				Pay.toOrderPay(this.payType,'','','',this.selectPremiumVip.id)
			},
		}
	};
</script>
<style scoped>
.buy-vip-container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(170deg, #FFEFF1 -0.34%, #FFF 100%);
}

.vip-level-progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 26rpx;
}

.vip-level-progress .level-tips {
  flex: 6;
  margin-right: 30rpx;
  align-self: center;
  font-size: 24rpx;
}

.vip-level-progress .level-tips text{
	color:#593C13;
}

.vip-level-progress .level-progress {
  margin: 6rpx 0;
}

.vip-level-progress .level-span {
  display: flex;
  justify-content: space-between;
  /* font-weight: bold; */
}

</style>

<template>
	<scroll-view class="find" scroll-y="true">
		<view class="find-box">
			<!-- <view class="find-title">视频播放</view> -->
			<image class="find-banner" src="https://m.wenet.com.cn/resources/shop-static/images/other/300d64a425f14711aa79bc170f8d9410.png"/>
		</view>

		<view class="content-box">
			<view class="find-list-box">
				<view class="find-list">
					<view class="find-list-item" v-for="(item, index) in leftRecords" :key="index" @tap="gotoDetail(item)">
						<image class="picture" :src="item.pic"/>
						<view class="bofangbox">
							<image class="bofang" :src="`${staticPicDomain}images/icon/bofang.png`"/>
						</view>
						<view class="bottom-info">
							<view class="prod-name">{{item.prodName}}</view>
							<view class="user-info">
								<view class="left">
									<image class="user-head" :src="item.shopLogo"/>
									<text class="user-name">{{item.shopName}}</text>
								</view>
								<view class="right">
									<image class="dz" :src="`${staticPicDomain}images/icon/eyes.png`"/>
									<text class="dz-num">{{item.videoViewCount}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="find-list">
					<view class="find-list-item" v-for="(item, index) in rightRecords" :key="index" @tap="gotoDetail(item)">
						<image class="picture" :src="item.pic"/>
						<view class="bofangbox">
							<image class="bofang" :src="`${staticPicDomain}images/icon/bofang.png`"/>
						</view>
						<view class="bottom-info">
							<view class="prod-name">{{item.prodName}}</view>
							<view class="user-info">
								<view class="left">
									<image class="user-head" :src="item.shopLogo"/>
									<text class="user-name">{{item.shopName}}</text>
								</view>
								<view class="right">
									<image class="dz" :src="`${staticPicDomain}images/icon/eyes.png`"/>
									<text class="dz-num">{{item.videoViewCount}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

	</scroll-view>
</template>

<script>
	// pages/category/category.js
	var http = require("../../utils/http.js");
	var config = require("../../utils/config.js");
	var util = require("../../utils/util.js");

	export default {
		data() {
			return {
				records: [],
				leftRecords: [],
				rightRecords: [],
			};
		},

		components: {},
		props: {},
		computed:{
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.init('')
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {

		},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			// 加载导航标题
			uni.setNavigationBarTitle({
				title:this.i18n.find
			});

		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			init: function(categoryId) {
				var ths = this; //加载分类列表

				var params = {
					url: "/prod/pageVideoProd",
					method: "GET",
					data: {
						categoryId
					},
					callBack: function(res) {
						const left = []
						const right = []
						for(let i=0;i<res.records.length;i++) {
							if(i%2==0) {
								right.push(res.records[i])
							}else{
								left.push(res.records[i])
							}
						}
						ths.setData({
							records: res.records,
							leftRecords: right,
							rightRecords: left
						});
					}
				};
				http.request(params);
			},
			gotoDetail: function(item) {
				// util.checkAuthInfo(() => {

				// })
				uni.navigateTo({
					url: `/pages/video-detail/video-detail?prodId=${item.prodId}`
				});
			},
		}
	};
</script>
<style>
	@import "video-list.css";
</style>

/* pages/category/category.wxss */

page {
  height: 100%;
  background: #f5f5f5;
}
.find{
	width: 100%;
	/*height: 100vh;*/
}

.find-box{
	width: 100%;
	height: 572rpx;
	background: url('https://m.wenet.com.cn/resources/shop-static/images/other/fa7b549e9387424a9f06664003c14c5b.png') no-repeat;
	background-size:100% auto ;
	box-sizing: border-box;
	padding-top: 34rpx;
	border-radius: 0 0 25rpx 25rpx;
}

.find-title{
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
}
.find-banner{
	width: 680rpx;
	height: 269rpx;
	margin-top: 24rpx;
	margin-left: 35rpx;
}
.content-box{
	width: 100%;
	background: #f5f5f5;
	box-sizing: border-box;
	border-radius: 25rpx 25rpx 0 0;
	position: relative;
	z-index: 1;
	margin-top: -160rpx;
	display: flex;
	flex-direction: column;
	flex: 1;
}

.find-list-box{
	display: flex;
	flex-direction: row;
	padding: 20px 12px 0;
	justify-content: space-between;
}
.find-list{
	display: flex;
	flex-direction: column;
	/* 	align-items: center;
        justify-content: space-between;
        flex-wrap: wrap; */
	/* width: 100%; */
	box-sizing: border-box;
}
.find-list-item{
	width: 338rpx;
	max-height: 696rpx;
	border-radius: 20rpx;
	background: #fff;
	margin-bottom: 12px;
	position: relative;
}
.bofangbox{
	width: 42rpx;
	height: 42rpx;
	background: rgba(0, 0, 0, 0.38);
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	left: 10px;
	top: 12px;
	border-radius: 50%;
}
.bofangbox .bofang{
	width: 24rpx;
	height: 26rpx;
}
.picture{
	width: 338rpx;
	height: 338rpx;
	border-radius: 20rpx;
	margin-bottom: 16rpx;
}
.bottom-info{
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	padding: 6px 20rpx;
}
.prod-name{
	font-size: 26rpx;
	color: #333;
	display: -webkit-box;
	-webkit-line-clamp: 2; /*设定显示行数*/
	-webkit-box-orient: vertical;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 16rpx;
}
.user-info{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;
	/*padding: 0 12rpx;*/
	padding-bottom: 16rpx;
}
.user-info .left{
	display: flex;
	flex-direction: row;
	align-items: center;
}
.user-info .left .user-head{
	width: 50rpx;
	height: 50rpx;
	min-width: 50rpx;
	min-height: 50rpx;
	border-radius: 100%;
	margin-right: 10rpx;
}
.user-info .left .user-name{
	font-size: 24rpx;
	color: #888;
}
.user-info .right{
	display: flex;
	flex-direction: row;
	align-items: center;
}
.user-info .right .dz{
	width: 32rpx;
	height: 32rpx;
	margin-right: 7rpx;
}
.user-info .right .dz-num{
	font-size: 24rpx;
	color: #888;
}
.bottom-prod{
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: 16rpx;
}
.bottom-prod .prod-img{
	width: 100rpx;
	height: 100rpx;
	min-width: 100rpx;
	min-height: 100rpx;
	border-radius: 16rpx;
	margin-right: 18rpx;
}
.bottom-prod .prod-info{
	display: flex;
	flex-direction: column;
}
.bottom-prod .prod-info .prod-name{
	font-size: 24rpx;
	color: #333;
	display: -webkit-box;
	-webkit-line-clamp: 2; /*设定显示行数*/
	-webkit-box-orient: vertical;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 10rpx;
}
.bottom-prod .prod-info .prod-price{
	font-size: 24rpx;
	color: #FF5733;
}


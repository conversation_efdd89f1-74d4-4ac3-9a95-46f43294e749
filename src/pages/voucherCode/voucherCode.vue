<!-- 虚拟商品-查看券码 -->
<template>
  <view class="view-voucher-code">
    <view class="card-vouchers card-model">
      <!-- 券码信息 -->
      <view class="top-half item">
        <view class="prod-name" v-if="!!voucherCodeInfo.orderItemDtos">{{voucherCodeInfo.orderItemDtos[0].prodName}}</view>
        <view class="total-num">
          <text class="weak">{{i18n.totalAmount}}：</text>
          ￥{{voucherCodeInfo.actualTotal}}
          <!-- （{{voucherCodeInfo.virtualInfoList.length + i18n.sheets}}） -->
        </view>
        <view class="expiry-date">
          <view class="lf weak">{{i18n.validityDate}}：</view>
          <view class="rg">
            <text v-if="voucherCodeInfo.writeOffStart && !voucherCodeInfo.writeOffEnd">{{i18n.longTermValidity}}</text>
            <text v-if="voucherCodeInfo.writeOffStart && voucherCodeInfo.writeOffEnd">
              {{voucherCodeInfo.writeOffStart + i18n.to + voucherCodeInfo.writeOffEnd}}
            </text>
          </view>
        </view>
      </view>
      <!-- 条码 & 二维码 -->
      <view class="bottom-half">
        <!-- 条码 -->
        <!-- <view class="bar-code">
          <tki-barcode
                ref="barcode"
                onval
                :val="voucherCodeInfo.orderNumber"
                :load-make="true"
                :opations="barOpations"/>
        </view> -->
        <view class="code-number">{{voucherCodeInfo.orderNumber}}</view>
        <view class="tips weak">{{i18n.tipsForUsingVoucherCode}}</view>
        <!-- 二维码 -->
        <view class="qr-code">
          <view class="code">
            <image :src="`data:image/png;base64,${qrcodeBase64}`" mode="scaleToFill" style="width: 400rpx;height: 400rpx;" @longpress="downloadImg" />
            <!-- isAllUsed || timeLine -->
          </view>
          <view class="qicode-tips" v-if="!isAllUsed && !timeLine">{{i18n.savePicTips}}</view>
          <view class="code-used-tip" v-if="isAllUsed && voucherCodeInfo.writeOffTime">核销时间：{{  voucherCodeInfo.writeOffTime }}</view>
        </view>
      </view>
    </view>
    <n-loading />

    <!-- 使用情况 -->
    <!-- <view class="code-list"> -->
      <!-- 已过期券码列表 -->
      <!-- <view v-if="unusedCodeList && unusedCodeList.length && timeLine" class="code-item">
        <view class="text">{{i18n.expired}}（{{unusedCount + i18n.sheets}}）</view>
        <view class="text-list">
          <view v-for="(unusedItem, unusedIndex) in unusedCodeList" :key="unusedIndex" class="text">
            {{i18n.voucherCode + unusedItem.index}}：{{unusedItem.writeOffCode}}
          </view>
        </view>
      </view> -->
      <!-- 未使用券码列表 -->
      <!-- <view v-if="unusedCodeList && unusedCodeList.length && !timeLine" class="code-item">
        <view class="text">{{i18n.toBeUsed}}（{{unusedCount + i18n.sheets}}）</view>
        <view class="text-list">
          <view v-for="(unusedItem, unusedIndex) in unusedCodeList" :key="unusedIndex" class="text">
            {{i18n.voucherCode + unusedItem.index}}：{{unusedItem.writeOffCode}}
          </view>
        </view>
      </view> -->
      <!-- 已使用券码列表 -->
      <!-- <view v-if="usedCodeList && usedCodeList.length" class="code-item used">
        <view class="text">{{i18n.used}}（{{usedCodeList.length + i18n.sheets}}）</view>
        <view class="text-list">
          <view v-for="(usedItem, usedIndex) in usedCodeList" :key="usedIndex" class="text">
            <text class="used-code">{{i18n.voucherCode + usedItem.index}}：<text class="code">{{usedItem.writeOffCode}}</text></text>
            <text class="right">{{usedItem.writeOffTime}}</text>
          </view>
        </view>
      </view> -->
    <!-- </view> -->
  </view>
</template>

<script>
import tkiBarcode from '@/components/tki-barcode/tki-barcode'
import tkiQrcode from '@/components/tki-qrcode/tki-qrcode'
var http = require("@/utils/http.js");
export default {
  components: {
    tkiBarcode,
    tkiQrcode
  },
  data () {
    return {
      voucherCodeInfo: {},
      // 条码
      barOpations: {
        height: 150, //高度
        displayValue: false //是否在条形码下方显示文字
      },
      // 未使用总数
      unusedCount: 0,
      // 未使用列表
      unusedCodeList: [],
      // 已使用列表
      usedCodeList: [],
      timeLine:null,

      qrcodeBase64: '',
    }
  },

  computed: {
    i18n () {
      return this.$t('index')
    },
    isAllUsed () {
      return this.unusedCount === 0
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad (options) {
    if(options.orderNum) {
      this.loadOrderDetail(options.orderNum)
    }
  },


  methods: {

    /**
     * 加载订单数据
     */
    loadOrderDetail: function (orderNum) {
      var ths = this;
      var params = {
        url: "/p/myOrder/orderDetail",
        method: "GET",
        data: {
          orderNumber: orderNum
        },
        callBack: function (res) {
          ths.setData({
            voucherCodeInfo: {
              // 商品信息
              orderItemDtos: res.orderItemDtos,
              // 实付金额
              actualTotal: res.actualTotal,
              // 虚拟商品-券码列表
              virtualInfoList: res.virtualInfoList,
              // 虚拟商品-有效期
              writeOffStart: res.writeOffStart,
              writeOffEnd: res.writeOffEnd,
              orderNumber: orderNum,
              writeOffTime: res.virtualInfoList?.[0]?.writeOffTime || ''
            }
          });
          ths.getQrcode(orderNum)
          if (res.virtualInfoList.length) {
            let unusedCount = 0
            let unusedCodeList = []
            let usedCodeList = []
            res.virtualInfoList.forEach((el, index) => {
              el.index = index + 1
              if (el.isWriteOff === 0) {
                unusedCount = unusedCount + 1
                unusedCodeList.push(el)
              } else if (el.isWriteOff === 1) {
                usedCodeList.push(el)
              }
            })
            console.log('未使用:', unusedCount);
            ths.unusedCount = unusedCount
            ths.unusedCodeList = unusedCodeList
            ths.usedCodeList = usedCodeList
          }
          if (res.writeOffEnd) {
            let writeOffEndTime = new Date(res.writeOffEnd).getTime()
            let nowTime = new Date().getTime()
            if(nowTime>writeOffEndTime) {
              ths.timeLine = true;
              console.log('已过期');
            }
          }
        }
      };
      http.request(params);
    },
    /**
     * 生成二维码
     */
    getQrcode: function(orderNum) {
      http.request({
        url: "/qrcodeTicket/generate",
        method: "POST",
        data: {
          content: orderNum,
          width: 300,
          height: 300,
          margin: 1,
        },
        callBack: (res) => {
          this.qrcodeBase64 = res
        }
      })
    },



    /**
     * 保存图片至相册
     */
    downloadImg(){
      let base64=this.qrcodeBase64;
      let filePath=wx.env.USER_DATA_PATH + '/hym_pay_qrcode.png';
      uni.getFileSystemManager().writeFile({
          filePath:filePath ,  //创建一个临时文件名
          data: base64,    //写入的文本或二进制数据
          encoding: 'base64',  //写入当前文件的字符编码
          success: res => {
              uni.saveImageToPhotosAlbum({
                  filePath: filePath,
                  success: function(res2) {
                      uni.showToast({
                          title: '保存成功',
                          icon:"none",
                          duration:5000
                      })
                  },
                  fail: function(err) {
                      // console.log(err.errMsg);
                  }
              })
          },
          fail: err => {
              //console.log(err)
          }
      })
    }

  },


  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload () {
    uni.removeStorageSync('voucherCodeInfo')
  }

}
</script>

<style>
@import "./voucherCode.css";
</style>

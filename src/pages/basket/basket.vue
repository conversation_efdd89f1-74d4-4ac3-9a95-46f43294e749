<template>
	<!--pages/basket/basket.wxml-->
	<view class="container">
		<view class="prod-list">
			<view v-for="(shopCart, shopIndex) in shopCartList" :key="shopIndex" class="shop-item">
				<!-- 头部店铺 -->
				<!-- <view class="head-shop">
					<view class="btn choose-btn">
						<label class="check-box-label">
							<checkbox  @tap="handleCheckShop(shopIndex)" :checked="shopCart.checked"
								color="#4A6CEA"></checkbox>
						</label>
					</view>
					<view class="shop-box" @tap="goShop(shopCart.shopId, shopCart.renovationId)">
						<view class="shop-icon">
							<image :src="`${staticPicDomain}images/icon/shop.png`"></image>
						</view>
						<view class="shop-name">{{shopCart.shopName}}<span></span></view>
					</view>
				</view> -->
				<!-- /头部店铺 -->

				<view v-for="(shopCartItem, shopCartItemIndex) in shopCart.shopCartItemDiscounts" :key="shopCartItemIndex">
					<view :class="'prod-block ' + (shopCartItem.chooseDiscountItemDto || shopCartItem.comboId ?'discount':'')">
						<!-- 满减提示 -->
						<view class="discount-tips" v-if="shopCartItem.chooseDiscountItemDto">
							<view class="combo-item">
								<text
								class="text-block">{{[i18n.amount, i18n.pieces, i18n.amountDiscount, i18n.piecesDiscount][shopCartItem.chooseDiscountItemDto.discountRule]}}</text>
							<text
								class="text-list">{{parseDiscountMsg(shopCartItem.chooseDiscountItemDto.discountRule,shopCartItem.chooseDiscountItemDto.needAmount,shopCartItem.chooseDiscountItemDto.discount,curLang)}}</text>
							</view>
							<view>
								<text class="text-list reduce-amount"	v-if="shopCartItem.chooseDiscountItemDto.reduceAmount >0"
									>{{i18n.haveDiscount}}￥{{parsePrice(shopCartItem.chooseDiscountItemDto.reduceAmount)[0]}}.{{parsePrice(shopCartItem.chooseDiscountItemDto.reduceAmount)[1]}}
								</text>
							</view>
						</view>
						<!-- 套装提示 -->
						<view class="discount-tips" v-if="shopCartItem.comboId">
							<view class="combo-item">
								<text class="text-block">{{i18n.packages}}</text>
								<text class="text-list combo-name">{{shopCartItem.chooseComboItemDto.name}}</text>
							</view>
							<view class="combo-item">
								<text class="text-list reduce-amount">{{i18n.haveDiscount}}￥{{parsePrice(shopCartItem.chooseComboItemDto.preferentialAmount)[0]}}.{{parsePrice(shopCartItem.chooseComboItemDto.preferentialAmount)[1]}}</text>
							</view>
						</view>

						<!-- 商品item -->
						<view v-for="(prod, prodIndex) in shopCartItem.shopCartItems" :key="prodIndex">
							<view class="goods-item">
								<!-- 商品信息 -->
								<view class="item">
									<view class="prodinfo" @tap="toProdPage(prod.prodId)">
										<view class="info-row">
											<view :class="['btn', shopCartItem.comboId && prod.parentBasketId !==0 ? 'hide' : '']">
												<label>
													<checkbox @tap.stop="handleCheckProdItem(shopIndex, shopCartItemIndex, prodIndex, prod.isCheck)"
														:value="toString(prod.prodId)" :checked="prod.checked" color="#4A6CEA">
													</checkbox>
												</label>
											</view>
											<view class="pic">
												<image lazy-load :src="prod.pic"></image>
											</view>
											<view class="opt">
												<view class="prod-name">{{prod.prodName}}</view>
												<view class="prod-center-row">
													<view :class="'prod-info-text ' + (prod.skuName?'':'empty-n')" @tap.stop="getSkuListByProdId(prod)">
														{{prod.skuName || ''}}</view>
													<view class="prod-discount" v-if="prod.discounts.length>0">
														<text
															class="prod-discount-tit"
															@tap.stop="onChooseDiscount(prod.basketId,prod.discountId,prod.discounts,prod.checked)"
															>
															{{getCurrDiscountName(prod.discountId,prod.discounts, curLang)}}
															</text>
													</view>
												</view>
												<view class="price-count">
													<view class="price">
														<text class="symbol">￥</text>
														<text class="big-num">{{parsePrice(shopCartItem.comboId ? prod.comboPrice : prod.price)[0]}}</text>
														<text class="small-num">.{{parsePrice(shopCartItem.comboId ? prod.comboPrice : prod.price)[1]}}</text>
														<!-- 套装内的商品的数量 -->
														<text v-if="shopCartItem.comboId" class="combo-count"> x {{ prod.prodCount }}</text>
													</view>
													<view v-if="!shopCartItem.comboId" class="m-numSelector">
														<view @tap.stop="handleChangeNum(shopCart.shopId, shopCartItem.comboId, prod, -1)" class="minus"></view>
														<input type="number" :value="prod.prodCount" disabled></input>
														<view @tap.stop="handleChangeNum(shopCart.shopId, shopCartItem.comboId, prod, 1)" class="plus"></view>
													</view>
												</view>
											</view>
										</view>
										<view v-if="prod.giveaway" class="gift-con">
											<view v-for="(giveawayItem, giveawayIndex) in prod.giveaway.giveawayProds" class="gift-item" :key="giveawayIndex" @tap.stop="toProdPage(giveawayItem.prodId)">
												<view class="gift-name">【{{i18n.Giveaways}}】{{ giveawayItem.prodName }}<text v-if="giveawayItem.skuName" class="gift-sku">{{giveawayItem.skuName||''}}</text></view>
												<view class="gift-count">
													<view>x {{ giveawayItem.giveawayNum }}</view>
													<image :src="`${staticPicDomain}images/icon/more.png`" mode="" />
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						<!-- 套餐的数量 -->
						<view v-if="shopCartItem.comboId" class="combo-oper">
							<view class="combo-price">
								<text>{{i18n.packagePrice}}￥</text>
								<text class="combo-amount">{{ parsePrice(shopCartItem.chooseComboItemDto.comboAmount)[0] }}.</text>
								<text>{{ parsePrice(shopCartItem.chooseComboItemDto.comboAmount)[1] }}</text>
							</view>
							<view class="combo-count">
								<view class="m-numSelector">
									<view @tap.stop="handleChangeNum(shopCart.shopId, shopCartItem.comboId, null, -1)" class="minus"></view>
									<input type="number" :value="shopCartItem.chooseComboItemDto.comboCount" disabled></input>
									<view @tap.stop="handleChangeNum(shopCart.shopId, shopCartItem.comboId, null, 1)" class="plus"></view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<!-- 英文时候购物车底部字体出现遮蔽 -->
		<view class="cart-footer" v-if="shopCartList.length>0"
			:style="i18n.instantReduction.length>2?'height:130rpx':''">
			<view class="btn all">
				<label @tap="handleCheckAll">
					<checkbox :checked="allChecked" color="#4A6CEA"></checkbox>{{i18n.selectAll}}
				</label>
			</view>
			<view class="btn del" @tap="handleDeletProd">
				<text>{{i18n.delete}}</text>
			</view>
			<view class="btn total">
				<view class="total-con" @tap.stop="showPriDet">
					<view class="finally">
						<text class="blod">{{i18n.total}}:</text>
						<view class="price">
							<text class="symbol">￥</text>
							<text class="big-num">{{parsePrice(finalMoney)[0]}}</text>
							<text class="small-num">.{{parsePrice(finalMoney)[1]}}</text>
						</view>
					</view>
					<view class="total-msg" v-if="subtractMoney>0">

						<!-- {{i18n.instantReduction}}:￥{{parsePrice(subtractMoney)[0] + '.' + parsePrice(subtractMoney)[1]}}</view> -->
						{{i18n.instantReduction}}:￥{{toFixxed(subtractMoney)}}</view>
					<!-- 总额:￥{{toPrice(totalMoney)}}  -->
				</view>
				<view class="arrow-icon" @tap.stop="showPriDet">
					<image :src="`${staticPicDomain}images/icon/down-arrow.png`" v-if="hidePriModal"></image>
					<image :src="`${staticPicDomain}images/icon/up-arrow.png`" v-if="!hidePriModal"></image>
				</view>
			</view>
			<view class="btn settle" @tap="toFirmOrder">
				<text>{{i18n.settlement}}</text>
			</view>
		</view>
		<!-- end 底部按钮 -->

		<!-- 底部活动选择弹框 -->
		<view class="modals modals-bottom-dialog" :hidden="hideModal">
			<view class="modals-cancel" @tap="hideModalFun"></view>
			<view class="bottom-dialog-body bottom-pos radius" :animation="animationData">
				<view class="discount-title radius">{{i18n.selectPromotion}}</view>
				<view class="radio-group">
					<radio-group @change="radioChange" v-if="reDraw">
						<label v-for="(item, index) in prodDiscounts" :key="index">
							<view class="radio-item">
								<radio color="#e43130" :value="String(item.discountId)"
									:checked="item.discountId==discountId"></radio>
								<view class="radio-text">
									{{item.discountName}}
								</view>
							</view>
						</label>
						<label>
							<view class="radio-item">
								<radio color="#e43130" value="-1" :checked="discountId==-1"></radio>
								<view class="radio-text">
									{{i18n.notPromotion}}
								</view>
							</view>
						</label>
					</radio-group>
				</view>
			</view>
		</view>

		<!-- 底部金额明细弹框 -->
		<view class="pri-modal modals-bottom-dialog" :hidden="hidePriModal">
			<view class="modals-cancel" @tap="hidePriModalFun"></view>
			<view class="bottom-dialog-box bottom-pos radius" :animation="animationData">
				<view class="discount-title radius">{{i18n.amountDetails}}</view>
				<view class="price-detail">
					<view class="price-detail-item">
						<view class="det-tit">{{i18n.comTotal}}</view>
						<view class="det-num">￥{{toPrice(totalMoney)}}</view>
					</view>
					<view class="price-detail-item" v-if="subtractMoney > 0">
						<view class="det-tit">{{i18n.promotionalDiscount}}</view>
						<view class="det-num">-￥{{toFixxed(subtractMoney)}}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 空 -->
		<view class="empty" v-if="!shopCartList.length">
			<view class="empty-icon">
				<image :src="`${staticPicDomain}images/icon/empty-basket.png`"></image>
			</view>
			<view class="empty-text">{{i18n.shoppingTips}}</view>
		</view>

		<!-- 商品配送方式选择 -->
		<view class="select-distribution modals-bottom-dialog" v-if="showDeliveryWay">
			<view class="modals-cancel" @tap="hideDeliveryFun"></view>
			<view class="bottom-dialog-box bottom-pos radius" :animation="animationData">
				<view class="discount-title radius">{{i18n.chooseCom}}</view>
				<view class="distribution-prods">
					<view class="des">{{i18n.deliveryTips}}</view>
					<view class="p-item" v-if="hasShopDeliveryList.length > 0">
						<view class="p-tit">{{i18n.expressDelivery}}</view>
						<view class="p-con">
							<view class="prods-item">
								<view class="pic" v-for="(item,index) in hasShopDeliveryList" :key="index">
									<image :src="item.pic"></image>
									<view class="prod-count">x{{item.prodCount}}</view>
								</view>
							</view>
							<view class="p-btn" @tap="toSubmitOrder(1)">{{i18n.toSettle}}</view>
						</view>
						<view class="p-total">{{i18n.inTotal}}
							<!-- <view class="num">{{hasShopDeliveryList.length}}</view>{{i18n.piece}}，{{i18n.total}}：<view class="price">￥{{totalSdPri}}</view> -->
							<view class="num">{{hasShopDeliveryProdCount}}</view>
							{{i18n.items}}
						</view>
					</view>
					<view class="p-item" v-if="hasCityDeliveryList.length > 0">
						<view class="p-tit">{{i18n.sameDelivery}}</view>
						<view class="p-con">
							<view class="prods-item">
								<view class="pic" v-for="(item,index) in hasCityDeliveryList" :key="index">
									<image :src="item.pic"></image>
									<view class="prod-count">x{{item.prodCount}}</view>
								</view>
							</view>
							<view class="p-btn" @tap="toSubmitOrder(4)">{{i18n.toSettle}}</view>
						</view>
						<view class="p-total">{{i18n.inTotal}}
							<!-- <view class="num">{{hasCityDeliveryList.length}}</view>{<view class="num">{{hasCityDeliveryList.length}}</view>{{i18n.piece}}，{{i18n.total}}：<view class="price">￥{{totalCdPri}}</view> -->
							<view class="num">{{hasCityDeliveryProdCount}}</view>{{i18n.items}}
						</view>
					</view>
					<view class="p-item" v-if="hasUserPickUpList.length > 0">
						<view class="p-tit">{{i18n.pickStore}}</view>
						<view class="p-con">
							<view class="prods-item">
								<view class="pic" v-for="(item,index) in hasUserPickUpList" :key="index">
									<image :src="item.pic"></image>
									<view class="prod-count">x{{item.prodCount}}</view>
								</view>
							</view>
							<view class="p-btn" @tap="toSubmitOrder(2)">{{i18n.toSettle}}</view>
						</view>
						<view class="p-total">{{i18n.inTotal}}
							<!-- <view class="num">{{hasUserPickUpList.length}}</view>{{i18n.piece}}，{{i18n.total}}：<view class="price">￥{{totalPuPri}}</view> -->
							<view class="num">{{hasUserPickUpProdCount}}</view>{{i18n.items}}
						</view>
					</view>
				</view>
			</view>
		</view>


		<!-- sku弹窗组件 -->
		<prod-sku-select
      :pic="currentProdItem.pic"
      :least-num="currentProdItem.leastNum"
      :sku-id="currentProdItem.skuId"
      :sku-name="currentProdItem.skuName"
      :sku-list="currentProdItem.skuList"
      :is-main="Boolean(currentProdItem.comboId && currentProdItem.parentBasketId===0)"
			v-if="showSkuPop && currentProdItem.skuList"
      @setSku="handleSetSku"
      @closeSkuPop="handleCloseSkuPop"
		></prod-sku-select>
	</view>
</template>

<script>
	import prodSkuSelect from '../../components/prodSkuSelect/prodSkuSelect'
	// pages/basket/basket.js
	var http = require("../../utils/http.js");
	const Big = require("../../utils/big.min.js");
	import util from "../../utils/util.js";


	export default {
		data() {
			return {
				//所有店铺的数据
				shopCartList: [],
				shopCartItemDiscounts: [],
				finalMoney: 0,
				totalMoney: 0,
				subtractMoney: 0,
				allChecked: false,
				hideModal: true, //模态框的状态  true-隐藏  false-显示
				hidePriModal: true, //金额明细模态框的状态  true-隐藏  false-显示
				animationData: {},
				prodDiscounts: [],
				discountId: 0, //促销活动id
				basketId: 0,
				currentDiscounts: [], //促销活动列表
				reDraw: true,

				showDeliveryWay: false, // 选择配送方式弹窗
				hasCityDeliveryList: [], // 同城配送
				hasShopDeliveryList: [], // 店铺配送
				hasUserPickUpList: [], // 用户自提
				totalCdPri: 0, // 同城配送总额
				totalSdPri: 0, // 店铺配送总额
				totalPuPri: 0, // 用户自提总额
				hasShopDeliveryProdCount: 0, // 店铺配送商品总数
				hasCityDeliveryProdCount: 0, // 同城配送商品总数
				hasUserPickUpProdCount: 0, // 用户自提商品总数
				dvyType: 1, // 配送类型 1:快递 2:自提 3：无需快递 4:同城配送
				requestingNum: 0,

				basketReqData: [], // 请求购物车的数据
				curBasketItemCheck: false, // 切换满减活动时商品是否勾选
				// shopSelectArr: [], // 选中的店铺
				curLang: uni.getStorageSync('lang'),

				// 切换sku弹窗显隐
				showSkuPop: false,
				// 当前操作的项商品
				currentProdItem: {}

			};
		},

		components: {
			prodSkuSelect
		},
		props: {},
		computed: {
			i18n() {
				return this.$t('index')
			},
      currentShop: function () {
        return this.$store.state?.currentShop || {}
      },
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			uni.setNavigationBarTitle({
				title: this.i18n.shoppingCart2
			});
			// #ifndef H5
			var logData = uni.getStorageSync('flowAnalysisLogDto')
			uni.setStorageSync('step', uni.getStorageSync('step') / 1 + 1)
			if (logData && logData.pageId != 8) {
				logData.pageId = 8
				logData.step = uni.getStorageSync('step')
				uni.setStorageSync('flowAnalysisLogDto', logData)
				http.saveLog(logData, 1)
			}
			// #endif
			this.showDeliveryWay = false
			if (!uni.getStorageSync('token')) {
				this.shopCartList = []
				util.removeTabBadge()
			}
			//加载购物车
			this.loadBasketData(null);
		},
		onHide: function() {
			this.hidePriModal = true
			this.hideModal = true
		},
		methods: {
			// 去到店铺首页
			goShop(shopId, renovationId) {
				let url
				url = renovationId ? '/pages/shop-feature-index/shop-feature-index0?shopId=' + shopId +
							'&renovationId=' + renovationId :  '/packageShop/pages/shopPage/shopPage?shopId=' + shopId
				uni.navigateTo({
					url: url
				})
			},
			/**
			 * 加载购物车方法
			 * @param {Object} discountData 选择的满减活动
			 */
			loadBasketData(discountData) {
				var reqData = [];
				var shopCartIds = [];
				if (discountData) {
					reqData.push(discountData)
				}
				this.shopCartList.forEach(shopCart => {
					shopCart.shopCartItemDiscounts.forEach(cItems => {
						cItems.shopCartItems.forEach(pItem => {
							if (pItem.checked) {
								shopCartIds.push(pItem.basketId);
							}
						})
					})
				})
				reqData = [...this.basketReqData, ...reqData]
				uni.showLoading(); //加载购物车
				var params = {
					url: "/p/shopCart/info",
					method: "POST",
					data: reqData,
          dontTrunLogin: true,
					callBack: res => {
						var shopCartList = res;
						if (res.length > 0) {
							if (shopCartIds.length == 0) {
								this.setData({
									shopCartList: shopCartList,
								});
							} else { // 修改购物车
								var checkedLog = []
								this.shopCartList.forEach(shopCart => {
									shopCart.shopCartItemDiscounts.forEach(shopCartItemDiscount => {
										shopCartItemDiscount.shopCartItems.forEach(
										shopCartItem => {
											if (shopCartItem.checked) {
												checkedLog.push(shopCartItem.basketId)
											}
										})
									})
								})
								shopCartList.forEach(newShopCart => {
									newShopCart.shopCartItemDiscounts.forEach(newShopCartItemDiscount => {
										newShopCartItemDiscount.shopCartItems.forEach(
											shopCartItem => {
												if (checkedLog.includes(shopCartItem
													.basketId)) {
													shopCartItem.checked = true
												}
											})
									})
								})
								this.shopCartList = shopCartList
							}
						} else {
							this.setData({
								shopCartList: []
							});
						}
						this.calTotalPrice(); //计算总价
						this.checkAllSelected()
					}
				};
				http.request(params);
				http.getCartCount(); //重新计算购物车总数量
			},

			/**
			 * 结算(结算按钮)
			 */
			toFirmOrder: function() {
				if (this.basketReqData.length > 50) {
					uni.showModal({
						title: this.i18n.tips,
						content: this.i18n.basketSelectedCount + this.basketReqData.length + this.i18n
							.basketCountOver,
						confirmText: this.i18n.confirm,
						showCancel: false,
						success: res => {},
						fail: () => {},
						complete: () => {}
					});
					return
				}
				var shopCartList = this.shopCartList;
				var basketIds = [];
				var selectShopId = []
				var hasCityDeliveryList = [] // 同城配送
				var hasShopDeliveryList = [] // 店铺配送
				var hasUserPickUpList = [] // 用户自提

				var hasShopDeliveryProdCount = 0 // 店铺配送商品总数
				var hasCityDeliveryProdCount = 0 // 同城配送商品总数
				var hasUserPickUpProdCount = 0 // 用户自提商品总数
				var totalCdPri = 0
				var totalSdPri = 0
				var totalPuPri = 0

				this.basketReqData.forEach(el => {
					if (el.isCheck) {
						basketIds.push(el.basketId)
					}
				})
				shopCartList.forEach(shopCart => {
					var shopCartItemDiscounts = shopCart.shopCartItemDiscounts;
					shopCartItemDiscounts.forEach(shopCartItemDiscount => {
						shopCartItemDiscount.shopCartItems.forEach(shopCartItem => {
							if (shopCartItem.checked) {
								selectShopId.push(shopCart.shopId)
								// 将商品item放入对应数组中
								if (!shopCartItem.comboId && shopCartItem.deliveryModeVO.hasCityDelivery) {
									hasCityDeliveryList.push(shopCartItem) // 同城配送
									var x = new Big(totalCdPri)
									var y = new Big(shopCartItem.price)
									totalCdPri = x.plus(y).valueOf()
									hasCityDeliveryProdCount += shopCartItem.prodCount
									// totalCdPri += shopCartItem.price
								}
								if (!shopCartItem.comboId && shopCartItem.deliveryModeVO.hasShopDelivery) {
									hasShopDeliveryList.push(shopCartItem) // 店铺配送
									var x = new Big(totalSdPri)
									var y = new Big(shopCartItem.price)
									totalSdPri = x.plus(y).valueOf()
									hasShopDeliveryProdCount += shopCartItem.prodCount
									// totalSdPri += shopCartItem.price
								}
								if (!shopCartItem.comboId && shopCartItem.deliveryModeVO.hasUserPickUp) {
									hasUserPickUpList.push(shopCartItem) // 用户自提
									var x = new Big(totalPuPri)
									var y = new Big(shopCartItem.price)
									totalPuPri = x.plus(y).valueOf()
									hasUserPickUpProdCount += shopCartItem.prodCount
									// totalPuPri += shopCartItem.price
								}
							}
						});
					});
				});
				var shopSelectArr = [...new Set(selectShopId)] // 选中的商品所在店铺去重
				this.totalCdPri = totalCdPri
				this.totalSdPri = totalSdPri
				this.totalPuPri = totalPuPri

				this.hasShopDeliveryProdCount = hasShopDeliveryProdCount
				this.hasUserPickUpProdCount = hasUserPickUpProdCount
				this.hasCityDeliveryProdCount = hasCityDeliveryProdCount

				this.hasShopDeliveryList = hasShopDeliveryList
				this.hasCityDeliveryList = hasCityDeliveryList
				this.hasUserPickUpList = hasUserPickUpList

				if (!basketIds.length) {
					uni.showToast({
						title: this.i18n.selectProduct,
						icon: "none"
					});
					return;
				}
			 	uni.setStorageSync("basketIds", JSON.stringify(basketIds));
				if (basketIds.length > 1 && shopSelectArr.length == 1) {
					// 单个店铺，且勾选的商品中有不同的配送方式
					if ((hasUserPickUpProdCount && hasUserPickUpProdCount != hasShopDeliveryProdCount) || (hasCityDeliveryProdCount && hasCityDeliveryProdCount != hasShopDeliveryProdCount)) {
						var animation = wx.createAnimation({
							duration: 600,
							timingFunction: 'ease'
						});
						this.animation = animation;
						this.showDeliveryWay = true // 底部选择配送方式弹窗
						setTimeout(() => {
							this.fadeIn();
						}, 200);
					} else {
						uni.navigateTo({
							url: '/pages/submit-order/submit-order?orderEntry=0' + '&dvyType=1' //dvyType 配送类型 1:快递 2:自提 3：无需快递 4:同城配送
						})
					}
				} else {
					uni.navigateTo({
						url: '/pages/submit-order/submit-order?orderEntry=0' + '&dvyType=1' //dvyType 配送类型 1:快递 2:自提 3：无需快递 4:同城配送
					});
				}

			},

			/**
			 * 单个店铺去结算
			 */
			toSubmitOrder: function(dvyType) {
				util.tapLog()
				let basketIds = []
				console.log('dvyType:', dvyType)
				if (dvyType == 1) { // 快递
					this.hasShopDeliveryList.forEach((el) => {
						basketIds.push(el.basketId)
					})
				} else if (dvyType == 4) { // 同城
					this.hasCityDeliveryList.forEach((el) => {
						basketIds.push(el.basketId)
					})
				} else if (dvyType == 2) { // 自提
					this.hasUserPickUpList.forEach((el) => {
						basketIds.push(el.basketId)
					})
				}
				wx.setStorageSync("basketIds", JSON.stringify(basketIds));
				uni.navigateTo({
					url: '/pages/submit-order/submit-order?orderEntry=0' + '&dvyType=' + dvyType
				});
			},


			// 关闭配送方式选择弹窗
			hideDeliveryFun() {
				util.tapLog()
				var that = this;
				var animation = wx.createAnimation({
					duration: 800,
					timingFunction: 'ease',
				})
				this.animation = animation
				that.fadeDown();
				setTimeout(function() {
					that.setData({
						showDeliveryWay: false
					})
				}, 680)
			},


			/**
			 * 全选
			 */
			handleCheckAll() {
				util.tapLog()
				var allChecked = this.allChecked;
				allChecked = !allChecked; //改变状态
				if(allChecked) {
					this.basketReqData = []
				}
				var shopCartList = this.shopCartList;
				shopCartList.forEach(shopCart => {
					shopCart.checked = allChecked;
					var shopCartItemDiscounts = shopCart.shopCartItemDiscounts;
					for (var i = 0; i < shopCartItemDiscounts.length; i++) {
						var cItems = shopCartItemDiscounts[i].shopCartItems;
						for (var j = 0; j < cItems.length; j++) {
							cItems[j].checked = allChecked;
							if (allChecked) {
								this.generateRequestData(cItems[j].basketId, allChecked)
							} else {
								this.basketReqData = []
							}
						}
					}
				});
				this.setData({
					allChecked: allChecked,
					shopCartList: shopCartList
				});
				this.loadBasketData()
			},

			/**
			 * 每一项的选择事件
			 */
			handleCheckProdItem (shopIndex, shopCartItemIndex, prodIndex, isCheck) {
				util.tapLog()
				var shopCartList = this.shopCartList;
				var shopCartItemDiscounts = shopCartList[shopIndex].shopCartItemDiscounts; // 获取购物车列表
				var checked = shopCartItemDiscounts[shopCartItemIndex].shopCartItems[prodIndex].checked; // 获取当前商品的选中状态
				shopCartItemDiscounts[shopCartItemIndex].shopCartItems[prodIndex].checked = !checked; // 改变状态
				const shopCartItem = shopCartItemDiscounts[shopCartItemIndex].shopCartItems[prodIndex]
				// 如果勾选的是套餐商品，需要变更该套餐下搭配商品的勾选状态
				if (shopCartItem.comboId) {
					shopCartItemDiscounts[shopCartItemIndex].shopCartItems.forEach(el => {
						el.checked = !checked
					})
				}
				this.generateRequestData(shopCartItem.basketId, !isCheck)
				this.loadBasketData()
			},

			/**
			 * 店铺勾选事件
			 */
			handleCheckShop (shopIndex) {
				util.tapLog()
				var shopCartList = this.shopCartList;
				var checked = shopCartList[shopIndex].checked; // 获取当前商品的选中状
				shopCartList[shopIndex].checked = !checked; // 改变状态
				shopCartList[shopIndex].shopCartItemDiscounts.forEach(shopCartItem => {
					var cItems = shopCartItem.shopCartItems;
					for (var j = 0; j < cItems.length; j++) {
						cItems[j].checked = !checked;
						this.generateRequestData(cItems[j].basketId, !checked)
					}
				});
				this.shopCartList = shopCartList
				this.loadBasketData()
			},

			/**
			 * 生成请求购物车的数据
			 * @param {Number} basketId 商品的购物车id
			 * @param {Boolean} isCheck 商品的选择状态
			 */
			generateRequestData(basketId, isCheck) {
				let matchingProdBasketsId = []
				for (let i = 0; i < this.shopCartList.length; i++) {
					const shopCart = this.shopCartList[i];
					for (let j = 0; j < shopCart.shopCartItemDiscounts.length; j++) {
						const cItems = shopCart.shopCartItemDiscounts[j];
						for (let k = 0; k < cItems.shopCartItems.length; k++) {
							const pItem = cItems.shopCartItems[k];
							if (cItems.comboId === pItem.comboId && pItem.parentBasketId === basketId) {
								matchingProdBasketsId.push(pItem.basketId)
							}
						}
					}
				}
				if (this.basketReqData.length) {
					this.setBasketsWhileNotEmpty(basketId, isCheck)
					// 套餐商品
					for (let i = 0; i < matchingProdBasketsId.length; i++) {
						this.setBasketsWhileNotEmpty(matchingProdBasketsId[i], isCheck)
					}
				} else {
					this.setBaskets(basketId, isCheck)
					// 套餐商品
					for (let i = 0; i < matchingProdBasketsId.length; i++) {
						this.setBaskets(matchingProdBasketsId[i], isCheck)
					}
				}
			},

			/**
			 * 生成请求购物车的数据: 当baskets不为空时
			 */
			setBasketsWhileNotEmpty (basketId, isCheck) {
				let index = this.basketReqData.findIndex(el => el.basketId === basketId)
				if (index > -1) {
					this.basketReqData[index].isCheck = isCheck
				} else {
					this.setBaskets(basketId, isCheck)
				}
			},

			/**
			 * 生成请求购物车的数据: 当basketId 中未包含当前修改的商品的购物车id时
			 */
			setBaskets (basketId, isCheck) {
				this.basketReqData.push({
					basketId: basketId,
					discountId: '',
					isCheck: isCheck
				})
			},

			/**
			 * 检查全选状态
			 */
			checkAllSelected() {
				var allChecked = true;
				var shopCartList = this.shopCartList;
				var flag = false;
				shopCartList.forEach(shopCart => {
					var shopChecked = true;
					var shopCartItemDiscounts = shopCart.shopCartItemDiscounts;
					for (var i = 0; i < shopCartItemDiscounts.length; i++) {
						var cItems = shopCartItemDiscounts[i].shopCartItems;
						for (var j = 0; j < cItems.length; j++) {
							if (!cItems[j].isCheck) {
								shopChecked = false;
								allChecked = false;
								flag = true;
							}
							cItems[j].checked = !!cItems[j].checked
						}
						if (flag) {
							break;
						}
					}
					shopCart.checked = shopChecked;
				})
				this.allChecked = allChecked
      	this.shopCartList = shopCartList
			},

			/**
			 * 计算购物车总额
			 */
			calTotalPrice() {
				var shopCartIds = [];
				this.basketReqData.forEach(item => {
					if (item.isCheck) {
						shopCartIds.push(item.basketId)
					}
				})
				var params = {
					url: "/p/shopCart/totalPay",
					method: "POST",
					data: shopCartIds,
					callBack: res => {
						this.finalMoney = res.finalMoney
						this.totalMoney = res.totalMoney
						this.subtractMoney = res.subtractMoney
						uni.hideLoading()
					}
				};
				http.request(params);
			},

			/**
			 * 修改购物车数量
			 * @param {Number} shopId 店铺
			 * @param {Number} comboId 套餐id
			 * @param {Object} prod 修改的商品项
			 * @param {Number} count 商品数量增量
			 */
			handleChangeNum(shopId, comboId, prod, count) {
				let basketId = 0
				let prodId = 0
				let mainProdSkuId = 0
				let matchingSkuIds = []
				if (comboId) {
					const shopCartList = this.shopCartList
					let shopIndex = shopCartList.findIndex(el => el.shopId === shopId)
					let shopCartItemDiscounts = shopCartList[shopIndex].shopCartItemDiscounts
					let shopCartItemIndex = shopCartItemDiscounts.findIndex(el => el.comboId === comboId)
					if (shopCartItemDiscounts[shopCartItemIndex].shopCartItems[shopCartItemIndex].comboCount === 1 && count === -1) {
						return
					}
					shopCartItemDiscounts[shopCartItemIndex].shopCartItems.forEach(el => {
						if (el.parentBasketId !== 0) {
							matchingSkuIds.push(el.skuId)
						} else {
							basketId = el.basketId
							prodId = el.prodId
							mainProdSkuId = el.skuId
						}
					})
				} else {
					this.currentProdItem = prod
				}
				const updateBasketParam = {
					basketId: comboId ? basketId : prod.basketId,
					comboId: comboId,
					count: count,
					matchingSkuIds: comboId ? matchingSkuIds : undefined,
					prodId: comboId ? prodId : prod.prodId,
					shopId: shopId,
					skuId: comboId ? mainProdSkuId : prod.skuId
				}
				this.updateBasket(updateBasketParam)
			},

			/**
			 * 更新购物车
			 * @param {Object} updateBasketParam 请求的参数
			 */
			updateBasket(updateBasketParam) {
				if (updateBasketParam.count == -1 && this.currentProdItem.prodCount == 1) {
					return
				}
				const params = {
					url: updateBasketParam.count === 0 ? "/p/shopCart/changeShopCartSku" : "/p/shopCart/changeItem",
					method: updateBasketParam.count === 0 ? "PUT" : "POST",
					data: updateBasketParam,
					callBack: res => {
						if (updateBasketParam.comboId) {
							this.loadBasketData()
						} else {
							const discountData = {
								basketId: updateBasketParam.basketId,
								discountId: updateBasketParam.discountId
							}
							this.loadBasketData(discountData)
						}
						uni.hideLoading()
						if (updateBasketParam.count && updateBasketParam.count >= 1) {
							const flowAnalysisLogDto = uni.getStorageSync('flowAnalysisLogDto')
							flowAnalysisLogDto.bizData = updateBasketParam.prodId
							flowAnalysisLogDto.bizType = 0
							uni.setStorageSync('flowAnalysisLogDto', flowAnalysisLogDto)
							util.tapLog(4, {
                orderNumbers: '',
                isPayRes: '',
                prodNum: updateBasketParam.count,
              })
						}
						this.showSkuPop = false
					}
				};
				http.request(params);
			},

			/**
			 * 修改商品的sku
			 */
			handleSetSku(sku) {
				const currentProdItem = this.currentProdItem
				const params = {
					basketId: currentProdItem.basketId,
					comboId: currentProdItem.comboId,
					count: 0,
					shopId: currentProdItem.shopId,
					prodId: currentProdItem.prodId,
					skuId: sku.skuId,
				}
				this.updateBasket(params)
			},
			
			/**
			 * 删除购物车商品
			 */
			handleDeletProd() {
				util.tapLog()
				var shopCartList = this.shopCartList;
				var basketIds = [];
				shopCartList.forEach(shopCart => {
					var shopCartItemDiscounts = shopCart.shopCartItemDiscounts;
					for (var i = 0; i < shopCartItemDiscounts.length; i++) {
						var cItems = shopCartItemDiscounts[i].shopCartItems;
						for (var j = 0; j < cItems.length; j++) {
							if (cItems[j].checked) {
								basketIds.push(cItems[j].basketId);
							}
						}
					}
				});
				if (basketIds.length == 0) {
					uni.showToast({
						title: this.i18n.selectProduct,
						icon: "none"
					});
				} else {
					this.showPriDet()
					this.hideModalFun()
					this.hidePriModal = true
					uni.showModal({
						title: '',
						content: this.i18n.deleteProductTips,
						cancelText: this.i18n.cancel,
						confirmText: this.i18n.confirm,
						confirmColor: "#eb2444",
						success: (res) => {
							if (res.confirm) {
								uni.showLoading({
								// #ifndef MP-TOUTIAO
								mask: true
								// #endif
								});
								var params = {
									url: "/p/shopCart/deleteItem",
									method: 'POST',
									data: basketIds,
									callBack: res2 => {
										this.basketReqData = []
										uni.hideLoading()
										this.loadBasketData();
									}
								};
								http.request(params);
							}
						}
					});
				}
			},

			/**
			 * 点击满减活动 弹窗
			 */
			onChooseDiscount(basketId, discountId, discounts, checked) {
				util.tapLog()
				this.reDraw = false
				this.setData({
					prodDiscounts: discounts,
					hideModal: false,
					discountId: discountId,
					basketId: basketId,
					curBasketItemCheck: Boolean(checked), // 当前展示满减活动的商品是否已选中
				});
				var animation = wx.createAnimation({
					duration: 600,
					timingFunction: 'ease'
				});
				this.animation = animation;
				this.reDraw = true
				setTimeout(() => {
					this.fadeIn();
				}, 200);
			},
			radioChange(e) {
				util.tapLog()
				this.loadBasketData({
					basketId: this.basketId,
					discountId: e.detail.value,
					isCheck: this.curBasketItemCheck
				});
				this.fadeDown()
				setTimeout(() => {
					this.setData({
						hideModal: true
					});
				}, 500)
				this.reDraw = false
			},

			/**
			 * 根据商品id获取skuList
			 * @param {Object} prod 当前要修改的选项
			 */
			getSkuListByProdId(prod) {
				let comboList = []
				if (prod.comboId) {
					const shopCartList = this.shopCartList
					let shopIndex = shopCartList.findIndex(el => el.shopId === prod.shopId)
					let shopCartItemDiscounts = shopCartList[shopIndex].shopCartItemDiscounts
					let shopCartItemIndex = shopCartItemDiscounts.findIndex(el => el.comboId === prod.comboId)
					shopCartItemDiscounts[shopCartItemIndex].shopCartItems.forEach(el => {
						comboList.push({
							basketId: el.basketId,
							skuId: el.skuId,
							prodId: el.prodId
						})
					})
					prod.comboList = comboList
				}
				this.currentProdItem = prod
				const params = {
					url: prod.comboId ? '/combo/skuList' : '/prod/skuList',
					method: 'GET',
					data: {
						comboId: prod.comboId,
						prodId: prod.prodId
					},
					callBack: res => {
						this.currentProdItem.skuList = res
						this.showSkuPop = true
					},
					errCallBack: errMsg => {
						console.log(errMsg)
					}
				}
				http.request(params)
			},

			/**
			 * 隐藏sku弹窗
			 */
			handleCloseSkuPop() {
				this.showSkuPop = false
			},

			// 隐藏遮罩层
			hideModalFun: function() {
				util.tapLog()
				this.reDraw = false
				var that = this;
				var animation = wx.createAnimation({
					duration: 800,
					timingFunction: 'ease'
				});
				this.animation = animation;
				that.fadeDown();
				setTimeout(function() {
					that.setData({
						hideModal: true
					});
				}, 720);
			},
			//动画集
			fadeIn: function() {
				this.animation.translateY(0).step();
				this.setData({
					animationData: this.animation.export() //动画实例的export方法导出动画数据传递给组件的animation属性

				});
			},
			fadeDown: function() {
				this.animation.translateY(300).step();
				this.setData({
					animationData: this.animation.export()
				});
			},

			/**
			 * 跳转到商品详情
			 */
			toProdPage(prodId) {
				util.tapLog()
				this.$Router.push({
					path: '/pages/prod/prod',
					query: {
						prodid: prodId
					}
				})
			},


			/**
			 * 金额明细弹窗
			 */
			showPriDet() {
				util.tapLog()
				var that = this;
				if (this.hidePriModal == true) {
					this.hidePriModal = false
					var animation = wx.createAnimation({
						duration: 500,
						timingFunction: 'ease',
					})
					this.animation = animation
					setTimeout(function() {
						that.fadeIn();
					}, 100)
				} else if (this.hidePriModal == false) {
					this.hidePriModalFun()
				}
			},

			hidePriModalFun() {
				util.tapLog()
				var that = this;
				var animation = wx.createAnimation({
					duration: 800,
					timingFunction: 'ease',
				})
				this.animation = animation
				that.fadeDown();
				setTimeout(function() {
					that.setData({
						hidePriModal: true
					})
				}, 680)
			},

			/**
			 * 满减信息处理
			 */
			parseDiscountMsg(discountRule, needAmount, discount, lang) {
				if (discountRule == 0) {
					return lang == 'zh_CN' ? '购满' + needAmount + '元减' + discount + '元' : 'Over ' + needAmount + ' minus ' + discount;
				} else if (discountRule == 1) {
					return lang == 'zh_CN' ? '购满' + needAmount + '件减' + discount + '元' : discount + ' less for ' + needAmount + ' pieces' ;
				} else if (discountRule == 2) {
					return lang == 'zh_CN' ? '购满' + needAmount + '元打' + discount + '折' : discount + '% off over ' + needAmount;
				} else if (discountRule == 3) {
					return lang == 'zh_CN' ? '购满' + needAmount + '件打' + discount + '折' : discount + '% off over ' + needAmount + ' pieces' ;
				} else {
					return '';
				}
			},

			/**
			 * 获取满减活动名称
			 */
			getCurrDiscountName(discountId, discounts,lang) {
				for (var i = 0; i < discounts.length; i++) {
					if (discounts[i].discountId == discountId) {
						return discounts[i].discountName;
					}
				}
				return lang == 'zh_CN' ? '不参与促销' : 'Not participating in promotion';
			}
		}
	};
</script>
<style scoped>
	/* pages/basket/basket.wxss */


.uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked{
	background: var(--primary-color) !important;
}

.container {
  width: 100%;
  height: auto;
  min-height: 100vh;
  padding-bottom: 112rpx;
  overflow: auto;
}

/*
.prod-list {
  margin-bottom: 120px;
} */

.shop-item {
  margin-top: 20rpx;
  background: #fff;
}

/* 店铺 */

.choose-btn {
  display: inline-block;
}

.shop-item .head-shop {
  padding: 20rpx;
  background: #fff;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #eee;
}

.shop-item .head-shop image {
  width: 100%;
  height: 100%;
  display: block;
}


.shop-item .head-shop .shop-box {
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: 8rpx;
}

.shop-item .head-shop .shop-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.shop-item .head-shop .shop-name {
  font-size: 26rpx;
  font-weight: 600;
  position: relative;
  padding-right: 16rpx;
}
.shop-name span{
	position: absolute;
	right: 0;
	top: 48%;
	display: block;
	width: 8rpx;
	height: 8rpx;
	content: " ";
	font-size: 0;
	border-top: 4rpx solid #333;
	border-right: 4rpx solid #333;
	transform: rotate(45deg) translateY(-50%);
}
.shop-item .prod-block .discount-tips {
  /* border-top: 2rpx solid #eee; */
  padding: 20rpx 16rpx;
  height: 40rpx;
  line-height: 40rpx;
  display: flex;
  justify-content: space-between;
}


.shop-item .prod-block .discount-tips .combo-item {
  display: flex;
  max-width: 50%;
}
.shop-item .prod-block .discount-tips .combo-item .combo-name {
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shop-item .prod-block .discount-tips .combo-item .text-block {
  display: flex;
  margin-right: auto;
  width: auto;
  padding: 0rpx 6rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  background: var(--primary-color);
  color: #fff;
  position: relative;
}

.shop-item .prod-block .discount-tips .text-block::before{
/*  position: absolute;
  left: 14rpx;
  bottom: -16rpx;
  content: "";
  display: block;
  width: 0;
  height: 0;
  border: 8rpx solid transparent;
  border-top: 8rpx solid #e43130; */
}

.shop-item .prod-block .discount-tips .text-list {
  font-size: 24rpx;
  margin-left: 10rpx;
  font-weight: 600;
}

.shop-item .prod-block .discount-tips .text-list.reduce-amount {
  font-weight: normal;
  color: #e43130;
	padding-left: 20rpx;
}

.shop-item .prod-block .goods-item {
  position: relative;
}

.shop-item .prod-block.discount .goods-item::after{
 /* position: absolute;
  left: 36rpx;
  top: 0;
  bottom: 0; */
	/* #ifdef MP-TOUTIAO */
	/* left: 44rpx; */
	/* #endif */
 /* width: 2rpx;
  height: auto;
  content: "";
  border-left: 2rpx dashed #eee;
  z-index: 0; */
}

.shop-item .prod-block .item {
  background: #fff;
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-top: 2rpx solid #f2f2f2;
  z-index: 2;
}

.shop-item .prod-block .item .btn {
  align-self: center;
  z-index: 3;
}
.shop-item .prod-block .item .btn.hide {
  z-index: -1;
}

.shop-item .prod-block .item .prodinfo {
  position: relative;
  color: #999;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* .shop-item .prod-block .item .prodinfo .pic {
  text-align: center;
  width: 200rpx;
  height: 200rpx;
  line-height: 180rpx;
  font-size: 0;
}

.shop-item .prod-block .item .prodinfo .pic image {
  width: 100%;
  height: 100%;
} */

.prod-list .item .prodinfo .info-row{
  display: flex;
}

.prod-list .item .prodinfo .pic {
  text-align: center;
  width: 180rpx;
  height: 180rpx;
  line-height: 180rpx;
  font-size: 0;
}

.prod-list .item .pic image {
  max-width: 100%;
  max-height: 100%;
  /* border-radius: 8rpx; */
  vertical-align: middle;
}

.shop-item .prod-block .item .prodinfo .opt {
  margin-left: 20rpx;
  flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.shop-item .prod-block .item .prodinfo .opt .prod-name {
  font-size: 28rpx;
  color: #333;
  line-height: 36rpx;
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.prodinfo .opt .prod-name {
  color: #333;
  max-height: 72rpx;
  line-height: 36rpx;
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.prodinfo .opt .prod-discount {
  color: var(--primary-color);
  display: block;
  font-size: 24rpx;
}
 .prod-info-text {
  font-size: 24rpx;
  font-family: arial;
  position: relative;
  padding: 10rpx 50rpx 10rpx 10rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
	max-width: 240rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: inline-block;
	word-break: keep-all;
}

.prodinfo .opt .prod-info-text::after {
  content: "";
  width: 12rpx;
  height: 12rpx;
  border: solid #999;
  border-width: 1rpx 0 0 1rpx;
  transform: translate(-50%, -50%) rotate(225deg);
  position: absolute;
  right: 10rpx;
  top: 22rpx;
}

.prodinfo .opt .prod-info-text.empty-n {
  padding: 0;
}

.prod-info-text:before {
  border-top: 5px solid #aaa;
}

.prod-info-text:after {
  border-top: 5px solid #f9f9f9;
  top: 9px;
}


.prodinfo .opt .prod-discount .prod-discount-tit {
  position: relative;
  padding: 10rpx 40rpx 10rpx 10rpx;
  background: var(--light-background);
  border-radius: 8rpx;
	max-width: 240rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: inline-block;
	word-break: keep-all;
}

.prodinfo .opt .prod-discount .prod-discount-tit::after {
  content: "";
  width: 12rpx;
  height: 12rpx;
  border: solid var(--primary-color);
  border-width: 1rpx 0 0 1rpx;
  transform: translate(-50%, -50%) rotate(225deg);
  position: absolute;
  right: 10rpx;
  top: 22rpx;
}

.shop-item .prod-block .item .prodinfo .opt .price-count {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.shop-item .prod-block .item .prodinfo .opt .price-count .combo-count {
  font-size: 28rpx;
  margin-left: 20rpx;
  color: #333;
}

/* 加减框 */

.shop-item .prod-block .item .prodinfo .opt .m-numSelector {
  display: flex;
  box-sizing: border-box;
  border: 2rpx solid #eee;
  border-radius: 4rpx;
}

.shop-item .prod-block .item .prodinfo .opt .m-numSelector .minus,
.shop-item .prod-block .item .prodinfo .opt .m-numSelector .plus {
  height: 56rpx;
  width: 56rpx;
  box-sizing: border-box;
  position: relative;
}

.shop-item .prod-block .item .prodinfo .opt .m-numSelector input {
  width: 56rpx;
  height: 56rpx;
  text-align: center;
  color: #333;
  border-left: 2rpx solid #eee;
  border-right: 2rpx solid #eee;
  font-size: 24rpx;
  font-family: arial;
}

.shop-item .prod-block .item .prodinfo .opt .m-numSelector .minus::before,
.shop-item .prod-block .item .prodinfo .opt .m-numSelector .plus::before,
.shop-item .prod-block .item .prodinfo .opt .m-numSelector .plus::after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  content: ' ';
  width: 22rpx;
  height: 2rpx;
  background-color: #7f7f7f;
}

.shop-item .prod-block .item .prodinfo .opt .m-numSelector .plus::after {
  transform: rotate(90deg);
}

.shop-item .prod-block .item .prodinfo .opt .m-numSelector:not(.disabled) .minus:not(.disabled):active,
.shop-item .prod-block .item .prodinfo .opt .m-numSelector:not(.disabled) .plus:not(.disabled):active {
  opacity: 0.6;
}

/*checkbox 选项框大小  */

checkbox .wx-checkbox-input {
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
}

/*checkbox选中后样式  */

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background: #e43130;
  border-color: #e43130;
}

/*checkbox选中后图标样式  */

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  text-align: center;
  font-size: 20rpx;
  color: #fff;
  background: transparent;
  transform: translate(-50%, -50%) scale(1);
  -webkit-transform: translate(-50%, -50%) scale(1);
}


/** 底部按钮 */

.cart-footer {
  position: fixed;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  height: 110rpx;
  border-top: 2rpx solid #f4f4f4;
  z-index: 99;
  background: #fff;
  padding: 0 10rpx 0 20rpx;
	box-sizing: border-box;
  bottom: 50px;
  bottom: calc(constant(safe-area-inset-bottom) + 50px);
  bottom: calc(env(safe-area-inset-bottom) + 50px);
}

/*  #ifdef  MP || APP-PLUS */
	.cart-footer {
    bottom: 50px;
    bottom: calc(constant(safe-area-inset-bottom) + 50px);
    bottom: calc(env(safe-area-inset-bottom) + 50px);
	}

/*  #endif  */

.cart-footer .all {
  font-size: 28rpx;
}

.cart-footer .all label {
  display: flex;
  align-items: center;
}

.cart-footer .del {
  color: #e43130;
  font-size: 24rpx;
  margin-left: 14rpx;
}

.cart-footer .total {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 40rpx;
	justify-content: flex-end;
}
.cart-footer .total .total-con .finally {
  text-align: center;
}
.cart-footer .total .total-con .finally .blod {
  font-weight: bold;
  font-size: 28rpx;
}

.cart-footer .total .total-con .finally .price  {
  font-size: 30rpx;
}

.cart-footer .total .total-msg {
  font-size: 22rpx;
  text-align: center;
}

.cart-footer .total .arrow-icon {
  width: 40rpx;
  height: 20rpx;
  margin-left: 10rpx;
}

.cart-footer .total .arrow-icon image {
  display: block;
  width: 100%;
  height: 100%;
}

.cart-footer .settle {
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 50rpx;
  background: var(--primary-color);
  color: #fff;
  border-radius: 70rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}

/** end 底部按钮 */

/* 满减活动弹框 */

/*模态框*/

.modals {
  position: fixed;
  z-index: 1999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.modals-bottom-dialog {
  position: fixed;
  z-index: 98;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.select-distribution {
  z-index: 100;
}

.modals-cancel {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.bottom-dialog-body {
  position: absolute;
  z-index: 10001;
  bottom: 95px;
  left: 0;
  right: 0;
  height: 500rpx;
  background-color: #fff;
}
.bottom-dialog-box {
  position: absolute;
  z-index: 10001;
  bottom: 90rpx;
  left: 0;
  right: 0;
  height: 500rpx;
  background-color: #fff;
}

.radius {
  border-radius: 20rpx 20rpx 0 0;
}

.select-distribution .bottom-dialog-box {
  position: absolute;
  z-index: 10001;
  left: 0;
  right: 0;
  bottom: 50px;
  height: 65%;
  background-color: #fff;
  overflow: auto;
}

/*动画前初始位置*/

.bottom-pos {
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
}

.discount-title {
  font-size: 30rpx;
  margin-bottom: 20rpx;
  padding:26rpx;
  background: #f1f1f1;
  text-align: center;
  position: relative;
}

.radio-group {
  display: block;
  width: 100%;
  height: 350rpx;
  overflow-y:auto;
	padding: 0 20rpx;
	box-sizing: border-box;
}

.radio-item {
	display: flex;
	width: 100%;
	margin-bottom: 20rpx;
}
.radio-item .radio-text {
	font-size: 28rpx;
	word-break: break-all;
	max-width: 100%;
}

.discount-radio label{
  display: inline-block;
  height:60rpx;
  line-height: 60rpx;
}
.discount-radio{
  font-size: 28rpx;
  height:60rpx;
  line-height: 60rpx;
  padding:0 20rpx
}

/* 购物车为空 */
.empty {
  margin-top: 100rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 100rpx;
  height: 100rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 2em;
}

/* 金额明细 */
.price-detail {
  max-height: 220rpx;
  overflow-y: scroll;
  padding: 10rpx 20rpx;
}
.price-detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 3em;
  font-size: 28rpx;
  border-bottom: 1px solid #f2f2f2;
}
.price-detail-item:last-child {
  border: 0;
}
.det-num {
  color: #e43130;
}

/* 商品配送方式选择 */
.distribution-prods {
  padding: 0 30rpx 30rpx;
  background: #fff;
}

.distribution-prods .des {
  color: #999;
  font-size: 24rpx;
  margin-top: 30rpx;
}

.distribution-prods .p-item {
  margin-top: 30rpx;
  background: #f9f9f9;
  border-radius: 10rpx;
  padding: 30rpx;
}

.distribution-prods .p-item .p-tit {
  font-size: 28rpx;
  font-weight: 600;
}

.distribution-prods .p-item .p-con {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx 0;
}

.distribution-prods .p-item .p-con .prods-item {
  max-width: 480rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 28rpx;
}

.distribution-prods .p-item .p-con .prods-item .pic {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  /* font-size: 0; */
  margin-right: 20rpx;
  display: inline-block;
}

.distribution-prods .p-item .p-con .prods-item .pic .prod-count {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 20rpx;
  padding: 0 10rpx;
  height: 30rpx;
  line-height: 30rpx;
  text-align: center;
  background: rgba(0, 0, 0, .3);
  border-radius: 12rpx 0 12rpx 0;
  color: #fff;
}

.distribution-prods .p-item .p-con .prods-item .pic image {
  width: 100%;
  height: 100%;
}

.distribution-prods .p-item .p-con .p-btn {
  background: #e43130;
  padding: 8rpx 30rpx;
  color: #fff;
  font-size: 24rpx;
  border-radius: 40rpx;
}

.distribution-prods .p-item .p-total {
  display: flex;
  align-items: baseline;
  font-size: 24rpx;
}

.distribution-prods .p-item .p-total .num {
  color: #e43130;
  font-family: arial;
  margin: 0 8rpx;
}


/* #ifdef APP-PLUS || MP-WEIXIN */
.select-distribution .bottom-dialog-box {
	bottom: 0;
}
.pri-modal .bottom-dialog-box{
	bottom: 90rpx;
}
.bottom-dialog-body{
	bottom: 0;
}
/* #endif */


/* 赠品 */
.gift-con {
  padding: 10rpx;
}
.gift-item {
  padding-left: 190rpx;
  box-sizing: border-box;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
  overflow: hidden;
  width: 700rpx;
}
.gift-item:last-child {
  margin-bottom: 0;
}
.gift-name {
  flex: 1;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 82%;
}
.gift-name .gift-sku{
  color: #999;
  margin-left: 20rpx;
}
.gift-count {
  display: flex;
  margin-left: auto;
  color: #333;
  align-items: center;
}
.gift-count image {
  width: 18rpx;
  height: 18rpx;
}

/* 套装 */
.combo-tips-bar {
  display: flex;
  font-size: 24rpx;
  padding: 20rpx 16rpx;
  align-items: center;
}
.combo-sign {
  margin-right: 12rpx;
  border-radius: 4rpx;
  padding: 0rpx 6rpx;
  font-size: 20rpx;
  background: #e43130;
  color: #fff;
  position: relative;
}
.combo-sign::before{
  position: absolute;
  left: 14rpx;
  bottom: -16rpx;
  content: "";
  display: block;
  width: 0;
  height: 0;
  border: 8rpx solid transparent;
  border-top: 8rpx solid #e43130;
}
.combo-name {
  font-weight: bold;
}
.combo-reduce {
  color: #e43130;
  margin-left: auto;
}

/* 套装商品 */
.combo-prod-con {
}
.combo-prod-item {
  display: flex;
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
  border-top: 2rpx solid #eee;
}
.combo-prod-img {
  width: 180rpx;
  height: 180rpx;
  margin: 0 20rpx 0 50rpx;
}
.combo-prod-info {
  flex: 1;
}
.combo-prod-name {
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.combo-prod-sku {
  display: inline-block;
  color: #999;
  background: #f9f9f9;
  padding: 0 10rpx 0 10rpx;
  border-radius: 4rpx;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  font-size: 24rpx;
  margin: 20rpx 0 40rpx 0;
  position: relative;
}
.combo-prod-price {
  font-size: 24rpx;
}
.combo-prod-price-amount {
  color: #e43130;
}
.combo-prod-price-amount .big-price {
  font-size: 32rpx;
}
.combo-prod-price-count {
  margin-left: 20rpx;
}

/* 套装底栏 */
.combo-oper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 20rpx 20rpx 80rpx;
  position: relative;
  border-bottom: 2rpx solid #eee;
}
.combo-oper::after {
  position: absolute;
  left: 18px;
  top: 0;
  bottom: 0;
  width: 1px;
  height: auto;
  content: "";
  border-left: 1px dashed #eee;
  z-index: 0;
}
.combo-price {
  color: #e43130;
  font-size: 24rpx;
  font-weight: bold;
}
.combo-amount {
  font-size: 32rpx;
}
.combo-count {

}

.combo-count .m-numSelector {
  display: flex;
  box-sizing: border-box;
  border: 2rpx solid #eee;
  border-radius: 4rpx;
}

.combo-count .m-numSelector .minus,
.combo-count .m-numSelector .plus {
  height: 56rpx;
  width: 56rpx;
  box-sizing: border-box;
  position: relative;
}

.combo-count .m-numSelector input {
  width: 56rpx;
  height: 56rpx;
  text-align: center;
  color: #333;
  border-left: 2rpx solid #eee;
  border-right: 2rpx solid #eee;
  font-size: 24rpx;
  font-family: arial;
}

.combo-count .m-numSelector .minus::before,
.combo-count .m-numSelector .plus::before,
.combo-count .m-numSelector .plus::after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  content: ' ';
  width: 22rpx;
  height: 2rpx;
  background-color: #7f7f7f;
}

.combo-count .m-numSelector .plus::after {
  transform: rotate(90deg);
}

.combo-count .m-numSelector:not(.disabled) .minus:not(.disabled):active,
.combo-count .m-numSelector:not(.disabled) .plus:not(.disabled):active {
  opacity: 0.6;
}

</style>

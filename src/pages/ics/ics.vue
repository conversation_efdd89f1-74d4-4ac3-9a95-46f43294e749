<template>
  <view>
    <web-view v-if="urlReady" :src="url"></web-view>
    <view v-else class="loading">加载中...</view>
  </view>
</template>

<script>
import { Base64 } from '../../js_sdk/base64/base64.js'
import config from '@/utils/config'

export default {
  name: "ics-service",
  data() {
    return {
      url: "",
      urlReady: false
    };
  },
  methods: {
    async compressToBase64(input) {
      try {
        return Base64.encode(input)
      } catch (error) {
        console.error('Base64编码失败:', error);
        return '';
      }
    },
    async initData() {
      const icsUrL = config.icsUrl
      try {
        const originalWenetAccount = wx.getStorageSync("originalWenetAccount");
        let uid = "";
        if (originalWenetAccount?.person) {
          uid = originalWenetAccount.person?.uid || "";
        }
        const encode = await this.compressToBase64(uid);
        this.url = `${icsUrL}?userId=${encode}`;
        console.log(this.url,'----this.url')
        this.urlReady = true; // 数据准备好后再显示WebView
      } catch (error) {
        console.error('初始化失败:', error);
        // 可以设置一个默认URL或错误页面
        this.url = icsUrL;
        this.urlReady = true;
      }
    },
  },
  computed: {},
  mounted() {
    this.initData();
  },
};
</script>

<style scoped></style>

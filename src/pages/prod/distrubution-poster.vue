<script>
import config from '@/utils/config'
import Qr from '@/utils/wxqrcode.js'
import NButton from '@/components/n-button'
import http from '@/utils/http'
export default {
  name: 'DistrubutionPoster',
  components: {
    NButton
  },
  options: {
    virtualHost: true
  },
  props: {
    prodId: {
      type: [String, Number],
      required: true
    },
    cardNo: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    prodName: {
      type: String,
      default: ''
    },
    prodPrice: {
      type: String,
      default: ''
    },
    prodImage: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      wxCode: '',
      posterUrl: '',
      // 使用一个确定存在的静态图片作为背景，方便调试
      posterTemplateUrl: config.iconDomain + 'images/other/share-bg2.png',
      templateImage: null, // 预加载的背景图
      prodLocalImage: null, // 预加载的商品图片
      qrLocalImage: null, // 预加载的二维码图片
      loading: false,
      // 海报配置，方便后续修改
      posterConfig: {
        width: 750, // 海报宽度
        height: 1200, // 海报高度
        background: '#ffffff', // 背景色
        elements: {
          template: {
            x: 0,
            y: 0,
            width: 750,
            height: 1200
          },
          prodImage: {
            x: 50,
            y: 50,
            width: 650,
            height: 650,
            radius: 12
          },
          price: {
            x: 50,
            y: 765,
            fontSize: 48,
            color: '#FF2900',
            symbol: '¥'
          },
          prodName: {
            x: 50,
            y: 825,
            width: 300,
            fontSize: 32,
            color: '#000000',
            lineHeight: 48,
            maxLines: 4
          },
          qrcode: {
            x: 450,
            y: 900,
            width: 250,
            height: 250,
          }
        }
      },
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.genWeixinCode()
      }
    }
  },
  methods: {
    genWeixinCode() {
      this.loading = true
      
      // #ifdef H5 || APP-PLUS
      let code = `${config.domainAddress}/pages/prod/prod?prodid=${this.prodId}&cardno=${this.cardNo}`
      this.wxCode = Qr.createQrCodeImg(code, {size: 250});
      // 先预加载图片资源
      this.preloadAllImages().then(() => {
        this.generatePoster()
      }).catch(err => {
        console.error('加载图片资源失败', err)
        uni.hideLoading()
        this.loading = false
        uni.showToast({
          title: '加载图片资源失败',
          icon: 'none'
        })
      })
      // #endif
      
      // #ifdef MP-WEIXIN
      const content = JSON.stringify({ shareProdId: this.prodId, cardNo: this.cardNo })
      uni.showLoading({ title: '生成中...' })
      http.request({
        url: '/qrcodeTicket/miniQrCode',
        method: 'GET',
        responseType: 'arraybuffer',
        data: { content, type: 2 },
        callBack: res => {
          // 将arraybuffer转为base64
          const base64Data = uni.arrayBufferToBase64(res)
          
          // 在小程序环境下，我们需要先将base64转为临时文件
          this.base64ToTempFilePath(base64Data)
            .then(tempFilePath => {
              console.log('二维码临时文件生成成功', tempFilePath)
              this.wxCode = tempFilePath
              return this.preloadAllImages()
            })
            .then(() => {
              this.generatePoster()
            })
            .catch(err => {
              console.error('处理二维码失败', err)
              uni.hideLoading()
              this.loading = false
              uni.showToast({
                title: '生成二维码失败',
                icon: 'none'
              })
            })
        },
        errCallBack: () => {
          uni.hideLoading()
          this.loading = false
          uni.showToast({
            title: '生成二维码失败',
            icon: 'none'
          })
        }
      })
      // #endif
    },
    
    // base64转临时文件路径 (仅小程序环境使用)
    base64ToTempFilePath(base64Data) {
      return new Promise((resolve, reject) => {
        // 创建文件系统管理器
        const fs = wx.getFileSystemManager()
        // 生成临时文件路径
        const tempFilePath = `${wx.env.USER_DATA_PATH}/qrcode_${Date.now()}.jpg`
        
        // 将base64写入临时文件
        fs.writeFile({
          filePath: tempFilePath,
          data: base64Data,
          encoding: 'base64',
          success: () => {
            resolve(tempFilePath)
          },
          fail: err => {
            console.error('写入二维码文件失败', err)
            reject(err)
          }
        })
      })
    },
    
    // 预加载所有需要的图片资源
    preloadAllImages() {
      return new Promise(async (resolve, reject) => {
        try {
          // 加载背景图
          const bgRes = await this.preloadImage(this.posterTemplateUrl)
          this.templateImage = bgRes.path
          
          // 加载商品图片
          if (this.prodImage) {
            try {
              const prodRes = await this.preloadImage(this.prodImage)
              this.prodLocalImage = prodRes.path
            } catch (err) {
              console.error('商品图片加载失败', err)
              // 商品图片加载失败不阻止流程
            }
          }
          
          // 二维码图片处理
          if (this.wxCode) {
            // #ifdef H5 || APP-PLUS
            // H5或APP环境，如果是base64格式，直接使用
            if (this.wxCode.indexOf('data:image') === 0) {
              this.qrLocalImage = this.wxCode
            } else {
              // 非base64格式，尝试预加载
              try {
                const qrRes = await this.preloadImage(this.wxCode)
                this.qrLocalImage = qrRes.path
              } catch (err) {
                console.error('二维码图片加载失败', err)
                this.qrLocalImage = this.wxCode
              }
            }
            // #endif
            
            // #ifdef MP-WEIXIN
            // 小程序环境，此时wxCode应该是临时文件路径
            try {
              const qrRes = await this.preloadImage(this.wxCode)
              this.qrLocalImage = qrRes.path
            } catch (err) {
              console.error('二维码图片加载失败', err)
              // 如果加载失败，尝试直接使用临时路径
              this.qrLocalImage = this.wxCode
            }
            // #endif
          }
          
          resolve()
        } catch (err) {
          reject(err)
        }
      })
    },
    
    // 预加载单个图片
    preloadImage(src) {
      return new Promise((resolve, reject) => {
        uni.getImageInfo({
          src: src,
          success: res => {
            console.log('图片加载成功', src, res)
            resolve(res)
          },
          fail: err => {
            console.error('图片加载失败', src, err)
            reject(err)
          }
        })
      })
    },
    
    // 生成海报
    async generatePoster() {
      try {
        // 创建canvas上下文
        const ctx = uni.createCanvasContext('posterCanvas', this)
        const config = this.posterConfig
        
        // 绘制白色背景
        ctx.setFillStyle(config.background)
        ctx.fillRect(0, 0, config.width, config.height)
        
        // 绘制模板背景
        console.log('templateImage', this.templateImage)
        if (this.templateImage) {
          await this.drawImage(ctx, this.templateImage, 
            config.elements.template.x, 
            config.elements.template.y, 
            config.elements.template.width, 
            config.elements.template.height
          )
        }
        
        // 绘制商品图片
        if (this.prodLocalImage) {
          await this.drawRoundImage(
            ctx, 
            this.prodLocalImage, 
            config.elements.prodImage.x, 
            config.elements.prodImage.y, 
            config.elements.prodImage.width, 
            config.elements.prodImage.height,
            config.elements.prodImage.radius
          )
        }
        
        // 绘制商品名称
        if (this.prodName) {
          this.drawMultiLineText(
            ctx, 
            this.prodName, 
            config.elements.prodName.x, 
            config.elements.prodName.y, 
            config.elements.prodName.width,
            config.elements.prodName.fontSize,
            config.elements.prodName.color,
            config.elements.prodName.lineHeight,
            config.elements.prodName.maxLines
          )
        }
        
        // 绘制商品价格
        if (this.prodPrice) {
          ctx.setFontSize(config.elements.price.fontSize)
          ctx.setFillStyle(config.elements.price.color)
          ctx.fillText(
            config.elements.price.symbol + this.prodPrice, 
            config.elements.price.x, 
            config.elements.price.y
          )
        }
        
        // 绘制二维码
        if (this.qrLocalImage) {
          await this.drawImage(
            ctx, 
            this.qrLocalImage, 
            config.elements.qrcode.x, 
            config.elements.qrcode.y, 
            config.elements.qrcode.width, 
            config.elements.qrcode.height
          )
        }
        
        // 绘制到canvas
        ctx.draw(false, () => {
          setTimeout(() => {
            this.canvasToTempFilePath()
          }, 200)
        })
      } catch (e) {
        console.error('生成海报失败', e)
        uni.hideLoading()
        this.loading = false
        uni.showToast({
          title: '生成海报失败',
          icon: 'none'
        })
      }
    },
    
    // 绘制图片
    drawImage(ctx, src, x, y, width, height) {
      return new Promise((resolve, reject) => {
        if (!src) {
          console.warn('图片源为空，跳过绘制', {x, y, width, height})
          resolve()
          return
        }
        
        console.log('绘制图片', {src: src.substring(0, 30) + '...', x, y, width, height})
        try {
          ctx.drawImage(src, x, y, width, height)
          resolve()
        } catch (err) {
          console.error('绘制图片失败', err, {src: src.substring(0, 30) + '...', x, y, width, height})
          // 绘制失败不影响整体流程
          resolve()
        }
      })
    },
    
    // 绘制圆角图片
    drawRoundImage(ctx, src, x, y, width, height, radius) {
      return new Promise((resolve, reject) => {
        if (!src) {
          console.warn('图片源为空，跳过绘制圆角图片', {x, y, width, height, radius})
          resolve()
          return
        }
        
        console.log('绘制圆角图片', {src: src.substring(0, 30) + '...', x, y, width, height, radius})
        try {
          // 保存当前环境
          ctx.save()
          // 创建圆角路径
          this.createRoundedRect(ctx, x, y, width, height, radius)
          // 剪切路径
          ctx.clip()
          // 绘制图片
          ctx.drawImage(src, x, y, width, height)
          // 恢复环境
          ctx.restore()
          resolve()
        } catch (err) {
          console.error('绘制圆角图片失败', err, {src: src.substring(0, 30) + '...', x, y, width, height, radius})
          // 绘制失败不影响整体流程
          resolve()
        }
      })
    },
    
    // 创建圆角矩形路径
    createRoundedRect(ctx, x, y, width, height, radius) {
      ctx.beginPath()
      ctx.moveTo(x + radius, y)
      ctx.lineTo(x + width - radius, y)
      ctx.arcTo(x + width, y, x + width, y + radius, radius)
      ctx.lineTo(x + width, y + height - radius)
      ctx.arcTo(x + width, y + height, x + width - radius, y + height, radius)
      ctx.lineTo(x + radius, y + height)
      ctx.arcTo(x, y + height, x, y + height - radius, radius)
      ctx.lineTo(x, y + radius)
      ctx.arcTo(x, y, x + radius, y, radius)
      ctx.closePath()
    },
    
    // 绘制多行文本
    drawMultiLineText(ctx, text, x, y, maxWidth, fontSize, color, lineHeight, maxLines) {
      ctx.setFontSize(fontSize)
      ctx.setFillStyle(color)
      
      let lines = []
      let currentLine = ''
      let currentLineWidth = 0
      
      for (let i = 0; i < text.length; i++) {
        const char = text[i]
        const charWidth = ctx.measureText(char).width
        
        if (currentLineWidth + charWidth <= maxWidth) {
          currentLine += char
          currentLineWidth += charWidth
        } else {
          lines.push(currentLine)
          currentLine = char
          currentLineWidth = charWidth
          
          if (lines.length >= maxLines - 1) {
            // 如果已经达到最大行数-1，处理最后一行
            if (i < text.length - 1) {
              // 如果还有更多文本，添加省略号
              while (ctx.measureText(currentLine + '...').width > maxWidth && currentLine.length > 0) {
                currentLine = currentLine.slice(0, -1)
              }
              currentLine += '...'
              lines.push(currentLine)
              break
            }
          }
        }
      }
      
      // 添加最后一行
      if (currentLine && lines.length < maxLines) {
        lines.push(currentLine)
      }
      
      // 绘制所有行
      lines.forEach((line, index) => {
        ctx.fillText(line, x, y + index * lineHeight)
      })
    },
    
    // 将canvas转换为临时文件路径
    canvasToTempFilePath() {
      console.log('开始转换canvas为图片')
      // #ifdef MP-WEIXIN
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        fileType: 'jpg',
        quality: 1,
        success: res => {
          console.log('canvas转换成功', res)
          this.posterUrl = res.tempFilePath
          uni.hideLoading()
          this.loading = false
        },
        fail: err => {
          console.error('canvas转换失败', err)
          
          // 尝试再次转换
          setTimeout(() => {
            uni.canvasToTempFilePath({
              canvasId: 'posterCanvas',
              fileType: 'jpg',
              quality: 1,
              success: res => {
                console.log('canvas二次转换成功', res)
                this.posterUrl = res.tempFilePath
                uni.hideLoading()
                this.loading = false
              },
              fail: err2 => {
                console.error('canvas二次转换也失败', err2)
                uni.hideLoading()
                this.loading = false
                uni.showToast({
                  title: '生成海报失败',
                  icon: 'none'
                })
              }
            }, this)
          }, 500)
        }
      }, this)
      // #endif
      
      // #ifdef H5 || APP-PLUS
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: res => {
          console.log('canvas转换成功', res)
          this.posterUrl = res.tempFilePath
          uni.hideLoading()
          this.loading = false
        },
        fail: err => {
          console.error('canvas转换失败', err)
          uni.hideLoading()
          this.loading = false
          uni.showToast({
            title: '生成海报失败',
            icon: 'none'
          })
        }
      }, this)
      // #endif
    },
    
    // 保存海报到相册
    downloadImg() {
      if (!this.posterUrl) {
        uni.showToast({
          title: '海报生成中，请稍候',
          icon: 'none'
        })
        return
      }
      
      uni.showLoading({ title: '保存中...' })
      
      // #ifdef H5
      // H5环境下提供下载链接
      const a = document.createElement('a')
      a.href = this.posterUrl
      a.download = 'poster.png'
      a.click()
      uni.hideLoading()
      // #endif
      
      // #ifdef MP-WEIXIN || APP-PLUS
      // 保存到相册
      uni.saveImageToPhotosAlbum({
        filePath: this.posterUrl,
        success: () => {
          uni.hideLoading()
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        },
        fail: err => {
          uni.hideLoading()
          if (err.errMsg.indexOf('auth deny') >= 0) {
            uni.showModal({
              title: '提示',
              content: '需要授权保存到相册',
              success: res => {
                if (res.confirm) {
                  uni.openSetting()
                }
              }
            })
          } else {
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            })
          }
        }
      })
      // #endif
    }
  }
}
</script>

<template>
  <view v-if="visible" class="code-popup" @tap="$emit('close')">
    <view class="code-main" @tap.stop>
      <view class="close-btn" @tap.stop="$emit('close')">
        <image :src="`${staticPicDomain}images/icon/close.png`" class="close-png"></image>
      </view>
      
      <view class="poster-container">
        <view v-if="loading" class="loading-container">
          <text>海报生成中...</text>
        </view>
        
        <!-- 海报预览 -->
        <image v-if="posterUrl" :src="posterUrl" class="poster-image" mode="aspectFill"></image>
        
        <!-- 用于生成海报的canvas，调试时可以显示 -->
        <canvas canvas-id="posterCanvas" class="poster-canvas"></canvas>
      </view>
      
      <view class="download-btn">
        <NButton type="primary" block @click="downloadImg" :disabled="loading || !posterUrl">保存到相册</NButton>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.code-popup {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-main {
  background: #fff;
  border-radius: 16rpx;
  width: 534rpx;
  height: 860rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.close-btn {
  position: absolute;
  right: 0rpx;
  top: -48rpx;
  width: 48rpx;
  height: 48rpx;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-png {
  width: 100%;
  height: 100%;
}

.poster-container {
  flex: 1;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 16rpx;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.poster-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.poster-canvas {
  position: fixed;
  left: -9999px;
  width: 750px;
  height: 1200px;
  background-color: rgba(255, 255, 255, 0); /* 调试时可以看到canvas */
}

.loading {
  color: #888;
  font-size: 24rpx;
}

.download-btn {
  position: absolute;
  bottom: -96rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 212rpx;
  display: flex;
  justify-content: center;
}
</style>

<template>
	<!-- 商品详情 -->
	<view :class="['contenta',popupShowHiden?'page-hidden' : '']">
		<view :class="['container', skuShow || commentShow ? 'overflow' : '']" v-if="!commentShow">
			<!-- 轮播图 & 商品视频-->
			<view class="swiper-con">
				<view class="video-container" v-if="video && isPlaying">
					<video enable-progress-gesture="false" id="myVideo" @ended="playEnd" :src="video" controls></video>
				</view>
				<view class="play-btn" v-if="video" @tap="videoOper">
					<image class="play-icon" v-if="!isPlaying" :src="`${staticPicDomain}images/icon/play-red.png`"></image>
					<text :class="'play-text ' + (isPlaying?'video-stop':'video-play')" v-if="isPlaying">{{isPlaying?i18n.quitPlaying:''}}</text>
				</view>
				<TopSwiper v-if="!isPlaying" :imgs="imgs" />
			</view>
			<!-- 轮播图 & 商品视频end -->
			<!-- 倒计时栏 -->
			<view class="countdown" v-if="groupActivityId">
        <view class="countdown__left">
          <n-i icon="shape1" width="246rpx" height="100rpx"  />
          <view class="countdown__left__text">
            拼 团
          </view>
        </view>
        <view class="countdown__center">
          <text>{{groupActivity.groupNumber}}人团</text>
        </view>
        <view>
          <text class="countdown-tips" v-if="groupActivity.activityStatus === 1">{{i18n.onlyStart}}</text>
          <CountDown :times="endOfGroupTime"></CountDown>
        </view>
			</view>
			<!-- 预售 -->
			<view class="pre-sale" v-if="preSellStatus == 1 && !groupActivityId">
				<view class="pre-sale-text">{{i18n.preSale}}</view>
				<view class="send-time"><text class="countdown-tips">{{i18n.expect}}</text> {{preSellTime}} <text class="countdown-tips">{{i18n.startDelivery}}</text></view>
			</view>
			<!-- 商品信息 -->
			<view class="prod-info">
        <view class="prod-price">
          <view class="prod-price__line1">
            <!-- 商品有会员价 展示黑色的非会员价格和红色的会员价格 -->
            <block v-if="everyPrice.memberPrice">
              <view class="prod-price__sell-price with-vip-price">
                <text class="prod-price__sell-price__symbol">¥</text>
                <text class="prod-price__sell-price__big">{{ parsePrice(sellPrice)[0] }}</text>
                <text class="prod-price__sell-price__big price-dot">.</text>
                <text class="prod-price__sell-price__small">{{ parsePrice(sellPrice)[1] }}</text>
              </view>
              <view class="prod-price__vip-price">
                <view class="prod-price__vip-price__text">
                  <text>¥ {{ parsePrice(everyPrice.memberPrice)[0] }}</text>
                  <text class="price-dot">.</text>
                  <text>{{ parsePrice(everyPrice.memberPrice)[1] }}</text>
                </view>
                <view  style="width:88rpx;height:36rpx;margin-left: 10rpx">
                  <n-i icon="vip-price-tag" width="88rpx" height="36rpx"></n-i>
                </view>
              </view>
            </block>
            <block v-else>
              <!-- 商品无会员价：展示红色的售价和划线的原价 -->
              <!-- 商品售卖价 -->
              <view class="prod-price__sell-price">
                <text class="prod-price__sell-price__symbol">¥</text>
                <text class="prod-price__sell-price__big">{{ parsePrice(sellPrice)[0] }}</text>
                <text class="prod-price__sell-price__big price-dot">.</text>
                <text class="prod-price__sell-price__small">{{ parsePrice(sellPrice)[1] }}</text>
              </view>
              <!-- 商品原价 -->
              <view class="prod-price__line-through-price" v-if="lineThroughPrice">
                <text class="prod-price__line-through-price__tip">价格 </text>
                <text class="prod-price__line-through-price__value">¥ {{ parsePrice(lineThroughPrice)[0] }}.{{ parsePrice(lineThroughPrice)[1] }}</text>
              </view>
            </block>
          </view>
          <view class="prod-price__line2" v-if="!!everyPrice.finalPrice || !!everyPrice.memberFinalPrice">
            <!-- 预估最终到手价 -->
            <view class="prod-price__final-price" v-if="isVip && everyPrice.memberFinalPrice" >预估到手价¥{{ parsePrice(everyPrice.memberFinalPrice)[0] }}.{{ parsePrice(everyPrice.memberFinalPrice)[1] }}</view>
            <view class="prod-price__final-price" v-else >预估到手价¥{{ parsePrice(everyPrice.finalPrice)[0] }}.{{ parsePrice(everyPrice.finalPrice)[1] }}</view>
          </view>
        </view>

	
				<view class="tit-wrap">
					<view class="prod-tit">{{prodName}}</view>
					<!-- 纯文字标签 -->
					<view class="font-labels">
						<text v-for="(item, index) in fontLabels" :key="item.labelId" :style="{color: item.textColor}">
							{{ item.labelName }}
							<text v-if="index !== fontLabels.length - 1" style="color: #888; margin: 0rpx 8rpx">|</text>
						</text>
					</view>
					 <!-- 角标标签 -->
					<view v-for="(row, idx) in cornerMarkLabels" :key="idx"  class="corner-mark-labels"> 
						<text
							v-if="row.style === 'background-only'" 
							class="corner-mark-style"
							:style="{color: '#fff', backgroundColor: row.bgColor}"
						>
							{{ row.labelName }}
						</text>
						<text 
							v-if="row.style === 'font-only'"  
							class="corner-mark-style"
							:style="{color: row.bgColor, borderColor: row.bgColor}"
						>
							{{ row.labelName }}
						</text>
						<text 
							v-if="row.style === 'both-colors'" 
							class="corner-mark-style"
							:style="{color: row.textColor, backgroundColor: row.bgColor}"
						>
							{{ row.labelName }}
						</text>
					</view>	

          <view class="sub-tit" v-if="brief">{{brief}}</view>
				</view>
        <!-- #ifdef MP-WEIXIN -->
        <EarnButton v-if="isDistProd"  @click-tap="onShowShare" :price="sharingPrice" />
        <!-- #endif -->
			</view>
			<!-- 商品信息end -->

      <!-- 超级会员 -->
       <view class="very-vip-wrap" v-if="everyPrice.memberPrice && !isVip && showVip">
         <view class="very-vip">
           <view class="vip">超级会员</view>
           <text class="vip-tips">会员0元领，本单立享超低价！</text>
           <view class="open-vip-btn" @tap="gotovip">去领取<n-i icon="arrow-right" ext="png" /></view>
         </view>
       </view>
			<!-- 超级会员end -->

      <ProdTableDetail
        :delivery-way-text="deliveryWayText"
        :selected-prop="selectedProp"
        :prod-num="prodNum"
        :coupon-list="couponList"
        :giveaway-list="giveawayList"
        :show-pre-selling="preSellStatus == 1 && groupActivityId"
        :pre-sell-time="preSellTime"
        :prod-discount-list="prodDiscountList"
        :show-discout-list="!!(prodDiscountList.length && preSellStatus!=1 && prodType != 5)"
        @tapSku="showSku"
        @tapCoupons="showPopup"
        @tapGiveaway="clickDiscount"
        @countMinus="onCountMinus"
        @countPlus="onCountPlus"
        @prodNumInp="prodNumInp"
        @tapDiscount="clickDiscount"
      />
			
			<!-- 拼团信息 -->
			<view class="spell-infor" v-if="joinGroupList.length">
				<view class="spell-infor-title">{{i18n.joinAGroup}}</view>
				<view class="spell-infor-content" v-for="(item, groupTeamId) in joinGroupList" :key="groupTeamId">
					<!-- 头像 -->
					<view class="head-img">
						<image :src="item.sharePic ? item.sharePic : '`${staticPicDomain}images/icon/head01.png`'"></image>
					</view>
					<!-- 信息 -->
					<view class="spell-msg">
						<view class="username">{{item.shareNickName}}</view>
						<view class="spell-text">{{i18n.lack}}<text class="red-font">{{item.groupNumber - item.joinNum}}</text>{{i18n.lack1}}
							{{item.endOfGroupTime.hou}}{{i18n.time}}{{item.endOfGroupTime.min}}{{i18n.minute}}{{item.endOfGroupTime.sec}}{{i18n.second}}</view>
					</view>
					<!-- 凑团按钮 -->
					<view class="join-group" @tap="toSpellGroupDetail" :data-groupteamid="item.groupTeamId">{{i18n.toGatherGroup}}</view>
				</view>
				<!-- 详情 -->
				<view class="rules clearfix">
					<text class="rules-text01">{{i18n.groupInvitation}}{{groupActivity.groupNumber}}{{i18n.groupInvitationTips}}</text>
					<!-- <text class="rules-text02">玩法详情</text>
		<view class="right-arrow"><image src="../../images/icon/more.png"></image></view> -->
				</view>
			</view>


			<!-- 拼团信息end -->

			<!-- 优惠套装列表 -->
			<view class="discount-package" v-if="comboList.length">

				<view class="discount-package-title">{{i18n.discountPackage}} <text class="discount-package-title-fu">（{{i18n.inTotal}}{{comboList.length}}{{i18n.itemGe}}）</text></view>
				<view class="discount-package-concent">
					<scroll-view scroll-x>
						<view class="discount-package-scroll">
							<view v-for="(item, index) in comboList" :key="index" class="discount-package-item" @tap="toDiscountPackageDetail(item.comboId)">
								<image @tap.stop="toDiscountPackageDetail(item.comboId)" mode="aspectFit" :src="item.mainProd.pic" class="main-image"></image>
								<view class="addFu" v-if="item.matchingProds.length"> + </view>
								<view class="fu-prod" v-if="item.matchingProds.length">
									<image @tap.stop="toDiscountPackageDetail(item.comboId)" mode="aspectFit" :src="item.matchingProds[0].pic" class="sli-image"></image>
								</view>
								<view class="discountPackageProd-detail">
									<text>{{i18n.packages}}<text class="yuan-number">{{index+1}}</text>,{{i18n.inTotal}}{{item.prodCount}}{{i18n.piece}}</text>
									<text class="price">￥{{item.comboAmount}}</text>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
			<!-- 优惠套装列表end -->

			<!-- 评价 -->
			<view class="cmt-wrap box-radius" v-if="prodCommentSwitch && prodCommData">
				<view class="cmt-tit" @tap="showComment(-1)">
					<view class="cmt-t">商品{{i18n.evaluation}}({{prodCommData.number}})
					</view>
					<view class="cmt-count">{{i18n.praise}}率{{prodCommData.positiveRating}}%<text class="cmt-more"></text>
					</view>
				</view>
				<view class="cmt-cont">
					<view class="cmt-items">
						<view class="cmt-item" v-for="(item, prodCommId) in littleCommPage" :key="prodCommId">
							<view class="cmt-user">
								<view class="cmt-user-info">
									<image class="user-img" :src="item.pic?item.pic:'`${staticPicDomain}images/icon/head04.png`'"></image>
									<view class="nickname">
										<view class="name-star">
											<view class="name">{{item.isAnonymous==1?i18n.anonymousEvaluation:item.nickName}}</view>
											<view>
												<comm-star :value="item.score"></comm-star>
											</view>
										</view>
										<view style="color: #999;">{{item.skuName || ''}}</view>
									</view>
								</view>
							</view>
							<text class="date">{{item.recTime}}</text>
							<view class="cmt-cnt">
								<text decode="true">{{item.content}}</text>
							</view>
						</view>
					</view>
					<view class="cmt-more-v" v-if="prodCommPage.records.length > 2">
						<text @tap="showComment(-1)">{{i18n.viewAllEvaluation}}</text>
					</view>
				</view>
			</view>
			<!-- 评价 end -->

			<!-- 商品详情 -->
			<view class="prod-detail">
        <view class="prod-detail-title">
          <view class="prod-detail-title-line"></view>
          <text class="prod-detail-title-text">商品详情图</text>
          <view class="prod-detail-title-line"></view>
        </view>
				<view>
					<view class="parameter-box" v-for="item in prodParameterList" :key="item.prodParameterId">
						<view class="parameter-key">
							{{ item.parameterKey }}
						</view>
						<view class="parameter-vaule">
							{{ item.parameterValue }}
						</view>
					</view>
				</view>
				<view>
					<ProductionRichText :nodes="content"></ProductionRichText>
				</view>
			</view>
			<!-- 商品详情end -->

			<!-- 底部按钮 -->
			<view class="cart-footer">
				<view class="btn icon" @tap="toHomePage">
					<image src="/static/images/tabbar/tabbar_icon_home_default.png"></image>
				</view>
				<view class="btn icon" @tap="handleCustomService" v-if="shopCustomerSwitch">
					<image style="width: 28px;margin-bottom: 6rpx;" mode="widthFix" :src="`${staticPicDomain}images/icon/shop-customer-service.png`"></image>{{i18n.service}}
				</view>
				<block v-if="!groupActivityId && prodType != 5">
					<block v-if="preSellStatus != 1">
						<view v-if="mold !== 1" class="btn cart cart-radius" @tap="addToCart">
							<text>{{i18n.addShoppingCart}}</text>
						</view>
						<view class="btn buy cart-radius" @tap="buyNow">
							<text>{{i18n.buyNow}}</text>
						</view>
					</block>
					<view class="btn pre-sale-buy cart-radius" @tap="buyNow" v-if="preSellStatus == 1">
						<text>{{i18n.buyNow}}</text>
					</view>
				</block>
				<block v-if="groupActivityId && prodType != 5">
					<view class="btn alone-buy cart-radius" @tap="showSku" data-alonebuy="1">
						<text class="cart-radius-text">¥{{defaultGroupSku.price}}</text>
						<text>{{i18n.individualShopping}}</text>
					</view>
					<view :class="['btn group-buy cart-radius', groupActivity.activityStatus === 1 ? 'gray-btn' : '']" @tap="showGroupSku">
						<text class="cart-radius-text">¥{{defaultGroupSku.actPrice}}</text>
						<text>去{{i18n.startAGroup}}</text>
					</view>
				</block>
				<!-- 活动商品不可购买 -->
				<block v-if="prodType == 5">
					<view class="btn buy cart-radius gray-btn" :class="{'en-btn': isEn}">
						<text>{{i18n.notAvailableForPurchase}}</text>
					</view>
				</block>
			</view>
			<!-- 底部按钮 end -->

			<!-- 满减折弹窗 -->
			<view class="popup-hide" v-if="showDiscountPopup" @tap='showDiscountPopup = false;popupShowHiden = false'>
				<view class="popup-box radius" @tap.stop>
					<view class="popup-tit radius">
						<text>{{i18n.promotion}}</text>
						<text class="close" @tap='clickDiscount'></text>
					</view>
					<view class="popup-cnt popup-discount">
						<view class="coupon-con discount-con">
							<block v-for="(item, index) in prodDiscountList" :key="index">
								<view class="discount-item">
									<view class="discount-tag">{{ [i18n.amount, i18n.pieces, i18n.amountDiscount, i18n.piecesDiscount][item.discountRule] }}</view>
										<view class="giveaways-name" @tap="toDiscountList(item.discountId)">
											<view class="discount-content"><text v-if="item.discountType">{{i18n.every}}</text><text>{{item.discountName}}，{{i18n.maximumDiscount}}{{item.maxReduceAmount}}{{i18n.yuan}}</text></view>
											<view class="giveaways-item-right"></view>
										</view>
								</view>
							</block>

							<!-- 赠品列表 -->
						<view class="giveaway-list" v-if="giveawayList.length">
							<view class="discount-tag">{{i18n.Giveaways}}</view>
							<view class="giveaway-prod">
								<block v-for="(item, index) in giveawayList" :key="index">
									<view class="giveaways-item">
										<view class="giveaways-content">
												<view class="giveaways-name" @tap="toGiveawaysProd(item.prodId)">
													<view class="discount-content">{{item.prodName}}</view>
													<view class="giveaways-item-right"></view>
												</view>
										</view>
										<view class="number"> <text v-if="item.skuName" decode>{{item.skuName+'&nbsp;&nbsp;'}}</text> x{{item.giveawayNum}}</view>
									</view>
								</block>
							</view>
						</view>

						</view>
					</view>
				</view>
			</view>
			<!-- 满减折弹窗 end -->

			<!-- 优惠券 -->
				<view class="popup-hide" v-if="popupShow" @tap='popupShow = false;popupShowHiden = false'>
					<view @tap.stop class="popup-box radius" style="background: #f4f4f4">
							<view class="popup-tit radius marginB" style="background: #fff">
								<text>领取{{i18n.coupon}}</text>
								<text class="close" @tap="closePopup"></text>
							</view>
							<view class="popup-cnt">
								<block v-for="(item, couponId) in couponList" :key="couponId">
									<coupon @refreshDeductPrice="getEveryPrice" :showTimeType="1" :canUse="item.canUse" :couponItem="item"></coupon>
								</block>
							</view>
					</view>
				</view>
			<!-- 优惠券 end -->

			<!-- 规格弹窗 -->
			<view class="pup-sku" v-if="skuShow" @tap='skuShow = false;popupShowHiden = false'>
				<view class="pup-sku-main radius" @tap.stop>
					<view class="pup-sku-header radius">
						<view class="close" @tap="closePopup"></view>
						<view class="pup-sku-img">
							<image :src="defaultSku.pic?defaultSku.pic:pic" mode="aspectFit"></image>
						</view>
						<view class="pup-sku-prod">
              <view class="prod-price inpup">
                <view class="prod-price__line1">
                  <!-- 商品有会员价 展示黑色的非会员价格和红色的会员价格 -->
                  <block v-if="everyPrice.memberPrice">
                    <view class="prod-price__sell-price with-vip-price">
                      <text class="prod-price__sell-price__symbol">¥</text>
                      <text class="prod-price__sell-price__big">{{ parsePrice(sellPrice)[0] }}</text>
                      <text class="prod-price__sell-price__big price-dot">.</text>
                      <text class="prod-price__sell-price__small">{{ parsePrice(sellPrice)[1] }}</text>
                    </view>
                    <view class="prod-price__vip-price">
                      <view class="prod-price__vip-price__text">
                        <text>¥ {{ parsePrice(everyPrice.memberPrice)[0] }}</text>
                        <text class="price-dot">.</text>
                        <text>{{ parsePrice(everyPrice.memberPrice)[1] }}</text>
                      </view>
                      <view  style="width:88rpx;height:36rpx;margin-left: 10rpx">
                        <n-i icon="vip-price-tag" width="88rpx" height="36rpx"></n-i>
                      </view>
                    </view>
                  </block>
                  <block v-else>
                    <!-- 商品无会员价：展示红色的售价和划线的原价 -->
                    <!-- 商品售卖价 -->
                    <view class="prod-price__sell-price">
                      <text class="prod-price__sell-price__symbol">¥</text>
                      <text class="prod-price__sell-price__big">{{ parsePrice(sellPrice)[0] }}</text>
                      <text class="prod-price__sell-price__big price-dot">.</text>
                      <text class="prod-price__sell-price__small">{{ parsePrice(sellPrice)[1] }}</text>
                    </view>
                    <!-- 商品原价 -->
                    <view class="prod-price__line-through-price" v-if="lineThroughPrice">
                      <text class="prod-price__line-through-price__tip">价格 </text>
                      <text class="prod-price__line-through-price__value">¥ {{ parsePrice(lineThroughPrice)[0] }}.{{ parsePrice(lineThroughPrice)[1] }}</text>
                    </view>
                  </block>
                </view>
                <view class="prod-price__line2" v-if="!!everyPrice.finalPrice || !!everyPrice.memberFinalPrice">
                  <!-- 预估最终到手价 -->
                  <view class="prod-price__final-price" v-if="isVip && everyPrice.memberFinalPrice" >预估到手价¥{{ parsePrice(everyPrice.memberFinalPrice)[0] }}.{{ parsePrice(everyPrice.memberFinalPrice)[1] }}</view>
                  <view class="prod-price__final-price" v-else >预估到手价¥{{ parsePrice(everyPrice.finalPrice)[0] }}.{{ parsePrice(everyPrice.finalPrice)[1] }}</view>
                </view>
              </view>
							<view class="pup-sku-price" v-if="!findSku">{{i18n.outOfStock}}</view>
							<view class="pup-sku-prop">
								<view>
									<text>{{i18n.selected}}</text>
									<text decode="true">
										<block v-for="(skuItem,index) in selectedProp" :key="index">
											{{index < selectedProp.length-1 ? '&nbsp;' + skuItem.value + '，' : '&nbsp;' + skuItem.value +'&nbsp;&nbsp;'}}
										</block>{{'&nbsp;'+prodNum+'&nbsp;'}}{{i18n.piece}}
									</text>
								</view>
								<view v-if="findSku">
									<text>{{i18n.inventory}}</text>
									<text decode="true">{{'&nbsp;'+defaultSku.stocks}}</text>
								</view>
							</view>
						</view>
					</view>
					<view class="pup-sku-body">
						<!-- 说明（虚拟商品） -->
						<view v-if="mold === 1 && (writeOffNum !== 0 || (writeOffNum === 0 && isRefund === 0))" class="virtual-goods-tips">
							<text class="vi-t">{{i18n.usageInstructions}}：</text>
							<!-- writeOffNum 0无需核销 1单次核销 -1多次核销 -->
							<block v-if="writeOffNum !== 0">
							<!-- writeOffTime核销有效期 -1.长期有效 0.自定义 x.x天内有效 -->
								<text v-if="writeOffTime === -1">{{i18n.longTermValidity}}</text>
								<text v-else-if="writeOffTime === 0">{{i18n.afterPurchase}} {{writeOffStart}} {{i18n.to}} {{writeOffEnd}} <text v-if="!isEn">{{i18n.effective}}</text></text>
								<text v-else-if="writeOffTime === 1">{{i18n.validOnTheSameDay}}</text>
								<text v-else>{{i18n.purchase}}{{writeOffTime}}{{i18n.validDay}}</text>
							</block>
							<!-- isRefund 0不支持退款 1支持退款 -->
							<text v-if="isRefund === 0"><text v-if="writeOffNum !== 0 ">，</text>{{i18n.refundsAreNotAllowed}}</text>
						</view>
						<!-- 规格 -->
						<view class="pup-sku-area">
							<view class="sku-box" v-if="skuList.length">
								<view class="items sku-text" v-for="(skuLine, key) in skuGroup" :key="key">
									<text class="sku-kind">{{key}}</text>
									<view class="con">
										<!-- <text class="sku-choose-item" @click="toChooseItem(skuLineItem, key)" :class="[selectedProp.indexOf(skuLineItem) !== -1 ? 'active':'',  isSkuLineItemNotOptional(allProperties,selectedPropObj,key,skuLineItem,propKeys) ? 'dashed' : '']"
										 v-for="skuLineItem in skuLine" :key="skuLineItem">{{skuLineItem}}</text> -->
										<text
											v-for="skuLineItem in skuLine"
											:key="skuLineItem"
										 	class="sku-choose-item"
											:class="
												[selectedProp.find(el => el.key === key && el.value === skuLineItem) ? 'active':'',
												isSkuLineItemNotOptional(allProperties,selectedPropObj,key,skuLineItem,propKeys) ? 'dashed' : '']
											"
											@click="toChooseItem(skuLineItem, key)"
										>
											<text class="sku-choose-item-text">{{skuLineItem}}</text>
										</text>
									</view>
								</view>
							</view>
						</view>
						<!-- 数量 -->
						<view class="pup-sku-count">
							<view class="num-wrap">
								<view class="minus" @tap="onCountMinus">
									<text class="row"></text>
								</view>
								<view class="text-wrap">
									<input type="number" :value="prodNum" @input="prodNumInp" />
								</view>
								<view class="plus" @tap="onCountPlus">
									<text class="row"></text>
									<text class="col"></text>
								</view>
							</view>
							<view class="count-name">{{i18n.quantity}}</view>
						</view>
						<!-- 留言（虚拟商品） -->
						<view v-if="mold===1 && !isCustomRemark"  class="pup-sku-count virtual-goods-msg">
							<view v-for="(item, index) in virtualRemarks" :key="index" class="msg-item">
								<view class="msg-tit"><text v-if="item.isRequired" class="stress">*</text>{{item.name}}</view>
								<input class="msg-int" v-model="item.value" :placeholder="i18n.pleaseEnter + `${item.name}`" maxlength="20" />
							</view>
						</view>
					</view>
					<view v-if="orderNumber ? false : prodType != 5" :class="'pup-sku-footer ' + (findSku && defaultSku.stocks >= prodNum?'':'gray')">
						<block v-if="skuShowType==0 && preSellStatus != 1">
							<view class="btn cart" @tap="addToCart" v-if="skuShowType == 0 && mold !== 1">{{i18n.addShoppingCart}}</view>
							<view class="btn buy" @tap="buyNow" v-if="skuShowType==0">{{i18n.buyNow}}</view>
						</block>
						<view class="btn pre-sale-buy cart" @tap="buyNow" v-if="preSellStatus == 1">{{i18n.buyNow}}</view>
						<view :class="['btn buy',groupActivity.activityStatus === 1 ? 'gray-btn' : '']" @tap="groupConfirmOrder"
						 data-teamid="0" v-if="skuShowType==1">{{i18n.startAGroup}}</view>
					</view>
					<view v-if="prodType == 5" class="pup-sku-footer gray">
						<view class="btn buy">{{i18n.notAvailableForPurchase}}</view>
					</view>
				</view>
			</view>
			<!-- 规格弹窗 end -->

			<!-- 评价弹窗 -->

			<!-- 评价弹窗 end -->
			<!-- 分享弹窗 -->
			<view class="promo-share" v-if="shareShow" @tap='shareShow = false;popupShowHiden = false'>
				<view class="promo-main" @tap.stop>
					<view class="promo-icons-close" @tap="closeEarn">
						<image :src="`${staticPicDomain}images/icon/close.png`"></image>
					</view>
					<view class="promo-tit">
						<text>{{i18n.shareFriendsNow}}</text>
					</view>
					<view class="promo-desc">
						<text>{{i18n.shareFriendsTips1}}</text>
					</view>
					<!-- 实名提示 -->
					<view class="real-name-notice">
						<text class="notice-warning">!</text>
						<text class="notice-text">请确保该微信账号已完善实名信息，否则佣金将无法到账</text>
					</view>
					<view class="promo-icons">
						<!-- #ifdef H5 -->
						<button class="promo-img1" @tap="onShareWay" open-type="share" v-if="isWechat">
							<image :src="`${staticPicDomain}images/icon/weixin.png`"></image>{{i18n.weChat}}
						</button>
						<!-- #endif -->
						<!-- #ifdef MP-WEIXIN -->
						<button class="promo-img1" open-type="share">
							<image :src="`${staticPicDomain}images/icon/weixin.png`"></image>{{i18n.weChat}}
						</button>
						<!-- #endif -->
						<view class="promo-img1" @tap="showPoster = true">
							<image :src="`${staticPicDomain}images/icon/erweima.png`"></image>推广海报
						</view>

					</view>
					<view class="promo-btn" @tap="toDistCenterPage">{{i18n.myDistributorCenter}}</view>
				</view>
			</view>

			</view>
			<!-- 二维码弹窗 end -->

			<!-- 引导分享蒙版 -->
			<view class="guide-share-mask" v-if="guideShare">
				<view class="mask"></view>
				<view class="guide-share-close" @tap="guideShare=false">
					<image :src="`${staticPicDomain}images/icon/close.png`" mode=""></image>
				</view>
				<view class="guide-content">
					<view class="share-img">
						<image :src="`${staticPicDomain}images/icon/shareIcon.png`"></image>
					</view>
					<view class="share-word">
						<view class="big-word">{{i18n.shareFriendsNow}}</view>
						<view class="small-word">{{i18n.shareFriendsTips2}}</view>
					</view>
				</view>
			</view>
			<!-- 引导分享蒙版 end -->

			<!-- 直播悬浮按钮 -->
			<!-- #ifdef MP-WEIXIN -->
			<view class="live" v-if="liveRoomParams[0] && liveRoomParams[0].liveStatus === 101" :data-roomid="liveRoomParams[0].roomId" :data-url="liveRoomParams[0].url"
			 @tap='toLivePage'>
				<rhythm-beat></rhythm-beat>
				<view class="live-txt">{{i18n.inLive}}</view>
			</view>
			<!-- #endif -->

			<!-- 回到顶部 -->
			<view class="promo-con">
				<view class="suspension-btn" v-if="showBacktop" @tap='handleScorllTop'>
					<image :src="`${staticPicDomain}images/icon/back-to-top.png`"></image>
				</view>
			</view>
			<!-- 回到顶部 end-->

			<!-- 悬浮按钮 start -->
			<view class="float-btns">
				<!-- #ifdef MP-WEIXIN -->
				<view class="float-icon" v-if="!isDistProd">
					<button open-type='share'>
						<n-i icon="share" width="48rpx" height="48rpx"  />
					</button>
				</view>
				<!-- #endif -->
				<view v-if="mold !== 1" class=" float-icon" style="height: 48rpx;" @tap="toCartPage">
					<n-i icon="basket" width="48rpx" height="48rpx"  />
				</view>
			</view>
			<!-- 悬浮按钮 end -->
			

		<!-- 评价弹窗 -->
		<view :class="prodCommData.number?'cmt-popup whitebg':'cmt-no-popup'" v-if="commentShow">
			<view class="eval-head">
				<text class="cmt-more close-arrow" @tap="closePopup"></text>
				<text class="head-title">商品评价</text>
			</view>
			<view class="cmt-cont paddingL paddingR">
				<view class="eval-page-modal">
					<view class="cmt-tit-modal">
						<text class="eval-text1">{{i18n.productEvaluation}}({{prodCommData.number}})</text>
						<text class="eval-text2">{{i18n.rating}}{{prodCommData.positiveRating}}%</text>
					</view>
					<view class="cmt-tag">
						<text @tap="getProdCommPage" data-evaluate="-1" :class="evaluate==-1?'selected':''">{{i18n.all + ' '}}({{prodCommData.number}})</text>
						<text @tap="getProdCommPage" data-evaluate="0" :class="evaluate==0?'selected':''">{{i18n.praise + ' '}}({{prodCommData.praiseNumber}})</text>
						<text @tap="getProdCommPage" data-evaluate="1" :class="evaluate==1?'selected':''">{{i18n.mediumEvaluation + ' '}}({{prodCommData.secondaryNumber}})</text>
						<text @tap="getProdCommPage" data-evaluate="2" :class="evaluate==2?'selected':''">{{i18n.badEvaluation + ' '}}({{prodCommData.negativeNumber}})</text>
						<text @tap="getProdCommPage" data-evaluate="3" :class="evaluate==3?'selected':''">{{i18n.havePictures + ' '}}({{prodCommData.picNumber}})</text>
					</view>
				</view>

				<view class="cmt-items cut-eval-item">
					<view class="cmt-item borderB" v-for="(item, prodCommId) in prodCommPage.records" :key="prodCommId">
						<view class="cmt-user">
							<view class="cmt-user-info">
								<!-- 匿名头像图片-->
								<image class="user-img" :src="item.pic?item.pic:`${staticPicDomain}images/icon/head04.png`"></image>
								<view class="nickname">
									<view class="name-star">
										<view class="name">{{item.isAnonymous==1?i18n.anonymousEvaluation:item.nickName}}</view>
										<view>
											<comm-star :value="item.score"></comm-star>
										</view>
									</view>
									<view style="color: #999;">{{item.skuName || ''}}</view>
								</view>
							</view>
							<text class="date">{{item.recTime}}</text>
						</view>
						<view class="cmt-cnt">
							<text decode="true">{{item.content}}</text>
						</view>
						<view class="cmt-attr cut-attr" v-if="item.pics && item.pics.length">
							<image class="cut-picture" :src="commPic" v-for="(commPic, index) in item.pics" :key="index" :data-pics="item.pics" :data-src="commPic"
							 @tap="clickImg"></image>
						</view>
						<view class="cmt-reply" v-if="item.replyContent">
							<text class="reply-tit">{{i18n.shopReply}}：</text><text class="reply-content">{{item.replyContent}}</text></view>
					</view>
				</view>
				<!-- 列表空 -->
				<view class="empty" v-if="!prodCommPage.records.length">
					<view class="empty-icon">
						<image :src="`${staticPicDomain}images/icon/empty-com.png`"></image>
					</view>
					<view class="empty-text">{{i18n.noProductReviewsTips}}</view>
				</view>
				<!-- /列表空 -->
				<view class="load-more" v-if="prodCommPage.pages > prodCommPage.current">
					<text @tap="getMoreCommPage">{{i18n.clickLoadMore}}</text>
				</view>
			</view>
		</view>
    <n-loading />
			<contact-button
				:title="prodName"
				:img="imgs[0]"
			></contact-button>
			<DistrubutionPoster 
			  :prodId="prodId" 
			  :cardNo="distributionCardNo" 
			  :visible="showPoster" 
			  :prodName="prodName"
			  :prodPrice="sellPrice ? parsePrice(sellPrice)[0] + '.' + parsePrice(sellPrice)[1] : ''"
			  :prodImage="imgs && imgs.length > 0 ? imgs[0] : pic"
			  @close="showPoster = false" 
			/>
	</view>
</template>

<script>
	// pages/prod/prod.js
	var http = require("../../utils/http.js");
	var config = require("../../utils/config.js");
	var util = require("../../utils/util.js");
	var Qr = require("../../utils/wxqrcode.js");
	import coupon from "../../components/coupon/coupon";
	import commStar from "../../components/commStar/commStar";
	import loginPopup from "../../components/loginPopup/loginPopup";
	import rhythmBeat from "../../components/rhythmBeat/rhythmBeat";
  import ProductionRichText from "@/components/production/ProductionRichText";
  import TopSwiper from '@/components/production/swiper'
	import ComplexCouponCard from '@/components/coupon-card/complex-coupon-card'
  import ProdTableDetail from '@/components/prod-table-detail'
  import CountDown from '@/components/count-down'
  import NI from '@/components/n-i'
	// #ifdef H5
	import Wechat from "../../utils/wechat.js";
	// #endif
	import EarnButton from '@/components/earn-button/earn-button'
  import ContactButton from '@/components/contact-button'
  import DistrubutionPoster from './distrubution-poster.vue'
	export default {
		data() {
			return {
				isEn: uni.getStorageSync('lang') == 'en' ? true : false, // 是否为英文

				isWechat: false, //是否为微信环境
				shopId: 1,
				shopInfo: '', // 店铺信息

				comboList:[], //优惠套餐列表
				mainDiscountPackageData:[],//套餐主商品信息
				giveawayList: [], // 赠品列表

				picDomain: config.picDomain,
				isAuthInfo: true,
				autoplay: true,
        checkBuyerOk: true,
				prodNum: 1,
				totalCartNum: 0,
				groupActivityId: 0,
				discountId: null, //满减折活动id
				pic: "",
				imgs: '',
				prodName: '',
				brief: '',
				price: 0,
				actPrice: 0,
				content: '',
				prodId: 0,
				brief: '',
				skuId: 0,
				labels: [], // 商品标签
				popupShow: false,
				// 是否获取过用户领取过的优惠券id
				loadCouponIds: false,
				skuShow: false,
				skuGroupShow: false,
				commentShow: false,
				couponList: [],
				skuList: [],
				skuGroup: {},
				findSku: true,
				defaultSku: "",
				defaultGroupSku: "",
				selectedProp: [],
				selectedPropObj: {},
				selectedProperties: "",
				propKeys: [],
				allProperties: [],
				prodCommData: '',
				prodCommPage: {
					current: 0,
					pages: 0,
					records: []
				},
				littleCommPage: [],
				evaluate: -1,
				isCollection: false,
				shareShow: false,
				//是否分销商品
				isDistProd: false,

        //最高可赚多少钱
        sharingPrice: 0,
				distributionCardNo: "",
				//分销员卡号
				shareWxCode: "",
				//分享二维码图片路径
				groupActivity: {},
				// 团购活动
				endOfGroupTime: {},
				// 距离团购活动结束还剩多久
				joinGroupList: [],
				// 可加入的团列表
				skuShowType: 0,
				// sku的显示类型 0普通sku 1拼团sku
				totalStocks: 0,
				// 活动剩余库存
				hasMaxNum: false,
				// 是否限购
				maxNum: 0,
				// 限购数量
				joinGroupListTimer: '',
				endOfGroupTimer: '',
				scene: '',
				prodDiscountList: [], // 商品促销活动列表
				shopLogo: "",
				teamId: "",
				prodType: 0, //商品类型(0普通商品 1拼团 2秒杀 3积分 5活动商品)
				showDiscountPopup: false, //促销弹窗显隐
				popupShowHiden: false,//所有弹窗的显隐判断
				everyPrice: {}, // 商品的各种价格

				guideShare: false, // 引导蒙版

				deliveryModeVO: null, // 配送方式

				video: '', // 商品视频
				isPlaying: false,
				showBacktop: false,
				liveRoomParams: [], // 直播列表

				// 预售
				preSellStatus: 0, // 预售状态 1：开启 0：未开启
				preSellTime: '', // 预售发货时间
				preSellTimer:null, //预售计时器

				scrollTop: -1, // 滚动监听
				carNum: '', //购物车图标右上角数字
				curLang: uni.getStorageSync('lang'),

				// 虚拟商品
				mold: '', // 1虚拟商品
				virtualRemarks: [], // 留言
				isRefund: null, // 0不支持退款 1支持退款
				writeOffTime: null, // 核销有效期 -1.长期有效 0.自定义 x.x天内有效
				writeOffNum: null, // 核销次数 -1.多次核销 0.无需核销 1.单次核销
				writeOffStart: null, // 核销有效期开始时间
				writeOffEnd: null, // 核销有效期结束时间
				prodParameterList: [], //商品参数
        isNewUser: false, // 当前用户是否为新用户
        hasNewerLimit: false,// 是否有新人限购
				orderNumber: null,
        showPoster: false,
			};
		},

		components: {
			coupon,
			commStar,
			loginPopup,
			rhythmBeat,
      ProductionRichText,
      TopSwiper,
			ComplexCouponCard,
      ProdTableDetail,
      CountDown,
      NI,
			EarnButton,
      ContactButton,
      DistrubutionPoster
		},
		props: {},

		computed: {
			i18n() {
				return this.$t('index')
			},
      isCustomRemark() {
        return this.virtualRemarks.find(el => el.type)
      },
      shopCustomerSwitch() {
        return this.$store.state.shopCustomerSwitch
      },
      prodCommentSwitch() {
        return this.$store.state.prodCommentSwitch
      },
      showVip() {
        return this.$store.state.shopVipSwitch
      },
			fontLabels() {
				return this.labels.filter(item => item.shown == true && item.type == '1')
			},
			cornerMarkLabels() {
				return this.labels.filter(item => item.shown == true && item.type == '2' && item.position === 'below-headline')
			},
      deliveryWayText() {
        if (this.deliveryModeVO && this.mold !== 1 && this.prodTypedeliveryModeVO != 5) {
          if (this.deliveryModeVO.hasShopDelivery) {
            return this.i18n.expressDelivery
          }
          if (this.deliveryModeVO.hasCityDelivery) {
            return this.i18n.sameDelivery
          }
          if (this.deliveryModeVO.hasUserPickUp) {
            return this.i18n.pickStore
          }
        } else if (this.mold === 1 && this.prodType != 5) {
          return this.i18n.noNeedDelivery
        }
      },

      // 界面上划线的价格
      lineThroughPrice() {
        if (this.groupActivityId) {
          return this.defaultGroupSku.price
        }
        if (this.everyPrice.discountPrice === this.everyPrice.price) {
          return ''
        }
        if (this.everyPrice.discountPrice) {
          return this.everyPrice.price
        }
      },
      // 商品售卖价
      sellPrice() {
        if (this.groupActivityId) {
          return this.defaultGroupSku.actPrice
        }
        if (this.everyPrice.discountPrice) {
          return this.everyPrice.discountPrice
        }
        return this.everyPrice.price
      },
      // 最终到手价
      finalPrice() {
        if (this.groupActivityId) {
          return
        }
        if (this.everyPrice.finalPrice) {
          return this.everyPrice.finalPrice
        }
      },
      scorePrice() {
        if (this.prodType == 3) {
          return this.defaultSku.scorePrice
        }
      },
      isVip() {
        const userInfo = this.$store.state.userInfo
        if (userInfo?.levelType > 0 || userInfo?.level > 1 ) {
          return true
        }
        return false
      }

		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			// #ifdef H5
			this.isWechat = Wechat.isWechat()
 			// #endif
			// #ifdef APP-PLUS
			this.isWechat = false
			// #endif

			this.setData({
				prodId: this.$Route.query.prodid,
				orderNumber: this.$Route.query.orderNumber,
				distributionCardNo: this.$Route.query.cardno || '',
			})
			// 普通的跳转
			if (this.$Route.query.prodid) {
				this.executionFunction()
			}
			// 扫码进入 (小程序)
			if (this.$Route.query.scene) {
				let scene = decodeURIComponent(this.$Route.query.scene)
				// 如果是微信小程序中下载保存的二维码
				if (scene.indexOf(',') !== -1) {
					this.setData({
						prodId: scene.split(',')[0],
						distributionCardNo: scene.split(',')[1]
					});
					this.saveShareLog()
					this.executionFunction()
				} else {
					this.setData({
						scene: scene
					});
					//根据Ticket获取保存的内容
					this.getContent();
				}
			}
			if (this.$Route.query.cardno || this.$Route.query.isShare) {
				this.saveShareLog()
			}
		},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {
			clearTimeout(this.joinGroupListTimer);
			clearTimeout(this.endOfGroupTimer);
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			uni.setNavigationBarTitle({
				title: this.i18n.commodityDetails
			});
			// 加载购物车数字
			// this.getCartCount()
			// 加载评论数据
			this.getProdCommData();
			// 加载评论分页
			this.getProdCommPage('', 1);
		},

		/**
		 * 页面相关事件处理函数--监听页面隐藏
		 */
		onHide: function() {
			this.skuShow = false
			this.popupShowHiden = false
			if (this.isPlaying) {
				this.stopPlay()
			}
		},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 页面滚动事件
		 */
		onPageScroll: function(e) {
			this.scrollTop = e.scrollTop
			if (this.scrollTop > 1500) {
				this.setData({
					showBacktop: true
				});
			} else if (this.scrollTop < 1500) {
				this.setData({
					showBacktop: false
				});
			}
		},
		/**
		 * 用户点击转发
		 */
		onShareAppMessage: function(res) {
			if (res.from === 'button') {
				var cardno = wx.getStorageSync("distCardNo");
				return {
					title: this.prodName,
					path: '/pages/prod/prod?prodid=' + this.prodId + '&cardno=' + cardno + '&isShare=1',
					imageUrl: this.pic
				};
			} else {
				return {
					title: this.prodName,
					path: '/pages/prod/prod?prodid=' + this.prodId + '&isShare=1',
					imageUrl: this.pic
				};
			}
		},
		watch: {
			skuShow(nv) {
				if (nv && this.isPlaying) {
					this.stopPlay()
				}
			},
			commentShow(nv) {
				if (nv && this.isPlaying) {
					this.stopPlay()
				}
			},
			showDiscountPopup(nv) {
				if (nv && this.isPlaying) {
					this.stopPlay()
				}
			},
			popupShow(nv) {
				if (nv && this.isPlaying) {
					this.stopPlay()
				}
			}
		},

		methods: {
			gotovip() {
				util.checkAuthInfo(() => {
				  uni.navigateTo({
				    url: '/packageMemberIntegral/pages/buyVip/buyVip'
				  });
				});
			},
			/**
			 * 输入商品数量
			 */
			prodNumInp(e) {
				let num = Number(e.detail.value.replace(/[^\d]/g, ''))
				this.prodNum = num
			},
			clickClosePop(name){
				this.setData({
					[name]:false,
					popupShowHiden:false
				})
			},
			/**
			 * 获取购物车
			 */
			getCartCount() {
				var params = {
					url: '/p/shopCart/prodCount',
					method: "GET",
					dontTrunLogin: true,
					callBack: res => {
						this.totalCartNum = res
					}
				}
				http.request(params)
			},
			/**
			 * 咨询客服
			 */
			handleCustomService() {
				util.checkAuthInfo(() => {
					uni.navigateTo({
						url: '/packageUser/pages/chat/chatIm?shopid=' + this.shopId + '&prodid=' + this.prodId
					});
				})
			},

			/**
			 * 执行函数
			 */
			executionFunction: function() {
				//加载商品数据
				this.getProdInfo();
				// 获取商品是否被收藏信息
				this.getCollection()
				// 查询分销开关是否开启
				this.getDistInfo()


				//分销员转发过来的，需要绑定用户
				// if (this.cardno) {
				// 	this.bindDistUser(this.cardno);
				// }

				// 获取商品所有促销活动
				this.getPordDiscountList();
				// // 获取商品预计到手价
				// this.getEveryPrice()
        
			},

			/**
			 * 根据Ticket获取保存的内容
			 */
			getContent: function() {
				http.request({
					url: "/qrcodeTicket/getContent",
					method: "GET",
					data: {
						ticket: this.scene
					},
					callBack: res => {
						var content = JSON.parse(res.content);
						var ids = JSON.parse(content.content);
						if (res.type == 1) {
							//pc团购二维码跳转
							this.setData({
								prodId: ids.prodId,
								groupActivityId: ids.groupActivityId
							});
						} else if (res.type == 2) {
							//分销员二维码跳转
							this.setData({
								prodId: ids.shareProdId,
								distributionCardNo: ids.cardNo
							});
						}
						this.executionFunction(); //所有需要加载的函数
						this.saveShareLog()
					}
				});
			},

			/**
			 * 分销绑定用户 - 已弃用
			 */
			// bindDistUser(cardno) {
			// 	http.request({
			// 		url: "/p/distribution/bindUser",
			// 		method: "post",
			// 		data: cardno
			// 	});
			// },

			/**
			 * 查询分销相关信息
			 */
			getDistInfo() {
				//查询分销开关是否开启
				http.request({
					url: "/p/distribution/distributionBasicSet/canDistribution",
					method: "GET",
					dontTrunLogin: true,
					// data: {
					//   shopId: this.data.shopId,
					// },
					callBack: res => {
						if (res == 1) {
              this.getIsDistProd();
						}
					}
				});
			},


			/**
			 * 查询是否为分销商品
			 */
			getIsDistProd() {
				http.request({
					url: "/p/distribution/prod/isStateByProdId",
					method: "GET",
					data: {
						prodId: this.prodId,
						state: 1
					},
					callBack: res => {
						if (res) {
							this.setData({
								isDistProd: res.isDistribution,
                sharingPrice: res.sharingPrice
							});
						}
					}
				});
			},

			/**
			 * 获取商品是否被收藏信息
			 */
			getCollection() {
				uni.showLoading();
				var params = {
					url: "/p/user/collection/isCollection",
					method: "GET",
					dontTrunLogin: true,
					data: {
						prodId: this.prodId
					},
					callBack: res => {
						this.setData({
							isCollection: res
						});
						uni.hideLoading()
					}
				};
				http.request(params);
			},

			/**
			 * 去往优惠套餐详情
			 */
			toDiscountPackageDetail(id) {
				uni.navigateTo({ url: '/packageShop/pages/discount-package-detail/discount-package-detail?comboId=' + id + '&shopId=' + this.shopId })
			},

			/**
			 * 添加或者取消收藏商品
			 */
			addOrCannelCollection() {
				util.tapLog(3, { shopId: this.shopId })
				util.checkAuthInfo(() => {
					var params = {
						url: "/p/user/collection/addOrCancel",
						method: "POST",
						data: this.prodId,
						callBack: res => {
							this.setData({
								isCollection: !this.isCollection
							});
							uni.showToast({
								title: res ? this.i18n.collectionAdded : this.i18n.collectionCancelled,
								duration: 1200,
								icon: 'none',
							})
						}
					};
					http.request(params);
				})
			},
			// 保存浏览记录
			prodBrowseLog() {
				http.request({
					url: "/p/prodBrowseLog",
					method: "POST",
					data: {
						prodId: this.prodId
					},
					callBack: res => {
					}
				});
			},
			// 获取商品信息
			getProdInfo() {
				uni.showLoading();
				var params = {
					url: "/prod/prodInfo",
					method: "GET",
					data: {
						prodId: this.prodId,
						...( !!this.orderNumber ? { orderNumber: this.orderNumber } : {})
					},
					dontTrunLogin: true,
					callBack: async res => {
						this.prodInfo = res
						var imgStrs = res.imgs;
						var imgs = imgStrs.split(",");
						var content = util.formatHtml(res.content);
						this.setData({
							imgs: imgs,
							content: content,
							price: res.price,
							prodName: res.prodName,
							brief: res.brief,
							prodId: res.prodId,
							brief: res.brief,
							totalStocks: res.totalStocks,
							skuList: res.skuList,
							pic: res.pic,
							shopId: res.shopId,
							prodType: res.prodType, //商品类型(0普通商品 1拼团 2秒杀 3积分 5活动商品)
							mold: res.mold, // 1虚拟商品
							deliveryModeVO: res.deliveryModeVO, // 配送方式
							video: res.video ? res.video : '',
							liveRoomParams: res.liveRoomParams, // 直播列表
							preSellStatus: res.preSellStatus, // 预售状态 1：开启 0：未开启
							giveawayList: res.giveaway?res.giveaway.giveawayProds:[], // 赠品商品栏
							comboList: res.comboList || [], //套装商品列表
							preSellTime: res.preSellTime, //预售发货时间
							// 虚拟商品
							virtualRemarks: res.virtualRemark ? JSON.parse(res.virtualRemark) : [], // 留言
							isRefund: res.isRefund, // 0不支持退款 1支持退款
							writeOffTime: res.writeOffTime, // 核销有效期 -1.长期有效 0.自定义 x.x天内有效
							writeOffNum: res.writeOffNum, // 核销次数 -1.多次核销 0.无需核销 1.单次核销
							writeOffStart: res.writeOffStart, // 核销有效期开始时间
							writeOffEnd: res.writeOffEnd, // 核销有效期结束时间
							prodParameterList: res.prodParameterList,
              checkBuyerOk: res.checkBuyerOk, // 用户是否符合购买条件 （可能是学校限制，用户只能购买自己学校店铺的商品；可能是该商品仅限新用户购买）
              hasNewerLimit: res.hasNewerLimit === 1,
							labels: res.labels,
						})
						this.preSellTime = res.preSellTime
						// 是否预售
						if(res.preSellStatus){
							this.preSellActivityCountdown()
						}

						// 初始化视频
						if (res.video) {
							this.$nextTick(() => {
								this.videoContext = uni.createVideoContext('myVideo', this)
							})
						}
						// 团购商品
						if (res.prodType === 1) {
							this.setData({
								groupActivityId: res.activityId
							});
							this.getGroupActivity();
						}

						// 获取优惠券
						this.getCouponList();
						// 组装sku
						this.groupSkuProp(res.skuList, res.price);
						this.getShopInfo();
						this.getEveryPrice()
            // 查询用户在该店铺是否为新用户
            this.isNewUser = await util.checkNewUser(this.shopId);
						uni.hideLoading()
						if(uni.getStorageSync('token')) {
							this.prodBrowseLog()
						}
						// 虚拟商品
						if (this.virtualRemarks && this.virtualRemarks.length) {
							this.virtualRemarks.forEach(el => el.value = '')
						}
					},
					errCallBack: err => {
						if (err.statusCode == 400) {
							uni.showModal({
								title: this.i18n.tips,
								content: err.data,
								showCancel: false,
								cancelText: this.i18n.cancel,
								confirmText: this.i18n.confirm,
								success: (res) => {
									const pages = getCurrentPages()
									if (res.confirm) {
										if (pages.length > 1) {
											uni.navigateBack({
												delta: 1
											})
										} else {
											uni.switchTab({
												url: '/pages/index/index'
											})
										}
									}
								}
							})
						}
					}
				};
				http.request(params);
			},

			/**
			 * 获取店铺信息
			 */
			getShopInfo() {
				http.request({
					url: "/shop/headInfo",
					method: "GET",
					data: {
						shopId: this.shopId
					},
					callBack: res => {
						this.setData({
							shopInfo: res
						})
						uni.setStorageSync('shopInfo', res)
					}
				});
			},

			getProdCommData() {
				http.request({
					url: "/prod/prodCommData",
					method: "GET",
					data: {
						prodId: this.prodId
					},
					callBack: res => {
						this.setData({
							prodCommData: res
						});
					}
				});
			},

			// 获取部分评论
			getLittleProdComm() {
				if (this.prodCommPage.records.length) {
					return;
				}
				this.getProdCommPage();
			},

			getMoreCommPage(e) {
				this.getProdCommPage();
			},

			/**
			 * 获取分页获取评论
			 */
			getProdCommPage(e, current) {
				if (e) {
					if (e.currentTarget.dataset.evaluate === this.evaluate) {
						return;
					}
					util.tapLog(3, { shopId: this.shopId })

					this.setData({
						prodCommPage: {
							current: 0,
							pages: 0,
							records: []
						},
						evaluate: e.currentTarget.dataset.evaluate
					});
				}

				http.request({
					url: "/prod/prodCommPageByProd",
					method: "GET",
					data: {
						prodId: this.prodId,
						size: 10,
						current: current || this.prodCommPage.current + 1,
						evaluate: this.evaluate
					},
					callBack: res => {
						res.records.forEach(item => {
							if (item.pics) {
								item.pics = item.pics.split(',');
							}
						});
						let records = this.prodCommPage.records;
						records =  res.current === 1 ? res.records : records.concat(res.records);
						this.setData({
							prodCommPage: {
								current: res.current,
								pages: res.pages,
								records: records
							},
							littleCommPage: records.filter((el,index) => { return index < 2 })
						});
					}
				});
			},

			/**
			 * 评论小图点击事件(点击图片显示大图)
			 */
			clickImg: function(e) {
				const current = e.currentTarget.dataset.src //获取当前点击的 图片 url
				var pics = e.currentTarget.dataset.pics
				uni.previewImage({
					current: current,
					urls: pics
				})
			},

			getCouponList() {
				http.request({
					url: "/coupon/listByProdId",
					method: "GET",
					data: {
						prodId: this.prodId,
						shopId: this.shopId,
						...( !!this.orderNumber ? { orderNumber: this.orderNumber } : {})
					},
					dontTrunLogin: true,
					callBack: res => {
						// this.initCouponFlag(res)
						this.setData({
							couponList: res
						});
					}
				});
			},

			/**
			 * 团购商品详情信息
			 */
			getGroupActivity() {
				http.request({
					url: "/groupProd/info",
					method: "GET",
					data: {
						prodId: this.prodId,
						groupActivityId: this.groupActivityId
					},
					callBack: res => {
						if (res.success) {
							this.setData({
								groupActivity: res.obj,
								maxNum: res.obj.maxNum,
								hasMaxNum: res.obj.hasMaxNum
							});
							this.setDefaultGroupSku();
							this.groupActivityCountdown(res.obj);
							if (res.obj.hasGroupTip) {
								this.getJoinGroupList()
							}
						} else {
							this.setData({
								groupActivityId: null
							});
						}
					}
				});
			},

			// 团购倒计时
			groupActivityCountdown(groupActivity) {
				let endOfGroupTime; // activityStatus 1 未开始

				if (groupActivity.activityStatus === 1) {
					endOfGroupTime = util.endOfStartTime(new Date().getTime(), util.dateToTimestamp(groupActivity.startTime));
				} else {
					endOfGroupTime = util.endOfStartTime(new Date().getTime(), util.dateToTimestamp(groupActivity.endTime));
				}
				if (this.endOfGroupTime.signs == 0) {
					clearTimeout(this.joinGroupListTimer);
					clearTimeout(this.endOfGroupTimer);
					// 拼团结束，刷新当前页面
					setTimeout(()=>{
						uni.redirectTo({
							url: '/pages/prod/prod?prodid=' + this.prodId
						})
					},100)
					return
				}
				this.setData({
					endOfGroupTime: endOfGroupTime,
					endOfGroupTimer: setTimeout(() => this.groupActivityCountdown(groupActivity), 1000)
				});
			},

			// 预售倒计时
			preSellActivityCountdown() {

				let endPreSellTime = new Date(this.preSellTime.replace(new RegExp('-', 'g'), '/')).getTime()
				let nowTime = new Date().getTime()
				if(nowTime>endPreSellTime){
					clearTimeout(this.preSellTimer)
					//预售结束，刷新当前页面
					this.preSellStatus = 0
					return
				}
				if (this.preSellStatus) {
					this.setData({
						preSellTimer: setTimeout(() => this.preSellActivityCountdown(), 1000)
					})
				}

			},

			// 可加入的拼团列表
			getJoinGroupList() {
				http.request({
					url: "/groupProd/joinGroupList",
					method: "GET",
					data: {
						prodId: this.prodId,
						groupActivityId: this.groupActivityId,
						showSize: 3
					},
					callBack: res => {
						this.setData({
							joinGroupList: res
						});
						this.joinGroupListCountdown(res);
					}
				});
			},

			joinGroupListCountdown(joinGroupList) {
				joinGroupList.forEach(item => {
					item.endOfGroupTime = util.endOfStartTime(new Date().getTime(), util.dateToTimestamp(item.endTime));
					if (!item.endOfGroupTime.signs) {
						uni.redirectTo({
							url: '/pages/prod/prod?prodid=' + this.prodId
						})
					}
				});
				this.setData({
					joinGroupList: joinGroupList,
					joinGroupListTimer: setTimeout(() => this.joinGroupListCountdown(joinGroupList), 1000)
				});
			},

			/**
			 * 组装SKU
			 */
			groupSkuProp: function(skuList, defaultPrice) {
				if (skuList.length == 1 && !skuList[0].properties) {
					this.defaultSku = skuList[0]
					this.setDefaultGroupSku();
					return;
				}
				var skuGroup = {};
				var allProperties = [];
				var propKeys = [];
				var selectedPropObj = {}
				var defaultSku = null;
				for (var i = 0; i < skuList.length; i++) {
					var isDefault = false;
					if (!defaultSku && skuList[i].price == defaultPrice) { //找到和商品价格一样的那个SKU，作为默认选中的SKU
						defaultSku = skuList[i];
						isDefault = true;
					}
					var properties = skuList[i].properties; //版本:公开版;颜色:金色;内存:64GB
					allProperties.push(properties);
					var propList = properties.split(";"); // ["版本:公开版","颜色:金色","内存:64GB"]

					for (var j = 0; j < propList.length; j++) {

						var propval = propList[j].split(":"); //["版本","公开版"]
						var props = skuGroup[propval[0]]; //先取出 版本对应的值数组

						//如果当前是默认选中的sku，把对应的属性值 组装到selectedProp
						if (isDefault) {
							propKeys.push(propval[0]);
							selectedPropObj[propval[0]] = propval[1];
						}

						if (props == undefined) {
							props = []; //假设还没有版本，新建个新的空数组
							props.push(propval[1]); //把 "公开版" 放进空数组
						} else {
							if (props.indexOf(propval[1]) === -1) { //如果数组里面没有"公开版"
								props.push(propval[1]); //把 "公开版" 放进数组
							}
						}
						skuGroup[propval[0]] = props; //最后把数据 放回版本对应的值
					}
				}
				this.defaultSku = defaultSku
				this.propKeys = propKeys
				this.selectedPropObj = selectedPropObj
				this.parseSelectedObjToVals(skuList);
				this.skuGroup = skuGroup
				this.allProperties = allProperties
				this.setDefaultGroupSku();
			},

			/**
			 * 将已选的 {key:val,key2:val2}转换成 [val,val2]
			 */
			parseSelectedObjToVals: function(skuList) {
				var selectedPropObj = this.selectedPropObj
				var selectedProperties = "";
				var selectedProp = [];
				for (var key in selectedPropObj) {
					// selectedProp.push(selectedPropObj[key]);
					selectedProp.push({key: key, value: selectedPropObj[key] });
					selectedProperties += key + ":" + selectedPropObj[key] + ";";
				}
				selectedProperties = selectedProperties.substring(0, selectedProperties.length - 1);
				this.selectedProp = selectedProp
				this.selectedProperties = selectedProperties
				this.selectedPropObj = selectedPropObj

				var findSku = false;
				for (var i = 0; i < skuList.length; i++) {
					// 解决排序问题导致无法匹配
					if (
						this.compareArray(selectedProperties.split(';').sort(),
							skuList[i].properties.split(';').sort())
					) {
						findSku = true;
						this.defaultSku = skuList[i]
						break;
					}
					// if (skuList[i].properties == selectedProperties) {
					// 	findSku = true;
					// 	this.defaultSku = skuList[i]
					// 	break;
					// }
				}
				this.findSku = findSku
				this.setDefaultGroupSku();
			},

			/**
			 * 比较两个数组中的元素是否相等
			 * @param a1 第一个数组
			 * @param a2 第二个数组
			 * @return boolean 两个数组中的元素都相等则返回 true，反之返回 false
			 */
			compareArray(a1, a2) {
				if (!a1 || !a2) {
					return false;
				}
				if (a1.length !== a2.length) {
					return false;
				}
				for (var i = 0, n = a1.length; i < n; i++) {
					if (a1[i] !== a2[i]) {
						return false;
					}
				}
				return true;
			},

			/**
			 * 判断当前的规格值 是否可以选
			 */
			isSkuLineItemNotOptional(allProperties, selectedPropObj, key, item, propKeys) {
				var selectedPropObj = Object.assign({}, selectedPropObj)
				var properties = "";
				selectedPropObj[key] = item;
				for (var j = 0; j < propKeys.length; j++) {
					properties += propKeys[j] + ":" + selectedPropObj[propKeys[j]] + ";";
				}
				properties = properties.substring(0, properties.length - 1);
				for (var i = 0; i < allProperties.length; i++) {
					if (properties == allProperties[i]) {
						return false;
					}
				}
				for (var i = 0; i < allProperties.length; i++) {
					if (allProperties[i].indexOf(item) >= 0) {
						return true;
					}
				}
				return false;
			},

			/**
			 * 规格点击事件
			 */
			toChooseItem(skuLineItem, key, event) {
				util.tapLog(3, { shopId: this.shopId })
				this.selectedPropObj[key] = skuLineItem;
				this.parseSelectedObjToVals(this.skuList);
				this.getEveryPrice()
			},

			//判断数组是否包含某对象
			array_contain: function(array, obj) {
				for (var i = 0; i < array.length; i++) {
					if (array[i] == obj) //如果要求数据类型也一致，这里可使用恒等号===
						return true;
				}
				return false;
			},
			/**
			 * 设置选中的拼团sku
			 */
			setDefaultGroupSku() {
				if (this.groupActivityId) {
					var groupSkuList = this.groupActivity.groupSkuList;

					if (groupSkuList) {
						if (groupSkuList.length === 1 && !groupSkuList[0].properties) {
							this.defaultGroupSku = groupSkuList[0]
							return
						}
						for (var i = 0; i < groupSkuList.length; i++) {
							if (groupSkuList[i].properties == this.selectedProperties) {
								this.setData({
									defaultGroupSku: groupSkuList[i]
								});
								break;
							}
						}
					}
				}
			},

			/**
			 * 去凑团
			 */
			toSpellGroupDetail(e) {
				util.tapLog(3, { shopId: this.shopId })
				const groupTeamId = e.currentTarget.dataset.groupteamid
				util.checkAuthInfo(() => {
					uni.navigateTo({
						url: '/packageActivities/pages/spellGroupDetails/spellGroupDetails?groupTeamId=' + groupTeamId
					})
				})
			},

			/**
			 * 跳转到首页
			 */
			toHomePage: function() {
				util.tapLog(3, { shopId: this.shopId })
				this.$Router.pushTab('/pages/index/index')
			},

			/**
			 * 跳转到满折
			 */
			toDiscountList(id){
				uni.navigateTo({
					url:'/packageActivities/pages/discountDetail/discountDetail?discountId='+ id
				})
			},

			/**
			 * 跳转到赠品详情
			 */
			toGiveawaysProd(id){
				uni.navigateTo({
					url:'/pages/prod/prod?prodid='+ id
				})
			},
			/**
			 * 跳转到购物车
			 */
			toCartPage: function() {
				util.tapLog(3, { shopId: this.shopId })
				this.$Router.pushTab('/pages/basket/basket')
			},

			/**
			 * 加入购物车
			 */
			addToCart: function(event) {
				this.getCommonCoupon()
				if (!this.skuShow) {
					this.skuShow = true
					this.popupShowHiden = true
					return
				}
				if (!this.findSku) {
					return;
				}
				uni.showLoading({
					// #ifndef MP-TOUTIAO
					mask: true
					// #endif
				}); // 查看是否授权

				util.checkAuthInfo(this.callChangeItem);
			},

			callChangeItem() {
				if (this.prodNum < 1) {
					uni.showToast({
						title: this.i18n.leastTips,
						icon: 'none'
					})
					this.prodNum = 1
					return
				}
				uni.showLoading({
					// #ifndef MP-TOUTIAO
					mask: true
					// #endif
				});
				http.request({
					url: "/p/shopCart/changeItem",
					method: "POST",
					data: {
						basketId: 0,
						count: this.prodNum,
						prodId: this.prodId,
						shopId: this.shopId,
						shopName: this.shopName,
						skuId: this.defaultSku.skuId,
						distributionCardNo: this.distributionCardNo,
            virtualRemarkList: this.virtualRemarks,
					},
					callBack: res => {
						util.tapLog(4, {
              orderNumbers: null,
              isPayRes: null,
              prodNum: this.prodNum,
              shopId: this.shopId,
            })
						this.setData({
							totalCartNum: this.totalCartNum + this.prodNum,
							skuShow: false,
							popupShowHiden: false
						});
						uni.showToast({
							title: this.i18n.successfullyAddedCart,
							icon: "none"
						});
					}
				});
			},


			/**
			 * 立即购买
			 */
			buyNow() {
        if (!this.checkBuyerOk) {
          if (this.hasNewerLimit && !this.isNewUser) {
            uni.showToast({
              title: '此商品为新用户专享',
              icon: 'none',
            })
            return
          }
          uni.showToast({
						title: '不符合购买条件',
						icon: 'none',
					})
          return
        }
        
				this.getCommonCoupon()
				util.tapLog(3, { shopId: this.shopId })
				if (!this.skuShow) {
					this.skuShow = true
					this.popupShowHiden = true
					return
				}
				if (!this.findSku) {
					return;
				}
				if (this.prodNum < 1) {
					uni.showToast({
						title: this.i18n.leastTips,
						icon: 'none'
					})
					this.prodNum = 1
					return
				}
				if (!this.defaultSku.stocks || this.defaultSku.stocks < this.prodNum) {
					uni.showToast({
						title: this.i18n.insufficientStock,
						icon: 'none',
					})
					return
				}
        if (this.mold === 1 && !this.isCustomRemark) {
          // 虚拟商品
          if (this.virtualRemarks.find(el => el.value && !el.value.trim())) {
            uni.showToast({
              title: this.i18n.mesgCannotBeAllSpaces,
              icon: 'none'
            })
            return
          }
          if(this.virtualRemarks.find(el => !el.value && el.isRequired)) {
            uni.showToast({
              title: this.i18n.requiredMessage,
              icon: 'none'
            })
            return
          }
          // 将已填写的留言存起来(在提交订单页面展示)
          let remarks = []
          this.virtualRemarks.forEach(el => {
            if (el.value) {
              remarks.push({
                ...el,
                prodId: this.prodId
              })
            }
          })
          uni.setStorageSync('virtualRemark', JSON.stringify(remarks))
        }
				uni.showLoading({
					// #ifndef MP-TOUTIAO
					mask: true
					// #endif
				});
				// 查看是否授权
				util.checkAuthInfo(() => {
					uni.setStorageSync("orderItem", JSON.stringify({
						prodId: this.prodId,
						skuId: this.defaultSku.skuId,
						prodCount: this.prodNum,
						shopId: this.shopId,
						distributionCardNo: this.distributionCardNo
					}))
					uni.navigateTo({
						url: '/pages/submit-order/submit-order?orderEntry=1&prodType=' + this.prodType + '&mold=' + this.mold || ''
					})
				});
			},

			/**
			 * 减数量
			 */
			onCountMinus: function() {
				util.tapLog(3, { shopId: this.shopId })
				var prodNum = this.prodNum;
				if (prodNum > 1) {
					this.setData({
						prodNum: prodNum - 1
					});
				}
			},

			/**
			 * 加数量
			 */
			onCountPlus: function() {
				util.tapLog(3, { shopId: this.shopId })
				var prodNum = parseInt(this.prodNum); // 取整数
				// 判断是否限购
				if (this.hasMaxNum && this.skuShowType == 1) {
					if (prodNum < this.defaultSku.stocks && prodNum < this.maxNum) {
						this.setData({
							prodNum: prodNum + 1
						});
					} else if (prodNum >= this.defaultSku.stocks && this.defaultSku.stocks < this.maxNum) {
						uni.showToast({
							title: this.i18n.insufficientStock,
							icon: 'none',
							duration: 1000,
							// #ifndef MP-TOUTIAO
							mask: true
              // #endif
						})
					} else {
						uni.showToast({
							title: this.i18n.purchaseLimit + this.maxNum + this.i18n.piece,
							icon: 'none'
						});
					}
				} else {
					if (prodNum < this.defaultSku.stocks) {
						this.setData({
							prodNum: prodNum + 1
						});
					} else {
						uni.showToast({
							title: this.i18n.insufficientStock,
							icon: 'none',
							duration: 1000,
							// #ifndef MP-TOUTIAO
							mask: true
              // #endif
						})
					}
				}
			},

			/**
			 * 优惠券
			 */
			showPopup: function() {
				util.tapLog(3, { shopId: this.shopId })
				if (this.loadCouponIds) {
					this.setData({
						popupShow: true,
						popupShowHiden: true
					});
					return;
				}

				http.request({
					url: "/p/myCoupon/listCouponIds",
					method: "GET",
					data: {},
					callBack: myCouponList => {
						var couponList = this.couponList;
						couponList.forEach(coupon => {
							if (myCouponList && myCouponList.length) {
								// 领取该优惠券数量
								var couponLimit = 0
								// 改用户是否有可以使用的优惠券
								var isMyUseCoupon = false
								myCouponList.forEach(myCouponItem => {
									if (myCouponItem.couponId == coupon.couponId) {
										couponLimit = couponLimit + myCouponItem.curUserReceiveCount
										if (myCouponItem.status == 1 && myCouponItem.curUserReceiveCount > 0) {
											isMyUseCoupon = true
										}
									}
								});
								
								// // 判断用户是否到达领取优惠券上限
								// if (couponLimit >= coupon.limitNum || coupon.stocks == 0) {
								// 	// 如果用户有该优惠券，则可以去使用
								// 	if (isMyUseCoupon) {
								// 		coupon.canGoUse = true
								// 	} 
								// 	// 如果没有，就不可使用，为了兼容为灰色不可用背景，设置库存为0
								// 	else {
								// 		coupon.canGoUse = false
								// 		coupon.stocks = 0
								// 	}
								// }
								// // 其他情况下如果有这个优惠券，都可以去使用
								// else if (isMyUseCoupon) {
								// 	coupon.canGoUse = true
								// }
								// else {
								// 	coupon.canGoUse = false
								// }

								// 判断用户是否到达领取优惠券上限
								if (couponLimit >= coupon.limitNum || coupon.stocks == 0) {
									coupon.canReceive = false
									// 判断领取优惠券到达上限后,该种优惠券如果没有可用优惠券,则不可领取和使用
									if (isMyUseCoupon == false) {
										coupon.canUse = false
									} else {
										coupon.canUse = true
									}
								} else {
									coupon.canUse = true
									coupon.canReceive = true
								}

							} else {
								coupon.canUse = true
								coupon.canReceive = true
							} 
						});
						this.setData({
							couponList: couponList,
							popupShow: true,
							popupShowHiden: true,
							loadCouponIds: true
						});
					}
				});
			},
			showSku: function(e) {
				util.tapLog(3, { shopId: this.shopId })
				if (e?.currentTarget.dataset.alonebuy == 1) {
					this.setData({
						skuShow: true,
						popupShowHiden: true,
						skuShowType: 0
					});
				} else {
					if (this.groupActivityId) {
						this.setData({
							skuShow: true,
							popupShowHiden: true,
							skuShowType: 1
						});
					} else {
						this.setData({
							skuShow: true,
							popupShowHiden: true,
							skuShowType: 0
						});
					}
				}
			},
			showGroupSku: function() {
				util.tapLog(3, { shopId: this.shopId })
				if (this.isPlaying) {
					this.stopPlay()
				}
				if (this.groupActivity.activityStatus === 1) {
					uni.showToast({
						title: this.i18n.actNotBegin,
						icon: 'none',
					})
					return
				}
				this.setData({
					skuShow: true,
					popupShowHiden: true,
					skuShowType: 1
				});
			},
			showComment: function(e) {
				util.tapLog(3, { shopId: this.shopId })
				this.setData({
					commentShow: true,
					evaluate: e,
					prodCommPage: {
						current: 0,
						pages: 0,
						records: []
					},
				});
				this.getProdCommPage();
			},
			closePopup: function() {
				util.tapLog(3, { shopId: this.shopId })
				this.setData({
					popupShow: false,
					skuShow: false,
					commentShow: false,
					popupShowHiden: false
				});
				this.getShopInfo()
			},
			onShowShare: function() {
        this.setData({
          shareShow: true,
          popupShowHiden: true
        });
			},
			closeEarn: function() {
				util.tapLog(3, { shopId: this.shopId })
				this.setData({
					shareShow: false,
					popupShowHiden: false,
				});
			},
			toDistCenterPage: function() {
				uni.navigateTo({
					url: '/packageDistribution/pages/dis-center/dis-center'
				});
			},

			/**
			 * 关闭二维码弹窗
			 */
			closeCodePopup() {
				util.tapLog(3, { shopId: this.shopId })
				this.setData({
					popupShowHiden: false
				});
			},

			/**
			 * 保存图片至相册
			 */
			downloadImg() {
				util.tapLog(3, { shopId: this.shopId })
				uni.showLoading({
					// #ifndef MP-TOUTIAO
					mask: true
					// #endif
				})
				// #ifdef APP-PLUS
				const bitmap = new plus.nativeObj.Bitmap('test')
				bitmap.loadBase64Data(
					this.shareWxCode,
					() => {
						const url = '_doc/' + new Date() + '.png'; // url建议用时间戳命名方式
						bitmap.save(
							url,
							{
								overwrite: true
							},
							i => {
								uni.saveImageToPhotosAlbum({
									filePath: url,
									success: () => {
										uni.hideLoading()
										this.popupShowHiden = false
										uni.showToast({
											title: this.i18n.downloadComplete,
										})
										bitmap.clear()
									}
								})
							},
							e => {
								uni.hideLoading()
								// alert('保存失败1111', JSON.stringify(e))
								bitmap.clear()
							}
						)
					},
					e => {
						// alert('保存失败2222', JSON.stringify(e))
						uni.hideLoading()
						bitmap.clear()
					}
				)
				// #endif
				// #ifdef MP-WEIXIN
				var cardno = wx.getStorageSync("distCardNo")
				wx.downloadFile({
					header: {
						'Authorization': wx.getStorageSync('token')
					},
					url: config.domain + "/p/distribution/qrCode/invitation?page=pages/prod/prod&scene=" + this.prodId + ',' +
						cardno,
					success: (res) => {
						uni.hideLoading()
						//图片保存到本地
						wx.getSetting({
							success: settingData => {
								let flag = settingData.authSetting['scope.writePhotosAlbum']
								if(flag === true || flag === undefined){
									uni.saveImageToPhotosAlbum({
										filePath: res.tempFilePath,
										success: data => {
											this.popupShowHiden = false
											uni.showToast({
												title: this.i18n.downloadComplete,
												icon: 'success',
												duration: 2000
											})
										},
										fail: err => {
											uni.showToast({
												title: this.i18n.failedSaveTips,
												icon: 'none',
												duration: 2000
											})
										}
									})
								} else {
									wx.openSetting({
										success: settingData => {
											if (settingData.authSetting['scope.writePhotosAlbum']) {
												uni.showToast({
													title: this.i18n.failedSave,
													icon: 'none',
													duration: 2000
												})
											} else {
												uni.showToast({
													title: this.i18n.failedSaveTips,
													icon: 'none',
													duration: 2000
												})
											}
										}
									})
								}
							}
						})
					}
				})
				// #endif
			},

			/**
			 * 用户点击分享到微信小程序
			 * @param {Object} sharedata 支付需要的参数
			 * @param {Object} cb 成功回调
			 * @param {Object} errorCb 失败回调
			 */
			onShareWay() {
				var cardno = wx.getStorageSync("distCardNo");
				var sharedata = {
					title: this.prodName, // 分享标题
					imgUrl: this.imgs[0], // 分享图标
					link: config.domainAddress + '/pages/prod/prod?prodid=' + this.prodId + '&cardno=' + cardno, // 分享链接
					desc: this.brief ? this.brief : this.i18n.shartTips, // 分享描述
					type: '', // 分享类型,music、video或link，不填默认为link
					dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
				};
				Wechat.callWexinShare(sharedata, () => {
					this.shareShow = false
					this.guideShare = true
				}, (failMsg) => {
					console.log(failMsg)
				})
			},

			/**
			 * 确认开团
			 */
			groupConfirmOrder: function(e) {
				util.tapLog(3, { shopId: this.shopId })
				if (!this.findSku) {
					return;
				}
				if (this.groupActivity.activityStatus === 1) {
					uni.showToast({
						title: this.i18n.actNotBegin,
						icon: 'none',
					})
					return
				}
				if (this.prodNum < 1) {
					uni.showToast({
						title: this.i18n.leastTips,
						icon: 'none'
					})
					this.prodNum = 1
					return
				}
				if (this.prodNum > this.defaultSku.stocks) {
					uni.showToast({
						title: this.i18n.insufficientStock,
						icon: 'none',
						duration: 1000,
						// #ifndef MP-TOUTIAO
						mask: true
						// #endif
					})
					return
				}
        if (this.mold === 1 && !this.isCustomRemark) {
          // 虚拟商品
          if (this.virtualRemarks.find(el => el.value && !el.value.trim())) {
            uni.showToast({
              title: this.i18n.mesgCannotBeAllSpaces,
              icon: 'none'
            })
            return
          }
          if(this.virtualRemarks.find(el => !el.value && el.isRequired)) {
            uni.showToast({
              title: this.i18n.requiredMessage,
              icon: 'none'
            })
            return
          }
          let remarks = []
          this.virtualRemarks.forEach(el => {
            if (el.value) {
              remarks.push({
                ...el,
                prodId: this.prodId
              })
            }
          })
          uni.setStorageSync('virtualRemark', JSON.stringify(remarks))
        }
				uni.showLoading({
					// #ifndef MP-TOUTIAO
						mask: true
						// #endif
				});
				this.setData({
					teamId: e.currentTarget.dataset.teamid
				}); // 查看是否授权

				util.checkAuthInfo(() => {
					uni.setStorageSync("groupOrderItem", JSON.stringify({
						groupSkuId: this.defaultGroupSku.groupSkuId,
						prodCount: this.prodNum,
						groupTeamId: this.teamId,
            prodId: this.prodId,
            shopId: this.shopId,
					}));
					uni.navigateTo({
						url: '/packageActivities/pages/groupConfirmOrder/groupConfirmOrder'
					});
				});
			},

			/**
			 * 跳转到店铺页
			 */
			toShopPage: function() {
				util.tapLog(3, { shopId: this.shopId })
				let url = ''
				if (this.shopInfo.renovationId) {
					url = '/pages/shop-feature-index/shop-feature-index0?shopId=' + this.shopInfo.shopId + '&renovationId=' + this.shopInfo.renovationId
				} else {
					url = '/packageShop/pages/shopPage/shopPage?shopId=' + this.shopInfo.shopId
				}
				uni.navigateTo({
					url
				})
			},

			/**
			 * 通过商品id获取商品所有促销活动
			 */
			getPordDiscountList() {
				http.request({
					url: "/marking/discount/getDiscountByProdId",
					method: "GET",
					data: {
						prodId: this.prodId,
					},
					callBack: res => {
						this.setData({
							prodDiscountList: res
						});
					}
				});
			},
			/**
			 * 促销活动弹窗
			 */
			clickDiscount: function() {
				this.showDiscountPopup = !this.showDiscountPopup
				this.popupShowHiden = this.showDiscountPopup
			},

			/**
			 * 视频的方法
			 */
			videoOper() {
				if (this.isPlaying) {
					this.stopPlay()
				} else {
					this.startPlay()
				}
			},
			playEnd() {
				this.stopPlay()
			},

			startPlay() {
				this.setData({
					isPlaying: true,
				})
				setTimeout(() => {
					this.videoContext.seek(0)
					this.videoContext.play()
				}, 200)
			},
			stopPlay() {
				this.setData({
					isPlaying: false
				})
				setTimeout(() => {
					this.videoContext.seek(0)
					this.videoContext.stop()
				}, 200)
			},

			/**
			 * 分享方法
			 */
			handleShare() {
				// #ifdef H5

				// #endif
				// #ifdef APP-PLUS

				// #endif
			},

			/**
			 * 回到页面顶部
			 */
			handleScorllTop() {
				uni.pageScrollTo({
					duration: 100,
					scrollTop: 0
				})
			},

			/**
			 * 前往直播页面
			 */
			toLivePage: function(e) {
				this.roomId = e.currentTarget.dataset.roomid // 填写具体的房间号
				this.url = e.currentTarget.dataset.url
				util.checkAuthInfo(this.toLivePlayer)
			},
			toLivePlayer: function() {
				let roomId = this.roomId
				let url = this.url
				if (this.isWechat) {
					if (this.liveRoomParams.length == 1) {
						// 开发者在直播间页面路径上携带自定义参数（如示例中的path和pid参数），后续可以在分享卡片链接和跳转至商详页时获取
						let customParams = encodeURIComponent(JSON.stringify({
							path: url
						}))
						wx.navigateTo({
							url: `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${roomId}&custom_params=${customParams}`
						}) // 其中wx2b03c6e691cd7370是直播组件appid不能修改
					} else {
						wx.navigateTo({
							url: '/pages/liveBroadcast/liveBroadcast?prodId=' + this.prodId,
						})
					}
				} else {
					uni.showToast({
						title: this.i18n.pleaseOpenInWechat,
						icon: 'none'
					})
				}
			},

			/**
			 * 满减信息处理
			 */
			parseDiscountProd(discountRule, needAmount, discount, lang) {
				if (discountRule == 1) {
					return lang == 'zh_CN' ? '满' + needAmount + '减' + discount : '￥ ' + discount + ' off on ' + needAmount;
				} else if (discountRule == 2) {
					return lang == 'zh_CN' ? '满' + needAmount + '打' + discount + '折' : (100-discount*10) + '% off on ' + '￥ ' + needAmount;
				} else {
					return '';
				}
			},

			/**
			 * 通过分享进入
			 */
			saveShareLog() {
				let flowAnalysisLogDto = uni.getStorageSync('flowAnalysisLogDto')
				flowAnalysisLogDto.bizData = this.prodId
        flowAnalysisLogDto.shopId = this.shopId
				http.saveLog(flowAnalysisLogDto, 2)
			},
			// 获取预计到手价
			getEveryPrice() {
				if(this.defaultSku.skuId && this.prodId) {
					http.request({
						url: '/prod/prodPriceBySkuId',
						method: "GET",
						data: {
							prodId: this.prodId,
							skuId: this.defaultSku.skuId,
							...( !!this.orderNumber ? { orderNumber: this.orderNumber } : {})
						},
						dontTrunLogin: true,
						callBack: res => {
              this.setData({
                everyPrice: res
              })
						}
					})
				}
			},
			// 自动领取公开优惠券
			getCommonCoupon() {
				http.request({
					url: '/p/prodBrowseLog/autoReceiveCoupon',
					method: "GET",
					data: {
						prodId: this.prodId,
					},
					dontTrunLogin: true,
				})
			},
			// changeShowDetail(coupon) {
			// 	var tempCouponList = this.couponList;
			// 	let index = tempCouponList.findIndex(item => item.couponId === coupon.couponId)
			// 	tempCouponList[index].isShowDetail = !tempCouponList[index].isShowDetail
			// 	this.setData({
			// 		couponList: tempCouponList
			// 	})
			// },
			// initCouponFlag(couponList) {
			// 	couponList.forEach(coupon => {
			// 		coupon.isShowDetail = false
			// 	});
			// },
			// setCouponCanGoUseFlag(coupon) {
			// 	var tempCouponList = this.couponList;
			// 	let index = tempCouponList.findIndex(item => item.couponId === coupon.couponId)
			// 	tempCouponList[index].canGoUse = true;
			// 	tempCouponList[index].stocks -= 1;
			// 	this.setData({
			// 		couponList: tempCouponList
			// 	});
			// },
		}
	};
</script>
<style>
	@import "./prod.css";
	
	/* 实名认证提示样式 */
	.real-name-notice {
		display: flex;
		align-items: center;
		margin: 0 20rpx 20rpx;
		padding: 16rpx 20rpx;
		background-color: rgba(228, 57, 60, 0.1);
		border-radius: 8rpx;
	}

	.notice-warning {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 32rpx;
		height: 32rpx;
		background-color: #e4393c;
		color: #fff;
		font-weight: bold;
		border-radius: 50%;
		font-size: 24rpx;
		margin-right: 10rpx;
		text-align: center;
		line-height: 32rpx;
	}

	.notice-text {
		color: #e4393c;
		font-size: 24rpx;
		line-height: 1.4;
		flex: 1;
	}
</style>

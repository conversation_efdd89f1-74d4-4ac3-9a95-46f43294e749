page {
  background: #fff;
  height: 100%;
  overflow: visible;
}
image {
  width: 100%;
  height: 100%;
}

.container {
  height: auto;
  padding-bottom: calc(110rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
  width: 100%;
}
.container.overflow {
  min-height: 100vh;
  overflow: hidden;
}

.lock-container {
  position: fixed;
  /* top: -30rpx; */
  width: 100%;
  height: 100%;
}

swiper {
  height: 750rpx;
  width: 100%;
}

swiper image {
  height: 750rpx;
  width: 100%;
}

/* 商品视频 */
.swiper-con {
  position: relative;
}
.video-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 750rpx;
  justify-content: center;
  align-items: center;
  background: #000;
  z-index: 10;
}
.video-container video {
  display: block;
  width: 100%;
  height: 100%;
}

.play-btn {
  position: absolute;
  left: 50%;
  bottom: 12%;
  padding: 2rpx;
  background: rgba(255, 255, 255, 0.75);
  border-radius: 50rpx;
  color: #000;
  font-size: 24rpx;
  text-align: center;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 15;
}

.play-icon {
  width: 50rpx;
  height: 50rpx;
}

.play-text {
  padding-right: 10rpx;
  margin: 0 10rpx;
}

.video-stop {
  padding: 2rpx 8rpx;
}

/* 预售 */
.pre-sale {
  background: var(--primary-color);
  padding: 20rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  color: #fff;
}
.pre-icon {
  display: inline-block;
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}
.sendway-item.pre-sale-red {
	color: #E43130;
}
/** 商品信息 */

.prod-info {
  width: 100%;
  padding: 48rpx;
  box-sizing: border-box;
  border-radius: 0 0 20rpx 20rpx;
  position: relative;
  background: #fff;
  word-break: break-all;
}

.tit-wrap {
  position: relative;
  border-radius: 8px;
  border: 1px solid #F1F1F1;
  padding: 32rpx;
}

.prod-tit {
  font-size: 32rpx;
  color: #333;
  word-break: break-all;
  word-wrap: break-word;
  font-weight: bold;
  padding-bottom: 8rpx
}

.sub-tit{
	font-size: 28rpx;
	color: #888;
  border-top: 1px solid #f1f1f1;
  padding-top: 24rpx;
  margin-top: 24rpx;
}


.prod-price {
  font-size: 30rpx;
  line-height: 40rpx;
  position: relative;
  margin-bottom: 32rpx;
  position: relative;
}

.prod-price.inpup {
  transform: scale(.9);
  transform-origin: left;
}

.prod-price__line1 {
  display: flex;
  column-gap: 16rpx;
  align-items: flex-end;
  height: 72rpx;
}

.prod-price__sell-price {
  color: var(--primary-color);
  font-size: 40rpx;
  font-weight: 600;
  font-family: "HelveticaNeueBold";
}
.prod-price__sell-price.with-vip-price {
  color: #252525;
}

.prod-price__vip-price {
  color: var(--primary-color);
  font-weight: 700;
  display: flex;
  align-items: center;
  font-family: "HelveticaNeueBold";
}

.prod-price__vip-price__text {
  font-size: 28rpx;
  height: 40rpx;
}


.prod-price__sell-price__symbol {
  margin-right: 8rpx;
  font-size: 40rpx;
}

.prod-price__sell-price__big {
  font-size: 60rpx;
}
.price-dot {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.prod-price__sell-price__small {
  font-size: 40rpx;
}

.prod-price__line-through-price {
  color: #999;
  font-size: 24rpx;
  font-weight: 400;
}

.prod-price__line-through-price__value {
  text-decoration: line-through;
}

.prod-price__line2 {
  margin-top: 12rpx;
}

.prod-price__final-price {
  color: var(--primary-color);
  font-size: 24rpx;
  font-weight: 400;
  border-radius: 138rpx;
  border: 2rpx solid var(--primary-color);
  width: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 32rpx;
  padding: 4rpx 32rpx;
}

.price {
  color: var(--primary-color);
  font-size: 26rpx;
  font-weight: 600;
  margin-right: 20rpx;
}

.prod-price-has-vip-price 


.price-num {
  font-size: 46rpx;
  font-weight: 400;
}

.share-icon {
  position: absolute;
  right: 50rpx;
  top: 50rpx;
  background: none;
  line-height: 40rpx;
  border: none;
  outline: none;
  box-shadow: 0;
  padding: 0;
}

.share-icon::after {
  border: none;
}

.share-icon image {
  width: 60rpx;
  height: 60rpx;
}

.share-text {
  font-size: 26rpx;
  color: #999;
  line-height: 30rpx;
}

/* 积分商品价格 */
.integral-prod-price {
  display: inline-block;
  padding: 10rpx 0 40rpx;
  color: #e43130;
  font-size: 30rpx;
}

/** end 商品信息 */

/* 优惠套餐列表 */

.discount-package {
  background: #fff;
  margin-top: 20rpx;
  padding: 30rpx 48rpx 0 48rpx;
}

.discount-package .discount-package-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  opacity: 1;
}

.discount-package .discount-package-title-fu {
  font-size: 24rpx;
  font-weight: bold;
  color: #333333;
  opacity: 1;
}
.discount-package-concent {
}
.discount-package-scroll {
  display: flex;
}
.discount-package-concent .discount-package-item {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  margin-bottom: 30rpx;
  padding-right: 32rpx;
  padding-left: 30rpx;
  border-right: 2px solid #EEEEEE;
}
.discount-package-concent .discount-package-item:first-child {
  padding-left: 0;
}
.discount-package-concent .discount-package-item:last-child {
  border: 0;
  padding-right: 0;
}
.discount-package-item .main-image{
  width: 120rpx;
  height: 120rpx;
}
.discount-package-item .addFu{
  display: flex;
  align-items: center;
  margin: 0 20rpx;
}

/* .discount-package-item .addFu::before{
  content: '\2020';
} */

.discount-package-item .fu-prod{
  width: 120rpx;
  height: 120rpx;
  box-sizing: border-box;
  padding: 10rpx 32rpx;
  border: 1px solid #F4F4F4;
  opacity: 1;
  border-radius: 6px;
  margin-right: 20rpx;
}
.discount-package-item .fu-prod .image{
  width: 56rpx;
  height: 100rpx;
  border-radius: 6px;
  margin-right: 20rpx;
}
.discount-package-item .discountPackageProd-detail{
  font-size: 24rpx;
  font-weight: 400;
}
.discountPackageProd-detail .yuan-number{
  display: inline-block;
  text-align: center;
  width: 22rpx;
  height: 24rpx;
  border: 1px solid #999999;
  border-radius: 50%;
}
.discountPackageProd-detail .price{
  font-size: 20rpx;
  font-weight: bold;
  color: #D2423A;
  opacity: 1;
}


/* 优惠套餐列表 end */

/* 赠品样式 */
.giveaway-list {
  display: flex;
}
.giveaways-content{
  display: flex;
  font-size: 24rpx;
}
.giveaway-prod {
  flex: 1;
  /* width: 100%; */
}
.giveaways-name{
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.giveaways-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.giveaways-item .number {
  font-size: 24rpx;
  margin-top: 4rpx;
  margin-bottom: 20rpx;
}
.giveaways-item-right{
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
}
.giveaways-item-right::after{
  content: '';
  width: 13rpx;
  height: 13rpx;
  border-top: 2rpx solid #969798;
  border-right: 2rpx solid #969798;
  transform: rotate(45deg);
}
/* 赠品样式 end */

/**优惠券*/
.coupon,
.discount,
.sendway {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  padding: 30rpx 20rpx;
  margin-top: 20rpx;
  border-radius: 8rpx;
}

.discount .coupon-tit,
.coupon .coupon-tit,
.sendway .coupon-tit {
  font-size: 28rpx;
  color: #888;
  margin-right: 20rpx;
}
.discount .coupon-con,
.coupon .coupon-con,
.sendway .coupon-con {
  max-width: 77%;
}

.coupon .coupon-con-en {
	max-width: 60%;
}

.coupon-con .item {
  position: relative;
  display: inline-block;
  /* vertical-align: top; */
  padding: 0 18rpx;
  background: var(--light-background);
  height: 36rpx;
  line-height: 36rpx;
  color: var(--primary-color);
  font-size: 22rpx;
  font-family: arial;
  margin-right: 16rpx;
}
/* .coupon-con .item:not(:first-child) {
	margin-bottom: 16rpx;
} */
.sendway .coupon-con {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.sendway-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.sendway-item:not(:last-child) {
  padding-right: 26rpx;
}

.sendway-item image {
  display: inline-block;
  width: 14px;
  height: 14px;
  padding-right: 3px;
}

.coupon-con .item:before,
.coupon-con .item:after {
  /* content: ''; */
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  top: 0;
  border: 18rpx solid transparent;
}

.coupon-con .item:before {
  left: 0;
  border-left: 4rpx solid #fff;
}

.coupon-con .item:after {
  right: 0;
  border-right: 4rpx solid #fff;
}

.coupon .num {
  width: 100rpx;
  top: 34rpx;
  text-align: right;
  font-size: 24rpx;
  color: #666;
  line-height: 36rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: arial;
}

.more {
  position: absolute;
  right: 20rpx;
  width: 60rpx;
  top: 16rpx;
  text-align: right;
  font-size: 40rpx;
  color: #999;
  letter-spacing: 1px;
}

/* 已选 */
.sku {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  background: #fff;
  padding: 30rpx 80rpx 30rpx 20rpx;
  margin-top: 20rpx;
  border-radius: 8rpx;
}

.sku-tit {
  font-size: 28rpx;
  color: #888;
  margin-right: 20rpx;
}

.sku-con {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  max-width: 77%;
  /* margin: 0 50rpx 0 100rpx; */
}

/** 评价*/

.cmt-wrap {
  background: #fff;
  margin-top: 20rpx;
  position: relative;
  line-height: 48rpx;
}

.cmt-tit {
  font-size: 32rpx;
  position: relative;
  /* border-bottom: 1px solid #ddd; */
  padding: 20rpx;
}

.cmt-t {
  width: 90%;
}

.cmt-good {
  font-size: 24rpx;
  color: #333;
  margin-left: 10rpx;
  font-weight: bold;
}

.cmt-count {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

.cmt-more {
  width: 18rpx;
  height: 18rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 10rpx;
  display: inline-block;
}

.cmt-cont {
  padding: 0 20rpx;
}

.cmt-tag {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 14px 3px 0 0;
  margin: 0;
}

.cmt-tag text {
  margin: 0 6px 15px 0;
  background: rgba(74,108,234,0.09);
  display: inline-block;
  padding: 0 15px;
  height: 30px;
  border-radius: 20px;
  line-height: 30px;
  font-size: 12px;
  font-family: -apple-system, Helvetica, sans-serif;
  color: var(--primary-color);
}

.cmt-tag text.selected {
  color: #fff;
  background: var(--primary-color);
}

.cmt-item {
  position: relative;
  padding: 0px 0 10px;
}
.cmt-item:not(:last-child) {
  border-bottom: 1px solid #eee;
}

.cmt-user {
  line-height: 25px;
  margin-bottom: 8px;
  font-size: 12px;
}

.cmt-user-info {
  display: flex;
  align-items: center;
  max-width: 80%;
}

.cmt-user .user-img {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  vertical-align: middle;
}

.cmt-user .nickname {
  margin-left: 20rpx;
  margin-right: 20rpx;
  display: inline-block;
  color: #333;
}
.cmt-user .nickname .name-star {
	display: flex;
	align-items: center;
}
.cmt-user .nickname .name {
	margin-right: 10rpx;
	font-size: 28rpx;
	max-width: 260rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.cmt-user .stars {
  display: flex;
  margin-left: 3px;
}

.cmt-user .stars image {
  width: 35rpx;
  height: 35rpx;
}

.date {
  color: #888;
  font-size: 28rpx;
}

.cmt-cnt {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  position: relative;
  line-height: 1.5;
  font-size: 14px;
  margin: 5px 0;
  word-wrap: break-word;
  word-break: break-all;
  max-height: 126px;
}

.cmt-attr {
  height: 85px;
  width: 100%;
  white-space: nowrap;
}

.cmt-attr .img-wrap {
  width: 85px;
  height: 85px;
  display: inline-block;
}

.cmt-attr image {
  display: inline-block;
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
  border-radius: 2px;
  background: #f3f3f3;
}

.cmt-more-v {
  text-align: center;
  background-color: #fff;
  font-size: 12px;
}

.cmt-more-v text {
  height: 25px;
  line-height: 25px;
  font-size: 12px;
  text-align: center;
  color: #333;
  padding: 0px 10px;
  margin: 10px 0;
  border: 1px solid #ccc;
  border-radius: 40px;
  display: inline-block;
}

/** 评价弹窗 */

.cmt-popup {
  /*  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 88; */
  height: 100vh;
  background-color: #fff;
  /* padding-bottom: 98rpx; */
}

/* 没有评价时候的样式 */
.cmt-no-popup {
  height: 100vh;
  background-color: #fff;
}

.cmt-popup .cmt-cont {
  height: calc(100% - 80rpx);
  overflow: auto;
}

.cmt-popup .cmt-cnt {
  -webkit-line-clamp: 20;
  max-height: 500px;
}

.cmt-reply {
  font-size: 14px;
  border-top: 1px dashed #ddd;
  padding: 5px 0;
}

.cmt-reply .reply-tit {
  color: #e43130;
}

.cmt-reply .reply-content {
  word-wrap: break-word;
}

.cmt-popup .load-more {
  font-size: 14px;
  padding: 20px;
  text-align: center;
  margin-bottom: 10px;
}

.cmt-popup .load-more text {
  border: 1px solid #ddd;
  padding: 5px 10px;
  border-radius: 10px;
  color: #666;
}

/** 店铺 */
.shop-box {
  margin: 20rpx 0;
  background: #fff;
  padding: 20rpx;
}
.shopbox-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.shopbox-head .sl {
  display: flex;
  align-items: center;
	flex: 1;
	max-width: 100%;
}
.shop-logo {
  width: 100rpx;
  height: 100rpx;
  background: #fff;
  margin-right: 20rpx;
}
.shop-con {
	display: flex;
	flex-direction: column;
	flex: 1;
}
.shop-name {
  font-size: 30rpx;
  font-weight: bold;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-word;
}
.shop-con .shop-fol {
  display: flex;
  align-items: center;
  margin-top: 6px;
}
.shop-con .shop-fol .self-operate {
  background: var(--primary-color);
  color: #fff;
  font-size: 20rpx;
  padding: 6rpx 8rpx;
  border-radius: 10rpx;
}
.shop-con .shop-fol .fol-num {
  font-size: 13px;
}
/* 进店看看 */
.enter-shop,.custom-btn{
	width: 192rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 44rpx;
  border: 1px solid #666;
  border-radius: 50rpx;
  margin: 30rpx auto 0;
  text-align: center;
  margin-right: 10rpx;
}

/* 粉丝&商品 */
.shop-situation {
  margin: 25rpx 0;
  display: flex;
}
.situation-item {
  width: 49%;
  /* border-right: 1rpx solid #e4e4e4; */
  text-align: center;
}
.situation-item:last-child {
  border: none;
}
.situation-item-num {
  font-size: 32rpx;
  line-height: 2em;
}
.situation-item-txt {
  font-size: 28rpx;
  color: #999;
}
/* 收藏店铺&进入店铺 */
.handle-shop-item {
  display: inline-block;
  width: 48%;
  margin-right: 20rpx;
  border: 1rpx solid #e4e4e4;
  border-radius: 8rpx;
  padding: 10rpx 0;
  text-align: center;
}
.handle-shop-item:last-child {
  margin: 0;
}
.handle-shop-icon {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.handle-shop-icon > image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}
.handle-txt {
  font-size: 28rpx;
  vertical-align: middle;
}
/** 参数 */
.parameter-box {
  margin: 0 30rpx;
  display: flex;
  height: 80rpx;
  align-items:center;
  font-size: 24rpx;
  border-bottom: 1px dashed #AAAAAA;
}
.parameter-box:last-child {
	border: 0 !important;
}
.parameter-box .parameter-key{
  min-width: 250rpx;
  color: #999999;
  line-height: 31rpx;
}
.parameter-box .parameter-vaule{
  line-height: 31rpx;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
/** 参数end*/
/** 商品详情 */
.prod-detail {
  width: 100% !important;
  background: #fff;
  margin: 20rpx 0;
  padding: 0 48rpx;
  box-sizing: border-box;
  position: relative;
  line-height: 48rpx;
  overflow: hidden;
}

.prod-detail-title {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
}
.prod-detail-title-line {
  width: 144rpx;
  height: 2rpx;
  background: #f1f1f1;
}

.prod-detail-title-text {
  flex-shrink: 0;
  color: #999;
  font-size: 24rpx;
  font-weight: 400;
  margin: 0 32rpx;
}
rich-text {
  width: 100% !important;
  /* 与发布商品预览图字体大小一致 */
  font-size: 28rpx;
}

.det-tit {
  width: 300rpx;
}

.detail-tit {
  font-size: 32rpx;
  position: relative;
  border-bottom: 1px solid #ddd;
  padding: 20rpx;
}

.prod-detail image {
  width: 750rpx !important;
  display: block;
}

rich-text image {
  width: 100% !important;
}

img {
  width: 100% !important;
  display: block;
}

/** end 商品详情 */

/** 底部按钮 */

.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: row nowrap;
  height: 110rpx;
  z-index: 20;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);;
}

.cart-footer .btn {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  width: 0;
  background-color: #fff;
  font-size: 28rpx;
  flex-flow: column;
}

.cart-footer .btn.icon {
  flex-grow: 0;
  flex-shrink: 0;
  width: 100rpx;
  font-size: 25rpx;
  color: #666;
}

.cart-footer .btn.icon image {
  width: 60rpx;
  height: 60rpx;
}

.cart-footer .btn.cart {
  background: #fff;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  margin: 0 12rpx;
  font-size: 28rpx;
  padding: 19rpx 0;
  height: 36px;
}

.cart-footer .btn.buy {
  background: var(--primary-color);
  color: #fff;
  margin-right: 12rpx;
  font-size: 28rpx;
  padding: 20rpx 0;
  height: 36px;
}
.cart-footer .btn.pre-sale-buy {
  background: var(--primary-color);
  color: #fff;

  margin-right: 12rpx;
  font-size: 30rpx;
}
.cart-radius {
  border-radius: 60rpx;
  /* height: 2.8em;
  line-height: 2.8em; */
  line-height: 1em;
  text-align: center;
  box-sizing: border-box;
  padding: 28rpx 10rpx;
}
.cart-radius.en-btn {
  padding: 12rpx;
}
.cart-footer.gray .btn.cart,
.cart-footer.gray .btn.buy {
  background: #ddd;
}

/* 团购按钮 */
.cart-footer .alone-buy, .cart-footer .group-buy {
  font-size: 30rpx;
  color: #fff;
  margin-right: 12rpx;
}

.cart-footer .alone-buy {
  background: #fff;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  box-sizing: border-box;
  height: 88rpx;
}
.cart-footer .group-buy {
  background: var(--primary-color);
  color: #fff;
  height: 88rpx;
}
.cart-footer .btn.cart-radius.gray-btn {
  background-color: #EEEEEE;
  color: #999999;
}

/** end  底部按钮 */

/* 购物车数量显示 */
.badge {
  position: absolute;
  top: 0;
  left: 55rpx;
  background-color: var(--primary-color);
  color: #fff;
  width: auto;
  height: 32rpx;
  line-height: 32rpx;
  border-radius: 32rpx;
  min-width: 24rpx;
  padding: 0 4rpx;
  font-size: 24rpx;
  text-align: center;
  white-space: nowrap;
}

/** end  底部按钮 */

/** 优惠券弹窗 **/

.popup-hide {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.popup-box {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 375px;
  max-height: 490px;
  overflow: hidden;
  background-color: #fff;
}

.popup-tit {
  position: relative;
  height: 46px;
  line-height: 46px;
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  background-color: #f7f7f7;
  margin-bottom: 30rpx;
}

.close {
  color: #aaa;
  border-radius: 12px;
  line-height: 20px;
  text-align: center;
  height: 20px;
  width: 20px;
  font-size: 18px;
  padding: 1px;
  top: 10px;
  right: 10px;
  position: absolute;
}

.close::before {
  content: '\2716';
}

.popup-cnt {
  max-height: 429px;
  overflow: auto;
  padding: 0 10px;
  background: #fff;
  padding-bottom: 60rpx;
}

.popup-coupon-con {
  padding-bottom: 120rpx;
}

/* .popup-discount {
  margin: 30rpx 0;
} */

/** 规格弹窗**/

.pup-sku {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.pup-sku-main {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 375px;
  max-height: 85%;
  background-color: #fff;
  overflow: auto;
}

.pup-sku-header {
  position: sticky;
  top: 0;
  font-size: 16px;
  color: #333;
  padding: 20rpx 20rpx;
  background-color: #fff;
  border-bottom: 2rpx solid #ebebeb;
  padding-top: 35rpx;
}

.pup-sku-img {
  display: inline-block;
  width: 27%;
  height: 180rpx;
  vertical-align: middle;
  border-radius: 10rpx;
}
.pup-sku-img image {
  display: block;
  width: 180rpx;
  height: 100%;
}

.pup-sku-prod {
  display: inline-block;
  width: 70%;
  vertical-align: middle;
  padding-left: 10rpx;
}

.prod-title {
  font-size: 28rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin: 15rpx 0;
}

.pup-sku-price {
  display: inline-block;
  height: 1.5em;
  line-height: 1.5em;
  color: #e43130;
  font-size: 26rpx;
}
.group-sku-pri {
  display: flex;
  align-items: center;
}

.pup-sku-price-int {
  font-size: 34rpx;
}

.pup-sku-prop {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  font-size: 26rpx;
  color: #333;
  line-height: 1.4em;
  padding-right: 10px;
  margin-top: 16rpx;
	overflow: unset;
}

.sel-sku {
  display: inline-block;
  max-width: 340rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 5px;
}

.pup-sku-prop text:first-child {
  color: #999;
}

.pup-sku-body {
  padding-bottom: calc(110rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
  /* overflow: auto; */
}
/*ipad适应样式 */
@media screen and (min-width: 500px) {
  /* 购买弹窗 */
  .pup-sku-body {
    max-height: 600px;
    padding-bottom: 120px;
  }
  .pup-sku-main {
    max-height: 775px;
  }
  /* 评论字体大小 */
  .cmt-tag text,
  .cmt-user,
  .cmt-cnt {
    font-size: 16px;
  }
}


/* .pup-sku-area {
  max-height: 245px;
  overflow-y: scroll;
}
.pup-sku-area .sku-box{
  max-height: 245px;
  overflow-y: scroll;
} */

.pup-sku-area .sku-kind {
  font-size: 12px;
  color: #999;
  margin: 0 10px;
  height: 40px;
  line-height: 40px;
}
.pup-sku-area .con {
	padding-right: 20rpx;
}

.pup-sku-area .sku-choose {
  overflow: hidden;
  margin-bottom: 3px;
}

.sku-choose-item {
  display: inline-block;
  min-width: 100rpx;
  padding: 8rpx 10rpx;
  overflow: hidden;
  line-height: 40rpx;
  text-align: center;
  margin-left: 10px;
  margin-bottom: 10px;
  border-radius: 30rpx;
  color: #333;
  background-color: #fff;
  font-size: 14px;
  border: 1px solid #E9EBED;
}

.sku-choose-item .sku-choose-item-text{
  display: inline-block;
  text-align: left;
}

.sku-choose-item.active {
  background-color: #fff;
  color: var(--primary-color);
  border: 1px solid var(--primary-color) !important;
}

.sku-choose-item.gray {
  background-color: #f9f9f9;
  color: #ddd;
}
.sku-choose-item.dashed {
  border: 1px dashed #ccc;
}

.pup-sku-count {
  padding: 0 0 13px 10px;
  font-size: 12px;
  margin-top: 20rpx;
}

.pup-sku-count .count-name {
  height: 31px;
  line-height: 31px;
  width: 100rpx;
  color: #000;
  font-weight: bold;
}

.pup-sku-count .num-wrap {
  position: relative;
  z-index: 0;
  width: 110px;
  float: right;
  vertical-align: middle;
  display: flex;
}

/* 留言（虚拟商品） */
.virtual-goods-tips {
  display: block;
  background: #F9F9F9;
  padding: 20rpx;
  color: #999999;
  font-size: 24rpx;
  margin-bottom: 20rpx;
  line-height: 1.5em;
}
.pup-sku-count.virtual-goods-msg {
  margin-bottom: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}
.pup-sku-count.virtual-goods-msg .msg-item {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #F2F2F2;
  font-size: 24rpx;
  padding-bottom: 20rpx;
}
.pup-sku-count.virtual-goods-msg .msg-item:not(:first-child) {
  margin-top: 40rpx;
}
.pup-sku-count.virtual-goods-msg .msg-item .msg-tit {
  flex-wrap: wrap;
  width: 180rpx;
  margin-right: 20rpx;
  word-break: break-word;
}
.pup-sku-count.virtual-goods-msg .msg-item .stress {
  color: #E43130;
  margin-right: 10rpx;
  font-size: 26rpx;
}
.pup-sku-count.virtual-goods-msg .msg-item .msg-int {
  font-size: 24rpx;
  width: 100%;
}
.pup-sku-count.virtual-goods-msg .msg-item .msg-int .uni-input-placeholder {
  color: #aaa;
}
/* 留言（虚拟商品）/ */

.num-wrap .minus,
.num-wrap .plus {
  position: relative;
  max-width: 24px;
  min-width: 24px;
  height: 24px;
  line-height: 24px;
  background: #f7f7f7;
  text-align: center;
}

.num-wrap .minus {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.num-wrap .plus {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.num-wrap .row {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -7px;
  margin-top: -1px;
  width: 14px;
  height: 2px;
  background-color: #ccc;
}

.num-wrap .col {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -1px;
  margin-top: -7px;
  width: 2px;
  height: 14px;
  background-color: #999;
}

.pup-sku-count .text-wrap {
  position: relative;
  width: 45px;
  z-index: 0;
  margin: 0 1px;
}

.pup-sku-count .text-wrap input {
  height: 24px;
  width: 100%;
  color: #333;
  background: #fff;
  font-size: 12px;
  text-align: center;
  border: none;
  background: #f7f7f7;
}

.pup-sku-footer {
  position: fixed;
  bottom: 0;
  bottom: constant(safe-area-inset-bottom);
  bottom: env(safe-area-inset-bottom);
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  align-items: center;
  height: 110rpx;
  z-index: 999;
  /* box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05); */
}

.pup-sku-footer .btn {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  font-size: 30rpx;
  flex-flow: column;
  border-radius: 60rpx;
  height: 2.8em;
  width: 45%;
  margin: 0 15rpx;
}

.pup-sku-footer .btn.cart {
  background: #fff;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.pup-sku-footer .btn.buy {
  background: var(--primary-color);
  color: #fff;
}
.pup-sku-footer .btn.pre-sale-buy {
  background: var(--primary-color);
  color: #fff;
}
.pup-sku-footer.gray .btn {
  background: #eee;
  color: #999;
  border: 1px solid #eee;
}

/* 悬浮按钮 */
.suspension-box {
  position: fixed;
  bottom: 200rpx;
  right: 40rpx;
  z-index: 2;
}
.scroll-top {
  bottom: 200rpx;
}
button.btn-type {
  background: #fff;
  padding: 0;
  margin: 0;
  margin-top: 30rpx;
}
button.btn-type::after {
  border: 0;
}

/* 赚字浮层 */
.promo-con {
  position: fixed;
  bottom: 400rpx;
  right: 40rpx;
}
.promo-con .earn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  box-shadow: 1px 2px 6px rgba(78, 78, 78, 0.4);
  color: #fff;
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  line-height: 1em;
  font-size: 32rpx;
  text-align: center;
  border: 1rpx solid transparent;
}

/* 直播按钮 */
.live {
  position: fixed;
  top: 200rpx;
  right: 30rpx;
  padding: 16rpx 10rpx;
  background: rgba(255, 2550, 255, 0.8);
  border: 1rpx solid #eee;
  border-radius: 20rpx;
}
.live .live-txt {
  font-size: 26rpx;
  color: #fc1640;
  margin-top: 16rpx;
  line-height: 1em;
}
/* .live .earn {
  width: 90rpx;
  height: 80rpx;
  border-radius: 14rpx;
} */

/* 回到顶部 */
.suspension-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #fff;
  border: 1rpx solid #ccc;
  margin-top: 30rpx;
}
.suspension-btn image {
  display: block;
  width: 40rpx;
  height: 40rpx;
}

/* 悬浮按钮 end */

/*分享弹窗 */
.promo-share {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 9999;
}
.promo-tit {
  padding-top: 30rpx;
  font-size: 30rpx;
  text-align: center;
  margin-left: 80rpx;
  font-weight: bold;
}
.promo-main {
  background: #fff;
  position: absolute;
  bottom: 0rpx;
  width: 100%;
  height: 500rpx;
  z-index: 9999;
}
.promo-icons-close image {
  width: 45rpx;
  height: 45rpx;
  /* border-radius: 55%; */
  float: right;
  padding-right: 20rpx;
  padding-top: 20rpx;
}
.promo-desc {
  margin-left: 73rpx;
  width: 600rpx;
  text-align: center;
}

.promo-desc text {
  padding: 20rpx;
  font-size: 20rpx;
  color: #999;
  line-height: 34rpx;
  display: inline-block;
}
.promo-icons {
  font-size: 20rpx;
  color: #666;
  display: flex;
  justify-content: space-around;
  padding: 10rpx 150rpx;
}
.promo-icons image {
  width: 75rpx;
  height: 75rpx;
  border-radius: 50%;
}
.promo-img1 {
  display: flex;
  flex-flow: column;
  align-items: center;
  line-height: 75rpx;
  font-size: 22rpx;
  background: none;
  border: 0;
  margin: 0;
  padding: 0;
}

.promo-img1::after {
  border: 0;
}

.promo-btn {
  font-size: 28rpx;
  width: 90%;
  height: 80rpx;
  border: 1px #e5e5e5 solid;
  text-align: center;
  line-height: 80rpx;
  margin-left: 40rpx;
}

/** 二维码弹窗 */

.code-popup {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.code-main {
  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  position: fixed;
  width: 500rpx;
  height: 700rpx;
}

.close-png {
  width: 50rpx;
  height: 50rpx;
  margin-top: -10rpx;
}

.code-v {
  padding: 20rpx;
  border-radius: 6rpx;
  background: #fff;
}

.wx-code {
  width: 460rpx;
  height: 460rpx;
}

.close-v {
  text-align: right;
  height: 60rpx;
}

.code-txt {
  margin-top: 20rpx;
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  background: #3a86b9;
  border-radius: 6rpx;
  height: 80rpx;
  line-height: 80rpx;
}

/** 二维码弹窗 end */

/* 倒计时栏 */
.countdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100rpx;
  box-sizing: border-box;
  background: linear-gradient(90deg, #FFE3CF 0%, #FFF0E6 74.58%, #FFB58C 100%);
  padding-right: 48rpx;
}

.countdown-tips {
  font-size: 26rpx;
  margin-right: 10rpx;
  font-size: 30rpx;
}

.countdown__left {
  height: 100%;
  position: relative;
}

.countdown__left__text {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  text-align: center;
  color: #fff;
  font-size: 48rpx;
  font-weight: 600;
}

.countdown__center {
  color: var(--primary-color);
  font-size: 24rpx;
  font-weight: 600;
}

/* 价格 */
.goods-price {
  padding: 15rpx 0;
  /* border-bottom: 1px solid #f3f3f3; */
}
.sub{
	font-size: 24rpx;
}
.current-price {
  color: #fff;
  font-size: 28rpx;
  margin-right: 15rpx;
  vertical-align: middle;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.original {
  font-size: 26rpx;
  color: #fff;
  margin-top: 15rpx;
}
.original-price {
  font-size: 23rpx;
  text-decoration: line-through;
  color: #fff;
  margin-left: 10rpx;
}
/* 拼团价格 */
.condition {
  font-size: 25rpx;
  border: 2rpx solid #fff;
  padding: 4rpx 20rpx;
  border-radius: 20rpx;
  vertical-align: middle;
  /* margin-right: 20rpx; */
  color: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.people-icon{
	width: 24rpx;
	height: 24rpx;
	margin-right: 8rpx;
}
/* 拼团详情 */
.spell-infor {
  border-top: 20rpx solid #f7f7f7;
  font-size: 28rpx;
  padding: 48rpx;
  background: #fff;
}
.spell-infor-title {
  font-size: 26rxp;
}
.spell-infor-content {
  position: relative;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.head-img {
  display: inline-block;
  width: 70rpx;
  height: 70rpx;
  margin-right: 15rpx;
  vertical-align: bottom;
  border-radius: 50%;
}
.head-img > image {
  border-radius: 50%;
}
.spell-msg {
  display: inline-block;
  vertical-align: top;
  font-size: 24rpx;
  max-width: 65%;
}
.username {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.spell-text {
  padding-top: 10rpx;
  color: #aaa;
}
.red-font {
  color: #e43130;
}
.join-group {
  position: absolute;
  right: 10rpx;
  bottom: 30rpx;
  color: #e43130;
  border: 1rpx solid #e43130;
  padding: 10rpx 15rpx;
  font-size: 24rpx;
	max-width: 200rpx;
	text-align: center;
}
.rules {
  position: relative;
  font-size: 25rpx;
  padding-top: 20rpx;
}
.rules-text02 {
  float: right;
  font-size: 23rpx;
  color: #aaa;
  padding-right: 30rpx;
}
.right-arrow {
  position: absolute;
  right: 5rpx;
  top: 20rpx;
  display: inline-block;
  width: 20rpx;
  height: 20rpx;
}
.act-price-con {
  background: var(--primary-color);
  border-radius: 24rpx;
  margin-left: 20rpx;
  padding: 4rpx 16rpx;
  color: #fff;
}

/* 列表为空 */
.empty {
  margin-top: 100rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 2em;
}
/* 圆角样式 */
.radius {
  border-radius: 26rpx 26rpx 0 0;
}
.box-radius {
  border-radius: 20rpx;
}

/* 引导蒙版 */
.guide-share-mask {
  position: fixed;
  left: 0;
  top: 0;
  display: block;
  width: 100%;
  height: 100%;
  z-index: 11111;
}
.guide-share-mask .mask {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
}

.guide-share-mask .guide-share-close {
  width: 50rpx;
  height: 50rpx;
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  z-index: 99;
}
.guide-share-mask .guide-share-close > image {
  width: 100%;
  height: 100%;
}
.guide-share-mask .guide-content {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 100%;
  height: auto;
}
.guide-share-mask .guide-content .share-img {
  display: block;
  width: 400rpx;
  height: 400rpx;
  margin-top: 60rpx;
  margin-bottom: 30rpx;
  margin-left: 35%;
}
.guide-content .share-img image {
  display: inline-block;
  width: 100%;
  height: 100%;
}
.guide-content .share-word {
  display: block;
  color: #fff;
  text-align: center;
}
.guide-content .share-word .big-word {
  font-size: 32rpx;
}
.guide-content .share-word .small-word {
  font-size: 26rpx;
  line-height: 2em;
}
/*弹窗解决遮罩层移动问题*/
.contenta{
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
.page-hidden{
  height: 100%;
  overflow: hidden
}

/* 引导蒙版 end */

scroll-view ::-webkit-scrollbar {

  width: 0;

  height: 0;

  background-color: transparent;

}

.very-vip-wrap {
  width: 100%;
  box-sizing: border-box;
  padding: 0 48rpx;
	margin: 0 0 40rpx;
}
.very-vip{
	width: 100%;
	height: 100rpx;
	background: #F7D2A8;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx;
	box-sizing: border-box;
	border-radius: 16rpx;
}
.vip{
	padding: 0 10rpx;
	height: 36rpx;
	background: #6d4f34;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #efd3ad;
	white-space: nowrap;
	border-radius: 8rpx;
  font-weight: 500;
}
.vip-tips{
	font-size: 24rpx;
	color: #3B2A1A;
	margin: 0 10rpx;
	flex: 1;
}
.vip-nums{
	color: #F05738;
}
.open-vip-btn{
	border-radius: 12rpx;
	padding: 0 14rpx;
	height: 50rpx;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	background: #3B2A1A;
	color: #efd3ad;
	white-space: nowrap;
}

.marT0{
	margin-top: 0px;
}
.borderT{
	border-top: 2rpx solid #ebebeb;
}
.borderB{
	border-bottom: 2rpx solid #ebebeb;
}
.radiusTLR{
	border-radius: 24rpx 24rpx 0 0;
}
.radiusBLR{
	border-radius: 0 0 24rpx 24rpx;
}
.paddingB{
	padding-bottom: 0;
}
.paddingT{
	padding-top: 0;
}
.paddingL{
	padding-left: 0;
}
.paddingR{
	padding-right: 0;
}
.cmt-tit-modal{
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
}
.whitebg{
	background: #f4f4f4;
}
.eval-page-modal{
	background: #fff;
	padding: 20rpx 20rpx 0;
	margin-bottom: 20rpx;
}
.eval-text1{
	font-size: 28rpx;
	color: #333;
}
.eval-text2{
	font-size: 24rpx;
	color: #888;
}
.cut-eval-item{
	background: #fff;
	padding: 20rpx 20rpx 50rpx;
}
.cut-attr{
	display: flex;
	flex-direction: row;
	align-items: center;
	flex-wrap: wrap;
	height: auto;
}
.cut-picture{
	width: 220rpx !important;
	height: 220rpx !important;
	border-radius: 12px !important;
	margin-right: 10px !important;
	margin-bottom: 10px !important;
}
.cut-picture:nth-child(3n){
	margin-right: 0px !important;
}
.eval-head{
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fff;
	position: relative;
	padding: 28rpx 0;
	margin-bottom: 20rpx;
}
.close-arrow{
	font-size: 18px;
	color: #666;
	position: absolute;
	left: 10px;
	transform: rotate(225deg);
}
.head-title{
	font-size: 14px;
	color: #333;
}
.marginB{
	margin-bottom: 0;
}

.cart-radius-text{
	margin-bottom: 10rpx;
}
.discountPrice{
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 20rpx;
  padding: 5rpx 10rpx;
}

.font-labels {
  font-size: 28rpx;
}
.corner-mark-labels {
  position: relative;
  display: inline-block;
  margin-right: 16rpx;
}
.corner-mark-style {
  display: inline-block;
  font-size: 24rpx;
  padding: 0rpx 8rpx;
  border: 2rpx solid transparent;
  border-radius: 4rpx;
}
.coupon-item {
  margin-top: 32rpx;
}

.float-btns {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32rpx;
  top: 48rpx;
  right: 48rpx;
  color: white;
}
.float-btns .float-icon {
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx; 
  background: rgba(0, 0, 0, 0.50);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8rpx;
}
.float-btns button {
  background-color: transparent;
  height: 48rpx;
  padding: 0;
}
.float-btns button:after{
  display:none;
}
.float-btns image {
  vertical-align: top;
}

.float-btns .col image {
  width: 32rpx;
  height: 32rpx;
}
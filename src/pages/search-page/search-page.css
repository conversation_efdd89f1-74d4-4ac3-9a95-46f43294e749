/* pages/search-page/search-page.wxss */

/* 搜索栏 */

.search-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  color: #777;
  background: #fff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.07);
  z-index: 3;
  display: flex;
  box-sizing: border-box;
  padding: 20rpx;
}

.select-theme {
  font-size: 28rpx;
  width: 15%;
  display: flex;
  justify-content: center;
  align-items: center;
  /* border-bottom: 1px solid #eee; */
}
.select-theme .select {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.select-theme .select text {
  color: #333;
}
.select-theme .select image.arrow {
  width: 28rpx;
  height: 28rpx;
}


.search-bar .search-box {
  position: relative;
  height: 70rpx;
  background: #fff;
  z-index: 999;
  width: 80%;
	border-bottom: 1px solid #999;
  margin-left: 20rpx;
}

.sear-input {
  height: 70rpx;
  border: 0;
  margin: 0 30rpx 0 64rpx;
  vertical-align: top;
  background: #fff;
  font-size: 28rpx;
}

.search-bar .search-hint {
  font-size: 30rpx;
  position: absolute;
  right: 40rpx;
  top: 50%;
  transform: translateY(-50%);
  color:var(--primary-color);
}

.search-bar .search-box .search-img {
  width: 34rpx;
  height: 34rpx;
  position: absolute;
  left: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  display: block;
}

/* 热门搜索&搜索历史 */

.search-display {
  background: #fff;
  padding: 20rpx;
  margin-top: 100rpx;
}

.search-display .title-text {
  padding: 30rpx 0;
  font-size: 30rpx;
  color: #666;
}

.hot-search .hot-search-tags {
  overflow: hidden;
  font-size: 26rpx;
  text-align: left;
  padding-bottom: 30rpx;
}

.hot-search .hot-search-tags .tags {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  border-radius: 50rpx;
  white-space: nowrap;
  background-color: #f2f2f2;
  box-sizing: border-box;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  padding: 14rpx 30rpx;
	text-overflow: ellipsis;
}

/* 搜索历史 */

.history-search .title-text.history-line {
  position: relative;
  border-top: 2rpx solid #e1e1e1;
}

.history-search .his-search-tags {
  overflow: hidden;
  font-size: 26rpx;
  text-align: center;
  display: inline-block;
}

.history-search .his-search-tags .tags {
  max-width: 300rpx;
  overflow: hidden;
  float: left;
  border-radius: 50rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  background-color: #f2f2f2;
  box-sizing: border-box;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  padding: 10rpx 30rpx;
}

.clear-history image {
  width: 32rpx;
  height: 32rpx;
  position: absolute;
  right: 10rpx;
  top: 30rpx;
}

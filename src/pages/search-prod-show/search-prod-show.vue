<template>
	<!--pages/search-prod-show/search-prod-show.wxml-->
	<view class="container">

		<!-- 搜索框 -->
		<view class="fixed-box">
			<view class="search-bar">
				<view class="search-box">
					<input class="sear-input" :placeholder="i18n.inpKeyWords" :value="prodName" @input="getSearchContent" confirm-type="search"
					 @confirm="toSearchConfirm"/>
					<image :src="`${staticPicDomain}images/icon/search.png`" class="search-img"></image>
				</view>
				<view class="search-list-img" @tap="changeShowType">
					<image v-if="showType==1" :src="`${staticPicDomain}images/icon/search-col.png`"></image>
					<image v-if="showType==2" :src="`${staticPicDomain}images/icon/search-col2.png`"></image>
				</view>
			</view>
			<view class="tabs">
				<view :class="'tab-item '" :style="{color:sortType==''?'#4A6CEA':''}" @tap="onStsTap('')" data-sts="0">{{i18n.Comprehensive}}</view>
				<view :class="'tab-item ' + (sortType=='sales' && (sts==2?'down':'up'))" @tap="onStsTap('sales')">{{i18n.sales}}</view>
				<view :class="'tab-item ' + (sortType=='price' && (sts==4?'down':'up'))" @tap="onStsTap('price')">{{i18n.price}}</view>
			</view>
		</view>

		<!-- 商品列表 -->
		<view class="prod-list">
			<!-- 横向列表 -->
			<view class="prod-show" v-if="showType==1">
				<view class="hotsale-item-cont">
					<block v-for="(item, index) in searchProdList" :key="index">
						<prod :item="item" sts="6"></prod>
					</block>
				</view>
			</view>

			<!-- 纵向列表 -->
			<view class="cont-item" v-if="showType==2">
				<block v-for="(item, index) in searchProdList" :key="index">
					<view class="show-item" @tap="toProdPage" :data-prodid="item.prodId">
						<view class="more-prod-pic">
							<image :src="item.pic" class="more-pic"></image>
						</view>
						<view class="prod-text-right">
							<view class="prod-text more">{{item.prodName}}</view>
							<view class="prod-price more">
								<text class="symbol">￥</text>
								<text class="big-num">{{parsePrice(item.price)[0]}}</text>
								<text class="small-num">.{{parsePrice(item.price)[1]}}</text>
							</view>
							<!-- <view class="evaluate-box">
								<view class="self-operate" v-if="item.shopId==1">自营</view>
								<text class="num">{{item.commentNum}}{{i18n.evaluation}}</text>
								<text class="percent">{{item.positiveRating}}%{{i18n.praise}}</text>
							</view> -->
						</view>
					</view>
				</block>
			</view>

			<!-- 加载完毕 -->
			<view class="all-load" v-if="loadAll">{{i18n.endTips}}</view>
		</view>

		<!-- 列表为空 -->
		<view class="empty" v-if="!searchProdList.length">
			<view class="empty-icon">
				<image :src="`${staticPicDomain}images/icon/empty.png`"></image>
			</view>
			<view class="empty-text">{{i18n.noProducts}}</view>
		</view>

		<!-- 回到顶部 -->
    	<back-top-btn v-if="showBacktop"></back-top-btn>
	</view>
</template>


<script>
	// pages/search-prod-show/search-prod-show.js
	var http = require("../../utils/http.js");
	import prod from "../../components/production/production";
	import backTopBtn from "../../components/backTopBtn/backTopBtn";

	export default {
		data() {
			return {
				sts: 21,
				showType: 2,
				searchProdList: [],
				prodName: "",
				pages: 0,
				current: 1,
				size: 20,
				orderBy: 1, // 排序(0升序 1降序)
				loadAll: false, // 是否加载完成
				scrollTop: "",
				showBacktop: false,
				sortType: ''
			};
		},

		components: {
			prod,
			backTopBtn
		},
		props: {},

		computed: {
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.setData({
				prodName: decodeURI(options.prodName)
			});
			this.toLoadData();
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			// 设置头部导航标题
			uni.setNavigationBarTitle({
				title: this.i18n.searchResult
			});
		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			if (this.current < this.pages) {
				this.setData({
					current: this.current + 1
				});
				this.toLoadData();
			} else {
				this.loadAll = true
			}
		},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			gotostore(item) {
				let url = ''
				if (item.renovationId) {
					url = '/pages/shop-feature-index/shop-feature-index0?shopId=' + item.shopId + '&renovationId=' + item.renovationId
				} else {
					url = '/packageShop/pages/shopPage/shopPage?shopId=' + item.shopId
				}
				uni.navigateTo({
					url
				})
			},
			// 页面滚动到指定位置指定元素固定在顶部
			onPageScroll: function (e) {
				this.scrollTop = e.scrollTop
				if (this.scrollTop > 200) {
					this.setData({
						showBacktop: true
					})
				} else if (this.scrollTop < 200) {
					this.setData({
						showBacktop: false
					})
				}
			},

			changeShowType: function() {
				var showType = this.showType;

				if (showType == 1) {
					showType = 2;
				} else {
					showType = 1;
				}

				this.setData({
					showType: showType
				});
			},
			//输入商品获取数据
			getSearchContent: function(e) {
				this.setData({
					prodName: e.detail.value
				});
			},
			//请求商品接口
			toLoadData: function() {
				uni.showLoading();
				var ths = this; //热门搜索
				var params = {
					// url: "/search/searchProdPage",
					url: "/search/page",
					method: "GET",
					data: {
						current: this.current,
						keyword: this.prodName || this.$route.query.prodName,
						size: this.size,
						sort: this.sts,
						orderBy: this.orderBy,
						isAllProdType: true,
						isActive: 1 // 过滤掉活动商品
					},
					callBack: function(res) {
						console.log(res)
						uni.hideLoading()
						let list = [];

						if (params.data.current == 1) {
							list = res.records[0].products;
						} else {
							list = ths.searchProdList;
							list = ths.searchProdList.concat(res.records[0].products);
						}
						ths.setData({
							searchProdList: list,
							pages: res.pages
						});

						if (res.pages == res.current) {
							ths.setData({
								loadAll: true
							})
						}
					}
				};
				http.request(params);
			},
			//当前搜索页二次搜索商品
			toSearchConfirm: function(e) {
				if (this.prodName === '' || !this.prodName.trim()) {
					uni.showToast({
						title: this.i18n.inpKeyWords,
						duration: 1000,
						icon: 'none',
					});
					return
				}
				// 商品搜索关键字
				let recentProdSearch = uni.getStorageSync('recentProdSearch') || [];
				recentProdSearch = recentProdSearch.filter(item => item !== e.detail.value);
				recentProdSearch.unshift(e.detail.value);
				if (recentProdSearch.length > 10) {
					recentProdSearch.pop();
				}
				// 将历史放到缓存中
				uni.setStorageSync('recentProdSearch', recentProdSearch);
				uni.redirectTo({
					url: '/pages/search-prod-show/search-prod-show?prodName=' + e.detail.value
				})
			},

			/**
			 * 切换排序
			 * @param {String} sortType 排序类型
			 */
			onStsTap: function(sortType) {
				this.sortType = sortType
				if (!sortType) {
					this.sts = 0
				} else if (sortType=='sales') {
					this.sts = this.sts == 3 ? 2 : 3
				} else if (sortType=='price') {
					this.sts = this.sts == 5 ? 4 : 5
				}
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				});
				this.setData({
					current: 1,
					pages: 1
				});
				this.toLoadData();
			},
			toProdPage: function(e) {
				var prodid = e.currentTarget.dataset.prodid;
				// uni.navigateTo({
				//   url: '/pages/prod/prod?prodid=' + prodid
				// });
				this.$Router.push({
					path: '/pages/prod/prod',
					query: {
						prodid: prodid
					}
				})
			}
		}
	};
</script>
<style>
	@import "./search-prod-show.css";
</style>

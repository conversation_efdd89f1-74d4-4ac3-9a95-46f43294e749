<template>
	<view class="set-box">
		<view class="item" @tap="toPersonalInformation">
			<text class="text">个人信息</text>
			<image class="arrow" :src="`${staticPicDomain}images/icon/set-arrow.png`"/>
		</view>
		
		<view class="three-box">
			<view class="three-item" @tap="toTermsOfService('servicePolicy')">
				<text class="text">隐私策略</text>
				<image class="arrow" :src="`${staticPicDomain}images/icon/set-arrow.png`"/>
			</view>
			<view class="three-item" @tap="toTermsOfService('serviceTerms')">
				<text class="text">服务条款</text>
				<image class="arrow" :src="`${staticPicDomain}images/icon/set-arrow.png`"/>
			</view>
		</view>
		
		 <view class="log-out" @tap="logout">
			<view class="log-out-n">
			  <text>{{i18n.signOut}}</text>
			</view>
		  </view>
			
	</view>
</template>

<script>
	var http = require("../../utils/http");
	var util = require('../../utils/util.js');
  import { AppType } from '@/utils/constant';

	export default {
		data() {
			return {

			};
		},

		components: {
			
		},
		props: {},
		computed: {
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
	
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			//头部导航标题
			uni.setNavigationBarTitle({
				title: '设置'
			});
		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},


		methods: {
    /**
     * 跳转到修改用户头像昵称资料
     */
    toPersonalInformation: function() {
      util.tapLog(3)
      uni.navigateTo({
        url: '/packageUser/pages/personalInformation/personalInformation'
      })
    },
    /**
			 * 去条款页
			 */
			toTermsOfService(key){
				uni.navigateTo({
					url: "/packageUser/pages/termsPage/termsPage?sts=" + key
				})
			},
			/**
			 * 跳转修改密码
			 */
			setPassword: function() {
			  util.tapLog(3)
			  uni.navigateTo({
			    url: '/pages/accountLogin/accountLogin?isForgetPassword=true' + '&isPersonalCenter=true'
			  });
			},
    /**
     * 退出登录
     */
    logout: function() {
      const appType = uni.getStorageSync("appType");

      util.tapLog(3)
      const params = {
        url: appType === AppType.MINI ? '/social/logOut' : '/logOut',
        method: 'POST',
        callBack: res => {
          this.isAuthInfo = false
          util.clearLoginData();
          let emptyObj = {}
          this.setData({
            orderAmount: emptyObj,
            couponNum: 0,  //优惠券数量
            score: 0, //用户积分
            totalBalance: 0, //用户余额
            notifyNum: 0,  // 消息提醒
            messageCount: 0,
          })
          // this.$Router.pushTab('/pages/index/index')
          uni.reLaunch({
            url: '/pages/index/index'
          })
        },
        data: {
          basToken: uni.getStorageSync('wenetToken')
        },
        errCallBack: errMsg => {
          console.log(errMsg)
        }
      }
      http.request(params)
    }
			
		}
	};
</script>
<style>
	@import "./set.css";
</style>

/* pages/category/category.wxss */

page {
  height: 100%;
}

.find{
	width: 100%;
	height: calc(100vh - 50px);
	background: #f5f5f5;
	box-sizing: border-box;
	position: fixed;
}

.scroll{
	/* height: calc(100vh - 100rpx); */
	height: 100%;
}

.active-find{
	width: 100%;
	height: 100%;
	background: #f5f5f5;
	box-sizing: border-box;
	position: fixed;
}

.find-box{
	width: 100%;
	height: 650rpx;
	background: url('https://m.wenet.com.cn/resources/shop-static/images/other/57fb5a622b3b442c80f3054d8fbd1026.png') no-repeat;
	background-size:100% auto ;
	box-sizing: border-box;
	padding-top: 34rpx;
	border-radius: 0 0 25rpx 25rpx;
}

.find-title{
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
}
.find-banner{
	width: 680rpx;
	height: 269rpx;
	margin-top: 44rpx;
	margin-left: 35rpx;
}
.find-three{
	display: flex;
	align-items: center;
	justify-content: space-around;
	width: 100%;
	box-sizing: border-box;
	padding: 47rpx 50rpx 0;
	/*background: rgba(255,255,255,0.88);*/
}
.find-three-item{
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.find-icon-box{
	width: 125rpx;
	height: 125rpx;
	border-radius: 36rpx;
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}
.find-three-item .find-icon{
	width: 100rpx;
	height: 100rpx;
}
.find-three-item .find-text{
	font-size: 24rpx;
	color: #1E2642;
	line-height: 24rpx;
}
.active{
  background: var(--light-background);
}
.active .find-text{
	color: var(--primary-color);
}
.find-nav{
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: 42rpx;
	padding:0 22rpx;
	overflow-x: auto;
}
.find-nav::-webkit-scrollbar {
    display:none
}
.find-nav-item{
	display: flex;
	flex-direction: column;
	align-items: center;
	flex-shrink: 0;
	margin-right: 42rpx;
}
.find-nav-item .find-nav-text{
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
}
.find-nav-item .find-line{
	background: #f5f5f5;
	border-radius: 3rpx;
	width: 40rpx;
	height: 6rpx;
}
.active-nav .find-nav-text{
	color: var(--primary-color);
}
.active-nav .find-line{
	background: var(--primary-color);
}
.find-list-box{
	display: flex;
	flex-direction: row;
	padding: 12px 12px 0;
	justify-content: space-between;
}
.find-list{
	display: flex;
	flex-direction: column;
/* 	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap; */
	/* width: 100%; */
	box-sizing: border-box;
}
.find-list-item{
	width: 338rpx;
	max-height: 696rpx;
	border-radius: 20rpx;
	background: #fff;
	margin-bottom: 12px;
}
.picture{
	width: 338rpx;
	height: 338rpx;
	border-radius: 20rpx 20rpx 0 0;
	margin-bottom: 16rpx;
}
.bottom-info{
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	padding: 6px 20rpx;
}
.prod-name{
	font-size: 26rpx;
	color: #333;
	display: -webkit-box;
	-webkit-line-clamp: 2; /*设定显示行数*/
	-webkit-box-orient: vertical;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 16rpx;
}
.user-info{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;
	/*padding: 0 12rpx;*/
	padding-bottom: 16rpx;
	border-bottom: 2rpx solid #F4F4F4;
}
.user-info .left{
	display: flex;
	flex-direction: row;
	align-items: center;
}
.user-info .left .user-head{
	width: 50rpx;
	height: 50rpx;
	border-radius: 100%;
	margin-right: 10rpx;
}
.user-info .left .user-name{
	font-size: 24rpx;
	color: #888;
}
.user-info .right{
	display: flex;
	flex-direction: row;
	align-items: center;
}
.user-info .right .dz{
	width: 26rpx;
	height: 26rpx;
	margin-right: 7rpx;
}
.user-info .right .dz-num{
	font-size: 24rpx;
	color: #888;
}
.bottom-prod{
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: 16rpx;
}
.bottom-prod .prod-img{
	width: 100rpx;
	height: 100rpx;
	min-width: 100rpx;
	min-height: 100rpx;
	border-radius: 16rpx;
	margin-right: 18rpx;
}
.bottom-prod .prod-info{
	display: flex;
	flex-direction: column;
}
.bottom-prod .prod-info .prod-name{
	font-size: 24rpx;
	color: #333;
	display: -webkit-box;
	-webkit-line-clamp: 2; /*设定显示行数*/
	-webkit-box-orient: vertical;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 10rpx;
}
.bottom-prod .prod-info .prod-price{
	font-size: 24rpx;
	color: #FF5733;
}

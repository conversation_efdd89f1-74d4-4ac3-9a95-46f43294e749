<template>
		<!-- #ifndef H5-->
		<view class="active-find">
			<scroll-view class="scroll" scroll-y="true">
			<view class="find-box">
				<!-- <view class="find-title">发现中心</view> -->
				<view class="find-three">
<!--					<view class="find-three-item">-->
<!--						<view class="find-icon-box">-->
<!--							<image class="find-icon" :src="`${staticPicDomain}images/icon/grass-icon.png`"/>-->
<!--						</view>-->
<!--						<text class="find-text">种草</text>-->
<!--					</view>-->
					<view class="find-three-item" @tap="gotoVideo">
						<view class="find-icon-box">
							<image class="find-icon" :src="`${staticPicDomain}images/icon/video-icon.png`"/>
						</view>
						<text class="find-text">视频</text>
					</view>
					<!-- <view class="find-three-item" @tap="gotoZhibo">
						<view class="find-icon-box">
							<image class="find-icon" :src="`${staticPicDomain}images/icon/live-icon.png`"/>
						</view>
						<text class="find-text">直播</text>
					</view> -->
					<view class="find-three-item" @tap="gotoStore">
						<view class="find-icon-box">
							<image class="find-icon" :src="`${staticPicDomain}images/icon/store-icon.png`"/>
						</view>
						<text class="find-text">店铺街</text>
					</view>
				</view>
			</view>

			<view class="find-nav">
				<view :class="'find-nav-item ' + ('' == categoryId ? 'active-nav' : '')" @tap="changNav('')">
					<text class="find-nav-text">全部</text>
					<view class="find-line"/>
				</view>
				<view :class="'find-nav-item ' + (item.categoryId == categoryId ? 'active-nav' : '')" v-for="(item, index) in categoryList" :key="index"  @tap="changNav(item.categoryId)">
					<text class="find-nav-text">{{item.categoryName}}</text>
					<view class="find-line"/>
				</view>
			</view>
				<view class="find-list-box">
					<view class="find-list">
						<view class="find-list-item" v-for="(item, index) in leftRecords" :key="index" @tap="gotoDetail(item)">
							<image class="picture" :src="item.pics.split(',')[0]"/>
							<view class="bottom-info">
								<view class="prod-name">{{item.content}}</view>
								<view class="user-info">
									<view class="left">
										<image class="user-head" :src="item.user.pic"/>
										<text class="user-name">{{item.user.nickName}}</text>
									</view>
									<view class="right">
										<image class="dz" :src="`${staticPicDomain}images/icon/pointer.png`"/>
										<text class="dz-num">{{item.starNum}}</text>
									</view>
								</view>
								<view class="bottom-prod">
									<image class="prod-img" :src="item.product.pic ? item.product.pic :'`${staticPicDomain}images/icon/head04.png`'"/>
									<view class="prod-info">
										<text class="prod-name">{{item.product.prodName}}</text>
										<text class="prod-price">¥{{item.product.price || 0}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<view class="find-list">
						<view class="find-list-item" v-for="(item, index) in rightRecords" :key="index" @tap="gotoDetail(item)">
							<image class="picture" :src="item.pics.split(',')[0]"/>
							<view class="bottom-info">
								<view class="prod-name">{{item.content}}</view>
								<view class="user-info">
									<view class="left">
										<image class="user-head" :src="item.user.pic"/>
										<text class="user-name">{{item.user.nickName}}</text>
									</view>
									<view class="right">
										<image class="dz" :src="`${staticPicDomain}images/icon/pointer.png`"/>
										<text class="dz-num">{{item.starNum}}</text>
									</view>
								</view>
								<view class="bottom-prod">
									<image class="prod-img" :src="item.product.pic ? item.product.pic :`${staticPicDomain}images/icon/head04.png`"/>
									<view class="prod-info">
										<text class="prod-name">{{item.product.prodName}}</text>
										<text class="prod-price">¥{{item.product.price || 0}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<!-- #endif -->

		<!-- #ifdef H5 -->
		<view class="find">
		<scroll-view class="scroll" scroll-y="true">
		<view class="find-box">
			<!-- <view class="find-title">发现中心</view> -->
			<view class="find-three">
<!--				<view class="find-three-item">-->
<!--					<view class="find-icon-box">-->
<!--						<image class="find-icon" :src="`${staticPicDomain}images/icon/grass-icon.png`"/>-->
<!--					</view>-->
<!--					<text class="find-text">种草</text>-->
<!--				</view>-->
				<view class="find-three-item" @tap="gotoVideo">
					<view class="find-icon-box">
						<image class="find-icon" :src="`${staticPicDomain}images/icon/video-icon.png`"/>
					</view>
					<text class="find-text">视频</text>
				</view>
				<!-- <view class="find-three-item" @tap="gotoZhibo">
					<view class="find-icon-box">
						<image class="find-icon" :src="`${staticPicDomain}images/icon/live-icon.png`"/>
					</view>
					<text class="find-text">直播</text>
				</view> -->
				<view class="find-three-item" @tap="gotoStore">
					<view class="find-icon-box">
						<image class="find-icon" :src="`${staticPicDomain}images/icon/store-icon.png`"/>
					</view>
					<text class="find-text">店铺街</text>
				</view>
			</view>
		</view>

		<view class="find-nav">
			<view :class="'find-nav-item ' + ('' == categoryId ? 'active-nav' : '')" @tap="changNav('')">
				<text class="find-nav-text">全部</text>
				<view class="find-line"/>
			</view>
			<view :class="'find-nav-item ' + (item.categoryId == categoryId ? 'active-nav' : '')" v-for="(item, index) in categoryList" :key="index"  @tap="changNav(item.categoryId)">
				<text class="find-nav-text">{{item.categoryName}}</text>
				<view class="find-line"/>
			</view>
		</view>
			<view class="find-list-box">
				<view class="find-list">
					<view class="find-list-item" v-for="(item, index) in leftRecords" :key="index" @tap="gotoDetail(item)">
						<image class="picture" :src="item.pics.split(',')[0]"/>
						<view class="bottom-info">
							<view class="prod-name">{{item.content}}</view>
							<view class="user-info">
								<view class="left">
									<image class="user-head" :src="item.user.pic"/>
									<text class="user-name">{{item.user.nickName}}</text>
								</view>
								<view class="right">
									<image class="dz" :src="`${staticPicDomain}images/icon/pointer.png`"/>
									<text class="dz-num">{{item.starNum}}</text>
								</view>
							</view>
							<view class="bottom-prod">
								<image class="prod-img" :src="item.product.pic ? item.product.pic :`${staticPicDomain}images/icon/head04.png`"/>
								<view class="prod-info">
									<text class="prod-name">{{item.product.prodName}}</text>
									<text class="prod-price">¥{{item.product.price || 0}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="find-list">
					<view class="find-list-item" v-for="(item, index) in rightRecords" :key="index" @tap="gotoDetail(item)">
						<image class="picture" :src="item.pics.split(',')[0]"/>
						<view class="bottom-info">
							<view class="prod-name">{{item.content}}</view>
							<view class="user-info">
								<view class="left">
									<image class="user-head" :src="item.user.pic"/>
									<text class="user-name">{{item.user.nickName}}</text>
								</view>
								<view class="right">
									<image class="dz" :src="`${staticPicDomain}images/icon/pointer.png`"/>
									<text class="dz-num">{{item.starNum}}</text>
								</view>
							</view>
							<view class="bottom-prod">
								<image class="prod-img" :src="item.product.pic ? item.product.pic :`${staticPicDomain}images/icon/head04.png`"/>
								<view class="prod-info">
									<text class="prod-name">{{item.product.prodName}}</text>
									<text class="prod-price">¥{{item.product.price || 0}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		</view>
		<!-- #endif -->
</template>

<script>
	// pages/category/category.js
	var http = require("../../utils/http.js");
	var config = require("../../utils/config.js");
	var util = require("../../utils/util.js");

	export default {
		data() {
			return {
				records: [],
				leftRecords: [],
				rightRecords: [],
				categoryList: [],
				categoryId: '',
				isWechat: uni.getSystemInfoSync().platform
			};
		},

		components: {},
		props: {},
		computed:{
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.findCate()
			this.init('')
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {

		},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			// 加载导航标题
			uni.setNavigationBarTitle({
				title:this.i18n.find
			});

			
			this.init('')

		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			init: function(categoryId) {
				var ths = this; //加载分类列表

				var params = {
					url: uni.getStorageSync('token') ? "/p/prodComm/recommend/page" : "/center/prodComm/recommend/page",
					method: "GET",
					data: {
						categoryId
					},
					callBack: function(res) {
						const left = []
						const right = []
						for(let i=0;i<res.records.length;i++) {
						  if(i%2==0) {
						    right.push(res.records[i])
						  }else{
						    left.push(res.records[i])
						  }
						}
						ths.setData({
							records: res.records,
							leftRecords: right,
							rightRecords: left
						});
					}
				};
				http.request(params);
			},
			findCate: function() {
				var ths = this; //加载分类列表
				var params = {
					url: "/category/categoryInfo",
					method: "GET",
					data: {
						parentId: ''
					},
					callBack: function(res) {
						ths.setData({
							categoryList: res,
						});
					}
				};
				http.request(params);
			},
			changNav: function(categoryId) {
				this.setData({
					categoryId,
				});
				this.init(categoryId)
			},
			gotoDetail: function(item) {
				// util.checkAuthInfo(() => {

				// })
				uni.navigateTo({
					url: `/pages/find-detail/find-detail?prodCommId=${item.prodCommId}`
				});
			},
			gotoZhibo: function() {
				// #ifdef MP-WEIXIN
				util.checkAuthInfo(() => {
					uni.navigateTo({
						url: '/pages/liveBroadcast/liveBroadcast'
					});
				})
				return
				// #endif
				uni.showToast({
					title: '当前环境不支持，请到小程序中访问',
					duration: 2000,
					icon: 'none',
				})
			},
			gotoVideo: function() {
				// util.checkAuthInfo(() => {

				// })
				uni.navigateTo({
					url: '/pages/video-list/video-list'
				});
			},
			gotoStore: function(e) {
				// util.checkAuthInfo(() => {

				// })
				uni.navigateTo({
					url: '/pages/good-store/good-store'
				});
			},

		}
	};
</script>
<style>
	@import "./find.css";
</style>

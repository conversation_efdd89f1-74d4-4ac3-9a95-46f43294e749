/* pages/chooseRefundWay/chooseRefundWay.wxss */
image {
  display: block;
  width: 100%;
  height: 100%;
}
.page {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #f8f8f8;
  overflow: auto;
}

/* 头部商品信息 */
.top-box {
  text-align: left;
  padding: 18rpx 25rpx;
  background: #fff;
  border-bottom: 1rpx solid #f2f2f2;
}
.order-number {
  font-size: 30rpx;
  vertical-align: middle;
}
.goods-msg-box {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 15rpx 25rpx;
}
.img-box {
  display: inline-block;
  width: 150rpx;
  height: 150rpx;
  background: #f8f8f8;
  margin-right: 25rpx;
  vertical-align: top;
}
.goods-msg {
  display: inline-block;
  vertical-align: top;
  width: 75%;
}
.goods-title {
  font-size: 30rpx;
  line-height: 1.5em;
  display: -webkit-box;
  -webkit-line-clamp: 2; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}
.goods-quantity {
  font-size: 28rpx;
  color: #888888;
  margin-top: 26rpx;
}
/* 整单退款列表 */
.refund-goods-item{
  display: flex;
  width: 100%;
  padding: 20rpx;
  margin-bottom:20rpx; 
  box-sizing: border-box;
  border-bottom: 1rpx solid #eee;
}
.goods-text{
  width: 70%;
}
.refund-goods-item:last-child{
border: none;
margin-bottom:0rpx;
}

/* 选择退款方式 */
.choose-refund-way {
  background: #fff;
  padding: 0 25rpx;
  font-size: 28rpx;
}
.choose-tit {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
  font-size: 30rpx;
}
.refund-way {
  position: relative;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
}
.refund-way-tit {
  padding-bottom: 15rpx;
  font-size: 30rpx;
}
.refund-way-explain {
  color: #888888;
  font-size: 28rpx;
}

/* 右箭头 */
.enter {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 25rpx;
  height: 25rpx;
}
<template>
	<view style="padding: 8rpx;">
		<view v-if="shopId" class="shop-info" @tap="goShop(shopInfo.shopId, shopInfo.renovationId)">
			<image class="shop-logo" :src="shopInfo.shopLogo"></image>
			<text class="shop-name">{{shopInfo.shopName}}</text>
		</view>
		<view v-else class="shop-info">
			<image class="shop-logo" @tap="goHome()" :src="uniLoginLogoImg"></image>
			<text class="shop-name">校园WeNet</text>
		</view>
    <view class="coupon-box">
      <view class="coupon-text">
        <text class="coupon-name">{{ couponInfo.couponName }}</text>
				<view v-if="couponInfo.validTimeType === 1">
					<text>有效时间：</text><br/>
					<text v-if="couponInfo.startTime">{{ formatStartTime }}~{{ formatEndTime }}</text>
				</view>
				<view v-if="couponInfo.validTimeType === 2">
					<text>领券以后{{ couponInfo.afterReceiveDays }}天生效，有效天数为{{ couponInfo.validDays }}天</text>
				</view>
			</view>
      <view class="coupon-btn" @click="receiveCoupon()">立即领取</view> 
    </view>
  </view>
</template>

<script>
	import popup from "../../components/popup/popup.vue";
	var http = require("../../utils/http");
	var util = require("../../utils/util.js");

	export default {
		data() {
			return {
				shopInfo: {}, // 店铺信息
				shopId: null,
        couponId: null,
        couponInfo: {},
        userInfo: {},
      	stsType: 4,
				uniLoginLogoImg: '',
			};
		},
		components: {
			popup,
		},
		props: {},
		computed: {
			i18n() {
				return this.$t('index')
			},
			formatStartTime: function(){
				return util.formatTimeExceptSecond(new Date(this.couponInfo?.startTime))
			},
			formatEndTime: function(){
				return util.formatTimeExceptSecond(new Date(this.couponInfo?.endTime))
			},
		},
    onLoad(options){
			if(typeof options.scene === 'string') {
				options.scene = decodeURIComponent(options.scene)
			}
			const decodeOptions = util.parseUrlToObj(decodeURIComponent(options.scene))
			if(decodeOptions)
      this.couponId= decodeOptions.couponId
			this.shopId = decodeOptions.shopId
			if(decodeOptions.shopId) {
				this.getShopInfo()
			}
      if(decodeOptions.couponId){
        this.getCouponInfo()
      }

			var uniLoginLogoImg = uni.getStorageSync("uniLoginLogoImg");
			if (uniLoginLogoImg) {
				this.uniLoginLogoImg = uniLoginLogoImg
			} else {
				// 获取uni-app相关配置
				this.getUniWebConfig()
			}
    },
		methods: {
			getUniWebConfig: function() {
				const params = {
					url: "/webConfig/getUniWebConfig",
					method: "GET",
					data: {},
					callBack: res => {
						this.setData({
							uniLoginLogoImg: res.uniLoginLogoImg
						});
						uni.setStorageSync("uniLoginLogoImg",this.uniLoginLogoImg)
					}
				};
				http.request(params);
			},
			/**
			 * 获取店铺信息
			 */
			getShopInfo() {
				http.request({
					url: "/shop/headInfo",
					method: "GET",
					data: {
						shopId: this.shopId
					},
					callBack: res => {
						this.setData({
							shopInfo: res
						})
						uni.setStorageSync('shopInfo', res)
					}
				});
			},
      /**
			 * 获取优惠券信息
			 */
			getCouponInfo() {
				http.request({
					url: "/coupon/couponById",
					method: "GET",
					data: {
						couponId: this.couponId
					},
					callBack: res => {
						this.setData({
							couponInfo: res
						})
					}
				});
			},
			// 去到店铺首页
			goShop(shopId, renovationId) {
				let url
				url = renovationId ? '/pages/shop-feature-index/shop-feature-index0?shopId=' + shopId +
							'&renovationId=' + renovationId :  '/packageShop/pages/shopPage/shopPage?shopId=' + shopId
				uni.navigateTo({
					url: url
				})
			},
			goHome() {
				wx.switchTab({
					url: '/pages/index/index' // 首页路径
				})
			},
      // 领取优惠券
      receiveCoupon(){
				http.request({
					url: "/p/myCoupon/receive",
					method: "POST",
					data: this.couponId,
					callBack: data => {
						this.goCoupon()
					}
				})
      },
			// 跳转至优惠券详情
			goCoupon() {
				util.checkAuthInfo(() => {
					let url = '/pages/prod-classify/prod-classify?sts=' + this.stsType;
					const id = this.couponInfo.couponId;
					const title = this.i18n.couponEventGoods;

					if (id) {
						url += "&tagid=" + id + "&title=" + title;
					}

					uni.navigateTo({
						url: url
					});
				});
			}
		}
	};
</script>
<style>
	@import "./share-coupon.css";
</style>

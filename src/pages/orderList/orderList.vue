<template>
	<!-- pages/orderList/orderList.wxml -->
	<view class="container">
		<!-- 头部菜单 -->
		<view class="order-tit">
			<text @tap="onStsTap" data-sts="0" :class="sts==0?'on':''">{{i18n.all}}</text>
			<text @tap="onStsTap" data-sts="1" :class="sts==1?'on':''">{{i18n.toBePaid}}</text>
			<text @tap="onStsTap" data-sts="2" :class="sts==2?'on':''">{{i18n.toBeDelivered}}</text>
			<text @tap="onStsTap" data-sts="3" :class="sts==3?'on':''">{{i18n.toBeReceived}}</text>
			<text @tap="onStsTap" data-sts="5" :class="sts==5?'on':''">{{i18n.completed}}</text>
		</view>
		<!-- 头部菜单 end -->

		<!-- 订单列表 -->
		<view class="main">
			<block v-for="(item, orderNumber) in list" :key="orderNumber">
				<view class="prod-item">
					<!-- 店铺 -->
					<!-- <view class="shop-box" @tap="toShopIndex(item.shopId, item.renovationId)"> -->
					<view class="shop-box">
						<view class="shop-icon">
							<image :src="`${staticPicDomain}images/icon/shop.png`"></image>
						</view>
						<view class="shop-name">{{item.shopName}}</view>
					</view>
					<!-- 订单编号 -->
					<view class="order-num">
						<text>{{i18n.orderNumber}}：{{item.orderNumber}}</text>
						<view class="order-state">
							<text :class="['order-sts' , {red: (item.status < 5) }]">{{['',i18n.toBePaid,i18n.toBeDelivered,i18n.toBeReceived,i18n.toBeEvaluated,i18n.completed,i18n.cancelled,i18n.inAGroup, '已完成'][item.status]}}</text>
							<view class="red">
                <!-- returnMoneySts 退款状态 1.买家申请 2.卖家接受 3.买家发货 4.卖家收货 5.退款成功 6.买家撤回申请(无) 7.商家拒绝(无) -1.退款关闭 -->
								<text v-if="item.returnMoneySts==0"></text>
								<text v-if="item.returnMoneySts==1||item.returnMoneySts==2||item.returnMoneySts==3||item.returnMoneySts==4">({{i18n.refunding}})</text>
								<!-- <text v-if="item.returnMoneySts==5||item.returnMoneySts==7">({{i18n.refundComplete}})</text> -->
                <!-- 退款完成 -->
								<text v-if="item.returnMoneySts == 5 && item.refundStatus !== 3">({{i18n.refundComplete}})</text>
                <!-- 部分退款完成refundStatus === 3 && returnMoneySts === 5 -->
								<text v-if="item.returnMoneySts == 5 && item.refundStatus == 3">({{i18n.partialRefundCompleted}})</text>
								<text v-if="item.returnMoneySts==-1">({{i18n.refundClosed}})</text>
							</view>
							<!-- 删除订单按钮 -->
							<view class="clear-btn" v-if="(item.status==5 || item.status==6) && (item.returnMoneySts>4 || item.returnMoneySts==-1 || item.returnMoneySts==null || item.returnMoneySts=='')">
								<image :src="`${staticPicDomain}images/icon/clear-his.png`" class="clear-list-btn" @tap="delOrderList" :data-ordernum="item.orderNumber"></image>
							</view>
						</view>
					</view>
					<!-- 商品列表 -->
					<!-- 一个订单单个商品的显示 -->
					<block v-if="item.orderItemDtos.length==1">
						<block v-for="(prod, prodId) in item.orderItemDtos" :key="prodId">
							<view>
								<view class="item-cont" @tap="toOrderDetailPage" :data-ordernum="item.orderNumber">
									<view class="prod-pic">
										<image lazy-load :src="prod.pic"></image>
									</view>
									<view class="prod-info">
										<view class="prodname">{{prod.prodName}}</view>
										<view class="sku-box">
											<!-- 拼团icon -->
											<text class="spell-group-icon" v-if="item.orderType">
												<!-- orderType 订单类型(0普通订单 1团购订单 2秒杀订单) -->
												{{['',i18n.aGroup,i18n.spike,i18n.integral][item.orderType]}}
											</text>
											<!-- 拼团icon end -->
											<!-- 配送类型 1:快递 2:自提 3：无需快递) -->
											<text class="spell-group-icon" v-if="item.dvyType == 2">{{i18n.pickitUp}}</text>
											<text class="prod-info-cont">{{prod.skuName || ''}}</text>
										</view>
										<view class="price-nums">
											<text class="prodprice">
												<text class="symbol" >￥</text>
												<text class="big-num" >{{parsePrice(prod.price)[0]}}</text>
												<text class="small-num" >.{{parsePrice(prod.price)[1]}}</text>
												<text class="small-num" v-if="item.orderType==3" decode="true">&emsp;+&emsp;</text>
												<text class="small-num" v-if="prod.useScore && item.orderType==3">{{prod.useScore}} {{i18n.integral}}</text>
											</text>
											<text class="prodcount">x{{prod.prodCount}}</text>
										</view>
									</view>
								</view>
							</view>
						</block>
					</block>
					<!-- 一个订单多个商品时的显示 -->
					<block v-else>
						<view class="item-cont" @tap="toOrderDetailPage" :data-ordernum="item.orderNumber">
							<scroll-view scroll-x="true" scroll-left="0" scroll-with-animation="false" class="categories">
								<block v-for="(prod, prodId) in item.orderItemDtos" :key="prodId">
									<view class="prod-pic">
										<!-- 拼团icon -->
										<!-- <view class="spell-group-order"><image src="../../images/icon/spell-group-order.png"></image></view> -->
										<!-- 拼团icon end -->
										<image lazy-load :src="prod.pic"></image>
									</view>
								</block>
							</scroll-view>
						</view>
					</block>
					<!-- 总计 -->
					<view class="total-num">
            <view>
              <voucher-link-btn v-if="item.writeOffNum != 0 && item.status === 5 && item.returnMoneySts != 5" :order-item="item" />
            </view>
            <view class="totals">
              <text class="prodcount">{{i18n.inTotal}}{{item.totalCounts}}{{i18n.items}}</text>
              <view class="prodprice">{{i18n.total}}：
                <text class="symbol" >￥</text>
                <text class="big-num">{{parsePrice(item.actualTotal)[0]}}</text>
                <text class="small-num"   decode="true">.{{parsePrice(item.actualTotal)[1] + '&nbsp;'}}</text>
                <text class="small-num" v-if="item.userScore > 0">+</text>
                <text class="big-num" v-if="item.userScore > 0"  decode="true">{{'&nbsp;' + item.userScore + '&nbsp;'}} <text class="small-num">{{i18n.integral}}</text></text>
              </view>
            </view>
					</view>
					<!-- 商品列表 end -->
					<view class="prod-foot" v-if="item.status == 1 || item.status == 3 || item.status==5 || (item.status==5 && item.orderItemDtos[0].commSts==0)">
						<view class="btn">
							<view
								class="button"
								@tap="toGroupDetail(item.orderNumber)"
								v-if="item.status === 7">{{ i18n.inviteFriendsJoin }}
							</view>
							<view
							class="button"
							@tap="viewIinvoice(item.orderNumber,item.orderInvoiceId)"
							v-if="((item.orderInvoiceId && item.status !== 6) ||
							(item.orderInvoiceId && (item.status === 6 && item.refundStatus))) && item.status !== 1 && item.orderType !== 3"
							>{{ i18n.invoice.viewInvoice }}</view>
							<!-- <view
							class="button"
							@tap="showInvoicePopup(item.shopId ,item.orderNumber)"
							v-if="!item.orderInvoiceId &&item.status !== 6 && item.status !== 1 && item.orderType !== 3">{{ i18n.invoice.InvoicingRequest }}</view> -->
							<text v-if="item.status==1" class="button" @tap="onCancelOrder(item.orderNumber)">{{i18n.cancelOrder}}</text>
							<text v-if="item.status==1" class="button warn"  @tap="onPayAgain(item.orderNumber, item.orderType, item.dvyType, item.isDistribution)">{{i18n.payment}}</text>
							<text v-if="(item.status==3 || item.status==5) && item.dvyType != 2 && item.dvyType != 3" class="button" @tap="toDeliveryPage(item.orderNumber)">{{i18n.viewLogistics}}</text>
							<text v-if="item.status==3" class="button warn" @tap="onConfirmReceive(item.orderNumber)" :data-ordernum="item.orderNumber">{{i18n.confirmRceipt}}</text>
							<!-- 评价status==5 -->
							<text v-if="item.status==5 && item.orderItemDtos[0].commSts==0 && item.shopId !== null && prodCommentSwitch" class="button warn" @tap="onComment" :data-info="JSON.stringify(item.orderItemDtos)"
							 :data-ordernum="item.orderNumber">{{i18n.evaluation}}</text>
						</view>
					</view>
					<view class="prod-foot" v-else>
						<view class="btn">
							<view
								class="button"
								@tap="toGroupDetail(item.orderNumber)"
								v-if="item.status === 7">{{ i18n.inviteFriendsJoin }}
							</view>
							<view
							class="button"
							@tap="viewIinvoice(item.orderNumber,item.orderInvoiceId)"
							v-if="((item.orderInvoiceId && item.status !== 6) ||
							(item.orderInvoiceId && (item.status === 6 && item.refundStatus))) && item.status !== 1 && item.orderType !== 3"
							>{{ i18n.invoice.viewInvoice }}</view>
							<!-- <view
							class="button"
							@tap="showInvoicePopup(item.shopId ,item.orderNumber)"
							v-if="!item.orderInvoiceId &&item.status !== 6 && item.status !== 1 && item.orderType !== 3">{{ i18n.invoice.InvoicingRequest }}</view> -->
						</view>
					</view>
				</view>
			</block>
		</view>
		<!-- 订单列表 end -->
		<!-- 返回首页的悬浮按钮 -->
		<view class="home" @tap="toHomePage">
		  <image :src="`${staticPicDomain}images/icon/order-home.png`"></image>
		</view>
		<!-- 到底了~ -->
		<view class="loadall" v-if="list.length > 10 && loadAll">{{i18n.endTips}}</view>
		<!-- 空 -->
		<view class="empty" v-if="!list.length">
			<view class="empty-icon">
				<image :src="`${staticPicDomain}images/icon/empty.png`"></image>
			</view>
			<view class="empty-text">{{i18n.noOrderTips}}</view>
		</view>
		<invoiceEdit v-if="isShowInvoicePopup" :shop-id="invoiceShopId" :order-number="invoiceOrderNumber" @closePopup="closePopup" @getData="getOrderDataList" />
    <PayComponent ref="payRef" :toOrderListWhenClose="false" />
	</view>
</template>

<script>
const http = require("../../utils/http.js")
const util = require("../../utils/util.js")
import invoiceEdit from '../../components/invoiceEdit/index'
import VoucherLinkBtn from '@/components/voucher-link-btn';
import PayComponent from '@/components/pay/pay'

export default {
	components:{
	  invoiceEdit,
    VoucherLinkBtn,
    PayComponent
	},
	data() {
		return {
			list: [],
			current: 1,
			pages: 0,
			sts: 0,
			endTime: '', //订单过期时间
			loadAll: false, // 已加载全部
			invoiceShopId: '',
			isShowInvoicePopup: false,
			invoiceOrderNumber: '', // 当前选择发票的id
      showBalancePay: true, //分销订单不显示余额支付
		}
	},
	props: {},
	computed:{
		i18n() {
			return this.$t('index')
		},
    prodCommentSwitch() {
      return this.$store.state.prodCommentSwitch
    }
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		if (options.sts) {
			this.setData({
				sts: options.sts
			});
		}
	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
		// 加载导航标题
		uni.setNavigationBarTitle({
			title:this.i18n.orderList
		});
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 0
		})
		this.loadOrderData(this.sts, 1);
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {
		if (this.current < this.pages) {
			this.loadOrderData(this.sts, this.current + 1);
		} else {
			this.setData({
				loadAll: true
			})
		}
	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		/**
		 * 邀请好友拼团(跳转拼团详情)
		 */
		toGroupDetail(orderNumber) {
			uni.navigateTo({
				url: '/packageActivities/pages/spellGroupDetails/spellGroupDetails?orderNumber=' + orderNumber
			})
		},

		// 发票信息弹窗
		showInvoicePopup (shopId,invoiceOrderNumber) {
		  this.invoiceShopId = shopId
		  this.invoiceOrderNumber = invoiceOrderNumber
		  this.isShowInvoicePopup = true
		},
		// 关闭弹窗
		closePopup() {
		  this.isShowInvoicePopup = false
		},
		getOrderDataList() {
			this.loadOrderData(this.sts, this.current)
		},
		/**
		 * 加载订单数据
		 */
		loadOrderData: function(sts, current) {
			var ths = this;
			wx.showLoading(); //加载订单列表
			var params = {
				url: "/p/myOrder/myOrder",
				method: "GET",
				data: {
					current: current,
					size: 10,
					status: sts,
				},
				callBack: function(res) {
					res.records.forEach(orderItem => {
						orderItem.totalCounts = 0
						if (orderItem.returnMoneySts == null) {
							orderItem.returnMoneySts = 0
						}
						orderItem.orderItemDtos.forEach(prod => {
							orderItem.totalCounts += prod.prodCount
						})
					})
					var list = [];
					if (res.current == 1) {
						list = res.records;
					} else {
						list = ths.list.concat(res.records)
					}
					ths.setData({
						list: list,
						pages: res.pages,
						current: res.current,
					});
					uni.hideLoading();
				},
			};
			http.request(params);
		},

		/**
		 * 状态点击事件
		 */
		onStsTap: function(e) {
			util.tapLog(3)
			var sts = e.currentTarget.dataset.sts;
			this.setData({
				sts: sts
			});
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0
			})
			this.loadOrderData(sts, 1);
		},

		/**
		 * 跳转店铺首页
		 */
		toShopIndex: function(shopId, renovationId) {
			let url
			url = renovationId ? '/pages/shop-feature-index/shop-feature-index0?shopId=' + shopId +
						'&renovationId=' + renovationId :  '/packageShop/pages/shopPage/shopPage?shopId=' + shopId
			url = shopId ? url : '/packageMemberIntegral/pages/integralIndex/integralIndex'
			uni.navigateTo({
				url: url
			})
		},

		/**
		 * 查看物流
		 */
		toDeliveryPage: function(orderNumber) {
			util.tapLog(3)
			uni.navigateTo({
				url: '/packageShop/pages/logisticsInfo/logisticsInfo?orderNumber=' + orderNumber
			});
		},

		/**
		 * 取消订单
		 */
		onCancelOrder: function(orderNumber) {
			util.tapLog(3)
			var ths = this;
			uni.showModal({
				title: '',
				content: ths.i18n.cancelOrderTips,
				confirmColor: "#3e62ad",
				cancelColor: "#3e62ad",
				cancelText: ths.i18n.no,
				confirmText: ths.i18n.yes,

				success(res) {
					if (res.confirm) {
						uni.showLoading({
						// #ifndef MP-TOUTIAO
						mask: true
						// #endif
						});
						var params = {
							url: "/p/myOrder/cancel/" + orderNumber,
							method: "PUT",
							data: {},
							callBack: function(res) {
								//console.log(res);
								ths.loadOrderData(ths.sts, 1);
								uni.hideLoading();
							}
						};
						http.request(params);
					} else if (res.cancel) { //console.log('用户点击取消')
					}
				}

			});
		},

		/**
		 * 付款
		 * @param {String/Numver} orderNumber 订单编号
		 * @param {String/Numver} orderType 订单类型
		 * @param {String/Numver} dvyType 物流方式
		 */
		onPayAgain: function(orderNumber, orderType, dvyType, isDistribution) {
			util.tapLog(3)
			uni.showLoading({
				// #ifndef MP-TOUTIAO
				mask: true
				// #endif
			});
			var params = {
				url: "/p/order/getOrderPayInfoByOrderNumber",
				method: "GET",
				data: {
					orderNumbers: orderNumber
				},
				callBack: res => {
					uni.hideLoading();
					this.setData({
						//订单过期时间
						endTime: res.endTime.replace(/-/g, "/"),
					});
					var nowTime = new Date().getTime(); //现在时间（时间戳）
					var endTime = new Date(this.endTime).getTime(); //结束时间（时间戳）
					var time = (endTime - nowTime) / 1000; //距离结束的毫秒数
					if (time > 0) {
            this.$refs.payRef.init({
              orderNumbers: orderNumber,
              orderType: orderType,
              dvyType: dvyType,
              hideBalancePay: isDistribution ? true : false
            })
					} else {
						uni.showModal({
							title: this.i18n.tips,
							content: this.i18n.orderExpired,
							cancelText: this.i18n.cancel,
							confirmText: this.i18n.confirm,
							showCancel: false,
							confirmColor: "#e43130",
							success: (res) => {
								if (res.confirm) {
									this.loadOrderData(this.sts, 1);
								} else if (res.cancel) {
									//console.log('用户点击取消')
								}
							}
						});
						return;
					}
				},
				errCallBack: res => {
					uni.hideLoading();
				},
			};
			http.request(params);
		},

		/**
		 * 查看订单详情
		 */
		toOrderDetailPage: function(e) {
			util.tapLog(3)
			// uni.navigateTo({
			// 	url: '/pages/order-detail/order-detail?orderNum=' + e.currentTarget.dataset.ordernum
			// });
			this.$Router.push({
				path:'/pages/order-detail/order-detail', query: {orderNum:e.currentTarget.dataset.ordernum}
			})
		},

		/**
		 * 确认收货
		 */
		onConfirmReceive: function(orderNumber) {
			util.tapLog(3)
			var ths = this;
			uni.showModal({
				title: '',
				content: ths.i18n.haveRceivedGoods,
				cancelText: ths.i18n.cancel,
				confirmText: ths.i18n.confirm,
				confirmColor: "#4A6CEA",

				success(res) {
					if (res.confirm) {
						uni.showLoading({
							// #ifndef MP-TOUTIAO
							mask: true
							// #endif
						});
						var params = {
							url: "/p/myOrder/receipt/" + orderNumber,
							method: "PUT",
							data: {},
							callBack: function(res) {
								//console.log(res);
								ths.loadOrderData(ths.sts, 1);
								uni.hideLoading();
							}
						};
						http.request(params);
					} else if (res.cancel) { //console.log('用户点击取消')
					}
				}

			});
		},
		//删除已完成||已取消的订单
		delOrderList: function(e) {
			util.tapLog(3)
			var ths = this;
			uni.showModal({
				title: '',
				content: ths.i18n.sureDeleteOrder,
				cancelText: ths.i18n.cancel,
				confirmText: ths.i18n.confirm,
				confirmColor: "#eb2444",

				success(res) {
					if (res.confirm) {
						var ordernum = e.currentTarget.dataset.ordernum;
						uni.showLoading();
						var params = {
							url: "/p/myOrder/" + ordernum,
							method: 'POST',
							data: {},
							callBack: function(res) {
								ths.loadOrderData(ths.sts, 1);
								uni.hideLoading();
							}
						};
						http.request(params);
					} else if (res.cancel) {
					}
				}

			});
		},

		/**
		 * 跳转评价页面
		 */
		onComment: function(e) {
			util.tapLog(3)
			var info = e.currentTarget.dataset.info;
			uni.setStorageSync("orderItemInfo", info);
			uni.navigateTo({
				url: '/pages/prodComm/prodComm'
			});
		},
		/**
		 * 跳转首页
		 * */
		toHomePage() {
			this.$Router.pushTab('/pages/index/index')
		},
		// 查看发票
		viewIinvoice (orderNumber, orderInvoiceId) {
		  uni.navigateTo({
			url: `/packageUser/pages/invoice-detail/invoice-detail?orderNumber=${orderNumber}&orderInvoiceId=${orderInvoiceId}`
		  })
		}
	}
}
</script>
<style lang="scss" scoped>
/* pages/orderList/orderList.wxss */

page {
  background-color: #f4f4f4;
  color: #333;
}
.container {
	height: auto;
}

/* 头部菜单 */

.order-tit {
  position: fixed;
  top: 0;
  display: flex;
  justify-content: space-around;
  z-index: 999;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #fff;
}

.order-tit text {
  display: block;
  font-size: 24rpx;
  text-align: center;
}

.order-tit text.on {
  border-bottom: 4rpx solid $wenet-color-brand;
  color: $wenet-color-brand;
  font-weight: 600;
}

/*  end 头部菜单 */

.main {
  margin-top: 108rpx;
}

/* 商品列表 */

.prod-item {
  background-color: #fff;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

/* 店铺 */
.shop-box {
  padding: 20rpx;
  border-bottom: 2rpx solid #f2f2f2;
  display: flex;
  align-items: center;
}

.shop-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
  font-size: 0;
}

.shop-icon > image {
  width: 100%;
  height: 100%;
}

.shop-name {
  font-size: 24rpx;
  font-weight: 600;
  position: relative;
  padding-right: 16rpx;
}

.shop-name span{
	position: absolute;
	right: 0;
	top: 48%;
	display: block;
	width: 12rpx;
	height: 12rpx;
	content: " ";
	font-size: 0;
	border-top: 2rpx solid #666;
	border-right: 2rpx solid #666;
	transform: rotate(45deg) translateY(-50%);
}

.prod-item .order-num {
  padding: 20rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
}

.order-num > text {
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-state {
  color: #999999;
  display: flex;
  align-items: center;
}

.order-state.red {
  color: #e43130;
}

.prod-item .order-num .clear-btn {
  width: 28rpx;
  height: 28rpx;
  font-size: 0;
  margin-left: 42rpx;
  position: relative;
}

.prod-item .order-num .clear-btn::after {
  content: " ";
  display: block;
  position: absolute;
  left: -20rpx;
  top: 0rpx;
  width: 2rpx;
  height: 32rpx;
  background: #eee;
}

.prod-item .order-num .clear-btn .clear-list-btn {
  width: 100%;
  height: 100%;
}

.prod-item .item-cont {
  display: flex;
  align-items: center;
  padding: 20rpx;
  display: -webkit-flex;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  background: #fff;
  position: relative;
}

.prod-item .item-cont .categories {
  white-space: nowrap;
}

.prod-item .item-cont .prod-pic {
  position: relative;
  display: inline-block;
  font-size: 0;
  width: 160rpx;
  height: 160rpx;
  background: #fff;
  margin-right: 20rpx;
  border-radius: 20rpx;
}

.prod-item .item-cont .prod-pic image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.spell-group-order {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 60rpx;
  height: 30rpx;
}

.prod-item .item-cont .prod-info {
  font-size: 24rpx;
  position: relative;
  height: 160rpx;
  -webkit-flex: 1;
  -ms-flex: 1;
  -webkit-box-flex: 1;
  -moz-box-flex: 1;
  flex: 1;
}

.prod-item .item-cont .prod-info .prodname {
  line-height: 34rpx;
  max-height: 68rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.prod-item .item-cont .prod-info .sku-box {
	display: flex;
	align-items: center;
	margin-top: 10rpx;
}

.prod-item .item-cont .prod-info .prod-info-cont {
  color: #999;
  font-size: 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.prod-item .price-nums {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: baseline;
  line-height: 32rpx;
}

.prod-item .price-nums .prodprice {
	flex: 1;
}

.prod-item .price-nums .prodcount {
  color: #999;
  font-family: arial;
  margin-right: 10rpx;
}

.prod-item  .spell-group-icon {
  margin-right: 10rpx;
  font-size: 20rpx;
  color: #fff;
  background: $wenet-color-brand;
  padding: 0 6rpx;
  border-radius: 4rpx;
	display: flex;
	align-items: center;
}

.prod-item .total-num {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 20rpx;
  position: relative;
	justify-content: space-between;
	border-top: 2rpx solid #eee;
  box-sizing: border-box;
}

.total-num .totals {
  display: flex;
  align-items: center;
}

.prod-item .total-num .right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: baseline;
  font-size: 24rpx;
}

.prod-item .total-num .prodcount {
  margin-right: 20rpx;
	color: #333;
}

.prod-item .prod-foot {
  
}

.prod-item .prod-foot .btn .button {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  margin-left: 0;
  margin-right: 20rpx;
}

.prod-item  .prod-foot .total {
  font-size: 24rpx;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid $wenet-color-brand;
}

.btn {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.button {
  display: inline-block;
  margin-left: 10px;
  font-size: 24rpx;
  background: #fff;
  padding: 10rpx 30rpx;
  border-radius: 80rpx;
  border: 2rpx solid $wenet-color-legacy-primary;
  color: $wenet-color-legacy-primary;
}

.button.warn {
 color: #fff;
/*  border-color: #e43130; */
  background: var(--primary-color);
}

/* end 商品列表 */


/* 根据状态显示不同的颜色 */

.order-state .order-sts {
  color: #333;
}

.order-state .red {
  color: $wenet-color-error;
}

/* 加载完成 */
.loadall {
  margin: 20rpx 0;
  line-height: 2em;
  font-size: 28rpx;
  color: #aaa;
  text-align: center;
}
/* 返回首页的按钮 */
.home image{
	position: fixed;
	bottom: 80rpx;
	right: 30rpx;
	width: 90rpx;
	height: 90rpx;
}

/* 列表为空 */
.empty {
  padding-top: 200rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  display: block;
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 40rpx;
}

</style>

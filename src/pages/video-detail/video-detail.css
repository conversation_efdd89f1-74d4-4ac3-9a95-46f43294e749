/* pages/category/category.wxss */

page {
  height: 100%;
  background: #f5f5f5;
}
.video-detail{
	width: 100%;
	height: 100vh;
	background: #000;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
}
.close-box{
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	top: 20px;
	right: 20px;
}
.close{
	width: 40rpx;
	height: 40rpx;
}
.video-box{
	width: 100%;
	height: 700rpx;
}

.video-box video {
  display: block;
  width: 100%;
  height: 100%;
}

.video-dz{
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	padding-right: 40rpx;
}
.video-item{
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-top: 20rpx;
}
.dz-icon{
	width: 54rpx;
	height: 54rpx;
	background: #b3b3b3;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 6rpx;
	border-radius: 50%;
}
.icon{
	width: 34rpx;
	height: 34rpx;
}
.text{
	font-size: 28rpx;
	color: #fff;
}
.video-product{
	display: flex;
	flex-direction: row;
	align-items: center;
	width: 550rpx;
	height: 132rpx;
	border-radius: 16rpx;
	background: #fff;
	padding: 0 6rpx;
	margin-left: 22px;
	margin-bottom: 60rpx;
}
.picture{
	width: 124rpx;
	height: 124rpx;
	min-width: 124rpx;
	min-height: 124rpx;
}
.title-box{
	display: flex;
	flex-direction: column;
}
.title{
	font-size: 28rpx;
	color: #333;
	display: -webkit-box;
	-webkit-line-clamp: 1; /*设定显示行数*/
	-webkit-box-orient: vertical;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
}
.price{
	font-size: 28rpx;
	color: #FF5733;
	margin-top: 20rpx;
}
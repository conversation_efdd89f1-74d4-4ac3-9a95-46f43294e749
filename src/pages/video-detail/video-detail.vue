<template>
	<view class="video-detail">
		<view class="close-box" @tap="goBack">
			<image class="close" :src="`${staticPicDomain}images/icon/video-xx.png`"/>
		</view>

		<view class="video-box">
			<video
				id="myVideo"
				:src="videoInfo.video"
				:show-fullscreen-btn="false"
				:enable-progress-gesture="false"
				:autoplay="true"
			/>
		</view>

		<view class="video-dz">
			<view class="video-item" @tap="dzFun">
				<view class="dz-icon">
					<image class="icon" :src="videoInfo.videoStarStatus ? `${staticPicDomain}/icon/sax.png` : `${staticPicDomain}/icon/video-ax.png`"/>
				</view>
				<text class="text">{{videoInfo.videoStarNum}}</text>
			</view>
<!--			<view class="video-item">-->
<!--				<view class="dz-icon">-->
<!--					<image class="icon" :src="`${staticPicDomain}images/icon/video-mes.png`"/>-->
<!--				</view>-->
<!--				<text class="text">{{videoInfo.videoViewCount}}</text>-->
<!--			</view>-->
		</view>

		<view class="video-product" @tap="goProddetail">
			<image class="picture" :src="videoInfo.pic"/>
			<view class="title-box">
				<text class="title">{{videoInfo.prodName}}</text>
				<text class="price">¥{{videoInfo.price}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	// pages/category/category.js
	var http = require("../../utils/http.js");
	var config = require("../../utils/config.js");
	var util = require("../../utils/util.js");

	export default {
		data() {
			return {
				videoInfo: {},
			};
		},

		components: {},
		props: {},
		computed:{
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.init(options.prodId)
			this.videoPlay(options.prodId)
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {

		},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			// 加载导航标题
			uni.setNavigationBarTitle({
				title:'视频播放'
			});

		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			init: function(prodId) {
				var ths = this; //加载分类列表

				var params = {
					url: uni.getStorageSync('token') ? "/p/user/video/prodInfo" : "/center/video/prodInfo",
					method: "GET",
					data: {
						prodId
					},
					callBack: function(res) {
						ths.setData({
							videoInfo: res,
						});
					}
				};
				http.request(params);
			},
			videoPlay: function(prodId) {
				var params = {
					url: "/prod/video/view",
					method: "GET",
					data: {
						prodId
					},
					callBack: function(res) {
					}
				};
				http.request(params);
			},
			dzFun: function() {
				var ths = this; //加载分类列表
				var params = {
					url: !this.videoInfo.videoStarStatus ? "/p/user/video/star" : "/p/user/video/unstar",
					method: "POST",
					data: {
						prodId:this.videoInfo.prodId
					},
					callBack: function(res) {
						ths.init(ths.videoInfo.prodId)
					}
				};
				http.request(params);
			},
			goBack: function() {
				uni.navigateBack({
					delta: 1
				});
			},
			goProddetail: function() {
				uni.navigateTo({
					url: `/pages/prod/prod?prodid=${this.videoInfo.prodId}`
				});
			},
		}
	};
</script>
<style>
	@import "video-detail.css";
</style>

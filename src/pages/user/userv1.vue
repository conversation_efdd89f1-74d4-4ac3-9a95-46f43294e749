<template>
  <view>
    <view class="container">
      <!-- 用户信息 -->
      <view class="userinfo-none" v-if="isAuthInfo">
        <view class="default-pic" @tap="toPersonalInformation">
          <image v-if="!!userInfo" :src="userInfo.pic ? userInfo.pic : `${staticPicDomain}images/icon/head04.png`"></image>
        </view>
        <view class="none-login">
          <text class="nick-name" v-if="!!userInfo" @tap="toPersonalInformation">{{ userInfo.nickName }}</text>
          <view class="level" v-if="showVip && isVip">
            <view class="vip-card__level-sign" :class="{ 'vip-level-2': isVip }">
              <image class="vip-card__level-img" width="40rpx" height="40rpx" v-if="userLevelInfo && userLevelInfo.userLevel" :src="userLevelInfo.userLevel.img" />
              <view class="vip-card__level-name" v-if="userLevelInfo.userLevel">{{userLevelInfo.userLevel.level==0 ? (i18n.ordinaryMembership || '普通会员') : userLevelInfo.levelName}}</view>
            </view>
          </view>
        </view>
        <view class="right-set-box">
          <view class="set-up-info" @tap="goset">
            <n-i icon="right" width="40rpx" height="40rpx" />
          </view>
        </view>
      </view>
      <view class="userinfo-none" v-if="!isAuthInfo" @tap="onGotUserInfo">
        <view class="default-pic">
          <image :src="`${staticPicDomain}images/icon/head04.png`"></image>
        </view>
        <view class="none-login">
          <button class="unlogin">{{ i18n.loginNow || '立即登录' }}</button>
        </view>
      </view>
      <!-- end 用户信息 -->
      <!-- 钱包数据 -->
      <view class="wallet-data" v-if="isAuthInfo">
        <view class="data-item" @tap="toMyWallet" v-if="showBlance">
          <view class="data-num">{{ (i18n.currencySymbol || '￥') + totalBalance }}</view>
          <view class="data-name">{{ i18n.balance || '余额' }}</view>
        </view>
        <view class="data-item" @tap="toMyCouponPage">
          <view class="data-num">{{ couponNum }}</view>
          <view class="data-name">{{ i18n.coupon || '优惠券' }}</view>
          <text :class="couponNum>99 ? 'num-badge-little' : 'num-badge'" v-if="showCouponBadge && couponNum > 0">{{ couponNum> 99 ? "99+" : couponNum + "" }}</text>
        </view>
        <view class="data-item" @tap="toMemberInteral">
          <view class="data-num">{{ score }}</view>
          <view class="data-name">{{ i18n.userIntegral || '积分' }}</view>
        </view>
      </view>
      <!-- 钱包数据 end -->
      <view class="list-cont">
        <!-- 订单状态 -->
        <view class="total-order">
          <view class="order-tit">
            <text style="font-weight:bold">{{ i18n.myOrder || '我的订单' }}</text>
            <view class="checkmore" @tap="toOrderListPage" data-sts="0">
              <text>全部订单</text>
              <text class="arrowhead"></text>
            </view>
          </view>
          <view class="procedure">
            <view class="items" @tap="toOrderListPage" data-sts="1">
              <n-i icon="user-dfk" width="60rpx" height="60rpx" />
              <text>{{ i18n.toBePaid || '待支付' }}</text>
              <text class="num-badge" v-if="orderAmount.unPay > 0">{{ orderAmount.unPay }}</text>
            </view>
            <view class="items" @tap="toOrderListPage" data-sts="2">
              <n-i icon="user-dfh" width="60rpx" height="60rpx" />
              <text>{{ i18n.toBeDelivered || '待发货' }}</text>
              <text class="num-badge" v-if="orderAmount.payed > 0">{{ orderAmount.payed }}</text>
            </view>
            <view class="items" @tap="toOrderListPage" data-sts="3">
              <n-i icon="user-dsh" width="60rpx" height="60rpx" />
              <text>{{ i18n.toBeReceived || '待收货' }}</text>
              <text class="num-badge" v-if="orderAmount.consignment > 0">{{ orderAmount.consignment }}</text>
            </view>
            <view class="items" @tap="toOrderListPage" data-sts="5">
              <text mode="widthFix" class="iconfont icon-toComment"></text>
              <text>{{ i18n.completed || '已完成' }}</text>
            </view>
            <view class="items" @tap="toAfterSalesPage">
              <n-i icon="user-sh" width="60rpx" height="60rpx" />
              <text>{{ i18n.refundAfterSale || '退款/售后' }}</text>
              <text class="num-badge after-sale-num-badge" v-if="orderAmount.refund > 0">{{ orderAmount.refund }}</text>
            </view>
          </view>
        </view>
        <!--end 订单状态 -->
        <view class="my-menu">
          <view class="menu-title">{{ i18n.commonTool || '常用功能' }}</view>
          <!-- #ifdef MP-WEIXIN -->
            <view class="memu-item" @tap="toDistCenter" v-if="canDistribution">
              <view class="i-name">
                <text mode="widthFix" class="iconfont icon-promotion"></text>
                <text>{{ i18n.distributioncenter || '分销中心' }}</text>
              </view>
            </view>
          <!-- #endif -->
          <view class="memu-item" @tap="toPlans">
            <view class="i-name">
              <n-i icon="user-mzrw" width="60rpx" height="60rpx" />
              <text>我的套餐</text>
            </view>
          </view>
          <view class="memu-item" @tap="toResetPwd">
            <view class="i-name">
              <!-- account-settings -->
              <n-i icon="user-mzrw" width="60rpx" height="60rpx" />
              <text>修改密码</text>
            </view>
          </view>
          <view class="memu-item" @tap="toBindPhone">
            <view class="i-name">
              <n-i icon="user-mzrw" width="60rpx" height="60rpx" />
              <text>绑定手机</text>
            </view>
          </view>
          <view class="memu-item" @tap="toMyDevice">
            <view class="i-name">
              <n-i icon="user-mzrw" width="60rpx" height="60rpx" />
              <text>我的设备</text>
            </view>
          </view>    
          <view class="memu-item" @tap="toActiveIspNetwork">
            <view class="i-name">
              <n-i icon="user-mzrw" width="60rpx" height="60rpx" />
              <text>宽带激活</text>
            </view>
          </view>
          <view class="memu-item" @tap="toBrowseHistory">
            <view class="i-name">
              <n-i icon="user-mzrw" width="60rpx" height="60rpx" />
              <text>上网历史</text>
            </view>
          </view>    
          <view class="memu-item" @tap="toExchangePlan">
            <view class="i-name">
              <n-i icon="user-mzrw" width="60rpx" height="60rpx" />
              <text>套餐兑换</text>
            </view>
          </view>
          <!-- <view class="memu-item" @tap="toUpdatePhone">
            <view class="i-name">
              <n-i icon="user-mzrw" width="60rpx" height="60rpx" />
              <text>换绑手机号</text>
            </view>
          </view> -->
        </view>
      </view>
    </view>
    <WenetAccountTip />
    <n-loading />
  </view>
</template>

<script>
// pages/user/user.js
var http = require("../../utils/http.js");
var util = require("../../utils/util.js");
import {requestServiceExpirationMessage} from '@/utils/wx-subscribe-message'
import Wechat from '../../utils/wechat.js';
import WenetAccountTip from '@/components/wenet-account-tip';
import NI from '@/components/n-i';
export default {
  data() {
    return {
      shopId: 0,
      orderAmount: {},
      sts: '',
      collectionCount: 0,
      //分销开关
      canDistribution: true,
      isAuthInfo: false,
      //店铺状态：0 未审核 1已通过 -1未通过 null 未开店
      shopAuditStatus: null,
      //是否已经设置过支付密码
      isSetPassword: false,
      userLevelInfo: {},
      // 店铺状态
      shopStatus: '',
      // 消息数量
      messageCount: 0,
      // 优惠券数量
      couponNum: 0,
      // 用户积分
      score: 0,
      // 用户余额
      totalBalance: 0,
      notifyNum: 0,
      // 应用类型
      appType: uni.getStorageSync('appType'),
      showCouponBadge: false
    };
  },

  props: {},

  components: {
    WenetAccountTip,
    NI
  },

  computed: {
    i18n() {
      return this.$t('index');
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    showVip() {
      return this.$store.state.shopVipSwitch;
    },
    shopCustomerSwitch() {
      return this.$store.state.shopCustomerSwitch;
    },
    isVip() {
        const userInfo = this.$store.state.userInfo
        if (userInfo?.levelType > 0 || userInfo?.level > 1 ) {
          return true
        }
        return false
      },
    showBlance() {
      const ou = this.userInfo.ou
      // 济南大学不展示余额菜单
      if (ou == 10427) {
        return false
      }
      return true
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.isAuthInfo = Boolean(uni.getStorageSync('token'));
    // http.getCartCount();
    // #ifndef H5
    var logData = uni.getStorageSync('flowAnalysisLogDto');
    uni.setStorageSync('step', uni.getStorageSync('step') / 1 + 1);
    if (logData && logData.pageId != 12) {
      logData.pageId = 12;
      logData.step = uni.getStorageSync('step');
      uni.setStorageSync('flowAnalysisLogDto', logData);
      http.saveLog(logData, 1);
    }
    // #endif
    // 排除影响，真实登陆后再请求数据
    if (uni.getStorageSync('token')) {
      //加载订单数据
      this.loadOrderCountFun();
      // 获取会员信息
      this.getUserLevelInfo();
      // 查询分销是否开启
      this.getDistInfo();
      // 查询用户信息
      this.queryUserInfo();
      //钱包数据
      this.queryUserData();
    } else {
      let emptyObj = {};
      this.setData({
        orderAmount: emptyObj,
        couponNum: 0,  //优惠券数量
        score: 0, //用户积分
        totalBalance: 0, //用户余额
        notifyNum: 0,  // 消息提醒
        messageCount: 0,
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () { },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    uni.setNavigationBarTitle({
      title: this.i18n.personalCenter
    });
    if (uni.getStorageSync('token')) {
      //加载订单数据
      this.loadOrderCountFun();
    } else {
      this.setData({
        orderAmount: {},
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () { 
    if (uni.getStorageSync('token')) {
      //加载订单数据
      this.loadOrderCountFun();
      // 获取会员信息
      this.getUserLevelInfo();
      // 查询分销是否开启
      this.getDistInfo();
      // 查询用户信息
      this.queryUserInfo();
      //钱包数据
      this.queryUserData();
      uni.stopPullDownRefresh();
    } else {
      let emptyObj = {};
      this.setData({
        orderAmount: emptyObj,
        couponNum: 0,  //优惠券数量
        score: 0, //用户积分
        totalBalance: 0, //用户余额
        notifyNum: 0,  // 消息提醒
        messageCount: 0,
      });
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () { },
  methods: {
    toExchangePlan() {
      uni.navigateTo({
        url: '/packageWenet/pages/exchange-plan/exchange-plan',
      });
    },
    toPlans() {
      uni.navigateTo({
        url: '/packageWenet/pages/plans/plans'
      });
    },
    toResetPwd() {
      uni.navigateTo({
        url: '/packageWenet/pages/resetPwd/resetPwd'
      });
    },
    toBindPhone() {
      uni.navigateTo({
        url: '/packageWenet/pages/bindPhone/bindPhone',
      });
    },
    toMyDevice() {
      uni.navigateTo({
        url: '/packageWenet/pages/myDevice/myDevice',
      });
    },
    toUpdatePhone() {
      uni.navigateTo({
        url: '/packageWenet/pages/bindPhone/updatePhone',
      });
    },
    toActiveIspNetwork() {
      uni.navigateTo({
        url: '/packageWenet/pages/active-isp-network/active-isp-network',
      });
    },
    toBrowseHistory() {
      uni.navigateTo({
        url: '/packageWenet/pages/browseHistory/browseHistory',
      });
    },

    /**
     * 去条款页
     */
    toTermsOfService(key) {
      uni.navigateTo({
        url: "/packageUser/pages/termsPage/termsPage?sts=" + key
      });
    },
    /**
     * 切换语言
     */
    changeLangs() {
      if (uni.getStorageSync("lang").indexOf('zh') != -1) {
        this._i18n.locale = 'en';
        uni.setStorageSync('lang', 'en');
      } else {
        this._i18n.locale = 'zh_CN';
        uni.setStorageSync('lang', 'zh_CN');
      }
      uni.reLaunch({
        url: '/pages/user/user',
      });
    },
    /**
     * 钱包数据
     */
    queryUserData: function () {
      var params = {
        url: "/p/user/getUserInfo",
        method: "GET",
        dontTrunLogin: true,
        data: {},
        callBack: res => {
          if (uni.getStorageSync('token')) {
            this.setData({
              couponNum: res.couponNum,  //优惠券数量
              score: res.score, //用户积分
              totalBalance: res.totalBalance, //用户余额
              notifyNum: res.notifyNum,  // 消息提醒
            });
          }
        }
      };
      http.request(params);
    },

    // 加载订单数据
    loadOrderCountFun() {
      var params = {
        url: "/p/user/centerInfo",
        method: "GET",
        dontTrunLogin: true,
        data: {},
        callBack: res => {
          if (uni.getStorageSync('token')) {
            this.setData({
              // 店铺审核状态shopAuditStatus：-1未通过 0 未审核 1已通过 null未开店
              shopAuditStatus: res.shopAuditStatus,
              isSetPassword: res.isSetPassword,
              orderAmount: res.orderCountData,
              shopId: res.shopId,
              // 店铺状态shopStatus：-1未开通 0停业中 1营业中 2平台下线 3平台下线待审核
              shopStatus: res.shopStatus
            },()=>{
              this.showMineTabBarBadge();
            });
          }
        }
      };
      http.request(params);
    },

    // 我的钱包
    toMyWallet() {
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageUser/pages/myWallet/myWallet'
        });
      });
    },
    goset: function () {
      uni.navigateTo({
        url: '/packageUser/pages/personalInformation/personalInformation'
      });
    },
    /**
     * 扫一扫
     */
    sacnCode: util.debounce(function () {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        // #ifdef H5
        if (!Wechat.isWechat()) {
          uni.showToast({
            title: this.i18n.openInWeChatBrowser,
            icon: "none"
          });
          return;
        }
        Wechat.scanQRCode((codeStr) => {
          var code = '';
          // 处理扫码结果
          if (codeStr.indexOf(',') > -1) {
            code = codeStr.split(',')[1];
          } else {
            code = codeStr;
          }
          if (!/^\d+$/.test(code)) {
            uni.showModal({
              title: this.i18n.tips,
              content: this.i18n.stationCodeTips,
              confirmText: this.i18n.confirm,
              showCancel: false
            });
          } else {
            uni.navigateTo({
              url: '/packageShop/pages/stationOrderList/stationOrderList?stationId=' + code
            });
          }
        }, () => {
          uni.showToast({
            title: this.i18n.failedInvokeScan,
            icon: 'none'
          });
        });
        // #endif

        // #ifdef APP-PLUS || MP-WEIXIN
        uni.scanCode({
          success: (e) => {
            if (!/^\d+$/.test(e.result)) {
              uni.showModal({
                title: this.i18n.tips,
                content: this.i18n.stationCodeTips,
                confirmText: this.i18n.confirm,
                showCancel: false
              });
            } else {
              uni.navigateTo({
                url: '/packageShop/pages/stationOrderList/stationOrderList?stationId=' + e.result
              });
            }
          },
          fail: (err) => {
            console.log(err);
          }
        });
        // #endif
      });
    }, 1000),


    /**
     * 获取用户信息
     */
    queryUserInfo: function () {
      this.$store.commit('refreshUserInfo', {
        forceRefresh: true
      });
    },


    /**
     * 查询分销相关信息
     */
    getDistInfo() {
      //查询分销开关是否开启
      http.request({
        url: "/p/distribution/distributionBasicSet/canDistribution",
        method: "GET",
        // data: {
        //   shopId: 1,
        // },
        dontTrunLogin: true,
        callBack: res => {
          this.setData({
            canDistribution: res == 1
          });
        }
      });
    },

    /**
     * 跳转分销员中心
     */
    toDistCenter: function () {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        http.request({
          url: "/p/distribution/user/distributionUserInfo",
          method: "GET",
          callBack: res => {
            if (res && res.state == 1) {
              uni.setStorageSync("distCardNo", res.cardNo);
              uni.setStorageSync('distInfo', res);
              uni.navigateTo({
                url: '/packageDistribution/pages/dis-center/dis-center'
              });
            } else if (res && res.state == 0) {
              uni.showToast({
                title: this.i18n.applicationReview,
                icon: "none"
              });
            } else if (res && res.state == 3) {
              uni.showModal({
                title: '',
                content: this.i18n.applicationFailed,
                cancelText: this.i18n.cancel,
                confirmText: this.i18n.confirm,
                confirmColor: "#eb2444",

                success(res2) {
                  if (res2.confirm) {
                    if (res.recruitState) {
                      uni.navigateTo({
                        url: '/packageDistribution/pages/applyDist/applyDist'
                      });
                    } else {
                      uni.navigateTo({
                        url: '/packageDistribution/pages/applyDistCon/applyDistCon'
                      });
                    }
                  } else if (res2.cancel) {
                    console.log('用户点击取消');
                  }
                }

              });
            } else if (res && res.state == -1) {
              uni.showModal({
                title: '',
                content: this.i18n.distributorBanned,
                confirmColor: "#eb2444",
                cancelText: this.i18n.cancel,
                confirmText: this.i18n.confirm,
                showCancel: false,
                success(res2) {
                  if (res2.confirm) {
                    console.log('用户点击确认');
                  } else if (res2.cancel) {
                    console.log('用户点击取消');
                  }
                }

              });
            } else if (res && res.state == 2) {
              uni.showModal({
                title: '',
                content: this.i18n.distributorCleared,
                confirmColor: "#eb2444",
                cancelText: this.i18n.cancel,
                confirmText: this.i18n.confirm,
                showCancel: false,
                success(res2) {
                }

              });
            } else {
              if (res.recruitState) {
                uni.navigateTo({
                  url: '/packageDistribution/pages/applyDist/applyDist'
                });
              } else {
                uni.navigateTo({
                  url: '/packageDistribution/pages/applyDistCon/applyDistCon'
                });
              }
            }
          }
        });
      });
    },
    toAfterSalesPage: function () {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageShop/pages/afterSales/afterSales'
        });
      });
    },
    //积分中心
    toPointsCenter: function () {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageMemberIntegral/pages/memberCenter/memberCenter'
        });
      });
    },
    toMyCouponPage: function () {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageActivities/pages/myCoupon/myCoupon'
        });
      });
    },
    toAddressList: function () {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageUser/pages/delivery-address/delivery-address'
        });
      });
    },
    toOrderListPage: function (e) {
      var sts = e.currentTarget.dataset.sts;
      util.tapLog(3);
      util.checkAuthInfo(() => {
        if (sts === '1') {
          requestServiceExpirationMessage({
            complete: () => {
              this.$Router.push({
                path: '/pages/orderList/orderList',
                query: {
                  sts: sts
                }
              });
            }
          })
        } else {
          this.$Router.push({
            path: '/pages/orderList/orderList',
            query: {
              sts: sts
            }
          });
        }
      });
    },

    /**
     * 查询所有的收藏量
     */
    showCollectionCount: function () {
      var ths = this;
      var params = {
        url: "/p/user/collection/count",
        method: "GET",
        data: {},
        callBack: function (res) {
          ths.setData({
            collectionCount: res
          });
        }
      };
      http.request(params);
    },

    /**
     * 我的收藏跳转
     */
    myCollectionHandle: function () {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        var url = '/pages/prod-classify/prod-classify?sts=5';
        var id = 0;
        var title = this.i18n.myCollection;
        if (id) {
          url += "&tagid=" + id + "&title=" + title;
        }
        uni.navigateTo({
          url: url
        });
      });
    },

    /**
     * 跳转修改密码
     */
    setPassword: function () {
      util.tapLog(3);
      uni.navigateTo({
        url: '/pages/accountLogin/accountLogin?isForgetPassword=true' + '&isPersonalCenter=true'
      });
    },

    /**
     * 跳转到修改用户头像昵称资料
     */
    toPersonalInformation: function () {
      util.tapLog(3);
      uni.navigateTo({
        url: '/packageUser/pages/personalInformation/personalInformation'
      });
    },


    /**
     * 获取用户信息
     */
    onGotUserInfo: function (e) {
      util.tapLog(3);
      util.checkAuthInfo();
    },

    /**
     * 跳转到申请开店
     */
    applyAShop: function () {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        if (this.shopAuditStatus == 0) {
          uni.showToast({
            title: this.i18n.shopOpeningReview,
            icon: "none"
          });
        } else {
          uni.navigateTo({
            url: '/packageShop/pages/openAShop/openAShop?shopAuditStatus=' + this.shopAuditStatus
          });
        }
      });

    },

    /**
     * 跳转到我的店铺
     */
    enterMyShop: function (e) {
      if (this.shopStatus == 0 || this.shopStatus == 2 || this.shopStatus == 3) {
        uni.showModal({
          title: this.i18n.tips,
          content: this.i18n.storeStatusTips,
          showCancel: false,//是否显示取消按钮
          cancelText: this.i18n.cancel,
          confirmText: this.i18n.confirm
        });
      } else {
        util.checkAuthInfo(() => {
          uni.navigateTo({
            url: '/packageShop/pages/shopPage/shopPage?shopId=' + e.currentTarget.dataset.shopid
          });
        });
      }
    },
    /**
     * 跳转到和客服聊天的界面
     */
    gotoChat() {
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageUser/pages/chat/chatIm?shopid=0'
        });
      });
    },
    /**
     * 跳转到和商家客服聊天的界面
     */
    gotoMessageBox() {
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageUser/pages/chat/chat'
        });
      });
    },
    /**
     * 更新用户信息
     */
    updateInfo() {
      util.tapLog(3);
      http.updateUserInfo();
    },
    /**
			 * 获取当前会员信息
			 */
			getUserLevelInfo() {
				var params = {
					url: '/p/score/scoreLevel/page',
					method: 'GET',
					data: {
						levelType: 0
					},
					callBack: res => {
            this.userLevelInfo = res
					}
				};
				http.request(params);
			},

    /**
     * 跳转购买会员
     */
    toBuyMember() {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageMemberIntegral/pages/buyVip/buyVip'
        });
      });
    },

    /**
     * 跳转积分中心
     */
    toMemberInteral() {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageMemberIntegral/pages/integralDetail/integralDetail'
        });
      });
    },
    /**
     *  获取各个状态下优惠券数量
     */
     couponCount() {
      const that = this;
      return new Promise((resolve) => {
        http.request({
          url: "/p/myCoupon/getMyCouponsStatusCount",
          method: "GET",
          data: {},
          callBack: function (res) {
            const unUse = res?.unUseCount || 0
            resolve(unUse);
            that.setData({
              couponNum: unUse
              })
          },                            
          errCallBack: (err) => {
            reject(err);
          },
        });
      });
    },
    async showMineTabBarBadge(){
      const couponCount = await this.couponCount();
      let showCouponCount = 0;
      let sum = 0;
      if(uni.getStorageSync("unUseCouponCount") && uni.getStorageSync("unUseCouponCount")>=couponCount){
        showCouponCount = 0
        this.setData({
          showCouponBadge:false
        })
      }else{
        showCouponCount = couponCount
        this.setData({
          showCouponBadge:true
        })
      }
      sum = this.orderAmount.unPay + showCouponCount;
      if(sum>0){
        uni.setTabBarBadge({
    			  index: 2,
    			  text: sum > 99 ? "99+" : sum + ""
    		  })
      }else{
        uni.removeTabBarBadge({index:2})
      }
    }
  }
};
</script>
<style>
@import "./user.css";

</style>

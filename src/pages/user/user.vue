<script>
import http from '@/utils/http'
import util from '@/utils/util'
import {requestServiceExpirationMessage} from '@/utils/wx-subscribe-message'
import WenetAccountTip from '@/components/wenet-account-tip';
import NI from '@/components/n-i';
import config from '@/utils/config'
export default {
  data() {
    return {
      shopId: 0,
      orderAmount: {},
      sts: '',
      collectionCount: 0,
      //分销开关
      canDistribution: true,
      isAuthInfo: false,
      //店铺状态：0 未审核 1已通过 -1未通过 null 未开店
      shopAuditStatus: null,
      //是否已经设置过支付密码
      isSetPassword: false,
      userLevelInfo: {},
      // 店铺状态
      shopStatus: '',
      // 消息数量
      messageCount: 0,
      // 优惠券数量
      couponNum: 0,
      // 用户积分
      score: 0,
      // 用户余额
      totalBalance: 0,
      notifyNum: 0,
      // 应用类型
      appType: uni.getStorageSync('appType'),
      showCouponBadge: false,
      mobileVisible: false,
      functionList: [
        { icon: 'my-icon001', label: '我的服务', key: 'plans', basWechatSelfMenuKey: 'MY_PLANS' },
        { icon: 'my-icon002', label: '设备管理', key: 'myDevice', basWechatSelfMenuKey: 'MY_DEVICES' },
        { icon: 'my-icon003', label: '兑换套餐', key: 'exchange-plan', basWechatSelfMenuKey: 'CODE_ACTIVE' },
        { icon: 'my-icon004', label: '激活宽带', key: 'active-isp-network', basWechatSelfMenuKey: 'ISP_NETWORK' },
        { icon: 'my-icon005', label: '上网密码', key: 'resetPwd', basWechatSelfMenuKey: 'CHANGE_PASSWORD' },
        { icon: 'my-icon006', label: '上网历史', key: 'browseHistory', basWechatSelfMenuKey: 'BROWSING_HISTORY' },
        { icon: 'my-icon008', label: '学习流量', key: 'studyFlow', basWechatSelfMenuKey: 'FREE_PRODS' },
      ],
      basWechatSelfMenuKeys: [],
    };
  },

  props: {},

  components: {
    WenetAccountTip,
    NI
  },

  computed: {
    i18n() {
      return this.$t('index');
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    ou() {
      return this.userInfo?.ou
    },
    showVip() {
      return this.$store.state.shopVipSwitch;
    },
    shopCustomerSwitch() {
      return this.$store.state.shopCustomerSwitch;
    },
    isVip() {
        const userInfo = this.$store.state.userInfo
        if (userInfo?.levelType > 0 || userInfo?.level > 1 ) {
          return true
        }
        return false
      },
    showBlance() {
      const ou = this.userInfo?.ou
      // 济南大学不展示余额菜单
      if (ou == 10427) {
        return false
      }
      return true
    },
    simpleWenetAccountInfo() {
      return this.$store.state.simpleWenetAccountInfo
    },
    mobileValue() {
      if (!this.simpleWenetAccountInfo) {
        return ''
      }
      if (this.mobileVisible) {
        return this.simpleWenetAccountInfo.basMobile
      }
      // 隐藏中间 4 位
      return this.simpleWenetAccountInfo.basMobile.slice(0, 3) + '****' + this.simpleWenetAccountInfo.basMobile.slice(-4)
    },
    filteredFunctionList() {
      if (!this.isAuthInfo) {
        return this.functionList
      }
      return this.functionList.filter(item => this.basWechatSelfMenuKeys.includes(item.basWechatSelfMenuKey))
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.isAuthInfo = Boolean(uni.getStorageSync('token'));
    // #ifndef H5
    var logData = uni.getStorageSync('flowAnalysisLogDto');
    uni.setStorageSync('step', uni.getStorageSync('step') / 1 + 1);
    if (logData && logData.pageId != 12) {
      logData.pageId = 12;
      logData.step = uni.getStorageSync('step');
      uni.setStorageSync('flowAnalysisLogDto', logData);
      http.saveLog(logData, 1);
    }
    // #endif
    // 排除影响，真实登陆后再请求数据
    if (uni.getStorageSync('token')) {
      //加载订单数据
      this.loadOrderCountFun();
      // 获取会员信息
      this.getUserLevelInfo();
      // 查询分销是否开启
      this.getDistInfo();
      // 查询用户信息
      this.queryUserInfo();
      //钱包数据
      this.queryUserData();
    } else {
      let emptyObj = {};
      this.setData({
        orderAmount: emptyObj,
        couponNum: 0,  //优惠券数量
        score: 0, //用户积分
        totalBalance: 0, //用户余额
        notifyNum: 0,  // 消息提醒
        messageCount: 0,
      });
    }
    this.getMenuConfig()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () { },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    uni.setNavigationBarTitle({
      title: this.i18n.personalCenter
    });
    if (uni.getStorageSync('token')) {
      //加载订单数据
      this.loadOrderCountFun();
      this.getMenuConfig(this.ou)
    } else {
      this.setData({
        orderAmount: {},
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () { 
    if (uni.getStorageSync('token')) {
      //加载订单数据
      this.loadOrderCountFun();
      // 获取会员信息
      this.getUserLevelInfo();
      // 查询分销是否开启
      this.getDistInfo();
      // 查询用户信息
      this.queryUserInfo();
      //钱包数据
      this.queryUserData();
      uni.stopPullDownRefresh();
    } else {
      let emptyObj = {};
      this.setData({
        orderAmount: emptyObj,
        couponNum: 0,  //优惠券数量
        score: 0, //用户积分
        totalBalance: 0, //用户余额
        notifyNum: 0,  // 消息提醒
        messageCount: 0,
      });
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () { },
  watch: {
    ou(newVal) {
      this.getMenuConfig(newVal)
    }
  },
  methods: {
    async getMenuConfigIndex() {
      return new Promise((resolve) => {
        http.request({
          domain: config.wechatSelfStaticUrl,
          url: '/homepage/config-index.json',
          method: 'GET',
          callBack: res => {
            resolve(res)
          },
          errCallBack: err => {
            resolve({})
          }
        })
      })
    },
    async getMenuConfig(ou) {
      if (!ou) {
        return
      }
      const configIndex = await this.getMenuConfigIndex()
      const targetConfig = configIndex[ou]
      if (!targetConfig) {
        return
      }
      const realConfigUrl = targetConfig?.configUrl || ''
      if (!realConfigUrl) {
        return
      }
      http.request({
        domain: config.wechatSelfStaticUrl,
        url: realConfigUrl,
        method: 'GET',
        callBack: res => {
          const menuGroups = res?.menuGroups || []
          const basWechatSelfMenuKeys = [];
          menuGroups.forEach(group => {
            group.menus.forEach(item => {
              basWechatSelfMenuKeys.push(item.systemMenuKey)
            })
          })
          this.basWechatSelfMenuKeys = basWechatSelfMenuKeys
        }
      })
    },
    /**
     * 钱包数据
     */
    queryUserData: function () {
      var params = {
        url: "/p/user/getUserInfo",
        method: "GET",
        dontTrunLogin: true,
        data: {},
        callBack: res => {
          if (uni.getStorageSync('token')) {
            this.setData({
              couponNum: res.couponNum,  //优惠券数量
              score: res.score, //用户积分
              totalBalance: res.totalBalance, //用户余额
              notifyNum: res.notifyNum,  // 消息提醒
            });
          }
        }
      };
      http.request(params);
    },

    // 加载订单数据
    loadOrderCountFun() {
      var params = {
        url: "/p/user/centerInfo",
        method: "GET",
        dontTrunLogin: true,
        data: {},
        callBack: res => {
          if (uni.getStorageSync('token')) {
            this.setData({
              // 店铺审核状态shopAuditStatus：-1未通过 0 未审核 1已通过 null未开店
              shopAuditStatus: res.shopAuditStatus,
              isSetPassword: res.isSetPassword,
              orderAmount: res.orderCountData,
              shopId: res.shopId,
              // 店铺状态shopStatus：-1未开通 0停业中 1营业中 2平台下线 3平台下线待审核
              shopStatus: res.shopStatus
            },()=>{
              this.showMineTabBarBadge();
            });
          }
        }
      };
      http.request(params);
    },

    // 我的钱包
    toMyWallet() {
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageUser/pages/myWallet/myWallet'
        });
      });
    },
    /**
     * 获取用户信息
     */
    queryUserInfo: function () {
      this.$store.commit('refreshUserInfo', {
        forceRefresh: true
      });
    },


    /**
     * 查询分销相关信息
     */
    getDistInfo() {
      //查询分销开关是否开启
      http.request({
        url: "/p/distribution/distributionBasicSet/canDistribution",
        method: "GET",
        dontTrunLogin: true,
        callBack: res => {
          this.setData({
            canDistribution: res == 1
          });
        }
      });
    },

    toMyCouponPage: function () {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageActivities/pages/myCoupon/myCoupon'
        });
      });
    },
    toOrderListPage: function (e) {
      var sts = e.currentTarget.dataset.sts;
      util.tapLog(3);
      util.checkAuthInfo(() => {
        if (sts === '1') {
          requestServiceExpirationMessage({
            complete: () => {
              this.$Router.push({
                path: '/pages/orderList/orderList',
                query: {
                  sts: sts
                }
              });
            }
          })
        } else {
          this.$Router.push({
            path: '/pages/orderList/orderList',
            query: {
              sts: sts
            }
          });
        }
      });
    },

    /**
     * 跳转到修改用户头像昵称资料
     */
    toPersonalInformation: function () {
      util.tapLog(3);
      uni.navigateTo({
        url: '/packageUser/pages/personalInformation/personalInformation'
      });
    },


    /**
     * 获取用户信息
     */
    onGotUserInfo: function (e) {
      util.tapLog(3);
      util.checkAuthInfo();
    },

    /**
     * 更新用户信息
     */
    /**
     * 获取当前会员信息
     */
    getUserLevelInfo() {
      var params = {
        url: '/p/score/scoreLevel/page',
        method: 'GET',
        data: {
          levelType: 0
        },
        callBack: res => {
          this.userLevelInfo = res
        }
      };
      http.request(params);
    },

    /**
     * 跳转购买会员
     */
    toBuyMember() {
      util.tapLog(3);
      util.checkAuthInfo(() => {
        uni.navigateTo({
          url: '/packageMemberIntegral/pages/buyVip/buyVip'
        });
      });
    },
    /**
     *  获取各个状态下优惠券数量
     */
     couponCount() {
      const that = this;
      return new Promise((resolve) => {
        http.request({
          url: "/p/myCoupon/getMyCouponsStatusCount",
          method: "GET",
          data: {},
          callBack: function (res) {
            const unUse = res?.unUseCount || 0
            resolve(unUse);
            that.setData({
              couponNum: unUse
              })
          },                            
          errCallBack: (err) => {
            reject(err);
          },
        });
      });
    },
    async showMineTabBarBadge(){
      const couponCount = await this.couponCount();
      let showCouponCount = 0;
      let sum = 0;
      if(uni.getStorageSync("unUseCouponCount") && uni.getStorageSync("unUseCouponCount")>=couponCount){
        showCouponCount = 0
        this.setData({
          showCouponBadge:false
        })
      }else{
        showCouponCount = couponCount
        this.setData({
          showCouponBadge:true
        })
      }
      sum = this.orderAmount.unPay + showCouponCount;
      if(sum>0){
        uni.setTabBarBadge({
    			  index: 2,
    			  text: sum > 99 ? "99+" : sum + ""
    		  })
      }else{
        uni.removeTabBarBadge({index:2})
      }
    },

    async toFunction(key) {
      if(!this.isAuthInfo) {
        uni.navigateTo({
          url: '/pages/login/login'
        });
        return
      }
      switch(key) {
        case 'studyFlow': {
          uni.navigateTo({
            url: '/packageShop/pages/free-prods/free-prods'
          });
          break
        }
        case 'networkRepair': {
          break
        }
        default: {
          uni.navigateTo({
            url: `/packageWenet/pages/${key}/${key}`
          });
          break
        }
      }
    }
  }
};
</script>

<template>
  <view class="container">
    <!-- 用户信息 begin -->
    <view class="user-info" v-if="!!userInfo">
      <view class="user-info-left" @click="toPersonalInformation">
        <image class="user-info-left-avatar" :src="userInfo.pic ? userInfo.pic : `${staticPicDomain}images/icon/head04.png`"></image>
      </view>
      <view class="user-info-right">
        <view class="user-info-right-line1 text-primary">
          <text class="text-size-20 font-weight-600">
            Hi
          </text>
          <text class="text-size-14 font-weight-600">{{ userInfo.nickName }}~</text>
          <view class="user-info-vip" v-if="showVip && isVip">
            <image class="user-info-vip-level-img" width="32rpx" height="32rpx" v-if="userLevelInfo && userLevelInfo.userLevel" :src="userLevelInfo.userLevel.img" />
            <text class="user-info-vip-level-name text-size-14 font-weight-400" v-if="userLevelInfo.userLevel">{{userLevelInfo.userLevel.level==0 ? '普通会员' : userLevelInfo.levelName}}</text>
          </view>
        </view>
        <view class="user-info-right-line2 text-primary text-size-14 font-weight-400" v-if="!!simpleWenetAccountInfo">
          <view class="user-info-right-line2-item">
            <text>账号</text>
            <text class="text-brand font-weight-500">{{ simpleWenetAccountInfo.basUsername }}</text>
          </view>
          <view class="user-info-right-line2-item">
            <text>电话</text>
            <text class="text-brand font-weight-500 user-mobile">{{ mobileValue }}</text>
            <n-i :icon="mobileVisible ? 'eyes-show' : 'eyes-hide'" width="32rpx" height="32rpx" @click="mobileVisible = !mobileVisible" />
          </view>
        </view>
      </view>
      <view class="user-info-arrow" @click="toPersonalInformation">
        <n-i icon="right" width="40rpx" height="40rpx" />
      </view>
    </view>

    <view class="user-info" v-else @click="onGotUserInfo">
      <view class="user-info-left">
        <image class="user-info-left-avatar" :src="`${staticPicDomain}images/icon/head04.png`"></image>
      </view>
      <view class="user-info-right">
        <text>立即登录</text>
      </view>
    </view>
    <!-- 用户信息 end -->

    <!-- 钱包信息 begin -->
    <view class="wallet-info text-size-14">
      <view class="wallet-info-item" @click="toOrderListPage" data-sts="0">
        <view class="wallet-info-item-content">
          <view class="wallet-info-item-value">{{ orderAmount.allCount || 0 }}</view>
          <view class="wallet-info-item-label">我的订单</view>
        </view>
      </view>
      <view class="wallet-info-item" @click="toOrderListPage" data-sts="1">
        <view class="wallet-info-item-content">
          <view class="wallet-info-item-value">{{ orderAmount.unPay || 0 }}</view>
          <view class="wallet-info-item-label">待支付</view>
          <view class="wallet-info-item-badge" v-if="orderAmount.unPay > 0"></view>
        </view>
      </view>
      <view class="wallet-info-item" @click="toMyWallet" v-if="showBlance">
        <view class="wallet-info-item-content">
          <view class="wallet-info-item-value">¥{{ totalBalance || 0 }}</view>
          <view class="wallet-info-item-label">余额</view>
        </view>
      </view>
      <view class="wallet-info-item" @click="toMyCouponPage">
        <view class="wallet-info-item-content">
          <view class="wallet-info-item-value">{{ couponNum || 0 }}</view>
          <view class="wallet-info-item-label">优惠券</view>
          <view class="wallet-info-item-coupon-badge" v-if="showCouponBadge && couponNum > 0">
            待使用
          </view>
        </view>
      </view>
    </view>
    <!-- 钱包信息 end -->

    <!-- 功能列表 begin -->
    <view class="function-list-title text-size-16 text-primary font-weight-600">自助服务</view>
    <view class="function-list">
      <view class="function-list-item-wrap" v-for="item in filteredFunctionList" :key="item.key" @click="toFunction(item.key)">
        <view class="function-list-item">
          <view class="function-list-item-icon">
            <n-i :icon="item.icon" width="68rpx" height="68rpx" />
          </view>
          <view class="function-list-item-label text-primary text-size-14 font-weight-400">{{ item.label }}</view>
        </view>
      </view>
    </view>
    <!-- 功能列表 end -->
  </view>

</template>

<style lang="scss" scoped>
page {
  height: 100%;
}
.container {
  padding: 48rpx 20rpx;
  background: #fff;
}

.user-info {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  column-gap: 22rpx;
  align-items: center;
  margin-bottom: 32rpx;

}

.user-info-left {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  overflow: hidden;
}

.user-info-left-avatar {
  width: 100%;
  height: 100%;
}

.user-info-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  row-gap: 15rpx;
}

.user-info-right-line1 {
  display: flex;
  align-items: center;
  column-gap: 20rpx;
  
}

.user-info-vip-level-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.user-info-right-line2 {
  display: flex;
  align-items: center;
  column-gap: 32rpx;
}

.user-info-student-id {
  display: flex;
}

.user-info-right-line2-item {
  display: flex;
  column-gap: 16rpx;
}

.user-info-arrow {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-mobile {
  width: 180rpx;
}

.wallet-info {
  width: 100%;
  height: 144rpx;
  background: #E8EDFF;
  border-radius: 16rpx;
  margin-bottom: 48rpx;
  padding: 0 24rpx;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  justify-content: space-between;
}

.wallet-info-item {
  flex: 1;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wallet-info-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: fit-content;
  position: relative;
  row-gap: 8rpx;
}

.wallet-info-item-badge {
  position: absolute;
  top: 0;
  right: 0;
  width: 24rpx;
  height: 24rpx;
  background: $wenet-color-error;
  border-radius: 50%;
}

.wallet-info-item-coupon-badge {
  position: absolute;
  top: -16rpx;
  right: -56rpx;
  font-size: 24rpx;
  font-weight: 400;
  border: 2rpx solid #fff;
  color: #fff;
  background: $wenet-color-error;
  border-radius: 18rpx;
  padding: 1rpx 4rpx;
  box-sizing: border-box;
}

.function-list-title {
  margin-bottom: 24rpx;
}

.function-list {
  display: flex;
  flex-wrap: wrap;
  row-gap: 36rpx;
  background: #E8EDFF;
  padding: 36rpx 24rpx;
  border-radius: 16rpx;
}

.function-list-item-wrap {
  width: 25%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.function-list-item {
  width: fit-content;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  row-gap: 10rpx;
}
</style>
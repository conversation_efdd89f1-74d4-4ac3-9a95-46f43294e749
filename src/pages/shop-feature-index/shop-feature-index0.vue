<template>
	<view class="shop-feature" >
		<feature ref="featureIndex" @pageLoaded="pageLoaded" :pageLoad="pageLoad" :pageId="targetRenovationId" :shopId="targetShopId" :pageScorllTop="pageScorllTop"></feature>
    <contact-button/>
	</view>
</template>

<script>
	const http = require('@/utils/http')
	import feature from '@/components/feature/index/index'
  import ContactButton from '@/components/contact-button'
	export default {
		data() {
			return {
				pageLoad: false,
				pageScorllTop: 0, // 页面滚动距离
        queryParams: {
          shopId: '',
          renovationId: ''
        }
			}
		},
    // 允许通过props传递shopId和renovationId，即支持店铺首页出现在tabbar上时url无参数情况， 还兼容之前的的url参数获取
    props: {
      shopId: {
        type: [String, Number],
        default: ''
      },
      renovationId: {
        type: [String, Number],
        default: ''
      }
    },
		components: {
			feature,
      ContactButton
		},
		mounted() {
			this.getShopInfo()
		},
		onPageScroll: function(e) {
			this.pageScorllTop = e.scrollTop
		},
    computed: {
      targetShopId() {
        return this.queryParams.shopId || this.shopId
      },
      targetRenovationId() {
        return this.queryParams.renovationId || this.renovationId
      }
    },
		methods: {
      parentOnShow() {

      },
      parentOnLoad(options) {
        this.queryParams.shopId = options.shopId
        this.queryParams.renovationId = options.renovationId
      },
      parentPullDownRefresh: function() {
        setTimeout(()=> {
          this.$nextTick(() => {
            this.$refs.featureIndex.getPageInfoById()
          })
          uni.stopPullDownRefresh(); //停止下拉刷新
        }, 100);
      },

			/**
			 * 获取店铺信息
			 */
			getShopInfo() {
				var params = {
					url: "/shop/headInfo",
					method: "GET",
					data: {
						shopId: this.shopId
					},
					callBack: (res) => {
						uni.setStorageSync("shopInfo", res);
						// 进入页面时判断店铺状态
						this.handleShopStatus(res)
					}
				};
				http.request(params);
			},

			/**
			 * 店铺状态处理
			 */
			handleShopStatus(res) {
				const shopStatus = res.shopStatus
				// shopStatus店铺状态(-1:未开通 0: 停业中 1:营业中 2:平台下线 3:平台下线待审核)，可修改
				if (shopStatus === -1) {
					this.handleTipsModal(this.i18n.storeStatusTips2)
					return
				}
				if (shopStatus === 0) {
					const contractStartTime = new Date(data.contractStartTime).getTime()
					const contractEndTime = new Date(data.contractEndTime).getTime()
					const today = new Date().getTime()
					// 1、店铺状态为0(停业中)时，当店铺未到签约开始时间，用户进入店铺提示：商家尚未营业
					if (today < contractStartTime) {
						this.handleTipsModal(this.i18n.storeStatusTips4)
						return
					}
					// 2、店铺状态为0(停业中)时，当店铺超过签约有效期，用户进入店铺提示：商家已暂停未营业
					if (today > contractEndTime) {
						this.handleTipsModal(this.i18n.storeStatusTips5)
						return
					}
				}
				if (shopStatus === 2 || shopStatus === 3) {
					this.handleTipsModal(this.i18n.storeStatusTips3)
					return
				}
			},
			handleTipsModal(tips) {
				uni.showModal({
					title: this.i18n.tips,
					content: tips,
					showCancel: false,//是否显示取消按钮
					cancelText: this.i18n.cancel,
					confirmText: this.i18n.confirm,
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack({
								delta: 1
							})
						}
					}
				})
			},

			// 页面加载回调
			pageLoaded(e) {
				uni.setNavigationBarTitle({
					title: e.detail.title
				})
			}
		}
	}
</script>

<style>
</style>

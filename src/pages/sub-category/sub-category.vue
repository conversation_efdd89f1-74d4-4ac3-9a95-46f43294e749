<template>
	<!--pages/sub-category/sub-category.wxml-->
	<view class="container">
		<view class="search-bar">
			<view class="search-box">
				<input :placeholder="i18n.enterKeywordSearch" class="sear-input" confirm-type="search" @confirm="toSearchConfirm"
				 @input="getSearchContent" :value="searchKey"></input>
				<image :src="`${staticPicDomain}images/icon/search.png`" class="search-img"></image>
			</view>
		</view>
		<!-- 顶部子分类tab -->
		<scroll-view scroll-x="true" class="category-tit" :scroll-into-view="intoView" :scroll-with-animation="true">
			<view class="tit-box">
				<view class="tit-item" v-for="(item, index) in subCategoryList" :key="index" @tap="onSubCategoryTap" :id="'sw' + item.categoryId" :data-id="item.categoryId">
					<image class="picture" :src="item.pic"/>
					<view :class="'category-item ' + (item.categoryId==categoryId? 'on':'')" >{{item.categoryName}}</view>
				</view>
			</view>
		</scroll-view>
		<!-- 商品列表 -->
		<view class="prod-item">
			<block v-for="(item, key) in prodList" :key="key">
				<prod :item="item"></prod>
			</block>
		</view>
		<!-- 空 || 加载完毕 -->
		<view class="empty all" v-if="!prodList.length || current == pages">
		  <view class="empty-icon" v-if="!prodList.length">
		    <image :src="`${staticPicDomain}images/icon/empty.png`"></image>
		  </view>
		  <view class="empty-text">{{!prodList.length?i18n.noCommodity:i18n.endTips}}</view>
		</view>
	</view>
</template>

<script>
	// pages/sub-category/sub-category.js
	var http = require("../../utils/http.js");
	import prod from "../../components/production/production";

	export default {
		data() {
			return {
				subCategoryList: [],
				categoryId: 0,
				prodList: [],
				parentId: "",
				current: 1,
				pages: 0,
				intoView: '',
				searchKey: ''
			};
		},

		components: {
			prod
		},
		props: {},

		computed:{
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.setData({
				parentId: options.parentId,
				categoryId: options.categoryId,
				intoView: options.intoView
			});
			this.getSubCategory();
			this.getProdList();
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			if (this.current < this.pages) {
				this.current = this.current + 1
				this.getProdList()
			}
		},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			getSearchContent: function(e) {
				this.setData({
					searchKey: e.detail.value
				});
			},
			toSearchConfirm: function() {
				this.getProdList()
			},
			/**
			 * 获取顶栏子分类数据
			 */
			getSubCategory() {
				var params = {
					url: "/category/categoryInfo",
					method: "GET",
					data: {
						parentId: this.parentId,
					},
					callBack: res => {
						this.setData({
							subCategoryList: res
						});
					}
				};
				http.request(params);
			},

			/**
			 * 根据分类id获取商品列表数据
			 */
			getProdList() {
				var params = {
					// url: "/search/searchProdPage",
					url: "/search/page",
					method: "GET",
					data: {
						categoryId: this.categoryId,
						current: this.current,
						size: 10,
						sort: 21,
						isAllProdType: true,
						isActive: 1, // 过滤掉活动商品
						keyword: this.searchKey || ''
					},
					callBack: res => {
						this.setData({
							prodList: params.data.current == 1 ? res.records[0].products : this.prodList.concat(res.records[0].products),
							// current: res.current,
							pages: res.pages
						});
					}
				};
				http.request(params);
			},

			/**
			 * 切换顶部分类
			 */
			onSubCategoryTap(e) {
				this.setData({
					categoryId: e.currentTarget.dataset.id,
					current: 1,
					pages: 1,
					intoView: 'sw' + e.currentTarget.dataset.id
				});
				this.getProdList();
			},
		}
	};
</script>
<style>
	@import "./sub-category.css";
</style>

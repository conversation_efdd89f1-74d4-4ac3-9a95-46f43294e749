/* pages/sub-category/sub-category.wxss */

page {
  background: #f4f4f4;
}

.category-tit {
  background-color: #fff;
	padding: 20rpx 0;
}

.category-tit::-webkit-scrollbar {
    display:none
}

.tit-box{
	display: flex;
	flex-direction: row;
	align-items: center;
}

.tit-item{
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
}

.picture{
	width: 120rpx;
	height: 120rpx;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
}

.search-bar{
	background-color: #fff;
	padding: 20rpx 0;
	margin-bottom: 20rpx;
}

.search-box {
  position: relative;
  height: 70rpx;
  background: #f7f7f7;
  z-index: 999;
  width: 710rpx;
  border-radius: 50rpx;
  margin: 0 auto;
}

.sear-input {
  height: 70rpx;
  border: 0;
  margin: 0 30rpx 0 64rpx;
  vertical-align: top;
  background: #f7f7f7;
  font-size: 28rpx;
}

.search-box .search-img {
  width: 34rpx;
  height: 34rpx;
  position: absolute;
  left: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  display: block;
}

.category-tit .category-item {
  display: inline-block;
  /* padding: 20rpx 10rpx; */
  margin: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.prod-item {
  margin-top: 20rpx;
}

.on {
  /* border-bottom: 4rpx solid #e43130; */
  color: var(--primary-color);
}
/* 清除 scroll-view 滚动条 */
::-webkit-scrollbar {  
  width: 0;
  height: 0;
  color: transparent;
}
.empty-tips{
  text-align: center;
  margin-top: 150rpx; 
  color: #888888;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

/* 列表为空 */
.empty {
  margin-top: 200rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #bbb;
  line-height: 2em;
}

/* 加载完毕 */
.all{
	margin-top: 30rpx
}
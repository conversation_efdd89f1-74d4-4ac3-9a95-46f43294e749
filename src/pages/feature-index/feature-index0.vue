<template>
	<view>
		<feature ref="featureIndex" @pageLoaded="pageLoaded" :pageLoad="pageLoad" :pageId="renovationId" :shopId="shopId" :pageScorllTop="pageScorllTop"></feature>
    <BottomLoginNav v-if="!isLogin" />
    <WenetAccountTip />
	</view>
</template>

<script>
	import feature from '../../components/feature/index/index'
  import BottomLoginNav from '@/components/bottom-login-nav'
  import WenetAccountTip from '@/components/wenet-account-tip'
	export default {
		data() {
			return {
				pageLoad: false,
				renovationId: '', // 页面id
				shopId: '',
				pageScorllTop: 0, // 页面滚动距离
			}
		},
		components: {
			feature,
      BottomLoginNav,
      WenetAccountTip
		},
    computed: {
      isLogin () {
        return this.$store.state.isLogin
      },
    },
    onShareAppMessage: function () {},
		onLoad: function(options) {
			uni.showLoading()
			if (options.renovationId) {
				this.renovationId = options.renovationId
			}
			if (options.shopId) {
				this.shopId = options.shopId
			}
		},
		onShow:function() {

		},
		onPageScroll: function(e) {
			this.pageScorllTop = e.scrollTop
		},
		methods: {
			// 页面加载回调
			pageLoaded(e) {
				uni.setNavigationBarTitle({
					title: e.detail.title
				})
				this.pageLoad = false
				setTimeout(() => {
					uni.hideLoading()
					this.pageLoad = true
				}, 1)
			}
		}
	}
</script>

<style>

</style>

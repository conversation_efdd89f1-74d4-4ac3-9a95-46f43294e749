/* pages/category/category.wxss */

page {
  height: 100%;
  background: #f5f5f5;
}
.find{
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.find-box{
	width: 100%;
	height: 572rpx;
	background: url('https://m.wenet.com.cn/resources/shop-static/images/other/fa7b549e9387424a9f06664003c14c5b.png') no-repeat;
	background-size:100% auto ;
	box-sizing: border-box;
	padding-top: 34rpx;
	border-radius: 0 0 25rpx 25rpx;
}

.find-title{
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
}
.find-banner{
	width: 680rpx;
	height: 269rpx;
	margin-top: 24rpx;
	margin-left: 35rpx;
}
.content-box{
	width: 100%;
	background: #fff;
	padding: 33rpx 23rpx 0;
	box-sizing: border-box;
	border-radius: 25rpx 25rpx 0 0;
	position: relative;
	z-index: 1;
	/* margin-top: -160rpx; */
	display: flex;
	flex-direction: column;
	flex: 1;
}
.nav-box{
	display: flex;
	align-items: center;
	margin-bottom: 38rpx;
}
.left-nav-box{
	font-size: 30rpx;
	color: #333;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-right: 50rpx;
}
.right-nav-box{
	font-size: 30rpx;
	color: #333;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.left-nav-box .find-line, .right-nav-box .find-line{
	width: 40rpx;
	height: 6rpx;
	border-radius: 3rpx;
	background: #fff;
	margin-top: 10rpx;
}
.active{
	color: var(--primary-color);
}
.active .find-line{
	background: var(--primary-color);
}

.scroll.h5{
	height: calc(100vh - 250rpx);
}

.scroll{
	height: calc(100vh - 100rpx);
}

.store-list{
	display: flex;
	flex-direction: column;
	width: 710rpx;
	background-color: #FAFAFA;
	border-radius: 20rpx;
	box-sizing: border-box;
	padding: 20px 10px;
	margin: 0 auto 20px;
}
.store-info{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}
.left-store{
	display: flex;
	flex-direction: row;
	align-items: center;
}
.left-store .store-head{
	width: 80rpx;
	height: 80rpx;
	border-radius: 100%;
	margin-right: 10rpx;
}
.left-store .store-text-box{
	display: flex;
	flex-direction: column;
}
.left-store .store-text-box .store-name{
	font-size: 30rpx;
	color: #333;
	margin-bottom: 10rpx;
}
.left-store .store-text-box .store-tit{
	font-size: 24rpx;
	color: #888;
}
.right-store{
	display: flex;
	align-items: center;
}
.go-store{
	width: 114rpx;
	height: 50rpx;
	background: linear-gradient(90deg, var(--gradient-ramp-deep), var(--gradient-ramp-light));
	background: -webkit-linear-gradient(90deg, var(--gradient-ramp-deep), var(--gradient-ramp-light));
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #fff;
	margin-right: 16px;
}
.guan-zhu{
	height: 50rpx;
	/*border: 2rpx solid var(--primary-color);*/
	box-sizing: border-box;
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.guan-zhu .icon{
	width: 24rpx;
	height: 24rpx;
	margin-right: 6rpx;
}
.guan-zhu .text{
	font-size: 24rpx;
	color: 4A6CEA;
}

.store-goods-list{
	display: flex;
	align-items: center;
	margin-top: 16px;
}
.store-good-item{
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 18px;
}
.goods-img{
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 5px;
}
.good-title{
	font-size: 24rpx;
	color: #333;
	margin-bottom: 10rpx;
	display: -webkit-box;
	-webkit-line-clamp: 1; /*设定显示行数*/
	-webkit-box-orient: vertical;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
}
.good-price{
	font-size: 32rpx;
	color: #FF5733;
}
.unit{
	font-size: 24rpx;
	color: #FF5733;
}

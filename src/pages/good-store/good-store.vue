<template>
	<view class="find">
		<!-- <view class="find-box"> -->
			<!-- <view class="find-title">优选好店</view> -->
		<!-- </view> -->

		<view class="content-box">
			<view class="nav-box">
				<view :class="'left-nav-box ' + (shopFlag == 0 ? 'active' : '')" @tap="changeNav(0)">
					<text>全部店铺</text>
					<view class="find-line"/>
				</view>
				<view :class="'right-nav-box ' + (shopFlag == 1 ? 'active' : '')" @tap="changeNav(1)">
					<text>优选好店</text>
					<view class="find-line"/>
				</view>
			</view>
			<view>

			<scroll-view 
				:class="scrollClassName" 
				scroll-y lower-threshold="30" 
				@scrolltolower="loadMoreCustomers" 
				:refresher-enabled="true"
				@refresherpulling="onPulling"
    		:refresher-triggered="triggered"
				@refresherrefresh="onRefresh"
				@refresherrestore="onRestore"
			>
				<view class="store-list" v-for="(item, index) in records" :key="index">
					<view class="store-info">
						<view class="left-store" @tap="toShopPage(item)">
							<image class="store-head" :src="item.shopLogo"/>
							<view class="store-text-box">
								<text class="store-name">{{item.shopName}}</text>
								<text class="store-tit">已经有{{item.fansCount}}人关注</text>
							</view>
						</view>
						<view class="right-store">
							<view class="go-store" @tap="toShopPage(item)">进店</view>
							<view class="guan-zhu" @tap="collectShop(item)">
								<image class="icon" :src="item.collectionType == 1 ? `${staticPicDomain}images/icon/focus-on2.png` : `${staticPicDomain}images/icon/focus-on1.png`"/>
								<text class="text">{{item.collectionType == 1 ? '已关注' :'关注'}}</text>
							</view>
						</view>
					</view>
				
					<view class="store-goods-list">
						<view class="store-good-item" v-for="(val, num) in item.prodList" :key="num">
							<image class="goods-img" :src="val.pic"/>
							<text class="good-title">{{val.prodName}}</text>
							<text class="good-price"><text class="unit">¥</text>{{val.price}}</text>
						</view>
					</view>
				</view>
			</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	// pages/category/category.js
	var http = require("../../utils/http.js");
	var config = require("../../utils/config.js");
	var util = require("../../utils/util.js");

	export default {
		data() {
			return {
				shopFlag: 0, // 店铺区分： 0 所有店铺，1 优选好店
				records: [],
				isFollowed: false,
				pages: 1,
        systemType: uni.getStorageSync('systemType'),
        canLoadmore: false,
      	triggered: false,
				scrollTop: 0
			};
		},
		components: {},
		props: {},
		computed:{
			i18n() {
				return this.$t('index')
			},
      scrollClassName() {
        if (this.systemType === 2) {
          return 'scroll h5'
        } else {
          return 'scroll'
        }
      }
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {

		},


		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {
      
    },

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
      parentOnShow() {
        // 加载导航标题
        uni.setNavigationBarTitle({
          title:this.i18n.find
        });
      },
      parentOnLoad(options) {
        this.init(0, 1)
      },
			// 自定义下拉刷新控件被下拉
			onPulling(e) {
				if (e.detail.deltaY < 0) return  // 防止上滑页面也触发下拉
				this.triggered = true;
			},
			onScroll(e) {  
      	this.scrollTop = e.detail.scrollTop
			},
			// 自定义下拉刷新被触发
			onRefresh() {
				setTimeout(() => {
					this.triggered = false;
					this.pages = 1
					this.$nextTick(() => {
						this.init(this.shopFlag, this.pages, true)
					})
				}, 100);
			},
			// 自定义下拉刷新被复位
			onRestore() {
				this.triggered = 'restore'; // 需要重置
			},
      loadMoreCustomers() {
				if (!this.canLoadmore) {
					return
				}
        this.pages = this.pages + 1
				this.init(this.shopFlag, this.pages)
      },
			// 添加/取消店铺
			collectShop(item) {
				var ths = this
			  util.checkAuthInfo(() => {
				var params = {
				  url: `/p/shop/collection/addOrCancel`,
				  method: "POST",
				  data: item.shopId,
				  callBack: res => {
					uni.hideLoading()
					this.records = []
					ths.init(ths.shopFlag, 1)
					// this.$emit('queryShoInfo')
				  }
				};
				http.request(params);
			  })
			},
			changeNav: function(num) {
				this.records = []
				this.setData({
					shopFlag: num,
				});
				this.pages = 1
				this.init(num, 1)
			},
			init: function(shopFlag, pages, isReFresh = false) {
				var ths = this; //加载分类列表
				var params = {
					url: uni.getStorageSync('token') ? "/p/shop/collection/shopStreet" : "/center/shopStreet",
					method: "GET",
					data: {
						shopFlag,
						size: 10,
						current: pages
					},
					callBack: function(res) {
						ths.setData({
							records: isReFresh ? res.records : ths.records.concat(res.records),
              canLoadmore: res.records.length > 0
						});
					}
				};
				http.request(params);
			},
			/**
			 * 跳转到店铺页
			 */
			toShopPage: function(item) {
				util.tapLog(3)
        let url = ''
        if (item.renovationId) {
          url = '/pages/shop-feature-index/shop-feature-index0?shopId=' + item.shopId + '&renovationId=' + item.renovationId
        } else {
          url = '/packageShop/pages/shopPage/shopPage?shopId=' + item.shopId
        }
        uni.navigateTo({
          url
        })
			},
			/**
			 * 分类点击事件，获取子分类
			 */
			onMenuTab: function(e) {

			},
			// 跳转搜索页
			toSearchPage: function() {
				util.tapLog(3)
				uni.navigateTo({
					url: '/pages/search-page/search-page'
				});
			},

		}
	};
</script>
<style>
	@import "./good-store.css";
</style>

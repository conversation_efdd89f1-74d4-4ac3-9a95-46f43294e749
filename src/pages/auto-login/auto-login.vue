<template>
  <view>
    <n-loading show />
  </view>
</template>
<script>
import http from '@/utils/http';
import util from '@/utils/util';
import { AppType } from '@/utils/constant.js';
import { cacheWenetAccountInfo, getWenetAccountInfo } from '@/utils/login-processor';
export default {
  name: 'auto-login',
  data() {
    return {};
  },
  onLoad(options) {
    const username = options.basUsername;
    const ou = options.ou;
    const uid = options.uid;
    const basMobile = options.basMobile || '';
    const redirectTo = options.redirectTo;
    if (username && ou && uid) {
      this.getTokenBayBasUsername({
        username,
        ou,
        uid,
        basMobile
      }, redirectTo);
    }
  },
  methods: {
    getTokenBayBasUsername(params, redirectTo) {
      this.getWxCode().then((code) => {
        const { username, ou, uid, basMobile } = params;
        http.request({
          url: '/self/login',
          methods: 'POST',
          data: {
            basUsername: username,
            ou,
            uid,
            code,
            mobile: basMobile
          },
          async callBack(res) {
            util.clearLoginData();
            const basToken = res.basToken
            if (basToken) {
              const wenetAccountInfo = await getWenetAccountInfo(basToken)
              cacheWenetAccountInfo(wenetAccountInfo)
            }
            if (redirectTo) {
              const decodeRedirectTo = decodeURIComponent(redirectTo);
              uni.setStorageSync('routeUrlAfterLogin', decodeRedirectTo);
            }
            if (res) {
              util.loginSuccess(res);
            }
            else {
              uni.redirectTo({
                url: `/pages/login/login`
              });
            }
          },
          errCallBack() {
            if (redirectTo) {
              const decodeRedirectTo = decodeURIComponent(redirectTo);
              uni.setStorageSync('routeUrlAfterLogin', decodeRedirectTo);
            }
            uni.redirectTo({
              url: `/pages/login/login`
            });
          }
        });
      });
    },
    getWxCode() {
      const appType = uni.getStorageSync('appType');
      return new Promise((resolve) => {
        if (appType === AppType.MINI) {
          wx.login({
            success: (res) => {
              resolve(res.code);
            }
          });
        }
        else {
          resolve('');
        }
      });
    }
  },
  computed: {},
  mounted() {
  },
};
</script>
<style scoped></style>
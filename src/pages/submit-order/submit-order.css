/* pages/submit-order/submit-order.wxss */

page {
	background: #f4f4f4;
}

/* 收货地址 */
.submit-order {
	padding: 0 20rpx;
	padding-top: 20rpx;
	padding-bottom: 130rpx;
}

.submit-order .delivery-addr {
	position: relative;
	background: #fff;
}

.delivery-addr .addr-bg .add-addr .plus-sign {
	color: #e43130;
	border: 2rpx solid #e43130;
	padding: 0rpx 6rpx;
	margin-right: 10rpx;
}

.delivery-addr .addr-bg {
	padding: 0 30rpx;
}

.delivery-addr .addr-bg.whole {
	padding: 0 39rpx 0 77rpx;
}

.delivery-addr .addr-bg .add-addr {
	font-size: 28rpx;
	color: #666;
	display: flex;
	align-items: center;
	padding: 30rpx 0;
}

.submit-order .delivery-addr .addr-icon {
	width: 32rpx;
	height: 32rpx;
	display: block;
	position: absolute;
	left: 30rpx;
	top: 24rpx;
}

.submit-order .delivery-addr .addr-icon image {
	width: 100%;
	height: 100%;
}

.submit-order .delivery-addr .user-info {
	padding-top: 20rpx;
	line-height: 48rpx;
	word-wrap: break-word;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.submit-order .delivery-addr .user-info .item {
	font-size: 30rpx;
	margin-right: 30rpx;
	vertical-align: top;
	display: inline-block;
}

.submit-order .delivery-addr .addr {
	font-size: 26rpx;
	line-height: 36rpx;
	color: #999;
	width: 90%;
	padding-bottom: 20rpx;
	margin-top: 15rpx;
}

.submit-order .delivery-addr .arrow {
	width: 15rpx;
	height: 15rpx;
	border-top: 2rpx solid #777;
	border-right: 2rpx solid #777;
	transform: rotate(45deg);
	position: absolute;
	right: 30rpx;
	top: 60rpx;
}

.submit-order .delivery-addr .arrow.empty {
	top: 39rpx;
}

.addr-bg .add-addr .plus-sign-img {
	width: 32rpx;
	height: 32rpx;
	font-size: 0;
	margin-right: 10rpx;
}

.addr-bg .add-addr .plus-sign-img image {
	width: 100%;
	height: 100%;
}

/* 背景 */
.submit-order .bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 750rpx;
	height: 700rpx;
	font-size: 0;
	/* background: linear-gradient(#e43130,#f4f4f4) */
}


/* 收货地址 */
.submit-order .distribution-mode {
	padding: 30rpx 30rpx 0;
	position: relative;
}

.submit-order .distribution-mode .item-box {
	background: rgba(74, 108, 234, 0.2);
	width: 380rpx;
	height: 60rpx;
	line-height: 60rpx;
	border-radius: 60rpx;
	display: flex;
	margin: 0 auto;

}

.submit-order .distribution-mode .item-box .item {
	flex: 1;
	border-radius: 60rpx;
	font-size: 28rpx;
	color: #fff;
	text-align: center;
}

.submit-order .distribution-mode .item-box .item.active {
	color: var(--primary-color);
	background: #fff;
}

/* 邮寄到家/到店自提 */
.submit-order .address-box {
	background: #fff;
	position: relative;
	border-radius: 10rpx;
	width: 100%;
	padding: 30rpx;
	box-sizing: border-box;
	margin-top: 30rpx;
}

.submit-order .address-box .tit {
	display: flex;
	justify-content: space-between;
	line-height: 40rpx;
	font-size: 28rpx;
}

.submit-order .address-box .tit .text {
	font-weight: 600;
}

.submit-order .address-box .tit .total {
	color: #e43130;
	position: relative;
	padding-right: 28rpx;
}

.submit-order .address-box .tit .total::after {
	position: absolute;
	right: 10rpx;
	top: 50%;
	display: block;
	width: 14rpx;
	height: 14rpx;
	border: 2rpx solid #666;
	border-width: 2rpx 2rpx 0 0;
	content: " ";
	font-size: 0;
	transform: rotate(45deg) translateY(-50%);
}

.submit-order .address-box .add-item {
	display: flex;
	align-items: center;
	margin-top: 30rpx;
	font-size: 24rpx;
	position: relative;
}


.submit-order .address-box .add-item .text {
	width: 120rpx;
}

.submit-order .address-box .add-item .input,
.submit-order .address-box .add-item .area {
	flex: 1;
	padding: 0 30rpx 0 30rpx;
	font-size: 24rpx;
	height: 80rpx;
	border-radius: 80rpx;
	background: #f5f5f5;
}

.submit-order .address-box .add-item .area {
	position: relative;
	height: 80rpx;
	line-height: 80rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
}

.submit-order .address-box .add-item .area .placeholder-text {
	color: #808080;
}

.submit-order .address-box .add-item .area::after {
	position: absolute;
	right: 30rpx;
	top: 48%;
	display: block;
	width: 14rpx;
	height: 14rpx;
	border: 2rpx solid #666;
	border-width: 2rpx 2rpx 0 0;
	content: " ";
	font-size: 0;
	transform: rotate(45deg) translateY(-50%);
}

.infoText {
	margin-top: 20rpx;
	text-align: center;
	width: 100%;
	justify-content: center;
}

picker-view {
	background-color: white;
	padding: 0;
	width: 100%;
	height: 380rpx;
	bottom: 0;
	position: fixed;
}

picker-view-column view {
	vertical-align: middle;
	font-size: 30rpx;
	line-height: 30rpx;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.animation-element-wrapper {
	display: flex;
	position: fixed;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 999;
}

.animation-element {
	display: flex;
	position: fixed;
	width: 100%;
	height: 530rpx;
	bottom: 0;
	background-color: rgba(255, 255, 255, 1);
}

.animation-button {
	top: 20rpx;
	width: 290rpx;
	height: 100rpx;
	align-items: center;
}

picker-view text {
	color: #999;
	display: inline-flex;
	position: fixed;
	margin-top: 20rpx;
	height: 50rpx;
	text-align: center;
	line-height: 50rpx;
	font-size: 34rpx;
	font-family: Arial, Helvetica, sans-serif;
}

.left-bt {
	left: 30rpx;
}

.right-bt {
	right: 20rpx;
	top: 20rpx;
	position: absolute;
	width: 80rpx !important;
}

.line {
	display: block;
	position: fixed;
	height: 2rpx;
	width: 100%;
	margin-top: 89rpx;
	background-color: #eee;
}

.submit-order .address-box .add-item .input {
	padding-right: 70rpx;
}

.submit-order .address-box .add-item .addres-icon {
	position: absolute;
	right: 24rpx;
	top: 50%;
	width: 30rpx;
	height: 30rpx;
	transform: translateY(-50%);
}

.submit-order .address-box .add-item .btn {
	width: 220rpx;
	height: 60rpx;
	line-height: 60rpx;
	border-radius: 60rpx;
	font-size: 24rpx;
	text-align: center;
	border: 2rpx solid #e43130;
	color: #e43130;
	margin: 0 auto;
}

.submit-order .address-box .current-address {
	margin: 20rpx 0;
	position: relative;
	padding-right: 58rpx;
}

.submit-order .address-box .current-address .c-address {
	font-size: 28rpx;
	font-weight: 600;
	line-height: 40rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	position: relative;
	/* word-wrap: break-word; */
	word-break: break-word;
}

.submit-order .address-box .current-address .c-user {
	font-size: 24rpx;
	margin-top: 20rpx;
}

.submit-order .address-box .current-address .c-edit {
	font-size: 0;
	position: absolute;
	top: 6rpx;
	right: 10rpx;
	opacity: .5;
}

.submit-order .address-box .current-address .c-edit image {
	width: 32rpx;
	height: 32rpx;
}
/* .c-edit image.add-addr {
	margin-top: 26rpx;
} */

/* .c-edit image.add-addr {
	margin-top: 26rpx;
} */

.submit-order .address-box .choose-way {
	padding-top: 30rpx;
	margin-top: 30rpx;
	border-top: 1px solid #eee;
	font-size: 28rpx;
	display: flex;
	line-height: 40rpx;
	justify-content: space-between;
}

.submit-order .address-box .choose-way .text {
	font-weight: 600;
}

.submit-order .address-box .choose-way .go {
	position: relative;
	padding-right: 28rpx;
}

.submit-order .address-box .choose-way .go::after {
	position: absolute;
	right: 8rpx;
	top: 45%;
	display: block;
	width: 14rpx;
	height: 14rpx;
	border: 2rpx solid #666;
	border-width: 2rpx 2rpx 0 0;
	content: " ";
	font-size: 0;
	transform: rotate(45deg) translateY(-50%);
}

/* 邮寄到家 end */

/* 到店自提 */
.submit-order .address-box .self-raising {
	padding-bottom: 30rpx;
	border-bottom: 2rpx solid #eee;
}

.submit-order .address-box .self-raising .choose-store {
	display: flex;
	align-items: flex-start;
	padding-top: 30rpx;
}

.submit-order .address-box .self-raising .choose-store .img {
	width: 28rpx;
	height: 28rpx;
	margin: 6rpx 10rpx 0 0;
}

.submit-order .address-box .self-raising .choose-store .text {
	font-size: 28rpx;
	font-weight: 600;
	/* height: 80rpx;
  line-height: 40rpx; */
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
	position: relative;
	padding-right: 28rpx;
	flex: 1;
}

.submit-order .address-box .self-raising .choose-store .text::after {
	position: absolute;
	right: 4px;
	top: 12rpx;
	display: block;
	width: 14rpx;
	height: 14rpx;
	border: 2rpx solid #666;
	border-width: 2rpx 2rpx 0 0;
	content: " ";
	font-size: 0;
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
}

.submit-order .address-box .raising-user {
	margin-top: 30rpx;
	padding-bottom: 6rpx;
}

.submit-order .address-box .raising-user .user-info {
	margin-top: 30rpx;
	font-size: 24rpx;
}

.submit-order .address-box .raising-user .user-info .input {
	padding: 0 30rpx;
	font-size: 24rpx;
	background: #f5f5f5;
	height: 80rpx;
	border-radius: 80rpx;
}

/* 到店自提 end */

/* 店铺 */
.shop-item {
	background: #fff;
	border-radius: 10rpx;
	margin-top: 20rpx;
	position: relative;
}

.shop-box {
	padding: 20rpx;
	position: relative;
	display: flex;
	align-items: center;
}

.shop-box::after {
	position: absolute;
	left: 20rpx;
	right: 20rpx;
	bottom: 0;
	display: block;
	width: auto;
	height: 2rpx;
	content: " ";
	font-size: 0;
	background: #eee;
}

.shop-icon {
	width: 28rpx;
	height: 28rpx;
	font-size: 0;
	margin-right: 10rpx;
}

.shop-icon image {
	width: 100%;
	height: 100%;
}

.shop-name {
	flex: 1;
	font-size: 28rpx;
	font-weight: 600;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
}

.prod-item .item-cont {
	display: flex;
	flex-direction: column;
	padding: 26rpx 20rpx;
	position: relative;
}

.prod-item .item-cont::after {
	position: absolute;
	left: 20rpx;
	right: 20rpx;
	bottom: 0;
	display: block;
	width: auto;
	height: 2rpx;
	content: " ";
	font-size: 0;
	background: #eee;
}

.prod-item .item-cont .info-row {
	display: flex;
	width: 100%;
}

/* 赠品 */

.gift-item {
  margin-left: 190rpx;
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
}
.gift-item:last-child {
  margin-bottom: 0;
}
.gift-name {
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 82%;
}
.gift-name .gift-sku{
  color: #999;
  margin-left: 20rpx;
}
.gift-count {
	font-size: 24rpx;
	color: #999;
	margin-right: 10rpx;
}

/* 赠品end */

.prod-item .item-cont .prod-pic {
	font-size: 0;
	width: 180rpx;
	height: 180rpx;
	border-radius: 10rpx;
	overflow: hidden;
	background: #fff;
	margin-right: 20rpx;
}

.prod-item .item-cont .prod-pic image {
	width: 100%;
	height: 100%;
}

.prod-item .item-cont .prod-info {
	font-size: 24rpx;
	position: relative;
	-webkit-flex: 1;
	-ms-flex: 1;
	-webkit-box-flex: 1;
	-moz-box-flex: 1;
	flex: 1;
}

.prod-item .item-cont .prod-info .prodname {
	font-size: 28rpx;
	line-height: 36rpx;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	text-overflow: ellipsis;
	word-break: break-word;
}

.prod-item .item-cont .prod-info .prod-info-cont {
	color: #999;
	margin-top: 10rpx;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	text-overflow: ellipsis;
	word-break: break-all;
}

.prod-item .price-nums {
	display: flex;
	align-items: baseline;
	position: absolute;
	bottom: 0;
	width: 100%;
}

.prod-item .price-nums .prodprice {
	flex: 1;
	margin-right: 20rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	color: #e43130;
}

.prod-item .price-nums .prodcount {
	font-size: 24rpx;
	color: #999;
	margin-right: 10rpx;
}

/* 促销样式 */
.prod-item .prod-block {
	background: #fff;
}

.prod-item .prod-block.discount .item-cont {
	position: relative;
	margin-left: 20rpx;
}

.prod-item .prod-block.discount .item-cont::before {
	content: "";
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	width: 0;
	/* border-left: 2rpx dashed #ddd; */
	z-index: 0;
}
.prod-item .prod-block.discount .item-cont:first-child::before {
	top: 26rpx;
}
.prod-item .prod-block.discount .pre-sell.item-cont::before {
	display: none;
}

.prod-item .prod-block.discount .item-cont::after {
	left: 0;
}

.prod-item .prod-block .discount-tips {
	padding: 20rpx 20rpx 0;
	margin-bottom: -3px;
	display: flex;
	align-items: flex-start;
	position: relative;
}

.prod-item .prod-block .discount-tips::before {
	content: "";
	position: absolute;
	left: 20rpx;
	top: 24rpx;
	bottom: -16rpx;
	width: 0;
	border-left: 2rpx dashed #ddd;
	z-index: 0;
}

.prod-item .prod-block .discount-tips .text-block {
	padding: 2rpx 10rpx;
	border-radius: 4rpx;
	font-size: 20rpx;
	background: var(--primary-color);
	color: #fff;
	position: relative;
	line-height: 24rpx;
	margin-top: 4rpx;
}

.prod-item .prod-block .discount-tips .text-block::before {
	position: absolute;
	left: 0;
	bottom: -8rpx;
	content: " ";
	font-size: 0;
	display: block;
	border: 6rpx solid transparent;
	border-left: 6rpx solid var(--primary-color);
	border-top: 6rpx solid var(--primary-color);
}

.prod-item .prod-block .discount-tips .text-list {
	margin-left: 10rpx;
	line-height: 36rpx;
	flex: 1;
	font-size: 24rpx;
}
.prod-item .prod-block .discount-tips .reduce-amount {
  margin-left: auto;
  font-size: 24rpx;
  line-height: 36rpx;
}

/* 不满足当前配送方式的商品 */
.shop-item .useless {
	/* margin: 0 20rpx; */
	padding: 20rpx;
	background: #f6f9fb;
	border-bottom: 2rpx solid #eee;
	border-radius: 10rpx;
}

.shop-item .useless .u-reason {
	color: #999;
	font-size: 24rpx;
}

.shop-item .useless .u-con {
	margin-top: 20rpx;
}

.shop-item .useless .u-con::-webkit-scrollbar {
	width: 0;
	height: 0;
}

.shop-item .useless .u-box {
	white-space: nowrap;
	width: 100%;
}

.shop-item .useless .u-box .u-prods {
	width: 80rpx;
	height: 80rpx;
	font-size: 0;
	margin-right: 20rpx;
	display: inline-block;
}

.shop-item .useless .u-box .u-prods image {
	width: 100%;
	height: 100%;
}

/* 优惠券和留言 */
.shop-item .msg-item {
	padding: 0 20rpx;
}

.shop-item .msg-item .item {
	display: flex;
	border-bottom: 2rpx solid #eee;
	font-size: 24rpx;
	padding: 24rpx 0;
	align-items: center;
}

.shop-item .msg-item .item:last-child {
	border: 0;
}

.shop-item .msg-item .item .free-box {
	display: flex;
	align-items: center;
	margin-left: auto;
}
.shop-item .msg-item .item .free-box .black {
	color: #333;
	margin-left: 10rpx;
}

.shop-item .msg-item .item .text-box {
	flex: 1;
}

.shop-item .msg-item .item .text-box .number {
	color: #999;
}

.shop-item .msg-item .item .amount {
	color: #e43130;
	position: relative;
	padding-right: 28rpx;
}

.shop-item .msg-item .item .amount::after {
	position: absolute;
	right: 8rpx;
	top: 45%;
	display: block;
	width: 14rpx;
	height: 14rpx;
	border: 2rpx solid #666;
	border-width: 2rpx 2rpx 0 0;
	content: " ";
	font-size: 0;
	transform: rotate(45deg) translateY(-50%);
}

.shop-item .msg-item .item .amount .big-num {
	font-size: 28rpx;
}

.shop-item .msg-item .item .input {
	flex: 1;
	background: #fff;
	margin-top: 2rpx;
	font-size: 24rpx;
}

.shop-item .msg-item .item .input-placeholder {
	color: #999;
}

/* 订单信息 */
.order-msg {
	background: #fff;
	border-radius: 10rpx;
	font-size: 24rpx;
	margin-top: 20rpx;
	padding: 0 20rpx 10rpx;
	position: relative;
}

.order-msg .item {
	position: relative;
	display: flex;
	align-items: center;
	padding: 16rpx 0;
}

.order-msg .item .text-box {
	flex: 1;
	display: flex;
	align-items: center;
}

.order-msg .item .text-box .number {
	color: #999;
}

.order-msg .item .amount {
	color: #e43130;
	position: relative;
	padding-right: 28rpx;
}

.order-msg .item .amount::after {
	position: absolute;
	right: 8rpx;
	top: 45%;
	display: block;
	width: 14rpx;
	height: 14rpx;
	border: 2rpx solid #666;
	border-width: 2rpx 2rpx 0 0;
	content: " ";
	font-size: 0;
	transform: rotate(45deg) translateY(-50%);
}

.order-msg .item .amount .big-num {
	font-size: 28rpx;
}

.order-msg .item .text-box .integral-tips {
	color: #999;
	display: flex;
	align-items: center;
}
.tips {
	flex: 1;
	margin-right: auto;
}

.order-msg .item .text-box .integral-tips image {
	width: 24rpx;
	height: 24rpx;
	margin-left: 10rpx;
}

.order-msg .item .price {
	flex: 1;
	text-align: right;
}

.order-msg .item .price .big-num {
	font-size: 28rpx;
}

.order-msg .item .price.black {
	color: #333;
}

/* 积分抵扣 */

/* 会员积分 */
.member-points {
	display: flex;
	justify-content: space-between;
	width: 100%;
}

.integral-icon {
	display: inline-block;
	width: 35rpx;
	height: 35rpx;
	vertical-align: middle;
}

.integral-icon>image {
	display: block;
	width: 100%;
	height: 100%;
}

.integral-deduction {
	display: flex;
}

.integral-tips {
	color: #999;
	display: flex;
	align-items: center;
}

.integral-tips > image {
	margin-left: 10rpx;
	width: 32rpx;
	height: 32rpx;
}

.integral-right-select {
	display: inline-block;
	line-height: 1em;
	margin-left: 15rpx;
	position: absolute;
	right: 0;
}
.integral-right-select1 {
	display: inline-block;
	line-height: 1em;
}
.all {
	font-size: 28rpx;
	margin-left: 20rpx;
}

.all label {
	display: flex;
	align-items: center;
}

/* 底部栏 */
.submit-order-footer {
	position: fixed;
	bottom: 0;
	width: 100%;
	max-width: 750rpx;
	background: #fff;
	margin: auto;
	display: flex;
	align-items: center;
	box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.05);
	z-index: 10;
}

.submit-order-footer .sub-order {
	flex: 1;
	margin: 0 30rpx;
	line-height: 100rpx;
	display: block;
	text-align: left;
	font-size: 24rpx;
	background: #fff;
	z-index: 5;
}

.item-txt {
	height: 110rpx;
	line-height: 110rpx;
}

.submit-order-footer .sub-order .item-txt .price {
	color: #e43130;
	font-weight: 600;
}

.submit-order-footer .sub-order .item-txt .price .symbol,
.submit-order-footer .sub-order .item-txt .price .small-num {
	font-size: 28rpx;
}

.submit-order-footer .sub-order .item-txt .price .big-num {
	font-size: 36rpx;
}

.submit-order-footer .footer-box {
  background: var(--primary-color);
	text-align: center;
	color: #fff;
	font-size: 28rpx;
	width: 220rpx;
	height: 70rpx;
	border-radius: 70rpx;
	line-height: 70rpx;
	margin-right: 20rpx;
}

/** 优惠券弹窗 **/
.popup-hide {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: rgba(0, 0, 0, 0.3);
}

.popup-box {
	position: absolute;
	bottom: 0;
	width: 100%;
	height: 80%;
	overflow: hidden;
	background-color: #fff;
	border-radius: 10rpx 10rpx 0 0;
}

.popup-tit {
	padding: 20rpx;
	font-size: 28rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.close {
	color: #666;
	font-size: 32rpx;
}

.close::before {
	content: "\2715";
}

.coupon-tabs {
	display: flex;
	font-size: 14px;
	justify-content: space-around;
	box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.07);
	position: relative;
	z-index: 1;
}

.coupon-tab {
	padding: 10px 0;
	font-family: arial;
}

.coupon-tab.on {
	border-bottom: 2px solid #e43130;
	font-weight: 600;
	color: #e43130;
}

.popup-cnt {
	height: calc(100% - 280rpx);
	overflow: auto;
	padding: 0 30rpx;
}
.popup-cnt.on {
	height: calc(100% - 200rpx);
	overflow: auto;
	padding: 0 30rpx;
}

.popup-cnt.sts2 {
	height: calc(100% - 160rpx);
}

.popup-cnt::-webkit-scrollbar {
	width: 0;
	height: 0;
}

.coupon-con {
	/* padding-bottom: 80rpx; */
}

.coupon-ok {
	position: fixed;
	bottom: 0;
	width: 100%;
	padding: 20rpx 30rpx;
	box-sizing: border-box;
	text-align: center;
	box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.05);
	background: #fff;
}

.coupon-ok .btn {
	background: #e43130;
	text-align: center;
	color: #fff;
	font-size: 28rpx;
	border-radius: 80rpx;
	height: 80rpx;
	line-height: 80rpx;
}

.botm-empty {
	height: 150rpx;
	line-height: 150rpx;
	font-size: 24rpx;
	color: #999;
	text-align: center;
}

/* 修改积分弹出层 */
.score-pop {
	position: absolute;
	top: 50%;
	left: 10%;
	transform: translateY(-50%);
	width: 80%;
	border-radius: 10rpx;
	padding: 30rpx;
	box-sizing: border-box;
	background: #fff;
}

.score-pop .popup-tit {
	padding: 0;
}

.score-pop .score-pop-con {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
	align-items: center;
	height: 90%;
}

.score-pop-item {
	width: 100%;
}

.usable-tips {
	display: inline-block;
	font-size: 24rpx;
	color: #999999;
	margin-top: 18rpx;
}

.score-pop .score-pop-con .score-int {
	padding: 30rpx 0 10rpx;
	font-size: 28rpx;
	font-family: arial;
	border-bottom: 2rpx solid #ddd;
	/* 解决ios输入框无法输入 */
	-webkit-user-select: text !important;
}

.score-pop .score-pop-con .confirm-btn {
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 80rpx;
	background: #e43130;
	color: #fff;
	text-align: center;
	margin-top: 40rpx;
	font-size: 28rpx;
}

/* 选择配送方式弹窗 */
.popup-hide .popup-box.height-auto {
	height: auto;
}

.popup-hide .con-tit {
	display: flex;
	justify-content: space-around;
	align-items: center;
	font-size: 28rpx;
	font-weight: bold;
	padding: 30rpx;
}

.popup-hide .con-tit .sure {
	font-size: 0;
	width: 32rpx;
	height: 32rpx;
}

.popup-hide .con-tit .sure image {
	width: 100%;
	height: 100%;
}

.popup-hide .con-tit .tit-text {
	flex: 1;
	text-align: center;
}

.popup-hide .pop-con {
	padding: 0 30rpx;
}

.popup-hide .pop-con .distribution-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 0;
	border-top: 2rpx solid #eee;
	font-size: 28rpx;
}

/* 可用地址弹窗 */

.popup-hide .address-list {
	height: 60%;
}

.popup-hide .address-list .con-tit {
	position: relative;
}

.popup-hide .address-list .con-tit::after {
	position: absolute;
	left: 30rpx;
	right: 30rpx;
	bottom: 0;
	display: block;
	width: auto;
	height: 2rpx;
	content: " ";
	font-size: 0;
	background: #eee;
}

.popup-hide .address-box {
	padding: 0 30rpx;
	height: calc(100% - 104rpx);
	overflow-y: auto;
}

.popup-hide .address-box::-webkit-scrollbar {
	width: 0;
	height: 0;
}

.popup-hide .address-box .item {
	display: flex;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 2rpx solid #eee;
}

.popup-hide .address-box .item:last-child {
	border: 0;
}

.popup-hide .address-box .item .text-box {
	flex: 1;
	margin-right: 20rpx;
	font-size: 26rpx;
}

.popup-hide .address-box .item .text-box .address {
	line-height: 36rpx;
	word-break: break-word;
}

.popup-hide .address-box .item .text-box .address .default-address {
	padding: 2rpx 6rpx;
	border-radius: 2px;
	font-size: 10px;
	background: #e43130;
	color: #fff;
	display: inline-block;
	line-height: 24rpx;
	vertical-align: top;
	margin-right: 10rpx;
	margin-top: 4rpx;
}

.popup-hide .address-box .item .text-box .user {
	word-break: break-all;
	margin-top: 10rpx;
	color: #999;
}

/* 历史提货人弹窗 */
.popup-hide .history-user {
	height: auto;
	min-height: 45%;
}
.popup-hide .history-user .item .text-box .history-picker-user {
	display: flex;
}
.popup-hide .history-user .item .text-box .address .gray {
	color: #999;
	word-break: keep-all;
}

/* 选择提货时间弹窗 */
.popup-hide .raising-time {
	height: 60%;
}

.popup-hide .raising-time .time-box {
	height: calc(100% - 104rpx);
	display: flex;
	font-size: 24rpx;
	font-family: arial;
	border-top: 2rpx solid #eee;
}

.popup-hide .raising-time .time-box .day-box {
	background: #f5f5f5;
	width: 220rpx;
}

.popup-hide .raising-time .time-box .day-box .item {
	padding: 30rpx 0;
	height: 40rpx;
	line-height: 40rpx;
	text-align: center;
}

.popup-hide .raising-time .time-box .day-box .item.active {
	background: #fff;
}

.popup-hide .raising-time .time-box .hour-box {
	flex: 1;
	padding: 10rpx 30rpx;
	overflow-y: auto;
}

.popup-hide .raising-time .time-box .hour-box .item {
	display: flex;
	align-items: center;
	height: 40rpx;
	padding: 24rpx 0;
}

.popup-hide .raising-time .time-box .hour-box .item .number {
	flex: 1;
	line-height: 40rpx;
	font-size: 14px;
}

.red-word {
	color: #e43130;
}

/* 查看留言弹窗 */
.popup-hide .virtual-goods-msg-pop {
	height: auto;
}
.popup-hide .virtual-goods-msg-pop .con-tit .tit-text {
	text-align: left;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .msg-list {
	padding: 20rpx 30rpx;
	margin-bottom: 140rpx;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .msg-item {
	display: flex;
	margin-bottom: 30rpx;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .item-con {
	font-size: 24rpx;
	word-wrap: break-word;
	word-break: break-word;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .item-con.weak {
	color: #999999;
	margin-right: 20rpx;
	width: 180rpx;
	min-width: 180rpx;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .pop-foot {
	position: absolute;
	bottom: 0;
	width: 100%;
	text-align: center;
	padding: 0 20rpx;
	margin-bottom: 30rpx;
	margin-top: 20rpx;
	box-sizing: border-box;
	background: #fff;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .pop-foot .foot-btn {
	font-size: 26rpx;
	color: #fff;
	width: 100%;
	background: var(--primary-color);
	border-radius: 140rpx;
	padding: 20rpx 0;
	box-sizing: border-box;
}

/* 错误提示 */
.error .error-text {
	display: block;
	width: 100%;
	font-size: 28rpx;
	color: #e43130;
	text-align: left;
	margin-top: 10rpx;
}

.error .error-text .warning-icon {
	display: inline-block;
	color: #fff;
	width: 26rpx;
	height: 26rpx;
	line-height: 26rpx;
	background: #e43130;
	border-radius: 50%;
	text-align: center;
	margin-right: 12rpx;
	font-size: 22rpx;
}

.map{
	height: 100%;
}

.goOut{
	background-color: #fff;
	text-align: center;
}
.invoice {
	position: relative;
	display: flex;
	justify-content: space-between;
	box-sizing: border-box;
}
.invoice-title {
	flex: 1;
}
.invoice-arrow {
	flex: 4;
}
.text-arrow {
	position: relative;
	padding-right: 28rpx;
}
.text-arrow ::after{
	position: absolute;
	right: 8rpx;
	top: 45%;
	display: block;
	width: 14rpx;
	height: 14rpx;
	border: 2rpx solid #666;
	border-width: 2rpx 2rpx 0 0;
	content: " ";
	font-size: 0;
	transform: rotate(45deg) translateY(-50%);
}

/* 全部留言 */
.item.all-msg .free-box {
	max-width: 75%;
}
.item.all-msg .tit {
	min-width: 120rpx;
}
.item.all-msg .text-arrow {
	max-width: 100%;
	box-sizing: border-box;
}
.item.all-msg .text {
	display: flex;
	align-items: center;
	max-width: 100%;
}
.item.all-msg .text .msg-name {
	margin-right: 5px;
	flex-wrap: wrap;
}
.item.all-msg .text .msg-con {
	flex: 1;
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}


.physical-remark-list {
  margin-bottom: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
  padding-top: 30rpx;
  font-size: 12px;
}
.physical-remark-list .physical-remark-item {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #F2F2F2;
  padding: 20rpx;
}
.physical-remark-list .physical-remark-item .physical-remark-item-tit {
  flex-wrap: wrap;
  width: 180rpx;
  margin-right: 20rpx;
  word-break: break-word;
}
.physical-remark-list .physical-remark-item .physical-remark-item-int {
  font-size: 14px;
}
.physical-remark-list .physical-remark-item .stress {
  color: #E43130;
  margin-right: 10rpx;
  font-size: 26rpx;
}
.physical-remark-list .physical-remark-item .physical-remark-item-tit .uni-input-placeholder {
  color: #aaa;
  font-size: 14px;
}

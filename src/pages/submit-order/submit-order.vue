<template>
	<view>
		<!--pages/submit-order/submit-order.wxml-->
		<view class="container" :style="{height:isMap?'100vh':''}">
			<view class="submit-order" v-if="!isMap">
				<view class="bg"></view>

				<!-- 收货地址 -->
				<view class="distribution-mode" v-if="selectDistributionWay && mold !== 1">
					<view class="item-box">
						<view :class="[isDistribution ? 'active' : '' , 'item']" @tap='changeDistribution(0)'>{{i18n.mailToHome}}</view>
						<view :class="[isDistribution ? '' : 'active' , 'item']" @tap='changeDistribution(1)'>{{i18n.pickStore}}</view>
					</view>
				</view>
				<!-- 邮寄到家 -->
				<view class="address-box" v-if="isDistribution && mold !== 1">
					<view v-if="!userAddr || isEditAddr">
						<view class="tit">
							<view class="text" v-if="!addressList.length">{{i18n.fillReceivingInformation}}</view>
							<view class="text" v-if="addressList.length">{{i18n.historicalAddress}}</view>
							<view class="total" v-if="addressList.length" @tap='AddressListPop'>{{i18n.inTotal}}{{addressList.length}}{{i18n.itemGe}}</view>
						</view>
						<view class="add-box">
							<view class="add-item">
								<view class="text">{{i18n.consignee}}</view>
								<input type="text" class="input" :value="receiver" maxlength="15" @input="onReceiverInput" :placeholder="i18n.consigneeTips" @click="hideTabbar" @focus="hideTabbar" @blur="showTabbar"/>
							</view>
							<view class="add-item">
								<view class="text">{{i18n.mobilePhone}}</view>
								<input type="text" class="input" :value="mobile" :placeholder="i18n.enterContactNumber" maxlength="11" @input="onMobileInput" @click="hideTabbar" @focus="hideTabbar" @blur="showTabbar"/>
							</view>
							<view class="add-item" @tap="translate">
								<view class="text">{{i18n.yourRegion}}</view>
								<view class="area" @input="onMobileInput" @click="hideTabbar" @focus="hideTabbar" @blur="showTabbar">
									<view class="" v-if="province">{{province + ' ' + city + ' ' + area}}</view>
									<view class="placeholder-text" v-if="!province || !saveEditFlag">{{i18n.selectProvinceCity}}</view>
									<view class="animation-element-wrapper" :animation="animation" :style="'visibility:' + (show ? 'visible':'hidden')"
									 @tap.stop="hiddenFloatViewScreenClick">
										<view class="animation-element" @tap.stop="nono">
											<text class="right-bt" @tap.stop="hiddenFloatView">{{i18n.confirm}}</text>
											<view class="line"></view>
											<picker-view indicator-style="height: 50rpx;" :value="value" @change="bindChange" @tap.stop="nono">
												<!--省-->
												<picker-view-column>
													<view v-for="(item, index) in provArray" :key="index">{{item.areaName}}</view>
												</picker-view-column>
												<!--地级市-->
												<picker-view-column>
													<view v-for="(item, index) in cityArray" :key="index">{{item.areaName}}</view>
												</picker-view-column>
												<!--区县-->
												<picker-view-column>
													<view v-for="(item, index) in areaArray" :key="index">{{item.areaName}}</view>
												</picker-view-column>
											</picker-view>
										</view>
									</view>
								</view>
							</view>
							<view class="add-item">
								<view class="text">{{i18n.detailedAddress}}</view>
								<input type="text" maxlength="50" class="input" :value="addr" @input="onAddrInput" :placeholder="i18n.enteDetailedAddress" @click="hideTabbar" @focus="hideTabbar" @blur="showTabbar"/>
								<image class="addres-icon" :src="`${staticPicDomain}images/icon/submit-address.png`" @tap="selMap"></image>
							</view>
							<view class="add-item">
								<view class="btn" @tap="onSaveAddr">{{i18n.saveAndUse}}</view>
							</view>
						</view>
					</view>
					<view class="current-address" v-if="userAddr && !isEditAddr">
						<view class="c-address">{{userAddr.province}}{{userAddr.city}}{{userAddr.area}}{{userAddr.addr}}</view>
						<view class="c-user">{{userAddr.receiver}} {{userAddr.mobile}}</view>
						<view class="c-edit" @tap="toAddrListPage">
							<image :src="`${staticPicDomain}images/icon/revise.png`"></image>
						</view>
					</view>
					<view class="choose-way" @tap="distributionPop" v-if="canChooseSameCity">
						<view class="text">{{i18n.deliveryMethod}}</view>
						<view class="go">{{dvyType==1?i18n.expressDelivery:dvyType==4?i18n.sameDelivery:i18n.selectDeliveryMethod}}</view>
					</view>
				</view>
				<!-- /邮寄到家 -->

				<!-- 到店自提 -->
				<view class="address-box" v-if="!isDistribution && mold !== 1">
					<!-- 选择自提点 -->
					<view class="self-raising">
						<view class="tit">
							<view class="text">{{i18n.pickup}}</view>
						</view>
						<view class="choose-store" @tap="goSelectStore">
							<image :src="`${staticPicDomain}images/icon/submit-address.png`" class="img"></image>
							<view class="text" v-if="selStationItem.province">{{selStationItem.province}}{{selStationItem.city}}{{selStationItem.area}}{{selStationItem.addr}}</view>
							<view class="text" v-if="!selStationItem.province">{{i18n.selectPickUpAddress}}</view>
						</view>
					</view>
					<!-- 历史提货人 -->
					<view class="raising-user">
						<view class="tit">
							<view class="text" v-if="false">{{i18n.fillPersonInformation}}</view>
							<view class="text">{{i18n.historicalPickPerson}}</view>
							<view class="total" @tap="raisingUserList" v-if="stationUserInfo">{{i18n.inTotal}}{{stationUserInfo?stationUserInfo.length:'0'}}{{i18n.itemGe}}</view>
						</view>

						<view class="user-info">
							<input type="text" class="input" :disabled="disabled" :value="stationUserName" @input="getConsigneeInt"
							 :placeholder="i18n.enterNamePerson" maxlength="15" @click="hideTabbar" @focus="hideTabbar" @blur="showTabbar"/>
						</view>
						<view :class="['item',errorTips ? 'error':'']">
							<view class="user-info">
								<input type="text" class="input" :disabled="disabled" :value="stationUserMobile" @input="getConMobileInt"
								 :placeholder="i18n.enterPhonePerson" maxlength="11" @click="hideTabbar" @focus="hideTabbar" @blur="showTabbar"/>
							</view>
							<view class="error-text" v-if="errorTips==1"><text class="warning-icon">!</text>{{i18n.phoneWarn}}</view>
						</view>
					</view>
					<!-- 提货时间 -->
					<view class="choose-way" @tap="raisingTimePop">
						<view class="text">{{i18n.takeDeliveryTime}}</view>
						<view class="go">{{timeContent?dateContent+' '+timeContent:i18n.choosePickUpTime}}</view>
					</view>
				</view>
				<!-- /到店自提 -->

				<view class="shop-item">
					<view v-for="(shopCart, index) in shopCartOrders" :key="index">
						<!-- 店铺信息 -->
						<view class="shop-box">
							<view class="shop-icon">
								<image :src="`${staticPicDomain}images/icon/shop.png`"></image>
							</view>
							<view class="shop-name">{{shopCart.shopName}}</view>
						</view>
						<!-- /店铺信息 -->

						<!-- 店铺商品明细 -->
						<view class="prod-item">
							<view v-for="(shopCartItem, index2) in shopCart.shopCartItemDiscounts" :key="index2">
								<view :class="['prod-block',shopCartItem.chooseDiscountItemDto || shopCartItem.chooseComboItemDto?'discount':'']">
									<!-- 满减提示 -->
									<!-- <view class='discount-tips' :hidden='!shopCartItem.chooseDiscountItemDto' v-if="shopCartItem.chooseDiscountItemDto && preSellStatus!=1">
										<text class='text-block'>{{ [i18n.amount, i18n.pieces, i18n.amountDiscount, i18n.piecesDiscount][shopCartItem.chooseDiscountItemDto.discountRule] }}</text>
										<text class='text-list'>{{parseDiscountMsg(shopCartItem.chooseDiscountItemDto.discountRule,shopCartItem.chooseDiscountItemDto.needAmount,shopCartItem.chooseDiscountItemDto.discount,curLang)}}</text>
										<text class='reduce-amount'>{{i18n.haveDiscount}} ￥{{toFixxed(shopCartItem.chooseDiscountItemDto.reduceAmount)}}</text>
									</view> -->
									<!-- 套餐提示 -->
									<view class='discount-tips' :hidden='!shopCartItem.chooseComboItemDto' v-if="shopCartItem.chooseComboItemDto && preSellStatus!=1">
										<text class='text-block'>{{ i18n.packages }}</text>
										<text class='text-list'>{{ shopCartItem.chooseComboItemDto.name }}</text>
									  <text class='reduce-amount'>{{i18n.haveDiscount}} ￥{{toFixxed(shopCartItem.chooseComboItemDto.preferentialAmount)}}</text>
									</view>
									<!-- 商品信息 -->
									<view class="item-box">
										<view :class="[preSellStatus==1?'pre-sell' : '','item-cont']" v-for="(item, prodId) in shopCartItem.shopCartItems" :key="prodId">
											<view class="info-row">
												<view class="prod-pic">
													<image :src="item.pic"></image>
												</view>
												<view class="prod-info">
													<view class="prodname">{{item.prodName}}</view>
													<view class="prod-info-cont">{{item.skuName || ''}}</view>
													<view class="price-nums">
														<view class="prodprice">
															<text class="symbol">￥</text>
															<text class="big-num">{{parsePrice(shopCartItem.chooseComboItemDto ? item.comboPrice : item.price)[0]}}</text>
															<text class="small-num">.{{parsePrice(shopCartItem.chooseComboItemDto ? item.comboPrice : item.price)[1]}}</text>
														</view>
														<view class="prodcount">×{{item.prodCount}}</view>
													</view>
												</view>
											</view>
                      <view class="physical-remark-list-wrap" v-if="mold !== 1">
                        <view class="physical-remark-list">
                          <template v-for="physicalRemarkItem in virtualRemarkList">
                            <view class="physical-remark-item"  v-if="physicalRemarkItem.prodId === item.prodId">
                              <view class="physical-remark-item-tit"><text v-if="physicalRemarkItem.isRequired" class="stress">*</text>{{physicalRemarkItem.name}}</view>
                              <input class="physical-remark-item-int" v-model="physicalRemarkItem.value" :placeholder="i18n.pleaseEnter + `${physicalRemarkItem.name}`" maxlength="20" />
                            </view>
                          </template>
                        </view>
                      </view>
											<!-- 赠品信息 -->
											<view v-if="item.giveaway" class="gift-con">
												<view v-for="(giveawayItem, giveawayIndex) in item.giveaway.giveawayProds" :key="giveawayIndex" class="gift-item">
													<view class="gift-name">【{{i18n.Giveaways}}】{{ giveawayItem.prodName }}<text v-if="giveawayItem.skuName" class="gift-sku">{{giveawayItem.skuName||''}}</text></view>
													<view class="gift-count">×{{ giveawayItem.giveawayNum }} </view>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>

						</view>
						<!-- /店铺商品明细 -->

						<!-- 店铺优惠券和买家留言 -->
						<view class="msg-item">
							<!-- 配送方式 -->
							<view v-if="mold !== 1 && isDistribution" class="item">
								<view class="tit">{{i18n.deliveryMethod}}：</view>
								<view class="free-box">
									<view class="free">{{i18n.courier}}</view>
									<view class="price black">
										<text class="symbol">￥</text>
										<text class="small-num">{{ parsePrice(shopCart.transfee)[0] }}</text>
										<text class="small-num">.{{ parsePrice(shopCart.transfee)[1] }}</text>
									</view>
								</view>
							</view>

							<!-- 全部留言 -->
							<view v-if="!isCustomUserMessage && mold === 1 && virtualRemarkList.length" class="item invoice all-msg">
								<view class="tit">{{i18n.allMsg}}：</view>
								<view class="free-box">
									<view class="text-arrow" @tap="showViewMsgPopup">
										<view class="text">
											<view class="msg-name">{{virtualRemarkList[0].name}}</view>
											<view class="msg-con">{{virtualRemarkList[0].value}}</view>
										</view>
									</view>
								</view>
							</view>
							<!-- / 全部留言 -->

							<!-- 店铺优惠券 -->
							<view class="item coupon" @tap="showCouponPopup" :data-index="index">
								<view class="text-box">
									<text class="text">{{i18n.coupon}}：</text>
									<text class="number" v-if="!shopCart.shopCoupons.canUseLength">{{i18n.notAvailable}}</text>
									<text class="number" v-else>{{shopCart.shopCoupons.canUseLength}}&nbsp;{{i18n.zhangAvailable}}</text>
								</view>
								<view class="amount" v-if="shopCart.shopCoupons.couponAmount>0">
									<text class="symbol">-￥</text>
									<text class="big-num">{{parsePrice(shopCart.shopCoupons.couponAmount)[0]}}</text>
									<text class="small-num">.{{parsePrice(shopCart.shopCoupons.couponAmount)[1]}}</text>
								</view>
							</view>
							<!-- / 店铺优惠券 -->
							<!-- 订单备注 -->
							<view class="item">
								<view class="tit">{{i18n.OrderNotes}}：</view>
								<input class="input" maxlength="100" :placeholder="i18n.storeNotesTips" :value="shopCart.remarks" :data-index="index" @input="onRemarkIpt" @click="hideTabbar" @focus="hideTabbar" @blur="showTabbar"/>
							</view>
							<!-- <view class="item invoice">
								<view class="tit invoice-title">{{i18n.invoice.onvoiceIssuance}}：</view>
								<view v-if="!shopCart.invoiceDataFrom || shopCart.invoiceDataFrom.invoiceType === 2" @tap="showInvoicePopup(shopCart.shopId)">
									<view class=" text-arrow">
										<view class="text">{{i18n.invoice.noInvoice}}</view>
									</view>
								 </view>
								 <view class='invoice-arrow' v-else  @tap="showInvoicePopup(shopCart.shopId,shopCart.invoiceDataFrom)">
									<view class="text-arrow">
									  <view class="text">本次不开具发票</view>  这里有一行注释
									  <view class="text">{{ i18n.invoice.electronic }}({{i18n.invoice.productDetails}}-{{ shopCart.invoiceDataFrom.headerName || i18n.invoice.personal }})</view>
									</view>
								 </view>
							</view> -->
						</view>
						<!-- /店铺优惠券和买家留言 -->
					</view>

					<!-- 不满足当前配送方式的商品 -->
					<view class="useless" v-if="filterShopItems && filterShopItems.length > 0">
						<view class="u-reason">{{i18n.productsNotSupported}}{{['',i18n.expressDelivery,i18n.pickStore,'',i18n.sameDelivery][dvyType]}}，{{i18n.chooseAnotherDelivery}}</view>
						<scroll-view scroll-x="true" class="u-con">
							<view class="u-box">
								<view class="u-prods" v-for="(item,index) in filterShopItems" :key="index">
									<image :src="item.pic"></image>
								</view>
							</view>
						</scroll-view>
					</view>

				</view>

				<!-- 总金额计算 -->
				<view class="order-msg">
					<!-- 平台优惠券 -->
					<view class="item bb" @tap="showCouponPopup" data-index="-1">
						<view class="text-box">
							<text class="tit">{{i18n.platformCoupons}}：</text>
							<text class="number" v-if="!platformCoupons.canUseLength">{{i18n.notAvailable}}</text>
							<text class="number" v-else>{{platformCoupons.canUseLength}}{{i18n.zhangAvailable}}</text>
						</view>
						<view class="amount" v-if="platformCoupons.couponAmount > 0">
							<text class="symbol">-￥</text>
							<text class="big-num">{{parsePrice(platformCoupons.couponAmount)[0]}}</text>
							<text class="small-num">.{{parsePrice(platformCoupons.couponAmount)[1]}}</text>
						</view>
					</view>
					<!-- 会员积分 -->
					<view class="item coupon" v-if="maxUsableScore > 0 && preSellStatus!=1">
						<view class="member-points">
							<view class="integral-wrap">
								<view class="integral-deduction">
									<text class="tit">{{i18n.pointsDeduction}}： </text>
									<view class="integral-tips" v-if="isScorePay==1" @tap.stop="handleScorePop">
										<text class="tips" v-if="totalScoreAmount>0 && totalUsableScore>0">{{scorePlaceholder}}</text>
										<text class="tips" v-else>
											{{i18n.enterPoints}}
										</text>
										<image :src="`${staticPicDomain}images/icon/revise.png`"></image>
										<view class="integral-right-select">
											<label>
												<checkbox color="#E43130" value="isScorePay" @tap.stop="useMemberPoints" :checked="isChecked"></checkbox>
											</label>
										</view>
									</view>
									<view class="integral-tips" v-if="isScorePay==0">
										<text class="tips">{{i18n.notUsePoints}}</text>
										<view class="integral-right-select">
											<label>
												<checkbox color="#E43130" value="isScorePay" @tap.stop="useMemberPoints" :checked="isChecked"></checkbox>
											</label>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<!-- 商品总额 -->
					<view class="item">
						<view class="item-tit">{{i18n.comTotal}}：</view>
						<view class="price black">
							<text class="symbol">￥</text>
							<text class="big-num">{{parsePrice(total)[0]}}</text>
							<text class="small-num">.{{parsePrice(total)[1]}}</text>
						</view>
					</view>
					<!-- 应付运费 -->
					<view class="item">
						<view class="item-tit">{{i18n.freightPayable}}：</view>
						<view class="price black">
							<text class="symbol">￥</text>
							<text class="big-num">{{parsePrice(transfee)[0]}}</text>
							<text class="small-num">.{{parsePrice(transfee)[1]}}</text>
						</view>
					</view>
					<!-- 平台开启会员包邮(运费减免) -->
					<view class="item" v-if="freeTransfee">
						<view class="item-tit">{{i18n.memberPackage}}：</view>
						<view class="price">
							<text class="symbol">-￥</text>
							<text class="big-num">{{parsePrice(freeTransfee)[0]}}</text>
							<text class="small-num">.{{parsePrice(freeTransfee)[1]}}</text>
						</view>
					</view>
					<!-- 会员等级折扣 -->
					<view class="item" v-if="totalLevelAmount">
						<view class="item-tit">{{i18n.memberDiscountAmount}}：</view>
						<view class="price">
							<text class="symbol">-￥</text>
							<text class="big-num">{{parsePrice(totalLevelAmount)[0]}}</text>
							<text class="small-num">.{{parsePrice(totalLevelAmount)[1]}}</text>
						</view>
					</view>
					<!-- 店铺优惠总额（优惠券+满减满折促销） -->
					<view class="item" v-if="preSellStatus !== 1 && orderShopReduce">
						<view class="item-tit">{{i18n.storeDiscount}}：</view>
						<view class="price">
							<text class="symbol">-￥</text>
							<text class="big-num">{{parsePrice(orderShopReduce)[0]}}</text>
							<text class="small-num">.{{parsePrice(orderShopReduce)[1]}}</text>
						</view>
					</view>
				</view>
				<!-- /总金额计算 -->
        <CustomUserMessage ref="customUserMessageRef" v-if="virtualRemarkList.length && isCustomUserMessage" :virtualRemarkList="virtualRemarkList" />
			</view>


			<!-- 腾讯地图组件 -->
			<view class="map" v-if="isMap">
				<view class="goOut" @click="isMapReturn">返回</view>
				<iframe id="mapPage"
						width="100%"
						height="100%"
						frameborder=0
						:src="`https://apis.map.qq.com/tools/locpicker?search=1&type=1&policy=1&key=${key}&referer=myappp`">
				</iframe>
				<!-- end 腾讯地图组件 -->
			</view>

			<!-- 底部栏 -->
			<view v-if="tabbar" class="submit-order-footer">
				<view class="sub-order">
					<view class="item-txt">{{i18n.total}}：<view class="price">
							<text class="symbol">￥</text>
							<text class="big-num">{{parsePrice(actualTotal)[0]}}</text>
							<text class="small-num">.{{parsePrice(actualTotal)[1]}}</text>
						</view>
					</view>
				</view>
				<view class="footer-box" @tap="toPay">{{i18n.submitOrders}}</view>
			</view>
		</view>

		<!-- 选择优惠券弹窗 -->
		<view class="popup-hide" v-if="popupShow">
			<view class="popup-box">
				<view class="popup-tit">
					<text>{{i18n.coupon}}</text>
					<view class="close" @tap="closePopup"></view>
				</view>
				<view class="coupon-tabs">
					<view :class="'coupon-tab ' + (couponSts==1?'on':'')" @tap="changeCouponSts" data-sts="1">{{i18n.availableCoupons}}({{showCoupons.canUseCoupons.length}})</view>
					<view :class="'coupon-tab ' + (couponSts==2?'on':'')" @tap="changeCouponSts" data-sts="2">{{i18n.unavailableCoupons}}({{showCoupons.unCanUseCoupons.length}})</view>
				</view>
				<view :class="'popup-cnt ' + (couponSts==2? 'on':'')">
					<!-- 可用 -->
					<view class="coupon-con" v-if="couponSts == 1">
						<block v-for="(item, index) in showCoupons.canUseCoupons" :key="index">
							<coupon :item="item" :order="order" :index="index" :isShopCoupon="isShopCoupon" @checkCoupon="checkCoupon" :canUse="isCanUse"></coupon>
						</block>
					</view>
					<!-- 不可用 -->
					<view class="coupon-con" v-if="couponSts == 2">
						<block v-for="(item, index) in showCoupons.unCanUseCoupons" :key="index">
							<coupon :item="item" :order="order" :canUse="!isCanUse" :isShopCoupon="isShopCoupon"></coupon>
						</block>
					</view>
					<view class="botm-empty" v-if="couponSts==1 && !showCoupons.canUseCoupons.length">{{i18n.getCouponTips}}</view>
					<view class="botm-empty" v-if="couponSts==2 && !showCoupons.unCanUseCoupons.length">{{i18n.NoRelevantCoupons}}</view>
				</view>
				<view class="coupon-ok" v-if="couponSts==1">
					<view class="btn" @tap="choosedCoupon">{{i18n.confirm}}</view>
				</view>
			</view>
		</view>
		<!-- 积分输入框弹窗 -->
		<view class="popup-hide" :hidden="!showScorePop">
			<view class="score-pop">
				<view class="popup-tit">
					<text>{{i18n.modifyDeductiblePoints}}</text>
					<view class="close" @tap="closePopup"></view>
				</view>
				<view class="score-pop-con">
					<view class="score-pop-item">
						<input class="score-int" type="number" :value="userUseScore" :placeholder="i18n.enterPoints"
						 @input="handleScoreInput" maxlength="8" @click="hideTabbar" @focus="hideTabbar" @blur="showTabbar"/>
						<text class="usable-tips">{{orderScorePlaceHolder}}</text>
						<view class="usable-tips" v-if="shopUseScore > 100">{{i18n.multipleOf10}}</view>
					</view>
					<view class="score-pop-item">
						<view class="confirm-btn" @tap="confirmScore">{{i18n.confirm}}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 选择配送方式弹窗 -->
		<view class="popup-hide" :hidden="!showDistributionPop">
			<view class="popup-box height-auto">
				<view class="con-tit">
					<view class="close" @tap="closePopup"></view>
					<view class="tit-text">{{i18n.selectDeliveryMethod}}</view>
					<!-- <view class="sure"><image :src="`${staticPicDomain}images/icon/sure.png`"></image></view> -->
				</view>
				<view class="pop-con">
					<!-- <view class="distribution-item">
						<view class="text">普通快递</view>
						<checkbox class="check" checked="isChecked"></checkbox>
					</view>
					<view class="distribution-item">
						<view class="text">同城配送</view>
						<checkbox class="check" checked="isChecked"></checkbox>
					</view> -->
					<radio-group @change="disRadioChange">
						<label class="distribution-item">
							<view class="text">{{i18n.expressDelivery}}</view>
							<radio color="#eb2444" class="check" value="1" :checked="dvyType==1"></radio>
						</label>
						<label class="distribution-item">
							<view class="text">{{i18n.sameDelivery}}</view>
							<radio color="#eb2444" class="check" value="4" :checked="dvyType==4"></radio>
						</label>
					</radio-group>

				</view>
			</view>
		</view>

		<!-- 可用地址弹窗 -->
		<view class="popup-hide" :hidden="!showAddressListPop">
			<view class="popup-box address-list">
				<view class="con-tit">
					<view class="close" @tap="closePopup"></view>
					<view class="tit-text">{{i18n.optionalAddress}}</view>
					<!-- <view class="sure"><image :src="`${staticPicDomain}images/icon/sure.png`"></image></view> -->
				</view>
				<view class="address-box">
					<radio-group @change="addrRadioChange" v-for="(item, index) in addressList" :key="index">
						<view class="item" @tap="selectAddr(item)">
							<view class="text-box">
								<view class="address"><text class="default-address" v-if="item.commonAddr == 1">{{i18n.default}}</text>{{item.province}}{{item.city}}{{item.area}}{{item.addr}}</view>
								<view class="user">{{item.receiver}} {{item.mobile}}</view>
							</view>
							<radio color="#eb2444" class="check" :value="String(item.addrId)" :checked="addrId == item.addrId"></radio>
						</view>
					</radio-group>
				</view>
			</view>
		</view>

		<!-- 历史提货人弹窗 -->
		<view class="popup-hide" :hidden="!showRaisingUserList">
			<view class="popup-box address-list history-user">
				<view class="con-tit">
					<view class="close" @tap="closePopup"></view>
					<view class="tit-text">{{i18n.historicalPickPerson}}</view>
					<!-- <view class="sure"><image :src="`${staticPicDomain}images/icon/sure.png`"></image></view> -->
				</view>
				<view class="address-box" v-if="stationUserInfo && stationUserInfo.length > 0">
					<radio-group @change="stationRadioChange" v-for="(item,index) in stationUserInfo" :key="index">
						<view class="item">
							<view class="text-box">
								<view class="address history-picker-user">
									<view class="gray">{{i18n.pickUpPerson}}：</view>
									<view>{{item.stationUserName}}</view>
								</view>
								<view class="address history-picker-user">
									<view class="gray">{{i18n.phoneNumber}}：</view>
									<view>{{item.stationUserMobile}}</view>
								</view>
							</view>
							<radio color="#eb2444" class="check" :value="String(index)" :checked="stationIdx == index" @tap="getStationItem(item)"></radio>
						</view>
					</radio-group>
				</view>
			</view>
		</view>

		<!-- 选择提货时间弹窗 -->
		<view class="popup-hide" :hidden="!showRaisingTimePop">
			<view class="popup-box raising-time">
				<view class="con-tit">
					<view class="tit-text">{{i18n.choosePickUpTime}}</view>
					<view class="close" @tap="closePopup"></view>
				</view>
				<view class="time-box">
					<view class="day-box" v-if="selStationItem && timeParams">
						<view :class="['item', dateIndex==dateIdx?'active':'']" v-for="(item,dateIdx) in timeParams" :key="dateIdx" @tap="changeData(item,dateIdx)">{{item.dateTime}}</view>
						<!-- <view class="item">07-10(后天)</view> -->
					</view>
					<block v-if="timeParams[dateIndex]">
						<view class="hour-box" v-if="timeParams[dateIndex].hourTimes">
							<radio-group v-for="(timeItem,timeIdx) in timeParams[dateIndex].hourTimes" :key="timeIdx">
								<view class="item" @tap="changeTime(timeItem,timeIdx)">
									<view :class="['number', timeIndex==timeIdx?'red-word':'']">{{timeItem}}</view>
									<radio color="#eb2444" class="check" :checked="timeIndex==timeIdx" :value="String(timeIdx)" v-if="timeIndex==timeIdx"></radio>
								</view>
							</radio-group>
						</view>
					</block>
				</view>
			</view>
		</view>

		<!-- 查看留言（虚拟商品、默认留言） -->
		<view class="popup-hide" :hidden="!showViewMsg">
			<view class="popup-box virtual-goods-msg-pop">
				<view class="con-tit">
					<view class="tit-text">{{i18n.viewMsg}}</view>
					<view class="close" @tap="closePopup"></view>
				</view>
				<view class="msg-pop-con">
					<view class="msg-list">
						<view v-for="(item, index) in virtualRemarkList" :key="index" class="msg-item">
							<view class="item-con weak">{{item.name}}</view>
							<view class="item-con">{{item.value}}</view>
						</view>
					</view>
					<view class="pop-foot">
						<view class="foot-btn" @tap="closePopup">{{i18n.gotIt}}</view>
					</view>
				</view>
			</view>
		</view>

		<invoiceEdit
		  v-if="isShowInvoicePopup"
		  :invoice-data-from="invoiceDataFrom"
		  :shop-id="invoiceShopId"
		  :invoice-id="invoiceId"
		  @closePopup="closePopup"
		  @getInvoiceData="getInvoiceData" />

    <Pay ref="payRef" />
    <NLoading />
	</view>
</template>

<script>
	var http = require("../../utils/http.js");
	var util = require("../../utils/util.js");
	var t = 0;
	var show = false;
	var moveY = 200;
	import coupon from "../../components/orderCoupon/coupon";
	import invoiceEdit from '../../components/invoiceEdit/index'
  import { requestServiceExpirationMessage } from '@/utils/wx-subscribe-message'
  import Pay from '@/components/pay/pay'
  import CustomUserMessage from '@/components/custom-user-message'
  import NLoading from '@/components/n-loading/n-loading'

	export default {
		data() {
			return {
				popupShow: false,
				orderEntry: "0", // 订单入口 0购物车 1立即购买
				prodType: 0, // 商品类型(0普通商品 1拼团 2秒杀 3积分)
				userAddr: null,
				orderItems: [],
				shopCartOrders: [],
				//所有店铺的数据
				couponSts: 1,
				platformCoupons: {
					canUseLength: 0,
					canUseCoupons: [],
					unCanUseCoupons: []
				},
				// 平台优惠券数据
				showCoupons: {
					canUseCoupons: [],
					unCanUseCoupons: []
				},
				actualTotal: 0,
				total: 0,
				totalCount: 0,
				transfee: 0,
				reduceAmount: 0, //减免金
				couponIds: [],
				couponUserIds: [],
				userChangeCoupon: 0, // 用户有没有对优惠券进行改变
				orderReduce: 0,
				choose: true,
				totalScoreAmount: 0, //积分抵扣金额
				totalUsableScore: 0, //整个订单可以使用的积分数
				isScorePay: 0, //用户是否选择积分抵现(0不使用 1使用 默认不使用)
				isChecked: false, //是否选择会员积分抵现
				isProhibit: false, //（积分抵现）checkbox是否禁止
				totalLevelAmount: 0, //等级抵扣金额
				freeTransfee: 0, //用户等级免运费金额
				orderShopReduce: '', // 店铺总优惠金额（满减满折+店铺优惠券）
				isCanUse: true,
				order: true,
				editorFlag: false, //用户点击编辑按钮所在区域隐藏
				userUseScore: '', // 积分数量
				scorePlaceholder: '', // 积分抵扣的占位符
				accountCanUseScore: 0, // 用户当前可用积分
				shopUseScore: '', // 积分抵现比例
				maxUsableScore: "", // 最大可用积分
				showScorePop: false, // 积分输入弹窗显隐
				isEditAddr: false, // 编辑地址
				showDistributionPop: false, // 选择物流方式弹窗
				addressList: [], // 地址列表
				showAddressListPop: false, // 可用地址弹窗
				isDistribution: true, // 配送方式tab状态
				showRaisingUserList: false, //选择历史提货人弹窗
				showRaisingTimePop: false, //选择提货时间弹窗
				dvyType: 1, // 配送类型 1:快递 2:自提 3：无需快递 4:同城配送
				filterShopItems: [], // 过滤掉的商品项
				value: [0, 0, 0],
				provArray: [],
				cityArray: [],
				areaArray: [],
				province: "",
				city: "",
				area: "",
				provinceId: 0,
				cityId: 0,
				areaId: 0,
				receiver: "",
				mobile: "",
				addr: "",
				addrId: 0,
				lat:"",
				lng:"",
				animation: "",
				show: "",
				saveEditFlag:false,//弹窗确认选择地址
				region: "",
				stationUserName: '', // 提货人
				stationUserMobile: '', // 提货人手机号
				stationUserInfo: [], //历史提货人
				stationIdx: null, // 历史提货人选中index
				selStationItem: {}, // 选中的自提点数据
				timeParams: [],
				dateIndex: 0, // 日期下标
				timeIndex: null, // 时间下标
				timeContent: '', // 选中的时间
				dateContent: '', // 选中的日期
				selectDistributionWay: false, // 选择订单发货方式
				orderScorePlaceHolder: '', // 积分弹窗输入框订单可使用积分提示占位符
				disabled: false, // 是否禁止输入框输入
				selStation: '', // 是否选择自提点
				errorTips: 0, //错误提示
				tabbar: true,
				// 获取屏幕高度
				windowHeight: '',
				// 判断是否输入了地址
				boolenRes:1,  //1地址确认状态  //0地址未确认状态
				preSellStatus: '', // 是否预售商品
				curLang: uni.getStorageSync('lang'),
				storeOfferAmount: 0, //店铺优惠
				eventOfferAmount: 0,//活动优惠
				key:'L4DBZ-VSSKJ-GCDFX-KGBVR-KHZ2J-RNF2Q',
				isMap: false,
				isFirst: true,
				// 是否可选同城配送(多店铺不支持)
				canChooseSameCity: true,

				invoiceId: '', // 发票id
				invoiceShopId: '',
				invoiceDataList: [],
				invoiceDataFrom: {},
				isShowInvoicePopup: false,

				// 1虚拟商品
				mold: '',

				// 查看留言弹窗显隐
				showViewMsg: false,

				// 留言（虚拟商品和实物商品都用这个）
				virtualRemarkList: [],

				// 是否正在打开店铺优惠券弹窗
				isShopCoupon: null,

				pickUpPointSelected: false, // 是否已选自提点
        isCustomUserMessage: false, // 是否显示自定义用户留言
        isDistributionOrder: false, // 是否为分销订单
			};
		},

		components: {
			coupon,
			invoiceEdit,
      Pay,
      CustomUserMessage,
      NLoading
		},
		props: {},

		computed:{
			i18n() {
				return this.$t('index')
			}
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.setData({
				orderEntry: options.orderEntry,
				prodType: Number(options.prodType),
				mold: Number(options.mold),
				pickUpPointSelected: options.pickUpPointSelected || false
			});
			if (options.dvyType) {
				this.dvyType = options.dvyType
			}
			if (this.dvyType == 1 || this.dvyType == 4) { // 同城 || 邮寄
				this.isDistribution = true
			} else if (this.dvyType == 2) { // 自提
				this.isDistribution = false
			}
			this.initCityData(this.provinceId, this.cityId, this.areaId)
			this.loadAddressList()  //加载地址列表

			// 获取虚拟商品留言
			const virtualRemarkList = uni.getStorageSync('virtualRemark')
			if (virtualRemarkList && virtualRemarkList.length) {
				this.virtualRemarkList = JSON.parse(virtualRemarkList)
			} else {
				this.virtualRemarkList = []
			}


			if(this.pickUpPointSelected) {
				const info = uni.getStorageSync('selectedPickupinfor') || {}
				if (info) {
					this.stationUserName = info.stationUserName
					this.stationUserMobile = info.stationUserMobile
					this.stationIdx = info.stationIdx
				}
				// 清除选中的提货人信息
				uni.removeStorageSync('selectedPickupinfor')
			} else {
				// 清除选中的提货人信息
				uni.removeStorageSync('selectedPickupinfor')
			}
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {
			this.animation = wx.createAnimation({
				transformOrigin: "50% 50%",
				duration: 0,
				timingFunction: "ease",
				delay: 0
			});
			this.animation.translateY(200 + 'vh').step();
			this.setData({
				animation: this.animation.export(),
				show: show
			});
		},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			// 设置头部导航标题
			uni.setNavigationBarTitle({
				title: this.i18n.submitOrders
			})
			const selStationItem = uni.getStorageSync('selStationItem')
			if (selStationItem) {
				this.selStationItem = selStationItem
				this.timeParams = selStationItem.timeParams
			}
			uni.removeStorageSync('selStationItem')
			// 获取当前可用积分
			this.getUserLevelInfo()
			this.loadOrderData()
		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {
			if (this.mold === 1) {
				// 清除虚拟商品留言缓存
				uni.removeStorageSync('virtualRemark')
			}
		},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},

		methods: {

			/**
			 * 腾讯地图以及微信小程序详细地址请求
			 */
			selMap () {
				// #ifdef H5
				this.isMap = true
				this.tabbar = false
				let that = this
				window.addEventListener('message', function (event) {
					// 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
					var loc = event.data
					if (loc && loc.module === 'locationPicker') { // 防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
						that.addr = loc.poiaddress
						that.lat = loc.latlng.lat
						that.lng = loc.latlng.lng
						that.isMap = false
						that.tabbar = true
					}
				}, false)
				// #endif

				// #ifdef MP-WEIXIN
				let that = this
				uni.chooseLocation({
					success: function (res) {
						that.setMapData(res);
					},
					fail: function (failMsg) {
						uni.getSetting({
							success(res) {
								if (!res.authSetting['scope.userLocation']) {
									uni.authorize({
										scope: 'scope.userLocation',
										success() {
											uni.chooseLocation({
												success: function (res) {
													that.setMapData(res);
													console.log(res)
												}
											})
										},
										fail() {
											uni.showToast({
											title: that.i18n.authorityTips,
											icon: 'none'
											});
										}
									})
								}
							}
						})
					}
				})
				// #endif

				// #ifdef APP-PLUS
				let that = this
				uni.chooseLocation({
					success: function(res1) {
						uni.getLocation({
								type: 'wgs84',
								geocode:true,
								success: function (res) {
								that.setData({
									lat: res1.latitude,
									lng: res1.longitude,
									addr: res1.address,
								});
							}
						});
					}
				});
				// #endif
			},

			isMapReturn(){
				this.isMap = false;
				this.tabbar = true;
			},

			// 提交订单浮框的显隐
			showTabbar(){
				if (this.disabled == false) {
					this.tabbar = true
				}
			},
			hideTabbar(){
				if (this.disabled == false) {
					this.tabbar = true
				}
			},
			// 切换配送方式
			changeDistribution: function(sts) { // 邮寄或同城
				if (sts == 0) {
					this.isDistribution = true
					this.dvyType = 1
				} else if (sts == 1) { // 自提
					this.isDistribution = false
					this.dvyType = 2
				}
				this.loadOrderData()
			},

			// 选择配送方式
			disRadioChange(e) {
				this.dvyType = e.detail.value
				this.loadOrderData()
				this.showDistributionPop = false
			},

			// 选择地址
			addrRadioChange(e) {
				console.log(e)
				this.addrId = e.detail.value
				this.showAddressListPop = false
				this.isEditAddr = false
				this.getAddrDet()
			},
			selectAddr: function(item) {
				console.log(item)
				this.addrId = item.addrId
				this.showAddressListPop = false
				this.isEditAddr = false
				this.getAddrDet()
				this.setData({
					boolenRes:1
				})
			},

			// 选择历史提货人
			stationRadioChange(e) {
				console.log(e)
				this.stationIdx = e.detail.value
				this.showRaisingUserList = false
				this.raisingUserList()
			},
			getStationItem: function(item) {
				this.stationUserName = item.stationUserName
				this.stationUserMobile = item.stationUserMobile
				this.showRaisingUserList = false
				console.log(item)
				console.log('提货人：', this.stationUserName)
				console.log('提货人手机号：', this.stationUserMobile)
			},

			// 加载地址列表
			loadAddressList: function() {
				var ths = this;
				wx.showLoading();
				var params = {
					url: "/p/address/list",
					method: "GET",
					data: {},
					callBack: function(res) {
						ths.setData({
							addressList: res
						});
						wx.hideLoading();
					}
				};
				http.request(params);
			},
			//根据地址id请求地址详情
			getAddrDet: function() {
				wx.showLoading();
				var params = {
					url: "/p/address/addrInfo/" + this.addrId,
					method: "GET",
					data: {},
					callBack: (res) => {
						wx.hideLoading();
						this.userAddr = res
						this.loadOrderData()
						this.addr = ''
					}
				};
				http.request(params);
			},

			/**
			 * 获取会员积分详情
			 */
			getUserLevelInfo() {
				var params = {
					url: '/p/score/scoreInfo',
					method: 'GET',
					dontTrunLogin: true,
					data: {},
					callBack: res => {
						this.setData({
							accountCanUseScore: res.score
						});
					}
				};
				http.request(params);
			},

			// 会员积分抵现选择
			useMemberPoints: function() {
				this.isChecked = !this.isChecked;
				if (this.maxUsableScore > 0) {
					//totalUsableScore整个订单可以使用的积分数
					this.setData({
						isScorePay: this.isChecked ? 1 : 0,
						userUseScore: this.isChecked ? this.maxUsableScore : 0
					});
					this.loadOrderData();
				}
			},

			/**
			 * 积分输入弹窗显示
			 */
			handleScorePop() {
				this.setData({
					showScorePop: true
				});
			},

			/**
			 * 积分抵扣输入框
			 */
			handleScoreInput(e) {
				this.userUseScore = Number(e.detail.value.replace(/[^\d]/g,''))
			},

			/**
			 * 修改积分确定弹窗
			 */
			confirmScore() {
				if (!this.userUseScore || this.userUseScore<0) {
					uni.showToast({
						title: this.i18n.enterCorrectPoints,
						icon: 'none'
					})
					return
				}
				if (this.userUseScore > this.maxUsableScore) {
					uni.showToast({
						title: this.i18n.mostUserPoints + this.maxUsableScore + this.i18n.integral,
						icon: 'none'
					});
					return
				}
				if (this.userUseScore > this.accountCanUseScore) {
					uni.showToast({
						title: this.i18n.notEnough,
						icon: 'none'
					})
					return
				}
				this.loadOrderData();
				this.closePopup();
			},

      // 检查是否有相同商品的待支付订单
      async checkHasSameProdOrderIsPendingPay(prodId) {
        return new Promise((resolve, reject) => {
          http.request({
            url: '/p/myOrder/myOrder',
            method: 'GET',
            data: {
              status: 1,
              prodId,
            },
            callBack: res => {
              resolve({ result: res.records && res.records.length > 0 });
            },
            errCallBack: err => {
              resolve({ result: false })
            }
          })
        })
      },

			/**
			 * 加载订单数据
			 */
			loadOrderData: function() {
				var addrId = 0;
				if (this.userAddr != null) {
					addrId = this.userAddr.addrId;
				}
				uni.showLoading({
					// #ifndef MP-TOUTIAO
					mask: true
					// #endif
				});
				let dvyType = this.dvyType
        const reportParams = uni.getStorageSync('reportParams'); // 获取需要上报的参数
        const orderItem = this.orderEntry === "1" ? JSON.parse(wx.getStorageSync("orderItem")) : undefined
        const distributionCardNo = orderItem?.distributionCardNo
        this.isDistributionOrder = !!distributionCardNo
        // 如果有distributionCardNo，则是分销订单
				var params = {
					url: "/p/order/confirm",
					method: "POST",
					data: {
						addrId: addrId,
						orderItem: this.orderEntry === "1" ? JSON.parse(wx.getStorageSync("orderItem")) : undefined,
						basketIds: this.orderEntry === "0" ? JSON.parse(wx.getStorageSync("basketIds")) : undefined,
						couponIds: this.couponIds,
						couponUserIds: this.couponUserIds,
						dvyType: dvyType || 1, // 配送类型 1:快递 2:自提 3：无需快递 4:同城配送
						userChangeCoupon: this.userChangeCoupon,
						isScorePay: this.isScorePay,
						userUseScore: this.userUseScore ? this.userUseScore : 0,
            extra: reportParams ? JSON.stringify(reportParams) : undefined
					},
					callBack: res => {
						uni.hideLoading();
						let canChooseSameCity = res.shopCartOrders.length === 1 && res.shopCityStatus
						var shopCartOrders = res.shopCartOrders; // 购物车店铺商品信息列表
						var remarksList = []
						var couponIds = []
						let couponUserIds = []
						var storeOfferAmount = 0
						var eventOfferAmount = 0
						let selectDistributionWay = false
						if (!this.isFirst) {
							this.shopCartOrders.forEach(el => {
								remarksList.push(el.remarks)
							})
						}
						for (let i = 0; i < shopCartOrders.length; i++) {
							const shopCart = shopCartOrders[i]
							if (this.isFirst) {
								shopCart.remarks = '';
							} else {
								shopCart.remarks = remarksList[i]
							}
							shopCart.shopCoupons = this.splitCouponAndPushCouponIds(shopCart.coupons, couponIds, couponUserIds);
							// 店铺优惠券优惠金额
							storeOfferAmount += shopCart.shopCoupons.couponAmount
							eventOfferAmount += shopCart.discountReduce
							if (canChooseSameCity) {
								for (let j = 0; j < shopCart.shopCartItemDiscounts.length; j++) {
									const shopCartItemDiscount = shopCart.shopCartItemDiscounts[j]
									for (let k = 0; k < shopCartItemDiscount.shopCartItems.length; k++) {
										const prodItem = shopCartItemDiscount.shopCartItems[k]
										if (!prodItem.deliveryModeVO.hasCityDelivery) {
											canChooseSameCity = false
											break
										}
									}
								}
							}
							// 顶部切换配送方式显隐判断 (单店铺结算时才需要)
							if (shopCartOrders.length === 1) {
								selectDistributionWay = true
								for (let j = 0; j < shopCart.shopCartItemDiscounts.length; j++) {
									const shopCartItemDiscount = shopCart.shopCartItemDiscounts[j]
									for (let k = 0; k < shopCartItemDiscount.shopCartItems.length; k++) {
										const prodItem = shopCartItemDiscount.shopCartItems[k]
										// 如果是套餐商品, 需要所有商品都包含自提才能切换
										if (prodItem.comboId) {
											if (!prodItem.deliveryModeVO.hasUserPickUp) {
												selectDistributionWay = false
												break
											}
										} else {
											if (prodItem.deliveryModeVO.hasUserPickUp) {
												selectDistributionWay = true
												break
											} else {
												selectDistributionWay = false
											}
										}
									}
								}
							}
						}
						// 平台优惠券
						var platformCoupons = this.splitCouponAndPushCouponIds(res.coupons, couponIds, couponUserIds);
						// 运费
						var transfee = res.totalTransfee - res.freeTransfee
						var scorePlaceholder = this.i18n.use +' ' + res.totalUsableScore +' '+ this.i18n.pointsDeduction+ ' ' +res.totalScoreAmount + this.i18n.yuan;
						var orderScorePlaceHolder = this.i18n.accountSurplus+' ' + this.accountCanUseScore +' '+ this.i18n.accountSurplus2 + ' ' + res.maxUsableScore;

            const newData = {
              selectDistributionWay: selectDistributionWay,
              canChooseSameCity: canChooseSameCity, // 订单是否支持同城配送
              dvyType: res.dvyType,
              platformCoupons: platformCoupons, // 整个订单可以使用的优惠券列表
              shopCartOrders: shopCartOrders, // 所有的店铺的购物车信息
              actualTotal: res.actualTotal, // 实际总值
              total: res.total, // 商品总值
              totalCount: res.totalCount, // 商品总数
              userAddr: res.userAddr, // 地址Dto
              transfee: transfee, // 总运费
              orderReduce: res.orderReduce, // 订单优惠金额(所有店铺优惠金额和使用积分抵现相加)
              totalScoreAmount: res.totalScoreAmount, // 积分抵扣金额
              totalUsableScore: res.totalUsableScore, // 使用的积分数量
              isScorePay: res.isScorePay, // 用户是否选择积分抵现(0不使用 1使用 默认不使用)
              totalLevelAmount: res.totalLevelAmount, // 等级抵扣金额
              freeTransfee: res.freeTransfee, // 用户等级免运费金额
              couponIds: couponIds,
              couponUserIds: couponUserIds,
              maxUsableScore: res.maxUsableScore, // 整个订单可以使用的积分数
              scorePlaceholder: scorePlaceholder,
              orderScorePlaceHolder: orderScorePlaceHolder,
              shopUseScore: res.shopUseScore, // 积分抵现比例;
              filterShopItems: res.filterShopItems, // 过滤掉的商品项
              preSellStatus: res.preSellStatus, // 是否预售商品
              storeOfferAmount: storeOfferAmount, // 店铺优惠券优惠
              eventOfferAmount: eventOfferAmount, // 活动优惠
              orderShopReduce: res.orderShopReduce, // 店铺总优惠金额（促销满减+店铺优惠券）
              isFirst: false,
            }
            if ((res.virtualRemarkList.length > 0 && !!res.virtualRemarkList[0].type) || this.mold !== 1) {
              this.isCustomUserMessage = this.mold === 1 // 虚拟商品才需要使用自定义留言
              newData.virtualRemarkList = res.virtualRemarkList // deleteMe
            }
						this.setData(newData);

						this.disabledInput() // 是否禁止提货人输入框输入
						if (this.dvyType == 2) {
							this.getStationUserInfo() // 历史提货人
						}
						if (res.dvyType != dvyType) {
							this.loadOrderData()
						}
						this.setOrderInvoice()
					},
					errCallBack: async(err) => {
						uni.hideLoading()
            if (err.data.includes('限购')) {
              const { result } = await this.checkHasSameProdOrderIsPendingPay(orderItem.prodId)
              if (result) {
                uni.showModal({
                  title: this.i18n.tips,
                  content: "您已有该商品的待支付订单，请前往查看",
                  showCancel: false,
                  confirmText: '前往查看',
                  success: (modalSuccess) => {
                    if (modalSuccess.confirm) {
                      uni.redirectTo({
                        url: `/pages/orderList/orderList?status=1`
                      })
                    }
                  }
                })
              } else {
                uni.showModal({
                  title: this.i18n.tips,
                  content: "您已达到限购条件，暂不可购买，请前往商品详情页查看限购条件",
                  showCancel: false,
                  confirmText: this.i18n.confirm,
                  success: (modalSuccess) => {
                    if (modalSuccess.confirm) {
                      uni.navigateBack({
                        delta: 1,
                      })
                    }
                  }
                })
              }
            } else {
              uni.showModal({
                title: this.i18n.tips,
                content: err.data,
                showCancel: false, // 是否显示取消按钮
                confirmText: this.i18n.confirm,
                success: (modalSuccess) => {
                  if (modalSuccess.confirm) {
                    uni.navigateBack({
                      delta: 1,
                    })
                  }
                }
              })
            }
					}
				};
				http.request(params);
			},

			/**
			 * 分割优惠券成
			 * 1. canUseCoupons 可使用优惠券列表
			 * 2. unCanUseCoupons 不可使用优惠券列表
			 * 3. couponAmount 选中的优惠券可优惠金额
			 * 4. 将选中的优惠券ids保存起来
			 * @param {*} coupons 优惠券列表
			 * @param {*} couponIds 选中的优惠券id
			 * @param {*} couponUserIds 选中的优惠券的使用id
			 */
			splitCouponAndPushCouponIds(coupons, couponIds, couponUserIds) {
				if (!coupons || !coupons.length) {
					return {
						canUseLength: 0,
						couponAmount: 0,
						canUseCoupons: [],
						unCanUseCoupons: []
					}
				}

				let canUseCoupons = [];
				let unCanUseCoupons = [];
				let couponAmount = 0;
				coupons.forEach(coupon => {
					if (coupon.canUse) {
						canUseCoupons.push(coupon);
					} else {
						unCanUseCoupons.push(coupon);
					}

					if (coupon.choose) {
						couponIds.push(coupon.couponId);
						couponUserIds.push(coupon.couponUserId)
						couponAmount = coupon.reduceAmount;
					}
				});
				return {
					canUseLength: canUseCoupons.length,
					couponAmount: couponAmount,
					canUseCoupons: canUseCoupons,
					unCanUseCoupons: unCanUseCoupons
				};
			},

			/**
			 * 提交订单校验
			 */
			toPay: function() {
				if (this.dvyType != 2) {
					if (!this.userAddr && this.mold != 1) {
						wx.showToast({
							title: this.i18n.pleaseSelectSddress,
							icon: "none"
						});
						return;
					}
					if (!this.shopCartOrders.length) {
						var title = this.dvyType == 1 ? this.i18n.expressDelivery : this.dvyType == 4 ? this.i18n.sameDelivery : ''
						wx.showToast({
							title: this.i18n.productNotSupported + title,
							icon: "none"
						});
						return;
					}
					if (this.dvyType == 4 && (!this.userAddr.lat || this.userAddr.lat == 0 || !this.userAddr.lng || this.userAddr.lng ==
							0)) {
						wx.showModal({
							title: this.i18n.tips,
							content: this.i18n.cityAddressPrompt,
							showCancel: false,
							confirmText: this.i18n.confirm,
							success: res => {
								uni.navigateTo({
									url: '/packageUser/pages/editAddress/editAddress?addrId=' + this.userAddr.addrId
								})
							}
						})
						// wx.showToast({
						// 	title: this.i18n.cityAddressPrompt,
						// 	icon: "none",
						// 	duration: 3000
						// });
						return;
					}
				} else if (this.dvyType == 2) {
					var reg = /^\s+$/g
					if (!this.shopCartOrders.length) {
						wx.showToast({
							title: this.i18n.productNotSupportedStop,
							icon: "none"
						});
						return;
					}
					if (!this.selStationItem.province) {
						wx.showToast({
							title: this.i18n.selectPickPoint,
							icon: "none"
						});
						return;
					}
					if (reg.test(this.stationUserName) || reg.test(this.stationUserMobile)) {
						wx.showToast({
							title: this.i18n.inputAllSpace,
							icon: "none"
						});
						return;
					}
					if (!this.stationUserName || !this.stationUserMobile) {
						wx.showToast({
							title: this.i18n.fillDeliveryPersonInformation,
							icon: "none"
						});
						return;
					}
					if (!util.checkPhoneNumber(this.stationUserMobile)) {
						wx.showToast({
							title:  this.i18n.enterCorrectPhone,
							icon: "none"
						});
						return;
					}
					if (!this.dateContent || !this.timeContent) {
						wx.showToast({
							title: this.i18n.selectPickUpTime,
							icon: "none"
						});
						return;
					}
				}

				// 点击地址判断
				if((!this.boolenRes || this.addressList[0] === undefined) && this.dvyType!=2 && this.mold != 1){
					uni.showToast({
						title: this.i18n.saveAndUseTips,
						icon: 'none'
					})
					return
				}

				// 积分按钮选择判断
				if (this.isScorePay == 1 && this.userUseScore <= 0) {
					uni.showToast({
						title: this.i18n.enterPoints,
						icon: 'none'
					})
					return
				}

        // 实物留言判断
        if (this.mold !== 1 && this.virtualRemarkList.length > 0) {
          if (this.virtualRemarkList.find(el => el.value && !el.value.trim())) {
            uni.showToast({
              title: this.i18n.mesgCannotBeAllSpaces,
              icon: 'none'
            })
            return
          }
          if(this.virtualRemarkList.find(el => !el.value && el.isRequired)) {
            uni.showToast({
              title: this.i18n.requiredMessage,
              icon: 'none'
            })
            return
          }
        }
        // 自定义用户留言判断
        if (this.isCustomUserMessage) {
          const values = this.$refs.customUserMessageRef.getValues()
          if (values.find(el => !el.value && el.isRequired)) {
            uni.showToast({
              title: this.i18n.requiredMessage,
              icon: 'none'
            })
            return
          } else {
            this.virtualRemarkList = values
          }
        }
        requestServiceExpirationMessage({
          complete: () => {
            this.submitOrder();
          }
        })
			},

			// 提交订单
			submitOrder: function() {
				var isPurePoints = this.actualTotal > 0 ? '' : 1 // 是否纯积分: 1是

				uni.showLoading({
					// #ifndef MP-TOUTIAO
					mask: true
					// #endif
				});
				var shopCartOrders = this.shopCartOrders;
			  var reg = /^\s+$/g
				var orderShopParam = [];
				shopCartOrders.forEach(shopCart => {
					orderShopParam.push({
						remarks: shopCart.remarks.trim() ? shopCart.remarks : '',
						shopId: shopCart.shopId
					});
				});

			  let remarksFlag = orderShopParam.some(item=>{
					return reg.test(item.remarks)
				})

				if (remarksFlag) {
					wx.showToast({
						title: this.i18n.inputAllSpace,
						icon: "none"
					});
					return;
				}
				var orderSelfStationDto = {
					stationId: this.selStationItem.stationId,
					stationTime: this.dateContent + ' ' + this.timeContent,
					stationUserMobile: this.stationUserMobile,
					stationUserName: this.stationUserName
				}
				let orderInvoiceList = [] // invoiceDataFrom
				  shopCartOrders.forEach((item) => {
					if (item.invoiceDataFrom && item.invoiceDataFrom.invoiceType === 1) {
					  orderInvoiceList.push(item.invoiceDataFrom)
					}
				  })
				  if (orderInvoiceList.length === 0) {
					orderInvoiceList = null
				  }
				var params = {
					url: "/p/order/submit",
					method: "POST",
					data: {
						orderShopParam: orderShopParam,
						// coupons: this.data.platformCoupons,
						coupons: this.coupons,
						isScorePay: this.isScorePay,
						orderSelfStationDto: orderSelfStationDto,
						orderInvoiceList: orderInvoiceList,
						virtualRemarkList: this.virtualRemarkList
					},
					callBack: res => {
						uni.hideLoading();
						if (res.duplicateError != null && res.duplicateError == 1) {
							uni.showModal({
								title: this.i18n.tips,
								content: this.i18n.duplicateErrorTips,
								confirmText: this.i18n.confirm,
								showCancel: false,
								success: res => {
									uni.navigateBack(1)
								}
							})
						}else{
							const param = {
								orderNumbers: res.orderNumbers,
								dvyType: this.dvyType,
								isPurePoints: isPurePoints,
                hideBalancePay: this.isDistributionOrder, // 分销订单不显示余额支付
							}
							if (this.mold === 1) {
								param.ordermold = this.mold
							}
              this.$refs.payRef.init(param)
						}
					},
					errCallBack: errMsg => {
						console.log(errMsg)
						if (errMsg.statusCode === 400) {
							this.orderErrMsgTips = errMsg.data
							uni.showModal({
								title: this.i18n.tips,
								showCancel: false,
								content: errMsg.data,
								confirmText: this.i18n.confirm,
								success: res => {
									uni.navigateBack(1)
								}
							})
						}
					}
				};
				http.request(params);
			},

			// 店铺切换可用/不可用优惠券列表
			changeCouponSts: function(e) {
				this.setData({
					couponSts: e.currentTarget.dataset.sts
				});
			},
			// 店铺优惠券弹框
			showCouponPopup: function(e) {
				var index = Number(e.currentTarget.dataset.index);
				var shopCartOrders = this.shopCartOrders;
				this.setData({
					showCoupons: index + '' === '-1' ? this.platformCoupons : shopCartOrders[index].shopCoupons,
					popupShow: true,
				});
				this.showCoupons.canUseCoupons.forEach(item =>{
					if(this.couponUserIds.indexOf(item.couponUserId) !== -1) {
						item.choose = true
					}else {
						item.choose = false
					}
				})
				this.isShopCoupon = index !== -1
			},
			// 查看留言弹窗
			showViewMsgPopup: function() {
				this.showViewMsg = true
			},
			closePopup: function() {
				this.setData({
					popupShow: false,
					showScorePop: false,
					showDistributionPop: false,
					showAddressListPop: false,
					showRaisingUserList: false,
					showRaisingTimePop: false,
					isShowInvoicePopup: false,
					showViewMsg: false
				});
			},
			// 发票信息弹窗
			showInvoicePopup (shopId,invoiceDataFrom) {
			  console.log(shopId,invoiceDataFrom)
			  this.invoiceDataFrom = invoiceDataFrom || {}
			  this.invoiceShopId = shopId
			  this.isShowInvoicePopup = true
			},
			getInvoiceData(data) {
			  // invoiceDataFrom
			  let flag = false
			  for(let i=0;i<this.invoiceDataList.length;i++) {
				  if(this.invoiceDataList[i].shopId === data.shopId) {
					  flag = true
					  if(data) {
						  this.invoiceDataList[i] = data
					  }
					  break
				  }
			  }
			  if(!flag && data) {
				  this.invoiceDataList.push(data)
			  }

			  this.shopCartOrders.forEach(item => {
				if(item.shopId === data.shopId) {
				  item.invoiceDataFrom = data
				}
			  })
			},
			setOrderInvoice() {
				this.invoiceDataList.forEach(invoice => {
					this.shopCartOrders.forEach(item => {
						if(item.shopId === invoice.shopId) {
						  item.invoiceDataFrom = invoice
						}
					})
				})
			},
			/**
			 * 去地址页面
			 */
			toAddrListPage: function() {
				// uni.navigateTo({
				// 	url: '/packageUser/pages/delivery-address/delivery-address?order=0'
				// });
				this.isEditAddr = true
				this.setData({
					boolenRes: 0,
					addrId: '',
					editorFlag: false,
					province: null,
					city: null,
					area: null,
				})
			},

			/**
			 * 确定选择好的优惠券
			 */
			choosedCoupon: function() {
				var couponIds = this.couponIds; // 店铺优惠券单选操作
				var couponUserIds = this.couponUserIds;
				if (!this.showCoupons) {
					this.setData({
						popupShow: false
					})
					return
				}
				var canUseCoupons = this.showCoupons.canUseCoupons;
				var checkedCouponId = ''
				var checkedCouponId1 = ''
				for (var canUseCouponIndex in canUseCoupons) {
					var coupon = canUseCoupons[canUseCouponIndex];
					if (!coupon.choose && couponIds.indexOf(coupon.couponId) !== -1) {
						couponIds.splice(couponIds.indexOf(coupon.couponId), 1)
						couponUserIds.splice(couponUserIds.indexOf(coupon.couponUserId), 1)
					}
					if (coupon.choose) {
						checkedCouponId = coupon.couponId
						checkedCouponId1 = coupon.couponUserId
					}
				}
				couponIds.push(checkedCouponId)
				couponUserIds.push(checkedCouponId1)
				this.setData({
					couponIds: couponIds,
					couponUserIds: couponUserIds,
					popupShow: false
				});
				this.loadOrderData();
			},

			/**
			 * 优惠券子组件发过来
			 */
			checkCoupon: function(e) {
				var showCoupons = this.showCoupons; // 店铺优惠券单选操作

				var canUseCoupons = showCoupons.canUseCoupons;

				for (var canUseCouponIndex in canUseCoupons) {
					if (e.couponUserId == canUseCoupons[canUseCouponIndex].couponUserId && canUseCouponIndex == e.index) {
						canUseCoupons[canUseCouponIndex].choose = !canUseCoupons[canUseCouponIndex].choose;
					} else {
						canUseCoupons[canUseCouponIndex].choose = false;
					}
				}
				this.setData({
					showCoupons: showCoupons,
					userChangeCoupon: 1
				});
			},

			/**
			 * 输入备注
			 */
			onRemarkIpt: function(e) {
				var index = e.currentTarget.dataset.index;
				var shopCartOrders = this.shopCartOrders;
				shopCartOrders[index].remarks = e.detail.value;
				this.setData({
					shopCartOrders: shopCartOrders
				});
			},

			/**
			 * 物流选择弹窗显示
			 */
			distributionPop() {
				this.setData({
					showDistributionPop: true
				});
			},

			/**
			 * 可用地址弹窗显示
			 */
			AddressListPop() {
				if (this.addressList.length) {
					this.setData({
						showAddressListPop: true
					});
				}
			},

			/**
			 * 历史提货人弹窗显示
			 */
			raisingUserList() {
				if (this.dvyType == 2 && !this.shopCartOrders.length) {
					uni.showToast({
						title: this.i18n.productNotSupportedStop,
						icon: 'none'
					})
					return
				}
				this.setData({
					showRaisingUserList: true
				});

			},

			/**
			 * 选择提货时间弹窗显示
			 */
			raisingTimePop() {
				if (!this.selStationItem.province) {
					uni.showToast({
						title: this.i18n.selectPickPoint,
						icon: 'none'
					})
				} else {
					this.setData({
						showRaisingTimePop: true
					});
					let a = (this.timeParams[0].dateTime).split('-')
					this.dateContent = a[0] + this.i18n.virtualMonth + a[1] + this.i18n.dayOfAMonth
				}
			},

			/**
			 * 选择自提点
			 */
			goSelectStore() {
				if (!this.shopCartOrders.length) {
					wx.showToast({
						title: this.i18n.productNotSupportedStop,
						icon: "none"
					});
					return;
				}
				// 选择与重新选择自提点时时间
				this.setData({
					dateIndex: 0, // 日期下标
					timeIndex: null, // 时间下标
					timeContent: '', // 选中的时间
					dateContent: '', // 选中的日期
				})
				// 将已选择/已填写的历史提货人信息保存起来
				if (this.stationUserName || this.stationUserMobile || this.stationIdx) {
					const selectedPickupinfor = {
						stationUserName: this.stationUserName,
						stationUserMobile: this.stationUserMobile,
						stationIdx: this.stationIdx
					}
					uni.setStorageSync('selectedPickupinfor', selectedPickupinfor)
				}
				uni.navigateTo({
					url: '/pages/selectStore/selectStore?shopId=' + this.shopCartOrders[0].shopId + '&orderEntry=' + this.orderEntry
				});
			},

			/**
			 * 禁止输入提货人信息
			 */
			disabledInput: function() {
				if (this.dvyType == 2 && !this.shopCartOrders.length) {
					this.disabled = true
				} else {
					this.disabled = false
				}
			},

			/**
			 * 获取历史提货人列表
			 */
			getStationUserInfo: function() {
				wx.showLoading();
				var params = {
					url: "/p/orderSelfStation/getStationUserInfo",
					method: "GET",
					data: {},
					callBack: (res) => {
						wx.hideLoading();
						this.stationUserInfo = res
					}
				};
				http.request(params);
			},

			getConsigneeInt: function(e) {
				this.stationUserName = e.detail.value
			},
			getConMobileInt: function(e) {
				this.stationUserMobile = e.detail.value
			},

			/**
			 * 改变预约自提日期选择
			 */
			changeData: function(item, dateIdx) {
				this.dateIndex = dateIdx
				// console.log(this.dateIndex, this.timeParams[this.dateIndex].hourTimes)

				// 点击日期时默认选中第一个时间段
				this.timeIndex = 0; // 时间下标
				this.timeContent = this.timeParams[this.dateIndex].hourTimes[0];

				let a = (item.dateTime).split('-')

				this.dateContent = a[0] + '-' + a[1]

			},

			/**
			 * 改变预约自提时间选择
			 */
			changeTime: function(timeItem, timeIdx) {
				this.timeIndex = timeIdx
				this.showRaisingTimePop = false
				this.timeContent = timeItem
			},


			/**
			 * 获取当前日期零点的时间戳
			 * @param date
			 */
			getStartTimestampOfDay: function() {
				let d = new Date
				// let c = d.toISOString()
				// let b= c.replace(/\d{2}:\d{2}:\d{2}\.\d{3}/g, "00:00:00.000")
				let n = d.getTime()
				return n
			},

			/**
			 * 处理当前日期与指定日期之间的关系
			 * @param date
			 */
			getWeekDayMappingOfCurrent: function() {
				const current = this.getStartTimestampOfDay();
				if (current) {
					return {
						[current - 2 * 86400000]: this.i18n.beforeYesterday,
						[current - 1 * 86400000]: this.i18n.yesterday,
						[current]: this.i18n.today,
						[current + 1 * 86400000]: this.i18n.tomorrow,
						[current + 2 * 86400000]: this.i18n.afterTomorrow
					};
				}
				return {};
			},

			/**
			 * 获取格式化的周几：前天、昨天、今天、明天、后天
			 * @param date
			 */
			// getWeekDayString: function() {
			// 	const timestamp = this.getStartTimestampOfDay();
			// 	const mapping = this.getWeekDayMappingOfCurrent();

			// 	console.log(mapping)
			// 	console.log(timestamp)

			// 	return mapping[timestamp];
			// },


			/**
			 * 获取全部省份
			 */
			initCityData: function(provinceId, cityId, areaId) {
				var ths = this;
				wx.showLoading();
				var params = {
					url: "/p/area/listByPid",
					method: "GET",
					data: {
						pid: 0
					},
					callBack: function(res) {
						ths.setData({
							provArray: res
						});
						if (provinceId) {
							for (var index in res) {
								if (res[index].areaId == provinceId) {
									ths.setData({
										value: [Number(index), ths.value[1], ths.value[2]]
									});
								}
							}
						}
						ths.getCityArray(provinceId ? provinceId : res[0].areaId, cityId, areaId);
						wx.hideLoading();
					}
				};
				http.request(params);
			},

			/**
			 * 根据省份ID获取 城市数据
			 */
			getCityArray: function(provinceId, cityId, areaId) {
				wx.showLoading();
				var ths = this;
				var params = {
					url: "/p/area/listByPid",
					method: "GET",
					data: {
						pid: provinceId
					},
					callBack: function(res) {
						//console.log(res)
						ths.setData({
							cityArray: res
						});

						if (cityId) {
							for (var index in res) {
								if (res[index].areaId == cityId) {
									ths.setData({
										value: [ths.value[0], Number(index), Number(ths.value[2])]
									});
								}
							}
						}

						ths.getAreaArray(cityId ? cityId : res[0].areaId, areaId);
						wx.hideLoading();
					}
				};
				http.request(params);
			},

			/**
			 * 根据城市ID获取 区数据
			 */
			getAreaArray: function(cityId, areaId) {
				var ths = this;
				var params = {
					url: "/p/area/listByPid",
					method: "GET",
					data: {
						pid: cityId
					},
					callBack: function(res) {
						//console.log(res)
						ths.setData({
							areaArray: res
						});

						if (areaId) {
							for (var _index in res) {
								if (res[_index].areaId == areaId) {
									ths.setData({
										value: [ths.value[0], ths.value[1], Number(_index)]
									});
								}
							}

							index = ths.value;
							ths.setData({
								province: ths.province,
								city: ths.city,
								area: ths.area,
								provinceId: ths.provinceId,
								cityId: ths.cityId,
								areaId: ths.areaId
							});
						} else {
							ths.setData({
								province: ths.provArray[ths.value[0]].areaName,
								city: ths.cityArray[ths.value[1]].areaName,
								area: ths.areaArray[ths.value[2]].areaName,
								provinceId: ths.provArray[ths.value[0]].areaId,
								cityId: ths.cityArray[ths.value[1]].areaId,
								areaId: ths.areaArray[ths.value[2]].areaId
							});
						}

						wx.hideLoading();
					}
				};
				http.request(params);
			},


			bindRegionChange: function(e) {
				//console.log('picker发送选择改变，携带值为', e.detail.value)
				this.setData({
					region: e.detail.value
				});
			},

			onReceiverInput: function(e) {
				this.setData({
					receiver: e.detail.value
				});
			},
			onMobileInput: function(e) {
				this.setData({
					mobile: e.detail.value
				});
			},
			onAddrInput: function(e) {
				this.setData({
					addr: e.detail.value
				});
			},



			/**
			 * 保存地址
			 */
			onSaveAddr: function() {
				var receiver = this.receiver;
				var mobile = this.mobile;
				var addr = this.addr;
				var reg = /^\s+$/g
				if (this.addressList.length == 10) {
					wx.showToast({
						title: this.i18n.newAddressesLimit,
						icon: "none",
						duration: 1500
					});
					return;
				}
				if (!receiver) {
					wx.showToast({
						title: this.i18n.consigneeTips,
						icon: "none"
					});
					return;
				}
				if (!mobile) {
					wx.showToast({
						title: this.i18n.enterMobileNumber,
						icon: "none"
					});
					return;
				}
				if (!util.checkPhoneNumber(mobile)) {
					wx.showToast({
						title: this.i18n.enterCorrectPhone,
						icon: "none"
					});
					return;
				}
				if (!this.province || !this.city || !this.area) {
					wx.showToast({
						title: this.i18n.selectProvinceCity,
						icon: "none"
					});
					return;
				}
				if (reg.test(addr)) {
					wx.showToast({
						title: this.i18n.inputAllSpace,
						icon: "none"
					});
					return;
				}
				if (!addr || addr.length<5) {
					wx.showToast({
						title: this.i18n.selectDetailedAddress,
						icon: "none"
					});
					return;
				}
				if(this.dvyType == 4) {
					// 同城配送
					if (!this.lat || !this.lng || this.lat == '' || this.lng == '') {
						uni.showModal({
							title: this.i18n.tips,
							content: this.i18n.selectCoordinates,
							showCancel: false,
							success: res => {
								uni.navigateTo({
									url: '/packageUser/pages/editAddress/editAddress?addrId=' + this.userAddr.addrId
								})
							}
						})
					}
				}
				wx.showLoading();
				var url = "/p/address/addAddr";
				var method = "POST";
				//添加或修改地址
				var params = {
					url: url,
					method: method,
					data: {
						receiver: this.receiver,
						mobile: this.mobile,
						addr: this.addr,
						province: this.province,
						provinceId: this.provinceId,
						city: this.city,
						cityId: this.cityId,
						areaId: this.areaId,
						area: this.area,
						userType: 0,
						// addrId: this.addrId,
						lat: this.lat, // 纬度
						lng: this.lng, // 经度
					},
					callBack: (res) => {
						wx.hideLoading();
						this.loadAddressList()
						wx.showToast({
							title: this.i18n.savedSuccessfully,
							icon: 'none',
							duration: 1000
						})
						this.addr = ''
						this.receiver = ''
						this.mobile = ''
						this.province = ''
						this.city = ''
						this.area = ''
						this.isEditAddr = false
						if(this.addressList.length>1){
							setTimeout(() => {
								this.addrId = this.addressList[1].addrId
							}, 100);
						}else{
							setTimeout(() => {
								this.addrId = this.addressList[0].addrId
							}, 100);
						}

						this.setData({
							province: this.provArray[this.value[0]].areaName,
							city: this.cityArray[this.value[1]].areaName,
							area: this.areaArray[this.value[2]].areaName,
							provinceId: this.provArray[this.value[0]].areaId,
							cityId: this.cityArray[this.value[1]].areaId,
							areaId: this.areaArray[this.value[2]].areaId,
							boolenRes:1
						});
						// this.showAddressListPop = true
						setTimeout(() => {
							this.getAddrDet()
						}, 100);
					}
				};
				http.request(params);
			},

			//滑动事件
			bindChange: function(e) {
				var val = e.detail.value; //判断滑动的是第几个column
				//若省份column做了滑动则定位到地级市和区县第一位
				if (this.value[0] != val[0]) {
					val[1] = 0;
					val[2] = 0; //更新数据
					this.getCityArray(this.provArray[val[0]].areaId); //获取地级市数据
				} else {
					//若省份column未做滑动，地级市做了滑动则定位区县第一位
					if (this.value[1] != val[1]) {
						val[2] = 0; //更新数据
						this.getAreaArray(this.cityArray[val[1]].areaId); //获取区县数据
					} else {}
				}

				this.setData({
					value: [val[0], val[1], val[2]]
				});
				if(this.province){
					this.setData({
						province: this.provArray[this.value[0]].areaName,
						city: this.cityArray[this.value[1]].areaName,
						area: this.areaArray[this.value[2]].areaName,
						provinceId: this.provArray[this.value[0]].areaId,
						cityId: this.cityArray[this.value[1]].areaId,
						areaId: this.areaArray[this.value[2]].areaId
					});

				}

			},

			//移动按钮点击事件
			translate: function(e) {
				if (t == 0) {
					moveY = 0;
					show = false;
					t = 1;
				} else {
					moveY = 200;
					show = true;
					t = 0;
				}

				this.setData({
					show: true
				}); // this.animation.translate(arr[0], arr[1]).step();

				this.animationEvents(this, moveY, show);
			},

			//隐藏弹窗浮层
			hiddenFloatView(e) {

				moveY = 200;
				show = true;
				t = 0;
				this.animationEvents(this, moveY, show);
				this.setData({
					saveEditFlag:true
				})

				this.setData({
					province: this.provArray[this.value[0]].areaName,
					city: this.cityArray[this.value[1]].areaName,
					area: this.areaArray[this.value[2]].areaName,
					provinceId: this.provArray[this.value[0]].areaId,
					cityId: this.cityArray[this.value[1]].areaId,
					areaId: this.areaArray[this.value[2]].areaId
				});


			},

			// 点击屏幕外事件
			hiddenFloatViewScreenClick(e) {
				moveY = 200;
				show = true;
				t = 0;
				this.animationEvents(this, moveY, show);
			},

			//动画事件
			animationEvents: function(that, moveY, show) {
				//console.log("moveY:" + moveY + "\nshow:" + show);
				that.animation = wx.createAnimation({
					transformOrigin: "50% 50%",
					duration: 400,
					timingFunction: "ease",
					delay: 0
				});
				that.animation.translateY(moveY + 'vh').step();
				that.setData({
					animation: that.animation.export()
				});
			},

			// 空方法
			nono() {

			},


			setMapData(res) {
				this.lat = res.latitude; // 纬度
				this.lng = res.longitude; // 经度
				this.addr = res.address; // 详细地址
				this.name = res.name; // 详细地址

				console.log('腾讯/高德地图经纬度：',this.lng,'，',this.lat)

				this.qqMapTransBMap(this.lng,this.lat)
			},

			// 将腾讯/高德地图经纬度转换为百度地图经纬度
			qqMapTransBMap: function(lng, lat) {
				let x_pi = 3.14159265358979324 * 3000.0 / 180.0;
				let x = lng;
				let y = lat;
				let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
				let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
				let lngs = z * Math.cos(theta) + 0.0065;
				let lats = z * Math.sin(theta) + 0.006;

				this.lng = lngs
				this.lat = lats

				console.log('百度地图经纬度：',this.lng,'，',this.lat)
				return {
					lng: lngs,
					lat: lats
				}
			},

			/**
			 * 满减信息处理
			 */
			parseDiscountMsg(discountRule, needAmount, discount, lang) {
				if (discountRule == 0) {
					return lang == 'zh_CN' ? '购满' + needAmount + '元减' + discount + '元' : 'Over ' + needAmount + ' minus ' + discount;
				} else if (discountRule == 1) {
					return lang == 'zh_CN' ? '购满' + needAmount + '件减' + discount + '元' : discount + ' less for ' + needAmount + ' pieces' ;
				} else if (discountRule == 2) {
					return lang == 'zh_CN' ? '购满' + needAmount + '元打' + discount + '折' : discount + '% off over ' + needAmount;
				} else if (discountRule == 3) {
					return lang == 'zh_CN' ? '购满' + needAmount + '件打' + discount + '折' : discount + '% off over ' + needAmount + ' pieces' ;
				} else {
					return '';
				}
			}

		},

	};
</script>
<style>
	@import "./submit-order.css";
</style>

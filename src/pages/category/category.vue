<template>
  <view class="container">
    <ShopCategory ref="shopCategoryRef" v-if="isLogin && currentShop" :shop-id="currentShop.shopId"></ShopCategory>
  </view>
</template>

<script>
import ShopCategory from '@/pages/shopCategory/shopCategory'
export default {
  data () {
    return {
    }
  },
  components: {
    ShopCategory,
  },
  onShow(options) {
    this.$nextTick(() => {
      if (this.isLogin && this.currentShop) {
        this.$refs.shopCategoryRef.parentOnShow(options);
      }
    })
  },
  onReachBottom(options) {
    if (this.isLogin && this.currentShop) {
      this.$refs.shopCategoryRef.parentOnReachBottom(options);
    }
  },
  onLoad(options) {
    this.$nextTick(() => {
      if (this.isLogin && this.currentShop) {
        this.$refs.shopCategoryRef.parentOnLoad(options);
      }
    })
  },
  methods: {

  },
  computed: {
    isLogin () {
      return this.$store.state.isLogin
    },
    currentShop: function () {
      return this.$store.state?.currentShop
    },
  },
  mounted () {

  },
}
</script>

<style scoped>
.container {
  width: 100%;
  height: auto;
  min-height: 100vh;
}
</style>
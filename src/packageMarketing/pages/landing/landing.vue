<template>
  <div class="container">
    <view v-show="!loading">
      <view v-if="isShowBtn" class="img-container">
        <img class="img" :src="`${resourcesUrl}${picUrl}`" @load="imgLoad"/>
        <a class="btn" :href="schema" @tap="jumpToWxMp">点击前往</a>
      </view>
      <view v-else class="img-container">
        当前页面不存在，请输入正确的页面地址
      </view>
    </view>

    <n-loading :show="loading" />
  </div>
</template>

<script>
import http from '@/utils/http.js'
import config from '@/utils/config.js'
const appEnv = process.env.VUE_APP_FLAG !== 'prod' ? 'trial' : 'release'

export default {
  name: '',
  data () {
    return {
      pageId: '',
      shareType: '',
      pageDetail: {},
      isShowBtn: false,
      loading: false,
      resourcesUrl: config.picDomain,
      picUrl: '',
      schema: `weixin://dl/business/?appid=${config.wxAppid}&path=packageMarketing/pages/station/station&env_version=${appEnv}`
    }
  },
  onLoad(options) {
    this.pageId = options.p
    this.shareType = options.t
    uni.setNavigationBarTitle({
      title: '校园网钜惠来袭',
    })
  },
  mounted () {
    this.getPageDetail()
  },
  methods: {
    getPageDetail() {
      this.startLoading()
      var params = {
        url: `/p/xpage/${this.pageId}`,
        method: "get",
        dontTrunLogin: true,
        callBack: (res) => {
          const accessType = this.shareType === 'c' ? 'click' : 'scan'
          this.schema = `weixin://dl/business/?appid=${config.wxAppid}&path=packageMarketing/pages/station/station&query=accessType%3D${accessType}%26pageId%3D${this.pageId}&env_version=${appEnv}`
          this.stopLoading()

          if(res) {
            this.pageDetail = res
            this.isShowBtn = true
            this.picUrl = JSON.parse(res.content).picUrl
            this.reportData(res, 'middleURL', accessType)
          }
        },
        errCallBack: () => {
          this.stopLoading()
        }
      };
      http.request(params);
    },
    imgLoad() {
      const container = document.getElementsByClassName('container')?.[0];
      const containerHeight = container?.clientHeight;

      if (containerHeight) {
        window.parent.postMessage({ height: containerHeight }, '*');
      }
    },
    jumpToWxMp() {
      const accessType = this.shareType === 'c' ? 'click' : 'scan'
      this.reportData(this.pageDetail, 'wxMpURL', accessType, () => {
        // 如果是ios safari浏览器，直接跳转到微信小程序
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent) && /safari/i.test(navigator.userAgent)) {
          window.location.href = this.schema
          return
        }
      })
    },
    reportData(pageDetail, clickPosition, accessType, cb) {
      fetch(`${config.domain}/market/report`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }, 
        body: JSON.stringify({
          cps: pageDetail.cps,
          originFrom: pageDetail.originFrom,
          clickPosition,
          accessType,
        }),
        keepalive: true
      }).then(() => {
        cb?.()
      })
    },
    startLoading() {
      this.loading = true;
    },
    stopLoading() {
      this.loading = false;
    },
  },
}
</script>

<style scoped>
.container {
  width: 100vw;
}
.img-container {
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  overflow: auto;
}
.btn {
  width: 80%;
  padding: 20rpx;
  text-align: center;
  background: var(--primary-color);
  color: #fff;
  border-radius: 70rpx;
  font-size: 28rpx;
  text-decoration: none;
}
.img {
  width: 100%;
  object-fit: cover;
  object-position: center;
}
</style>
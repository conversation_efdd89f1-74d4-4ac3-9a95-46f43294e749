<template>
  <view>
  </view>
</template>

<script>
import http from '@/utils/http.js'
import utils from '@/utils/util.js'
import config from '@/utils/config.js'

export default {
  name: 'marketing-station',
  data () {
    return {
      pageId: '',
    }
  },
  onLoad(query) {
    if (query.accessType) {
      uni.setStorageSync('accessType', query.accessType)
    }
    this.pageId = query.pageId
  },
  methods: {
    getUserInfo() {
      return new Promise((resolve, reject) => {
        http.request({
          url: "/p/user/userInfo",
          method: "GET",
          data: {},
          dontTrunLogin: true,
          callBack: (res) => {
            resolve(res)
          },
          errCallBack: (err) => {
            reject(err)
          }
        });
      })
    },
    getLandingPageInfo() {
      return new Promise((resolve, reject) => {
        http.request({
          url: `/p/xpage/${this.pageId}`,
          method: "get",
          dontTrunLogin: true,
          callBack: (res) => {
            resolve(res)
          },
          errCallBack: (err) => {
            reject(err)
          }
        });
      })
    },
    toLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },
    toHome() {
      uni.switchTab({
        url: '/pages/index/index',
        fail: (err) => {
          console.log(err);
        }
      })
    },
    getLandingPageUrl(landingPageInfo) {
      const targetPage = JSON.parse(landingPageInfo.content).h5JumpUrl
      const targetPageParams = JSON.parse(landingPageInfo.content).h5JumpParams
      let fullPath = `/${targetPage}`
      if (targetPageParams) {
        fullPath += `?${targetPageParams}`
      }
      return fullPath;
    },
    reportData({cps, originFrom}) {
      return new Promise((resolve) => {
        const openid = uni.getStorageSync('openid')
        const unionid = uni.getStorageSync('unionid')
        const launchOptions = wx.getLaunchOptionsSync()
        if (cps && originFrom && launchOptions.scene) {
          const reportParams = {
            cps,
            originFrom,
            clickPosition: 'wxMp',
            openId: openid,
            unionId: unionid,
            wxScene: `${launchOptions.scene}:${utils.getSceneInfo(launchOptions.scene).value}`,
          }
          const accessType = uni.getStorageSync('accessType')
          if (accessType) {
            reportParams.accessType = accessType
          }
          http.request({
            url: '/market/report',
            method: 'POST',
            data: reportParams,
            callBack: () => resolve(),
            errCallBack: () => resolve(),
          })
        } else {
          uni.removeStorageSync('reportParams')
          resolve()
        }
      })
    },
    async exec() {
      const userInfo = await this.getUserInfo();
      const userOu = userInfo.ou;
      const landingPageInfo = await this.getLandingPageInfo();
      const landingPageOu = landingPageInfo.ou;
      if (!userOu || !landingPageInfo) {
        this.toHome();
        return
      }
      if (userOu !== landingPageOu) {
        console.log('用户的学校和落地页的学校配置不符');
        this.toHome();
        return
      }
      const landingPageUrl = this.getLandingPageUrl(landingPageInfo)
      await this.reportData({cps: landingPageInfo.cps, originFrom: landingPageInfo.originFrom});
      uni.reLaunch({
        url: landingPageUrl
      })
    }
  },
  onShow() {
    const isLogin = uni.getStorageSync('token')
    if (!!!isLogin) {
      uni.setStorageSync('routeUrlAfterLogin', `/packageMarketing/pages/station/station?pageId=${this.pageId}`)
      this.toLogin()
    } else {
      this.exec()
    }
  },
}
</script>

<style scoped>
</style>
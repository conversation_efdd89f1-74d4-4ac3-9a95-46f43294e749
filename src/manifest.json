{"name": "wenet商城", "appid": "__UNI__FFE8D8A", "description": "wenet商城", "versionName": "1.1.0", "versionCode": 110, "transformPx": false, "app-plus": {"compatible": {"ignoreVersion": true}, "usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Share": {}, "Maps": {}, "Payment": {}, "Geolocation": {}, "VideoPlayer": {}, "Webview-x5": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a"], "permissionExternalStorage": {"request": "none", "prompt": "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"}, "permissionPhoneState": {"request": "none", "prompt": "为保证您正常、安全地使用，需要获取设备识别码（部分手机提示为获取手机号码）使用权限，请允许。"}}, "ios": {"idfa": false, "dSYMs": false}, "sdkConfigs": {"oauth": {}, "payment": {"weixin": {"__platform__": ["ios", "android"], "appid": "wx6aeaa25ef735d54f", "UniversalLinks": "https://api.wenet.com/"}, "alipay": {"__platform__": ["ios", "android"]}}, "share": {"weixin": {"appid": "wx6aeaa25ef735d54f", "UniversalLinks": "https://api.wenet.com/"}}, "maps": {"amap": {"appkey_ios": "725b502f21999694328fdfb5023b6131", "appkey_android": "04f37dd45a78544e29dd900a1fc2e363"}}, "geolocation": {"amap": {"__platform__": ["ios", "android"], "appkey_ios": "725b502f21999694328fdfb5023b6131", "appkey_android": "04f37dd45a78544e29dd900a1fc2e363"}}, "ad": {}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png"}, "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "proapp@2x": "unpackage/res/icons/167x167.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}}}, "splashscreen": {"androidStyle": "common", "iosStyle": "common", "useOriginalMsgbox": true}}, "uniStatistics": {"enable": false}}, "quickapp": {}, "mp-weixin": {"appid": "wx310a4a81d534256a", "setting": {"urlCheck": false, "minified": true, "postcss": false, "es6": true}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "uniStatistics": {"enable": false}, "optimization": {"subPackages": true}}, "mp-toutiao": {"appid": "tt56e92054c9d1422f01"}, "h5": {"title": "wenet商城", "domain": "m.wenet.com.cn", "router": {"mode": "history"}, "uniStatistics": {"enable": false}, "optimization": {"treeShaking": {"enable": false}}, "template": "index.html", "sdkConfigs": {"maps": {"qqmap": {"key": "OBOBZ-N3C36-62GSP-EMQFI-FEH6Z-M2FBU"}}}, "devServer": {"host": "0.0.0.0", "disableHostCheck": true, "sockHost": "0.0.0.0:3366", "port": 3366, "proxy": {"/api": {"target": "https://h5.online.shop.dev.wenet.group", "changeOrigin": true}, "/bas": {"target": "https://h5.online.shop.dev.wenet.group", "changeOrigin": true}, "/bas-wechat-self-static": {"target": "https://h5.online.shop.dev.wenet.group", "changeOrigin": true}}}}}
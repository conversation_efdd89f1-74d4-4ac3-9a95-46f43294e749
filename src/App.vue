<script>
//app.js
var http = require("./utils/http.js");
var util = require('./utils/util.js')
import { AppType } from './utils/constant.js'
const { wxAppid, talkingdataAppId, wxMpName } = require('@/utils/config')
// import { weChatLogin } from '@/utils/login-processor'
// import { isEqual } from 'lodash'
import { doWechatOfficalAccountOAuth } from '@/utils/login-processor'

let state = true
export default {
  onLaunch: function (options) {
    const conf = {
      appKey: talkingdataAppId,
      appName: wxMpName,
      versionName: wxMpName,
      versionCode: "2.4.0",
      wxAppId: wxAppid,
      channelId:"default",
    };
		uni.removeStorageSync('tempUid')
    // #ifdef H5
    uni.getSystemInfo({
      success: function (res) {
        //客户端平台，值域为：ios、android
				if (res.platform == 'ios') {
					uni.setStorageSync('iosUrl', window.location.href.split('#')[0])
        }
			}
    });
    // 判断浏览器环境
    var ua = navigator.userAgent.toLowerCase();
    // 排除企业微信
		if (ua.search(/MicroMessenger/i) > -1 && ua.search(/wxwork/i) === -1) {
      // 微信环境
		  uni.setStorageSync('appType', AppType.MP)
      const officalAccountOpenid = uni.getStorageSync('officalAccountOpenid');
      if (options.query.openid) {
        uni.setStorageSync('officalAccountOpenid', options.query.openid)
      } else if (!officalAccountOpenid) {
        doWechatOfficalAccountOAuth()
      }
      
		} else if (ua.search(/Alipay/i) > -1 && !uni.getStorageSync('appType')) {
      // 支付宝环境
		  uni.setStorageSync('appType', AppType.ALI)
    } else {
			if (!uni.getStorageSync('appType')) {
				uni.setStorageSync('appType', AppType.H5)
      }
    }
    // #endif
    // #ifdef APP-PLUS
    // APP模式下保持竖屏
		plus.screen.lockOrientation('portrait-primary')
    uni.getSystemInfo({
      success: (sysInfo) => {
		    if (sysInfo.platform == 'android') {
		      uni.setStorageSync('appType', AppType.ANDROID)
        } else {
		      uni.setStorageSync('appType', AppType.IOS)
        }
		  }
		})
    // #endif
    // #ifdef MP-WEIXIN
		uni.setStorageSync('appType', AppType.MINI)
    // #endif

    // #ifdef MP-ALIPAY
		uni.setStorageSync('appType', AppType.ALI)
    // #endif

    // #ifdef MP-TOUTIAO
		uni.setStorageSync('appType', AppType.TTMINI)
    // #endif

    // 获取系统支付设置
		util.getSysPaySwitch()

    // 获取APP功能开关

    this.$store.commit('getAppSwitch')
    // 微信环境统一登录方法
    // weChatLogin()

    // #ifdef MP-WEIXIN
    uni.setStorageSync('wxScene', options.scene)
		this.checkMiniUpdate()
    this.getOpenIdAndUnionId(options)
    // #endif

    this.showMineTabBarBadge()
  },
  onShow: function () {
    if (state) {
      var params = {
        url: "/sys/config/info/getSysServiceSwitch",
        method: "GET",
        callBack: data => {

          if (!uni.getStorageSync('token') && data.browseAuditSwitch) {
            uni.setStorageSync('browseAuditSwitch', data.browseAuditSwitch)
            
            setTimeout(function() {
              uni.redirectTo({
                url: '/pages/accountLogin/accountLogin'
              })
            }, 1000);

            return
          }

        }
      };
      http.request(params);
      state = false
    }
    this.$store.commit('refreshUserInfo', {
      forceRefresh: true,
      callBack: (res) => {
        this.$umaTracker.setUserBasicInfo({
          userId: res.uid,
          schoolName: res.ouName
        })
      }
    })
    this.setTabbar();
  },
  onHide: function () {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
     if (currentPage && currentPage.route === 'pages/ics/ics') {
      uni.navigateBack({
        delta: 1
      });
    }
  },

  onSaveExitState: function () {
    // 清空上报数据
    uni.removeStorageSync('reportParams')
  },
  globalData: {
    // 定义全局请求队列
    requestQueue: [],
    // 是否正在进行登陆
    isLanding: false,
    // 购物车商品数量
    totalCartCount: 0
  },
  methods: {
    getOpenIdAndUnionId(options) {
      wx.login({
        success: res => {
          if (res.code) {
            http.request({
              url: '/self/wxInfo',
              method: 'POST',
              data: {
                code: res.code
              },
              callBack: res => {
                uni.setStorageSync('openid', res.openid)
                uni.setStorageSync('unionid', res.unionid)
                this.$uma.setOpenid(res.openid)
              }
            })
          }
        }
      })
      uni.removeStorageSync('reportParams')
      const quires = options.query
      if (quires) {
        const cps = quires.cps
        const originFrom = quires.from
        if (cps && originFrom) {
          const openid = uni.getStorageSync('openid')
          const unionid = uni.getStorageSync('unionid')
          const reportParams = {
            cps,
            originFrom,
            clickPosition: 'wxMp',
            openId: openid,
            unionId: unionid,
            wxScene: options.scene ? `${options.scene}:${util.getSceneInfo(options.scene).value}` : "",
          }
          uni.setStorageSync('reportParams', reportParams)
          http.request({
            url: '/market/report',
            method: 'POST',
            data: reportParams,
          })
        }
      }
    },
    checkMiniUpdate() {
			const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate(function (res) {
        // 请求完新版本信息的回调
        // console.log(res.hasUpdate)
			})

      updateManager.onUpdateReady(function () {
        wx.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
          success: function (res) {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							updateManager.applyUpdate()
            }
					}
				})
			})

      updateManager.onUpdateFailed(function () {
        // 新版本下载失败
        uni.showToast({
					title: '新版本下载失败,请重试',
          duration: 2000,
					icon: 'none'
				})
			})
    },
    setTabbar() {
      util.transTabbar(this.isLogin)
    },
    // 加载订单数据
    loadOrderCountFun() {
      return new Promise((resolve, reject) => {
        http.request({
          url: "/p/user/centerInfo",
          method: "GET",
          dontTrunLogin: true,
          data: {},
          callBack: (res) => {
            if (uni.getStorageSync("token")) {
              resolve(res?.orderCountData?.unPay || 0);
            }
          },
          errCallBack: (err) => {
            reject(err);
          },
        });
      });
    },
     /**
     *  获取各个状态下优惠券数量
     */
     couponCount() {
      const that = this;
      return new Promise((resolve, reject) => {
        http.request({
          url: "/p/myCoupon/getMyCouponsStatusCount",
          method: "GET",
          data: {},
          callBack: function (res) {
            const unUse = res?.unUseCount || 0
            resolve(unUse);
            that.setData({
              couponNum: unUse
              })
          },                            
          errCallBack: (err) => {
            reject(err);
          },
        });
      });
    },
    async showMineTabBarBadge(){
      const unpay = await this.loadOrderCountFun();
      const couponCount = await this.couponCount();
      let showCouponCount = 0;
      let sum = 0;
      if(uni.getStorageSync("unUseCouponCount") && uni.getStorageSync("unUseCouponCount")>=couponCount){
        showCouponCount = 0
      }else{
        showCouponCount = couponCount
      }
      sum = unpay + showCouponCount;
      if(sum>0){
        uni.setTabBarBadge({
    			  index: 2,
    			  text: sum > 99 ? "99+" : sum + ""
    		  })
      }else{
        uni.removeTabBarBadge({index:2})
      }
    }
  },
  computed: {
    isLogin() {
      return this.$store.state.isLogin
    },
  }
};
</script>
<style>
@import './common/variables.css';
@import './common/init.css';
@import './common/font/iconfont.css';
@import './common/resets/checkbox.css'
</style>

/*
*改变checkbox样式
*自定义样式
*/
/* reg	  */
uni-checkbox-group {
	width: 100% !important;
}
uni-checkbox-group uni-label{
	width: 33% !important;
	display: inline-block;
	margin-bottom: 20rpx;
}
/*checkbox 选项框大小  */
uni-checkbox .uni-checkbox-input{
	width: 32rpx !important;
	height: 32rpx !important;
	border-radius: 50%!important;
}
/*checkbox选中后样式  */
uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked{
	background: #2F54D4;
	border: 1px solid transparent !important;
}
/*checkbox选中后图标样式  */
uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked::before{
	display: inline-block;
	width: 20rpx;
	height: 20rpx;
	line-height: 20rpx;
	text-align: center;
	font-size: 20rpx;
	color: #fff;
	background: transparent;
	transform: translate(-50%, -50%) scale(1);
	-webkit-transform: translate(-50%, -50%) scale(1);
}
uni-checkbox .uni-checkbox-input.uni-checkbox-input-disabled {
  background: #e89f9b;
}

/* #ifdef MP-WEIXIN */
/*checkbox 选项框大小  */
checkbox .wx-checkbox-input {
  border-radius: 50%;
  width: 35rpx;
  height: 35rpx;
}
/*checkbox选中后样式  */
checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background: #2F54D4;
  border-color: #2F54D4;
}
/*checkbox选中后图标样式  */
checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  text-align: center;
  font-size: 22rpx;
  color: #fff;
  background: transparent;
  transform: translate(-50%, -50%) scale(1);
  -webkit-transform: translate(-50%, -50%) scale(1);
}
/* #endif */
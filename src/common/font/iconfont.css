@font-face {
  font-family: "iconfont"; /* Project id 4166362 */
  src: url('https://m.wenet.com.cn/resources/shop-static/font/iconfont.woff2?v=3') format('woff2'),
        url('https://m.wenet.com.cn/resources/shop-static/font/iconfont.woff?v=3') format('woff'),
        url('https://m.wenet.com.cn/resources/shop-static/font/iconfont.ttf?v=3') format('truetype');
}

@font-face {
  font-family: "HelveticaNeueBold";
  src: url('https://m.wenet.com.cn/resources/shop-static/font/other/HelveticaNeueBold.woff')
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-xia:before {
  content: "\e681";
}

.icon-fuzhi:before {
  content: "\e6c5";
}
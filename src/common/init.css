
/**app.wxss**/
.container {
  height: 100%;
  box-sizing: border-box;
  color: #333;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 清除浮动 */
.clearfix::after {
  content: '';
  display: block;
  clear: both;
  height:0;
  visibility: hidden;
}
/* /清除浮动 */


/* 价格数字显示不同大小 */

.price{
  font-family: Arial;
  display: inline-block;
  color: #FF2442;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 20rpx;
 }
.symbol {
  margin-right: 4rpx;
}

.small-num {
  font-size: 24rpx;
}

.origin-price {
  font-family: Arial;
  display: inline-block;
  color: #999;
  font-weight: 600;
  font-size: 20rpx;
  line-height: 20rpx;
  text-decoration: line-through;
  margin-left: 16rpx;
}

/* 底部按钮兼容 iPhone X以上 */
@media screen and (width: 375px) and (height: 812px){
    .container {
        padding-bottom: 34px;
    }
}

@media screen and (width: 414px) and (height: 736px){
    .container {
        padding-bottom: 34px;
    }
}

.uni-tabbar .uni-tabbar__icon{
	width: 30px !important;
	height: 30px !important;
	margin-top: 0 !important;
}
.uni-tabbar .uni-tabbar__label{
	margin-top: 0 !important;
}

/* 隐藏头部 */
uni-page-head {
  display: none;
}

/* 轮播图指示点 */
uni-swiper .uni-swiper-dots-horizontal {
  bottom: 20px !important;
}

/* 新增底部tab挡住左侧分类tab点击 */
.uni-tabbar-bottom, uni-tabbar{
  bottom: 0px !important;
}

.uni-tabbar {
  position: fixed !important;
}

view {
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}
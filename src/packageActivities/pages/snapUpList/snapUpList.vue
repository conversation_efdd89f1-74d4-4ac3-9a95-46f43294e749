<template>
<view class="sk-wrap">
  <view class="sk-header">
    <view class="sk-header__info">
      <n-i icon="sk-title" width="184rpx" height="46rpx" />
    </view>
  </view>

  <view class="sk-content">
    <GoodsContainer>
      <SeckillCard
        v-for="item in seckillList"
        @tap-goods="toSeckillDetaile(item.seckillSearchVO.seckillId)"
        :goods-item="item"
        :key="item.prodId"
      />
    </GoodsContainer>
  </view>

	<pageBottomTips :isAll="loadAll" :current='current>1?true:false'/>

</view>
</template>


<script>
var http = require("../../../utils/http.js");
var util = require("../../../utils/util.js");
import GoodsContainer from '@/components/goods-card/goods-container'
import SeckillCard from '@/components/goods-card/seckill-card'
import NI from '@/components/n-i'
import pageBottomTips from '../../components/pageBottomTips/pageBottomTips';
export default {
  data() {
    return {
      seckillList: [],
      // 秒杀商品列表
      countDownList: [],
      // 秒杀倒计时列表
      countDownEndTimeList: [],
      timer: '',
			current: 1,
      pages: 0,
      loadAll: false, // 已加载全部
    };
  },

  components: {
    pageBottomTips,
    GoodsContainer,
    NI,
    SeckillCard
  },
  props: {},

  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
	  //头部导航标题
	  uni.setNavigationBarTitle({
      title:this.i18n.spikeSpecial
	  });
    
    // 秒杀商品列表
    this.getSecList(); 
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    clearTimeout(this.timer);
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
		if(this.current < this.pages) {
			this.current = this.current + 1
			this.getSecList()
		}else {
      this.setData({
        loadAll: true
      })
    }
	},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    /**
     * 获取秒杀商品列表
     */
    getSecList() {
			const pages = getCurrentPages()
			const currPage = pages[pages.length - 1].options
      const params = {
        // url: "/seckill/pageProd",
        url: "/search/page",
        method: "GET",
				data: {
					current: this.current,
					size: 20,
          prodType: 2,
          isActive: 1, // 过滤掉活动商品
					...(!!currPage.shopId && currPage.shopId !== '0' ? { shopId: currPage.shopId } : {}),
				},
        callBack: res => {
          let result = res.records[0].products.filter(item => item.seckillSearchVO && (util.dateToTimestamp(item.seckillSearchVO.endTime) > new Date().getTime()));
					for(let i = 0; i < result.length; i++) {
						const el = result[i]
						el.salesRatio = el.seckillSearchVO.seckillOriginStocks <= 0 ? 100 : parseInt(((el.seckillSearchVO.seckillOriginStocks - el.seckillSearchVO.seckillTotalStocks) / el.seckillSearchVO.seckillOriginStocks) * 100)
					}
          this.setData({
            seckillList: params.data.current==1? result : this.seckillList.concat(result),
						pages: res.pages
          });
          let endTimeList = []; // 倒计时数组
          for (let i = 0; i < this.seckillList.length; i++) {
            var objs = {};
            objs.eTime = this.seckillList[i].seckillSearchVO.endTime;
            objs.sTime = this.seckillList[i].seckillSearchVO.startTime;
            objs.countType = null; //1表示秒杀活动正在进行,0表示秒杀活动未开始
            endTimeList.push(objs);
          }
          this.setData({
            countDownEndTimeList: endTimeList
          });
          if(res.total<=4 && res.total>0){
            this.setData({
              loadAll:true
            })
          }
          this.countdown();
        }
      }
      http.request(params);
    },

    countdown() {
      // 获取当前时间，同时得到活动结束时间数组
      let newTime = new Date().getTime();
      let endTimeList = this.countDownEndTimeList;
      let countDownArr = []; // 对结束时间进行处理渲染到页面

      endTimeList.forEach(o => {
        if (newTime - util.dateToTimestamp(o.sTime) > 0) {
          let endTime = util.dateToTimestamp(o.eTime);
          let obj = null; // 如果活动未结束，对时间进行处理

          if (endTime - newTime > 0) {
            let time = (endTime - newTime) / 1000; // 获取天、时、分、秒

            let day = parseInt(time / (60 * 60 * 24));
            let hou = parseInt(time % (60 * 60 * 24) / 3600);
            let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
            let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
            obj = {
              day: this.timeFormat(day),
              hou: `${this.timeFormat(hou)}`,
              min: `${this.timeFormat(min)}`,
              sec: `${this.timeFormat(sec)}`,
              type: 1 // 表示秒杀正在进行

            };
          } // 活动已结束
          else {
              obj = {
                day: '00',
                hou: '00',
                min: '00',
                sec: '00'
              };
            }

          countDownArr.push(obj);
        } // 活动未开始
        else {
            let startTime = util.dateToTimestamp(o.sTime);
            let time = (startTime - newTime) / 1000;
            let obj = null; // 获取天、时、分、秒

            let day = parseInt(time / (60 * 60 * 24));
            let hou = parseInt(time % (60 * 60 * 24) / 3600);
            let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
            let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
            obj = {
              day: this.timeFormat(day),
              hou: `${this.timeFormat(hou)}`,
              min: `${this.timeFormat(min)}`,
              sec: `${this.timeFormat(sec)}`,
              type: 0 // 表示秒杀还没开始
            };
            countDownArr.push(obj);
          }
      }); // 渲染，然后每隔一秒执行一次倒计时函数

      this.setData({
        countDownList: countDownArr,
        timer: setTimeout(this.countdown, 1000)
      });
    },

    //小于10的格式化函数
    timeFormat(times) {
      return Number(times) < 10 ? '0' + times : times;
    },

    //跳转秒杀商品详情页
    toSeckillDetaile: function (seckillId) {
      uni.navigateTo({
        url: "/packageActivities/pages/snapUpDetail/snapUpDetail?seckillId=" + seckillId
      });
    }
  }
};
</script>
<style>
.sk-wrap {
  padding-bottom: 16rpx;
}
.sk-header {
  background: linear-gradient(168deg, #C1E5F1 0%, #E2DBF0 7.79%, #FFD2D3 14.59%, #F7EDE4 44.27%, #FFF 56.18%);
  padding: 48rpx;
  margin-bottom: 32rpx;
}

.sk-header__info {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px;
}


.sk-content {

}
</style>

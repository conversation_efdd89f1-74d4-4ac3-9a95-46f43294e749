<template>
<view class="container">
  <view class="coupons">
    <view class="banner">
      <image class="picture" src="https://m.wenet.com.cn/resources/shop-static/images/other/e6bc01d88cb941b6bf000720c0cc6e21.png"></image>
    </view>
    <view class="con-box">
      <view class="c-tit" v-if="couponList.length">平台券</view>
      <view v-if="couponList.length">
        <view 
          v-for="(item, index) in couponList"
          :key="item.couponId"
          class="coupon-item"
        >
          <ComplexCouponCard 
            :coupon="item"
            @setCouponCanGoUseFlag="setCouponCanGoUseFlag"
            @changeShowDetail="changeShowDetail"
          />
        </view>
      </view> 
    </view>
	  
    <view class="con-box">
      <view class="c-tit" v-if="prodCouponList.length">店铺券</view>
      <view v-if="prodCouponList.length">
        <view 
          v-for="(item, index) in prodCouponList"
          :key="item.couponId"
          class="coupon-item"
        >
          <ComplexCouponCard 
            :coupon="item"
            @setCouponCanGoUseFlag="setCouponCanGoUseFlag"
            @changeShowDetail="changeShowDetail"
          />
        </view>
      </view>
    </view>
	 
  </view>

  <view class="loadall" v-if="prodCouponList.length > 10 && loadAll">{{i18n.endTips}}</view>

  <!-- 空 -->
  <view class="empty" v-if="couponList.length==0 && prodCouponList.length==0">
    <view class="empty-icon">
      <image :src="`${staticPicDomain}images/icon/empty.png`"></image>
    </view>
    <view class="empty-text">{{i18n.merchantCoupon}}</view>
  </view>
</view>
</template>


<script>
// pages/couponCenter/couponCenter.js
var http = require("../../../utils/http.js");
var util = require("../../../utils/util.js");
import ComplexCouponCard from '@/components/coupon-card/complex-coupon-card'

export default {
  data() {
    return {
      couponList: [],
      // 通用券列表
      prodCouponList: [],
      // 商品券列表
      selectedCouponId: 0,
      // 要领取的优惠券id
      current: 1,
	    isLogin: uni.getStorageSync('token') ? true : false,
      size: 20,
      // 当前页数
      pages: 1, // 总页数
      loadAll: false, // 已加载全部
			isEn: uni.getStorageSync('lang') == 'en' ? true : false, // 是否为英文
    };
  },

  components: {
    ComplexCouponCard,
  },
  props: {},
  computed:{
  	i18n() {
  		return this.$t('index')
  	},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {//获取通用列表
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
	  //头部导航标题
	  uni.setNavigationBarTitle({
	      title: '领券中心'
	  });

    this.getCouponList();
    this.getProdCouponList(1);
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},

  /**
  * 页面上拉触底事件的处理函数
  */
  onReachBottom: function () {
    if (this.current < this.pages) {
      this.getProdCouponList(this.current + 1);
    }else {
      this.setData({
        loadAll: true
      })
    }
  },
  methods: {
    /**
     * 取整
     */
    // parseInt(val) {
    //   if (!val) {
    //     val = 0;
    //   }
    //   return parseInt(val);
    // },
    /**
     * 获取通用列表
     */
    getCouponList() {
	  let url;
	  url = this.isLogin ? '/p/myCoupon/generalCouponList' : '/coupon/generalCouponList'
      var params = {
        url: url,
        method: "GET",
        callBack: res => {
          this.initCouponFlag(res);
          this.setData({
            couponList: res
          });
        }
      };
      http.request(params);
    },

    /**
     * 获取指定商品券
     */
    getProdCouponList(cur) {
		let url;
		url = this.isLogin ? '/p/myCoupon/getCouponPage' : '/coupon/getCouponPage'
      var params = {
        url: url,
        method: "GET",
        data: {
          current: cur,
          size: this.size
        },
        callBack: res => {
          res.records = res.records?.filter(el => el.prods && el.prods.length > 0) || []
          this.initCouponFlag(res.records);
          let list = [];

          if (res.current == 1) {
            list = res.records;
          } else {
            list = this.prodCouponList;
            list = list.concat(res.records);
          }

          this.setData({
            prodCouponList: list,
            pages: res.pages,
            current: res.current
          });
        }
      };
      http.request(params);
    },

    /**
     * 初始化优惠券去使用、详情未展开的标记
     */
    initCouponFlag(couponList) {
      couponList.forEach(coupon => {
        coupon.canGoUse = coupon.curUserReceiveCount >= coupon.limitNum;
        coupon.isShowDetail = false
      });
    },

    /**
     * 设置优惠券去可以使用的标记
     */
    setCouponCanGoUseFlag(coupon) {
      if (coupon.shopId == 0) {
        var tempCouponList = this.couponList;
        let index = tempCouponList.findIndex(item => item.couponId === coupon.couponId)
        tempCouponList[index].canGoUse = true;
        tempCouponList[index].stocks -= 1;
        this.setData({
          couponList: tempCouponList
        });
      } else {
        var tempCouponList = this.prodCouponList;
        let index = tempCouponList.findIndex(item => item.couponId === coupon.couponId)
        tempCouponList[index].canGoUse = true;
        tempCouponList[index].stocks -= 1;
        this.setData({
          prodCouponList: tempCouponList
        });
      }
    },

    /**
     * 立即领取
     */
    receiveCoupon(e) {
      this.setData({
        selectedCouponId: e.currentTarget.dataset.couponid
      });
      util.checkAuthInfo(() => {
        var ths = this;

        if (ths.selectedCouponId) {
          uni.showLoading();
          http.request({
            url: "/p/myCoupon/receive",
            method: "POST",
            data: ths.selectedCouponId,
            callBack: data => {
              uni.hideLoading()
							uni.showToast({
                title: ths.i18n.successfullyReceived,
                icon: 'success',
                duration: 2000
              });
              this.setCouponCanGoUseFlag(e.currentTarget.dataset.couponindex, e.currentTarget.dataset.coupontype);
            }
          });
        }
      });
    },

    /**
     * 立即使用
     */
    useCoupon(e) {
      let url = '/pages/prod-classify/prod-classify?sts=4';
      let couponId = e.currentTarget.dataset.couponid;
      var title = this.i18n.couponEventGoods;
      var prodList = e.currentTarget.dataset.prodlist;

      if (prodList && prodList.length == 1) {
        // uni.navigateTo({
        //   url: '/pages/prod/prod?prodid=' + prodList[0].prodId
        // });
        this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodList[0].prodId }})
      } else {
        if (couponId) {
          url += "&tagid=" + couponId + "&title=" + title;
        }

        uni.navigateTo({
          url: url
        });
      }
    },
    changeShowDetail(coupon) {
      if (coupon.shopId == 0) {
        var tempCouponList = this.couponList;
        let index = tempCouponList.findIndex(item => item.couponId === coupon.couponId)
        tempCouponList[index].isShowDetail = !tempCouponList[index].isShowDetail
        this.setData({
          couponList: tempCouponList
        });
      } else {
        var tempCouponList = this.prodCouponList;
        let index = tempCouponList.findIndex(item => item.couponId === coupon.couponId)
        tempCouponList[index].isShowDetail = !tempCouponList[index].isShowDetail
        this.setData({
          prodCouponList: tempCouponList
        });
      }
    },
    formatStartTime: function(startTime){
      return util.formatTimeExceptSecond(new Date(startTime)) 
    },
    formatEndTime: function(endTime){
      return util.formatTimeExceptSecond(new Date(endTime)) 
    },
    formatTime: function(startTime, endTime) {
      return util.calculateTimeStatus(startTime, endTime)
    }
  }
};
</script>
<style scoped>
@import "./couponCenter.css";
</style>

page {
  background: #f4f4f4;
}
.container {
  background-color: #fff;
}

.coupons image {
  width: 100%;
  height: 100%;
}

.coupons .banner {
  position: relative;
  width: 750rpx;
  height: 320rpx;
  padding-top: 24rpx;
}

.coupons .banner .img {
  font-size: 0;
  width: 750rpx;
  height: 320rpx;
  position: relative;
}

.coupons .con-box {
  padding: 0 20rpx;
}

.coupons .con-box .c-tit {
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  position: relative;
  margin-top: 10rpx;
}

.coupons .con-box .c-tit::before {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  display: block;
  width: 6rpx;
  height: 30rpx;
  background: var(--primary-color);
  content: " ";
  font-size: 0;
}

.coupon-item {
  margin-bottom: 32rpx;
}

/* 加载完成 */
.loadall {
  margin: 20rpx 0;
  line-height: 2em;
  font-size: 28rpx;
  color: #aaa;
  text-align: center;
}


/* 列表为空 */
.empty {
  margin-top: 100rpx;
  text-align: cneter;
}

.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}

.empty-icon > image {
  width: 100%;
  height: 100%;
}

.empty-text {
  font-size: 24rpx;
  text-align: center;
  color: #999;
  line-height: 40rpx;
  margin-top: 10rpx;
}

/* 新人专享 */
.newuser-coupon {
  position: absolute;
  top: 0;
  right: 0;
}
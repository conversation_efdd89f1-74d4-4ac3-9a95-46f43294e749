<template>
<view class="coupon-list">
  <SimpleCouponCard 
    v-for="(item, index) in couponList"
    :key="index" 
    :coupon="item" 
    @setCouponCanGoUseFlag="setCouponCanGoUseFlag(index)"
  />
</view>
</template>
<script>
import http from '@/utils/http.js'
import SimpleCouponCard from '@/components/coupon-card/simple-coupon-card'

export default {
  data() {
    return {
      couponIds: [],
      couponList: [],
    }
  },
  components: {
    SimpleCouponCard,
  },
  onLoad: function (option) { 
    this.couponIds = decodeURIComponent(option.couponIds)
    //头部导航标题
	  uni.setNavigationBarTitle({
	      title: '优惠券列表'
	  });
  },
  mounted() {
    this.getCouponList()
  },
  methods: {
    getCouponList() {
      http.request({
        url: `/coupon/couponByIds`,
        method: 'GET',
        data: {
          couponIds: this.couponIds,
        },
        callBack: res => {
          this.initCouponCanGoUseFlag(res);
          this.couponList = res
        }
      })
    },
    // 初始化优惠券去可以使用的标记
    initCouponCanGoUseFlag(couponList) {
      couponList.forEach(coupon => {
        coupon.canGoUse = coupon.curUserReceiveCount >= coupon.limitNum;
      });
    },
    // 设置优惠券去使用的标记
    setCouponCanGoUseFlag(index) {
      var tempCouponList = this.couponList;
      tempCouponList[index].canGoUse = true;
      tempCouponList[index].stocks -= 1;
      this.setData({
        couponList: tempCouponList
      });
    },
  }
}
</script>
<style scoped>
@import url('./couponList.css');
</style>
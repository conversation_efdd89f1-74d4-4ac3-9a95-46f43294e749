<template>
  <!-- 团购列表 -->
  <view class="gb-wrap">
    <view class="gb-header">
      <n-i icon="gb-header" ext="png" width="100vw" height="436rpx"></n-i>
      <view class="gb-header__title">
        <n-i icon="gb-title" ext="png" width="100%" height="100%" mode="aspectFit"/>
      </view>
    </view>
    <view class="gb-content">
      <GoodsContainer>
        <GroupBookingCard
          v-for="item in aBulkList"
          @tap-goods="toAbulkListPage(item)"
          :goods-item="item"
          :key="item.prodId"
        />
      </GoodsContainer>
    </view>
    <view class="empty" v-if="aBulkList.length==0">
			<view class="empty-icon">
				<image :src="`${staticPicDomain}images/icon/empty.png`"></image>
			</view>
			<view class="empty-text">{{i18n.noMoreActivities}}</view>
		</view>
    <pageBottomTips :isAll="loadAll" :current='current>1?true:false' />
  </view>
</template>


<script>
import http from '@/utils/http.js'
import pageBottomTips from '../../components/pageBottomTips/pageBottomTips'
import GoodsContainer from '@/components/goods-card/goods-container'
import GroupBookingCard from '../../components/group-booking-card/group-booking-card'
import NI from '@/components/n-i'

export default {
  data() {
    return {
      aBulkList: [],
      //商品列表
      current: 1,
      size: 20,
      pages: "",
      loadAll: false, // 已加载全部
      // 秒杀商品列表
      countDownList: [],
      // 秒杀倒计时列表
      countDownEndTimeList: [],
      timer: '',
      pageParams: {},
      activityName: '',
      activityId: '',
    };
  },

  components: {
		pageBottomTips,
    GoodsContainer,
    NI,
    GroupBookingCard,
	},
  props: {},
  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.pageParams = options;
    this.getAbulkList();
    uni.setNavigationBarTitle({
      title: decodeURIComponent(decodeURIComponent(options.activityName)) || this.i18n.groupBuyingList
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
	  //头部导航标题
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.current < this.pages) {
      this.setData({
        current: this.current + 1
      });
      this.getAbulkList();
    }else {
      this.setData({
        loadAll: true
      })
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    // countdown() {
    //   // 获取当前时间，同时得到活动结束时间数组
    //   let newTime = new Date().getTime();
    //   let endTimeList = this.countDownEndTimeList;
    //   let countDownArr = []; // 对结束时间进行处理渲染到页面

    //   endTimeList.forEach(o => {
    //     if (newTime - util.dateToTimestamp(o.sTime) > 0) {
    //       let endTime = util.dateToTimestamp(o.eTime);
    //       let obj = null; // 如果活动未结束，对时间进行处理

    //       if (endTime - newTime > 0) {
    //         let time = (endTime - newTime) / 1000; // 获取天、时、分、秒

    //         let day = parseInt(time / (60 * 60 * 24));
    //         let hou = parseInt(time % (60 * 60 * 24) / 3600);
    //         let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
    //         let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
    //         obj = {
    //           day: this.timeFormat(day),
    //           hou: `${this.timeFormat(hou)}`,
    //           min: `${this.timeFormat(min)}`,
    //           sec: `${this.timeFormat(sec)}`,
    //           type: 1 // 表示秒杀正在进行

    //         };
    //       } // 活动已结束
    //       else {
    //           obj = {
    //             day: '00',
    //             hou: '00',
    //             min: '00',
    //             sec: '00'
    //           };
    //         }

    //       countDownArr.push(obj);
    //     } // 活动未开始
    //     else {
    //         let startTime = util.dateToTimestamp(o.sTime);
    //         let time = (startTime - newTime) / 1000;
    //         let obj = null; // 获取天、时、分、秒

    //         let day = parseInt(time / (60 * 60 * 24));
    //         let hou = parseInt(time % (60 * 60 * 24) / 3600);
    //         let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
    //         let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
    //         obj = {
    //           day: this.timeFormat(day),
    //           hou: `${this.timeFormat(hou)}`,
    //           min: `${this.timeFormat(min)}`,
    //           sec: `${this.timeFormat(sec)}`,
    //           type: 0 // 表示秒杀还没开始
    //         };
    //         countDownArr.push(obj);
    //       }
    //   }); // 渲染，然后每隔一秒执行一次倒计时函数

    //   this.setData({
    //     countDownList: countDownArr,
    //     timer: setTimeout(this.countdown, 1000)
    //   });
	  // },
	  
    //小于10的格式化函数
    timeFormat(times) {
      return Number(times) < 10 ? '0' + times : times;
    },
    /**
     * 获取团购商品列表
     */
    getAbulkList: function () {
      uni.showLoading();
			const pages = getCurrentPages()
			const currPage = pages[pages.length - 1].options
      var params = {
        // url: "/groupProd/page",
        url: "/groupProd/list",
        method: "GET",
        data: {
          current: this.current,
          size: this.size,
          groupActivityId: this.pageParams.activityId,
					...(!!currPage.shopId && currPage.shopId !== '0' ? { shopId: currPage.shopId } : {}),
        },
        callBack: res => {
          uni.hideLoading()
					var aBulkList = [];
          if (params.data.current == 1) {
            aBulkList = res.records;
          } else {
            aBulkList = this.aBulkList.concat(res.records);
          }
          if(res.total<=4 && res.total>0){
            this.setData({
              loadAll:true
            })
          }
          this.setData({
            aBulkList: aBulkList,
            pages: res.pages
          });
		  
        let endTimeList = []; // 倒计时数组
        for (let i = 0; i < this.aBulkList.length; i++) {
          var objs = {};
          objs.eTime = this.aBulkList[i].endTime;
          objs.sTime = this.aBulkList[i].startTime;
          objs.countType = null; //1表示秒杀活动正在进行,0表示秒杀活动未开始
          endTimeList.push(objs);
        }
        this.setData({
          countDownEndTimeList: endTimeList
        });
      }
      };
      http.request(params);
    },

    /**
     * 跳转到拼团商品详情
     */
    toAbulkListPage: function (item) {
      var prodId = item.prodId;
      var groupActivityId = item.groupActivityId;
      this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodId,groupActivityId: groupActivityId }})
    }
  }
};
</script>
<style>
.gb-wrap {
  position: relative;
}

.gb-header {
  position: sticky;
  height: 436rpx;
  top: 0;
}

.gb-header__title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 46rpx;
  z-index: 5;
  text-align: center;
}

.gb-content {
  position: relative;
  border-radius: 8px 8px 0 0;
  background-color: #fff;
  z-index: 10;
  top: -16rpx;
}

/* 列表为空 */
.empty {
  margin-top: 100rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 2em;
}

/* 加载完成 */
.loadall {
  margin: 20rpx 0;
  line-height: 2em;
  font-size: 28rpx;
  color: #aaa;
  text-align: center;
}
</style>

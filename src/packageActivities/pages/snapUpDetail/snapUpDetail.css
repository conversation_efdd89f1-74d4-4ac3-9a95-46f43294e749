/* pages/snapUpDetail/snapUpDetail.wxss */
page{
	height: 100%;
  overflow: visible;
}

clearfix::after {
  display: block;
  content: '';
  clear: both;
}
image {
  display: block;
  width: 100%;
  height: 100%;
}
/* 轮播图栏 */
/* .goods-img {
  width: 100%;
  height: 750rpx;
} */
swiper {
  height: 750rpx;
  width: 100%;
}

swiper image {
  height: 750rpx;
  width: 100%;
}
/* 商品视频 */
.swiper-con {
  position: relative;
}
.video-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 750rpx;
  justify-content: center;
  align-items: center;
  background: #000;
  z-index: 10;
}

.video-container video {
  display: block;
  width: 100%;
}

.play-btn {
  position: absolute;
  left: 50%;
  bottom: 12%;
  padding: 2rpx;
  background: rgba(255, 255, 255, 0.75);
  border-radius: 50rpx;
  color: #000;
  font-size: 24rpx;
  text-align: center;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 15;
}

.play-icon {
  width: 50rpx;
  height: 50rpx;
}

.play-text {
  padding-right: 10rpx;
  margin: 0 10rpx;
}

.video-stop {
  padding: 2rpx 8rpx;
}

.display {
  display: none;
}
.sub {
  font-size: 26rpx;
}


/* 倒计时栏 */
.countdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100rpx;
  box-sizing: border-box;
  background: linear-gradient(90deg, #FFE3CF 0%, #FFF0E6 74.58%, #FFB58C 100%);
  padding-right: 48rpx;
}

.countdown-tips {
  font-size: 26rpx;
  margin-right: 10rpx;
  font-size: 30rpx;
}

.countdown__left {
  height: 100%;
  position: relative;
}

.countdown__left__text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  text-align: center;
  color: #fff;
  font-size: 48rpx;
  font-weight: 600;
}

.countdown__center {
  color: var(--primary-color);
  font-size: 24rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  column-gap: 16rpx;
}

.prod-info {
  width: 100%;
  padding: 48rpx;
  box-sizing: border-box;
  border-radius: 0 0 20rpx 20rpx;
  position: relative;
  background: #fff;
  word-break: break-all;
}

.tit-wrap {
  position: relative;
  border-radius: 8px;
  border: 1px solid #F1F1F1;
  padding: 32rpx;
}

.prod-tit {
  font-size: 32rpx;
  color: #333;
  word-break: break-all;
  word-wrap: break-word;
  font-weight: bold;
  padding-bottom: 8rpx
}

.sub-tit{
	font-size: 28rpx;
	color: #888;
  border-top: 1px solid #f1f1f1;
  padding-top: 24rpx;
  margin-top: 24rpx;
}


.prod-price {
  font-size: 30rpx;
  line-height: 40rpx;
  position: relative;
  margin-bottom: 32rpx;
}

.prod-price__line1 {
  display: flex;
  height: 60rpx;
  column-gap: 16rpx;
}

.prod-price__sell-price {
  height: 60rpx;
  color: var(--primary-color);
  font-size: 40rpx;
  font-weight: 600;
}

.prod-price__sell-price__symbol {
  margin-right: 8rpx;
}

.prod-price__sell-price__big {
  font-size: 60rpx;
}

.prod-price__line-through-price {
  color: #999;
  font-size: 24rpx;
  font-weight: 400;
}

.prod-price__line-through-price__value {
  text-decoration: line-through;
}

.font-labels {
  font-size: 28rpx;
}
.corner-mark-labels {
  position: relative;
  display: inline-block;
  margin-right: 16rpx;
}
.corner-mark-style {
  display: inline-block;
  font-size: 24rpx;
  padding: 0rpx 8rpx;
  border: 2rpx solid transparent;
  border-radius: 4rpx;
}

/* 运费&库存 */
.other-infor {
  font-size: 23rpx;
  color: #aaa;
  padding-top: 15rpx;
  text-align: left;
}
.inventory {
  font-size: 26rpx;
  color: #777777;
}

/* 拼团详情 */
.spell-infor {
  border-bottom: 20rpx solid #f7f7f7;
  font-size: 28rpx;
  padding: 20rpx;
}
.spell-infor-title {
  font-size: 26rxp;
}
.spell-infor-content {
  position: relative;
  padding-top: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.head-img {
  display: inline-block;
  width: 80rpx;
  height: 80rpx;
  margin-right: 15rpx;
}
.spell-msg {
  display: inline-block;
  vertical-align: top;
  font-size: 24rpx;
}
.spell-text {
  padding-top: 10rpx;
  color: #aaa;
}
.red-font {
  color: red;
}
.join-group {
  position: absolute;
  right: 10rpx;
  bottom: 30rpx;
  color: red;
  border: 1rpx solid red;
  padding: 10rpx 15rpx;
  font-size: 24rpx;
}
.rules {
  position: relative;
  font-size: 25rpx;
  padding-top: 20rpx;
}
.rules-text02 {
  float: right;
  font-size: 23rpx;
  color: #aaa;
  padding-right: 30rpx;
}
.right-arrow {
  position: absolute;
  right: 5rpx;
  top: 20rpx;
  display: inline-block;
  width: 20rpx;
  height: 20rpx;
}

/* 预计开始时间 */
.expect-start {
  border-top: 1px solid #f2f2f2;
  margin-top: 15rpx;
  font-size: 23rpx;
  color: #999;
  padding-top: 15rpx;
}
.second-kill-icon {
  margin-right: 15rpx;
  color: #e60000;
  padding: 4rpx 8rpx;
  border: 1px solid #e60000;
  font-size: 20rpx;
}

.prod-detail {
  width: 100%;
  background: #fff;
  margin: 20rpx 0;
  padding: 0 48rpx 128rpx;
  box-sizing: border-box;
  position: relative;
  line-height: 48rpx;
}

.prod-detail-title {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
}
.prod-detail-title-line {
  width: 144rpx;
  height: 2rpx;
  background: #f1f1f1;
}

.prod-detail-title-text {
  flex-shrink: 0;
  color: #999;
  font-size: 24rpx;
  font-weight: 400;
  margin: 0 32rpx;
}

/* 评价 */
.estimate {
  border-top: 20rpx solid #f7f7f7;
  border-bottom: 20rpx solid #f7f7f7;
  font-size: 25rpx;
  padding: 25rpx 20rpx;
  text-align: left;
  margin-bottom: 150rpx;
}
.estimate-num {
  float: right;
  font-size: 23rpx;
  color: #999;
}

/* 底部按钮 */
.footer {
	position: fixed;
	display: flex;
	width: 100%;
	left: 0;
	bottom: 0;
	background-color: #fff;
	padding: 18rpx 30rpx ;
  padding-bottom: calc(18rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(18rpx + env(safe-area-inset-bottom));
	box-sizing: border-box;
	z-index: 20;
}
.foot-btn {
	font-size: 24rpx;
	color: #666;
	margin-right: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.foot-btn:last-child {
	margin: 0
}
.foot-btn > image {
	width: 60rpx;
	height: 60rpx;
}
.buy-btn {
	flex: 1;
	box-sizing: border-box;
	background-color: var(--primary-color);
	color: #FFFFFF;
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
}
/* 零售价goumai */
.retail-price-buy {
  background: #ff8854;
}
/* 底部按钮end */





/* 弹框 */
/*模态框*/
.modals{
	position:fixed;
	z-index: 999;
	top:0;
	left: 0;
	right:0;
	bottom: 0;
}
.modals-cancel{
	position:absolute;
	z-index:190;
	top:0; left: 0;
	right:0;
	bottom: 0;
	background-color: rgba(0,0,0,.5);
}
.bottom-dialog-body {
	position:absolute;
	z-index:200;
	bottom:0;
	left:0;
	right:0;
	max-height:80%;
	background-color: #fff;
}
/*动画前初始位置*/
.bottom-pos{
	-webkit-transform:translateY(100%);
	transform:translateY(100%);
  padding-bottom: 200rpx;
  box-sizing: border-box;
}
/* 上部内容 */
.top-content {
  position: relative;
  border-bottom: 1px solid #f2f2f2;
  font-size: 26rpx;
  padding: 15rpx;
}
.top-content-img-box {
  display: inline-block;
  width: 150rpx;
  height: 150rpx;
  background: #f2f2f2;
  vertical-align: middle;
}
.top-content-img-box > image {
  width: 100%;
  height: 100%;
}
.goods-msg-box {
  display: inline-block;
  width: 68%;
  margin-left: 20rpx;
  vertical-align: middle;
}
.goods-names {
  line-height: 1.5em;
  color: #000;
  font-size: 28rpx;
  padding-right: 15rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2; /*设定显示行数*/
  -webkit-box-orient: vertical;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
}
.group-tips {
  font-size: 20rpx;
  color: var(--primary-color);
  padding: .3em 0;
  padding-top: 0;
}
.goods-prices {
  font-size: 30rpx;
  color: var(--primary-color);
  margin-top: 5rpx;
}
.rmb-symbol {
  font-size: 34rpx;
}
.close-btn {
  position: absolute;
  display: block;
  right: 20rpx;
  top: 15rpx;
  width: 40rpx;
  height: 40rpx;
  /* background: url("../../static/icon/close.png");
  background-size: 100% 100%; */
  z-index: 250;
}
.close-btn > image {
  width: 100%;
  height: 100%;
}

/* 颜色选择 */
.choose-color-box {
  border-bottom: 1px solid #f2f2f2;
}
.choose-color-box .items {
  padding: 20rpx;
  font-size: 28rpx;
}
.color-txt {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
}
.color-box {
  display: block;
  box-sizing: border-box;
  padding: 8rpx 0;
}
.color-item {
  display: inline-block;
  padding: 0 16px;
  max-width: 270px;
  overflow: hidden;
  line-height: 2em;
  text-align: center;
  margin-top: 10px;
  color: #333;
  background-color: #fff;
  font-size: 24rpx;
  border: 1px solid #E9EBED;
  border-radius: 60rpx;
}
.color-item:not(:first-child) {
  margin-left: 10px;
}
.color-item.gray{
  background-color: #f9f9f9;
  color: #ddd;
}
.color-item.dashed{
  border:1px dashed #ccc;
}
.color-item-current {
  color: var(--primary-color); 
  border:1px solid var(--primary-color) !important;
}



/* 数量选择 */
.quantity {
  padding: 1em;
  font-size: 28rpx;
  border-bottom: 1px solid #f2f2f2;
  /* margin-bottom: 180rpx; */
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.left-text {
  line-height: 1.5em;
}
.left-text-bottom {
  font-size: 25rpx;
  color: #999;
}
.left-text-bottom-max-num{
   color: #f00;
}
/* choose-quantity */
.right-choose {
  margin-right: 10rpx;
	display: flex;
}
.subtract,
.show-num,
.add {
	display: flex;
	align-items: center;
	justify-content: center;
  background: #f5f5f5;
  color: #666;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
}
.show-num {
  width: 70rpx;
  margin: 0 10rpx;
}

/* 确认按钮 */
.confirm-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  /* height: 110rpx; */
  padding: 20rpx 6rpx;
  box-sizing: border-box;
  z-index: 6;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.confirm-btn .confirm {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  font-size: 30rpx;
  flex-flow: column;
  border-radius: 60rpx;
  padding: 26rpx 0;
  margin: 0 6rpx;
  background: var(--primary-color);
  color: #fff;
  line-height: 1.2em;
  text-align: center;
  margin-bottom: 10rpx;
}
.confirm-btn.gray .confirm {
  background-color: #f5f5f5;
  color: #aaa;
}
.sku-selectbox  {
	max-height: 275px;
	overflow-y: scroll;
}
/* 底部弹框end */


/* 直播按钮 */
.live {
  position: fixed;
  top: 600rpx;
  right: 30rpx;
}
.live .earn {
  width: 90rpx;
  height: 80rpx;
  border-radius: 14rpx;
}

/* 留言（虚拟商品） */
.virtual-goods-tips {
  display: block;
  background: #F9F9F9;
  padding: 20rpx;
  color: #999999;
  font-size: 24rpx;
  margin-bottom: 20rpx;
  line-height: 1.5em;
}
.virtual-goods-msg {
  display: block;
  max-height: 540rpx;
  overflow-y: auto;
  padding: 20rpx;
}
.virtual-goods-msg .msg-item {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #F2F2F2;
  font-size: 24rpx;
  padding-bottom: 20rpx;
  margin-top: 40rpx;
}
.virtual-goods-msg .msg-item .msg-tit {
  flex-wrap: wrap;
  width: 180rpx;
  margin-right: 20rpx;
  word-break: break-word;
}
.virtual-goods-msg .msg-item .stress {
  color: var(--primary-color);
  margin-right: 10rpx;
}
.virtual-goods-msg .msg-item .msg-int {
  font-size: 24rpx;
  width: 100%;
}
.virtual-goods-msg .msg-item .msg-int .uni-input-placeholder {
  color: #aaa;
}
/* 留言（虚拟商品）/ */
/** 参数 */
.parameter-box {
  margin: 0 30rpx;
  display: flex;
  height: 80rpx;
  align-items:center;
  font-size: 24rpx;
  border-bottom: 1px dashed #AAAAAA;
}
.parameter-box:last-child {
	border: 0 !important;
}
.parameter-box .parameter-key{
  min-width: 250rpx;
  color: #999999;
  line-height: 31rpx;
}
.parameter-box .parameter-vaule{
  line-height: 31rpx;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
/** 参数end*/

/*弹窗解决遮罩层移动问题*/
.contenta{
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
.page-hidden{
  height: 100%;
  overflow: hidden
}

/* 引导蒙版 end */


.float-btns {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32rpx;
  top: 48rpx;
  right: 48rpx;
  color: white;
}
.float-btns .float-icon {
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx; 
  background: rgba(0, 0, 0, 0.50);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8rpx;
}
.float-btns button {
  background-color: transparent;
  height: 48rpx;
  padding: 0;
}
.float-btns button:after{
  display:none;
}
.float-btns image {
  vertical-align: top;
}

/* 赠品样式 */
.giveaway-list {
  display: flex;
}
.giveaways-content{
  display: flex;
  font-size: 24rpx;
}
.giveaway-prod {
  flex: 1;
  /* width: 100%; */
}
.giveaways-name{
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.giveaways-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.giveaways-item .number {
  font-size: 24rpx;
  margin-top: 4rpx;
  margin-bottom: 20rpx;
}
.giveaways-item-right{
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
}
.giveaways-item-right::after{
  content: '';
  width: 13rpx;
  height: 13rpx;
  border-top: 2rpx solid #969798;
  border-right: 2rpx solid #969798;
  transform: rotate(45deg);
}
.discount-tag {
  min-width: 100rpx;
  text-align: center;
  box-sizing: border-box;
  font-size: 20rpx;
  padding: 0 6rpx;
  background: var(--light-background);
  color: var(--primary-color);
  margin-right: 10rpx;
  height: 36rpx;
  line-height: 36rpx;
  font-family:arial;
}
/* 赠品样式 end */


/** 弹窗 **/

.popup-hide {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.popup-box {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 375px;
  max-height: 490px;
  overflow: hidden;
  background-color: #fff;
}

.popup-tit {
  position: relative;
  height: 46px;
  line-height: 46px;
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  background-color: #f7f7f7;
  margin-bottom: 30rpx;
}

.close {
  color: #aaa;
  border-radius: 12px;
  line-height: 20px;
  text-align: center;
  height: 20px;
  width: 20px;
  font-size: 18px;
  padding: 1px;
  top: 10px;
  right: 10px;
  position: absolute;
}

.close::before {
  content: '\2716';
}

.popup-cnt {
  max-height: 429px;
  overflow: auto;
  padding: 0 10px;
  background: #fff;
  padding-bottom: 60rpx;
}

.popup-coupon-con {
  padding-bottom: 120rpx;
}

/* 弹窗 end */
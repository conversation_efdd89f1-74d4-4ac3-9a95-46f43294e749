<template>
	<view :class="['contenta',popupShowHiden?'page-hidden' : '']">

		<view class="" v-if="countDownList[0]">

			<!-- 秒杀商品详情 -->
			<!-- pages/snapUpDetail/snapUpDetail.wxml -->
			<view class="top-goods-box">
				<!-- 商品图片 -->
				<!-- 轮播图 -->
				<!-- 轮播图 -->
				<view class="swiper-con">
					<view class="video-container" v-if="video && isPlaying">
						<video enable-progress-gesture="false" objectFit="fill" id="myVideo" @ended="playEnd" :src="video" controls></video>
					</view>
					<view class="play-btn" v-if="video" @tap="videoOper">
						<image class="play-icon" v-if="!isPlaying" :src="`${staticPicDomain}images/icon/play-red.png`"></image>
						<text :class="'play-text' + isPlaying ? '':'video-play'">{{isPlaying?this.i18n.quitPlaying: ''}}</text>
					</view>
          <TopSwiper v-if="!isPlaying" :imgs="imgs"/>
				</view>
				<!-- 轮播图end -->
				<!-- 倒计时栏 -->
				<view class="countdown">
          <view class="countdown__left">
            <n-i icon="shape1" width="246rpx" height="100rpx"  />
            <view class="countdown__left__text">
              <n-i icon="seckill-text" width="158rpx" height="44rpx" />
            </view>
          </view>
          <view class="countdown__center">
            <n-i icon="lighting" width="37rpx" height="47rpx" />
            <text>还剩{{ defaultSku.seckillStocks }}件</text>
          </view>
          <view v-if="countDownList[0].type!==2">
            <text class="countdown-tips" v-if="countDownList[0].type===0">{{i18n.onlyStart}}</text>
            <CountDown :times="countDownList[0]"></CountDown>
          </view>
          <view v-else>
            <text class="countdown-tips">{{i18n.secondKillOver}}</text>
          </view>
				</view>

					<!-- 悬浮按钮 start -->
				<view class="float-btns">
					<!-- #ifdef MP-WEIXIN -->
					<view class="float-icon">
						<button open-type='share' v-if="!(isDist && isDistProd)">
							<n-i icon="share" width="48rpx" height="48rpx"  />
						</button>
					</view>
					<!-- #endif -->
					<view v-if="mold !== 1" class=" float-icon" style="height: 48rpx;" @tap="toCartPage">
						<n-i icon="basket" width="48rpx" height="48rpx"  />
					</view>
				</view>
				<!-- 悬浮按钮 end -->
			</view>
      
      <view class="prod-info">
        <view class="prod-price">
          <view class="prod-price__line1">
            <!-- 商品售卖价 -->
            <view class="prod-price__sell-price">
              <text class="prod-price__sell-price__symbol">¥</text>
              <text class="prod-price__sell-price__big">{{ parsePrice(defaultSku.seckillPrice)[0] }}.</text>
              <text class="prod-price__sell-price__small">{{ parsePrice(defaultSku.seckillPrice)[1] }}</text>
            </view>
            <!-- 商品原价 -->
            <view class="prod-price__line-through-price">
              <text class="prod-price__line-through-price__tip">价格 </text>
              <text class="prod-price__line-through-price__value">¥ {{ toPrice(originalPrice) }}</text>
            </view>
          </view>
        </view>

	
				<view class="tit-wrap">
					<view class="prod-tit">{{seckilldet.prodName}}</view>
					<!-- 纯文字标签 -->
					<view class="font-labels">
						<text v-for="(item, index) in fontLabels" :key="item.labelId" :style="{color: item.textColor}">
							{{ item.labelName }}
							<text v-if="index !== fontLabels.length - 1" style="color: #888; margin: 0rpx 8rpx">|</text>
						</text>
					</view>
          <!-- 角标标签 -->
					<view v-for="(row, idx) in cornerMarkLabels" :key="idx" class="corner-mark-labels"> 
						<text
							v-if="row.style === 'background-only'" 
							class="corner-mark-style"
							:style="{color: '#fff', backgroundColor: row.bgColor}"
						>
							{{ row.labelName }}
						</text>
						<text 
							v-if="row.style === 'font-only'"  
							class="corner-mark-style"
							:style="{color: row.bgColor, borderColor: row.bgColor}"
						>
							{{ row.labelName }}
						</text>
						<text 
							v-if="row.style === 'both-colors'" 
							class="corner-mark-style"
							:style="{color: row.textColor, backgroundColor: row.bgColor}"
						>
							{{ row.labelName }}
						</text>
					</view>	
          <view class="sub-tit" v-if="!!brief">{{ brief }}</view>
				</view>
			</view>
      <ProdTableDetail
        :delivery-way-text="deliveryWayText"
        :selected-prop="selectedProp"
        :prod-num="prodNum"
        :giveaway-list="giveawayList"
        @tapSku="showModal"
        @countMinus="onCountMinus"
        @tapGiveaway="clickDiscount"
        @countPlus="onCountPlus"
        @prodNumInp="prodNumInp"
      />

			<!-- 商品详情 -->

			<view class="prod-detail">
        <view class="prod-detail-title">
          <view class="prod-detail-title-line"></view>
          <text class="prod-detail-title-text">商品详情图</text>
          <view class="prod-detail-title-line"></view>
        </view>
				<view>
					<view class="parameter-box" v-for="item in prodParameterList" :key="item.prodParameterId">
						<view class="parameter-key"> 
							{{ item.parameterKey }}
						</view>
						<view class="parameter-vaule">
							{{ item.parameterValue }}
						</view>
					</view>
				</view>
				<ProductionRichText :nodes="content"></ProductionRichText>
			</view>
			<!-- 商品详情end -->
			<!-- 底部弹框 -->
			<view class="modals" :hidden="hideModal" @tap='hideModal = true;popupShowHiden= false'>
				<view class="modals-cancel" @tap="hideModalFun"></view>
				<view class="bottom-dialog-body bottom-pos" :animation="animationData" @tap.stop>
					<!-- 上部 -->
					<view class="top-content">
						<view class="top-content-img-box">
							<image :src="defaultSku.pic?defaultSku.pic:pic"></image>
						</view>
						<!-- 商品信息 -->
						<view class="goods-msg-box">
							<view class="goods-names">{{seckilldet.prodName}}</view>
							<view class="goods-prices" v-if="findSku">{{i18n.secondKillPrice}}：￥<text class="rmb-symbol">{{toPrice(defaultSku.seckillPrice)}}</text>
							</view>
							<view class="goods-prices" v-if="!findSku">{{i18n.outOfStock}}</view>
							<!-- 关闭按钮 -->
						</view>
						<view class="close-btn" @tap="hideModalFun">
							<image :src="`${staticPicDomain}images/icon/close.png`"></image>
						</view>
					</view>
					<!-- 说明（虚拟商品） -->
					<view v-if="mold === 1 && (writeOffNum !== 0 || (writeOffNum === 0 && isRefund === 0))" class="virtual-goods-tips">
						<text class="vi-t">{{i18n.usageInstructions}}：</text>
						<!-- writeOffNum 0无需核销 1单次核销 -1多次核销 -->
						<block v-if="writeOffNum !== 0">
						<!-- writeOffTime核销有效期 -1.长期有效 0.自定义 x.x天内有效 -->
							<text v-if="writeOffTime === -1">{{i18n.longTermValidity}}</text>
							<text v-else-if="writeOffTime === 0">{{i18n.afterPurchase}} {{writeOffStart}} {{i18n.to}} {{writeOffEnd}} <text v-if="!isEn">{{i18n.effective}}</text></text>
							<text v-else-if="writeOffTime === 1">{{i18n.validOnTheSameDay}}</text>
							<text v-else>{{i18n.purchase}}{{writeOffTime}}{{i18n.validDay}}</text>
						</block>
						<!-- isRefund 0不支持退款 1支持退款 -->
						<text v-if="isRefund === 0"><text v-if="writeOffNum !== 0 ">，</text>{{i18n.refundsAreNotAllowed}}</text>
					</view>
					<!-- 规格 -->
					<view class="sku-selectbox" v-if="seckilldet.skuList.length && seckilldet.skuList.length > 0">
						<view class="choose-color-box">
							<view class="items sku-text" v-for="(skuLine, key) in skuGroup" :key="key">
								<text class="color-txt">{{key}}</text>
								<view class="color-box">
									<!-- <text class="color-item" @click="toChooseItem(skuLineItem, key)" :class="[selectedProp.indexOf(skuLineItem) !== -1?'color-item-current':'',  isSkuLineItemNotOptional(allProperties,selectedPropObj,key,skuLineItem,propKeys)? 'dashed' : '']"
									 v-for="skuLineItem in skuLine" :key="skuLineItem">{{skuLineItem}}</text> -->
									 <text
									 	v-for="skuLineItem in skuLine"
										:key="skuLineItem"
									 	class="color-item"
										@click="toChooseItem(skuLineItem, key)"
										:class="
											[selectedProp.find(el => el.key === key && el.value === skuLineItem)?'color-item-current':'',
											isSkuLineItemNotOptional(allProperties,selectedPropObj,key,skuLineItem,propKeys)? 'dashed' : '']
										"
										>{{skuLineItem}}</text>
								</view>
							</view>
						</view>
					</view>
					<!-- 数量 -->
					<view class="quantity">
						<view class="left-text">
							<view class="left-text-top">{{i18n.purchaseQuantity}}</view>
							<view class="left-text-bottom" v-if="defaultSku">{{i18n.remaining}}{{defaultSku.seckillStocks}}{{i18n.piece}}</view>

						</view>
						<!-- 数量加减 -->
						<view class="right-choose">
							<view :class="['subtract',prodNum>1?'current-state':'']" @tap="onCountMinus">–</view>
							<view class="show-num current-state">
								<input type="number" :value="prodNum" @input="prodNumInp" />
							</view>
							<view class="add current-state" @tap="onCountPlus">＋</view>
						</view>
					</view>
					<!-- 留言（虚拟商品） -->
					<view v-if="mold === 1" class="virtual-goods-msg">
						<view v-for="(item, index) in virtualRemarks" :key="index" class="msg-item">
							<text class="msg-tit"><i v-if="item.isRequired" class="stress">*</i>{{item.name}}</text>
							<input class="msg-int" v-model="item.value" :placeholder="i18n.pleaseEnter + `${item.name}`" maxlength="20" />
						</view>
					</view>

					<!-- 确认按钮 -->
					<view :class="'confirm-btn ' + (findSku && defaultSku.seckillStocks >= prodNum && defaultSku.seckillStocks >0? '':'gray')" @tap="seckillconfirm"
						v-if="orderNumber ? false : countDownList[0].type == 1">
						<view class="confirm">{{i18n.confirm}}</view>
					</view>
					<view class="confirm-btn gray" v-if="countDownList[0].type != 1">
						<view class="confirm">{{i18n.secondKillTips}}</view>
					</view>
				</view>
			</view>

			<!-- 【秒杀】立即抢购btn -->
			<view class="footer">
				<view class="foot-btn" @tap="toHomePage">
					<image src="/static/images/tabbar/tabbar_icon_home_default.png"></image>{{i18n.homepage}}
				</view>
				<view class="foot-btn" @tap="handleCustomService" v-if="shopCustomerSwitch">
					<image :src="`${staticPicDomain}images/icon/shop-customer-service.png`"></image>{{i18n.service}}
				</view>
				<view class="foot-btn buy-btn" v-if="countDownList[0].type===1" @tap="showModal">
					{{i18n.immediatelyBuy}}
				</view>
				<view class="foot-btn buy-btn retail-price-buy" v-if="countDownList[0].type===0" @tap="toProdDetailsPage">
					{{i18n.retailPricePurchase}}
				</view>
			</view>

			<!-- 直播悬浮按钮 -->
			<view class="live" v-if="liveRoomParams && liveRoomParams.length" :data-roomid="liveRoomParams[0].roomId" :data-url="liveRoomParams[0].url"
			 @tap='toLivePage'>
				<image class="earn" :src="`${staticPicDomain}images/liveBroadcast/living03.png`"></image>
			</view>

    <!-- 	客服按钮 -->
				<contact-button
					:title="seckilldet.prodName"
					:img="imgs[0]"
				/>

		</view>

    <!-- 满减折弹窗 -->
		<view class="popup-hide" v-if="showDiscountPopup" @tap='showDiscountPopup = false'>
			<view class="popup-box radius" @tap.stop>
				<view class="popup-tit radius">
					<text>{{i18n.promotion}}</text>
					<text class="close" @tap='clickDiscount'></text>
				</view>
				<view class="popup-cnt popup-discount">
					<view class="coupon-con discount-con">

						<!-- 赠品列表 -->
					<view class="giveaway-list" v-if="giveawayList.length">
						<view class="discount-tag">{{i18n.Giveaways}}</view>
						<view class="giveaway-prod">
							<block v-for="(item, index) in giveawayList" :key="index">
								<view class="giveaways-item">
									<view class="giveaways-content">
											<view class="giveaways-name" @tap="toGiveawaysProd(item.prodId)">
												<view class="discount-content">{{item.prodName}}</view>
												<view class="giveaways-item-right"></view>
											</view>
									</view>
									<view class="number"> <text v-if="item.skuName" decode>{{item.skuName+'&nbsp;&nbsp;'}}</text> x{{item.giveawayNum}}</view>
								</view>
							</block>
						</view>
					</view>

					</view>
				</view>
			</view>
		</view>
		<!-- 满减折弹窗 end -->
	</view>
</template>


<script>
	// pages/snapUpDetail/snapUpDetail.js
	var http = require("../../../utils/http.js");
	var util = require("../../../utils/util.js");
	var config = require("../../../utils/config.js");
  import TopSwiper from '@/components/production/swiper'
  import ProductionRichText from "@/components/production/ProductionRichText";
  import ProdTableDetail from '@/components/prod-table-detail'
  import CountDown from '@/components/count-down'
  import NI from '@/components/n-i'
	// #ifdef H5
	import Wechat from "../../../utils/wechat.js";
	// #endif
  import ContactButton from '@/components/contact-button'
	export default {
		data() {
			return {
				isEn: uni.getStorageSync('lang') == 'en' ? true : false, // 是否为英文
				isWechat: false, //是否为微信环境
				hideModal: true,
				popupShowHiden: false,
				seckillId: 0,
				orderNumber: null,
				seckilldet: {},
				countdown: "",
				// 秒杀倒计时
				countdownlist: [],
				// 秒杀倒计时列表
				clearTimer: null,
				originalPrice: 0,
				skuList: [],
				skuGroup: {},
				defaultSku: undefined,
				selectedProp: [],
				selectedPropObj: {},
				propKeys: [],
				allProperties: [],
				seckillPrice: 0,
				prodNum: 1,
				maxNum: 1,
				orderPath: '',
				seckillTotalStocks: 1,
				//秒杀活动剩余库存
				findSku: true,
				isAuthInfo: true,
				countDownListTimer: '',
				countDownList: "",
				animationData: "",
				content: '', // 商品详情富文本
				// 轮播图片相关
				imgs: '',
				video: '', // 商品视频
				isPlaying: false,
				showBacktop: false,
				liveRoomParams: [], // 直播列表信息
				shopId: '',
				pic: "",
				prodId:null,

				// 虚拟商品
				mold: null, // 1虚拟商品
				virtualRemarks: [],  // 留言
				isRefund: null, // 0不支持退款 1支持退款
				writeOffTime: null, // 核销有效期 -1.长期有效 0.自定义 x.x天内有效
				writeOffNum: null, // 核销次数 -1.多次核销 0.无需核销 1.单次核销
				writeOffStart: null, // 核销有效期开始时间
				writeOffEnd: null, // 核销有效期结束时间
				prodParameterList: [],
        brief: '',
        labels: [],
        deliveryModeVO: null,
        giveawayList: [], // 赠品商品栏
        showDiscountPopup: false,
			};
		},
		props: {},

		computed: {
			i18n() {
				return this.$t('index')
			},
      shopCustomerSwitch() {
        return this.$store.state.shopCustomerSwitch
      },
      fontLabels() {
				return this.labels.filter(item => item.type == '1')
			},
			cornerMarkLabels() {
				return this.labels.filter(item => item.type == '2' && item.position === 'below-headline')
			},
      deliveryWayText() {
        if (this.deliveryModeVO && this.mold !== 1 && this.prodTypedeliveryModeVO != 5) {
          if (this.deliveryModeVO.hasShopDelivery) {
            return this.i18n.expressDelivery
          }
          if (this.deliveryModeVO.hasCityDelivery) {
            return this.i18n.sameDelivery
          }
          if (this.deliveryModeVO.hasUserPickUp) {
            return this.i18n.pickStore
          }
        } else if (this.mold === 1) {
          return this.i18n.noNeedDelivery
        }
      },
		},
    components: {
      TopSwiper,
      ProductionRichText,
      ProdTableDetail,
      CountDown,
      NI,
      ContactButton,
    },

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			// #ifdef H5
			this.isWechat = Wechat.isWechat()
			// #endif
			// #ifdef APP-PLUS
			this.isWechat = false
			// #endif
			// #ifdef MP-WEIXIN
			this.isWechat = true
			// #endif

			this.setData({
				seckillId: options.seckillId,
				orderNumber: options.orderNumber,
			});
			// this.getskdet(true);
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			//头部导航标题
			uni.setNavigationBarTitle({
				title: this.i18n.secondsKillProductDetails
			});

			this.getskdet(true);
		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {
			this.hideModal = true
			this.popupShowHiden = false
			if (this.isPlaying) {
				this.stopPlay()
			}
		},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {
			clearTimeout(this.countDownListTimer);
		},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		watch: {
			skuShow(nv) {
				if (nv && this.isPlaying) {
					this.stopPlay()
				}
			},
			commentShow(nv) {
				if (nv && this.isPlaying) {
					this.stopPlay()
				}
			},
			popupShow(nv) {
				if (nv && this.isPlaying) {
					this.stopPlay()
				}
			}
		},
		methods: {
      /**
			 * 跳转到赠品详情
			 */
			toGiveawaysProd(id){
				uni.navigateTo({
					url:'/pages/prod/prod?prodid='+ id
				})
			},
      clickDiscount: function() {
				this.showDiscountPopup = !this.showDiscountPopup
			},
			/**
			 * 输入商品数量
			 */
			prodNumInp(e) {
				let num = Number(e.detail.value.replace(/[^\d]/g,''))
				this.prodNum = num
			},
			/**
			 * 咨询客服
			 */
			handleCustomService() {
				util.checkAuthInfo(() => {
					uni.navigateTo({
						url: '/packageUser/pages/chat/chatIm?shopid=' + this.shopId +'&prodid=' + this.prodId
					});
				})
			},

      getProdInfo(prodId) {
        http.request({
          url: "/prod/prodInfo",
					method: "GET",
					data: {
						prodId,
						...( !!this.orderNumber ? { orderNumber: this.orderNumber } : {})
					},
          callBack: res => {
            this.setData({
              brief: res.brief,
              labels: res.labels,
              deliveryModeVO: res.deliveryModeVO, // 配送方式
              giveawayList: res.giveaway?res.giveaway.giveawayProds:[], // 赠品商品栏
            })
          }
        })
      },

			/**
			 * 获取秒杀商品详情
			 */
			getskdet(isFirst) {
				http.request({
					url: '/seckill/prod',
					data: {
						seckillId: this.seckillId,
						...( !!this.orderNumber ? { orderNumber: this.orderNumber } : {})
					},
					method: "GET",
					callBack: res => {
						let endTimeList = [];
						var objs = {};
						// 轮播图片
						var imgStrs = res.imgs;
						var imgs = imgStrs.split(",");
						objs.eTime = res.seckill.endTime;
						objs.sTime = res.seckill.startTime;
						objs.countType = null; //1表示秒杀活动正在进行,0表示秒杀活动未开始
						endTimeList.push(objs);
						res.skuList.forEach(skuItem=> {
							if (skuItem.pic && skuItem.pic.indexOf('http')===-1) {
								skuItem.pic = config.picDomain +skuItem.pic
							}
						})
						this.setData({
							imgs,
							seckillTotalStocks: res.seckill.seckillTotalStocks,
							seckilldet: res,
							countdownlist: endTimeList,
							skuList: res.skuList,
							seckillPrice: res.seckill.seckillPrice,
							maxNum: res.seckill.maxNum,
							originalPrice: res.price,
							content: util.formatHtml(res.content),
							video: res.video ? res.video : '',
							liveRoomParams: res.liveRoomParams, // 直播列表
							shopId: res.shopId,
							prodId: res.prodId,
							pic: res.pic,
							// 虚拟商品
							mold: res.mold, // 1虚拟商品
							virtualRemarks: res.virtualRemark ? JSON.parse(res.virtualRemark) : [], // 留言
							isRefund: res.isRefund, // 0不支持退款 1支持退款
							writeOffTime: res.writeOffTime, // 核销有效期 -1.长期有效 0.自定义 x.x天内有效
							writeOffNum: res.writeOffNum, // 核销次数 -1.多次核销 0.无需核销 1.单次核销
							writeOffStart: res.writeOffStart, // 核销有效期开始时间
							writeOffEnd: res.writeOffEnd, // 核销有效期结束时间
							prodParameterList: res.prodParameterList
						});
						// 初始化视频
						if (res.video) {
							this.$nextTick(() => {
								this.videoContext = uni.createVideoContext('myVideo', this)
							})
						}
						if (isFirst) this.groupSkuProp(res.skuList, res.price); // 组装sku
						this.countdownFun();
						if(isFirst && uni.getStorageSync('token')) {
							this.prodBrowseLog(res.prodId)
						}
						// 虚拟商品
						if (res.mold === 1 && this.virtualRemarks && this.virtualRemarks.length) {
							this.virtualRemarks.forEach(el => el.value = '')
						}
            this.getProdInfo(res.prodId)
					},
					errCallBack: err => {
						if (err.statusCode === 204) {
							uni.showToast({
								title: this.i18n.secondKillOver,
								duration: 2000,
								icon: 'none',
								complete: res => {
									setTimeout(()=> {
										uni.navigateBack({
											delta: 1
										})
									}, 2000)
								}
							})
						}
					}
				});
			},
			// 保存浏览记录
			prodBrowseLog(prodId) {
				http.request({
					url: "/p/prodBrowseLog",
					method: "POST",
					data: {
						prodId
					},
					callBack: res => {
					}
				});
			},
			// 倒计时
			countdownFun() {
				// 获取当前时间，同时得到活动结束时间数组
				let newTime = new Date().getTime();
				let endTimeList = this.countdownlist;
				let countDownArr = []; // 对结束时间进行处理渲染到页面

				endTimeList.forEach(o => {
					if (newTime - util.dateToTimestamp(o.sTime) > 0) {
						let endTime = util.dateToTimestamp(o.eTime);
						let obj = null; // 如果活动未结束，对时间进行处理
						if (endTime - newTime > 0) {
							let time = (endTime - newTime) / 1000; // 获取天、时、分、秒
							let day = parseInt(time / (60 * 60 * 24));
							let hou = parseInt(time % (60 * 60 * 24) / 3600);
							let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
							let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
							// obj = {
							// 	day: `${this.timeFormat(day)} 天`,
							// 	hou: `${this.timeFormat(hou)} 时`,
							// 	min: `${this.timeFormat(min)} 分`,
							// 	sec: `${this.timeFormat(sec)} 秒`,
							// 	type: 1 // 表示秒杀正在进行
							// };
							obj = {
								day: this.timeFormat(day),
								hou: this.timeFormat(hou),
								min: this.timeFormat(min),
								sec: this.timeFormat(sec),
								type: 1 // 表示秒杀正在进行

							};
						} // 活动已结束
						else {
							obj = {
								day: '00',
								hou: '00',
								min: '00',
								sec: '00'
							};
						}
						countDownArr.push(obj);
					} // 活动未开始
					else {
						let startTime = util.dateToTimestamp(o.sTime);
						let time = (startTime - newTime) / 1000;
						let obj = null; // 获取天、时、分、秒

						let day = parseInt(time / (60 * 60 * 24));
						let hou = parseInt(time % (60 * 60 * 24) / 3600);
						let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
						let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
						// obj = {
						// 	day: `${this.timeFormat(day)} 天`,
						// 	hou: `${this.timeFormat(hou)} 时`,
						// 	min: `${this.timeFormat(min)} 分`,
						// 	sec: `${this.timeFormat(sec)} 秒`,
						// 	type: 0 // 表示秒杀还没开始
						// };
						obj = {
							day: this.timeFormat(day),
							hou: this.timeFormat(hou) + ':',
							min: this.timeFormat(min) + ':',
							sec: this.timeFormat(sec),
							type: 0
						};
						countDownArr.push(obj);
					}
				}); // 渲染，然后每隔一秒执行一次倒计时函数
				this.setData({
					countDownList: countDownArr,
					countDownListTimer: setTimeout(this.countdownFun, 1000)
				});
			},

			// 小于10的格式化函数
			timeFormat(times) {
				return times < 10 ? '0' + times : times;
			},

			/**
			 * 组装SKU
			 */
			groupSkuProp: function(skuList) {
				if (skuList.length == 1 && !skuList[0].properties) {
					this.defaultSku = skuList[0]
					return;
				}
				var skuGroup = {};
				var allProperties = [];
				var propKeys = [];
				var selectedPropObj = {}
				var defaultSku = null;
				for (var i = 0; i < skuList.length; i++) {
					var isDefault = false;
					if (!defaultSku && skuList[i].seckillPrice == this.seckillPrice) { //找到和商品价格一样的那个SKU，作为默认选中的SKU
						defaultSku = skuList[i];
						isDefault = true;
					}
					var properties = skuList[i].properties; //版本:公开版;颜色:金色;内存:64GB
					allProperties.push(properties);
					var propList = properties.split(";"); // ["版本:公开版","颜色:金色","内存:64GB"]

					for (var j = 0; j < propList.length; j++) {

						var propval = propList[j].split(":"); //["版本","公开版"]
						var props = skuGroup[propval[0]]; //先取出 版本对应的值数组

						//如果当前是默认选中的sku，把对应的属性值 组装到selectedProp
						if (isDefault) {
							propKeys.push(propval[0]);
							selectedPropObj[propval[0]] = propval[1];
						}

						if (props == undefined) {
							props = []; //假设还没有版本，新建个新的空数组
							props.push(propval[1]); //把 "公开版" 放进空数组
						} else {
							if (props.indexOf(propval[1]) === -1) { //如果数组里面没有"公开版"
								props.push(propval[1]); //把 "公开版" 放进数组
							}
						}
						skuGroup[propval[0]] = props; //最后把数据 放回版本对应的值
					}
				}
				this.defaultSku = defaultSku
				this.propKeys = propKeys
				this.selectedPropObj = selectedPropObj
				this.parseSelectedObjToVals(skuList);
				this.skuGroup = skuGroup
				this.allProperties = allProperties
			},

			/**
			 * 将已选的 {key:val,key2:val2}转换成 [val,val2]
			 */
			parseSelectedObjToVals: function(skuList) {
				var selectedPropObj = this.selectedPropObj
				var selectedProperties = "";
				var selectedProp = [];
				for (var key in selectedPropObj) {
					// selectedProp.push(selectedPropObj[key]);
					selectedProp.push({key: key, value: selectedPropObj[key] });
					selectedProperties += key + ":" + selectedPropObj[key] + ";";
				}
				selectedProperties = selectedProperties.substring(0, selectedProperties.length - 1);
				this.selectedProp = selectedProp
				this.selectedProperties = selectedProperties
				this.selectedPropObj = selectedPropObj

				var findSku = false;
				for (var i = 0; i < skuList.length; i++) {
					// 解决排序问题导致无法匹配
					if (
						this.compareArray(selectedProperties.split(';').sort(),
							skuList[i].properties.split(';').sort())
					) {
						findSku = true;
						this.defaultSku = skuList[i]
						break;
					}
					// if (skuList[i].properties == selectedProperties) {
					// 	findSku = true;
					// 	this.defaultSku = skuList[i]
					// 	break;
					// }
				}
				this.findSku = findSku
			},

			/**
			 * 比较两个数组中的元素是否相等
			 * @param a1 第一个数组
			 * @param a2 第二个数组
			 * @return boolean 两个数组中的元素都相等则返回 true，反之返回 false
			 */
			compareArray(a1, a2) {
				if (!a1 || !a2) {
					return false;
				}
				if (a1.length !== a2.length) {
					return false;
				}
				for (var i = 0, n = a1.length; i < n; i++) {
					if (a1[i] !== a2[i]) {
						return false;
					}
				}
				return true;
			},

			/**
			 * 判断当前的规格值 是否可以选
			 */
			isSkuLineItemNotOptional(allProperties, selectedPropObj, key, item, propKeys) {
				var selectedPropObj = Object.assign({}, selectedPropObj)
				var properties = "";
				selectedPropObj[key] = item;
				for (var j = 0; j < propKeys.length; j++) {
					properties += propKeys[j] + ":" + selectedPropObj[propKeys[j]] + ";";
				}
				properties = properties.substring(0, properties.length - 1);
				for (var i = 0; i < allProperties.length; i++) {
					if (properties == allProperties[i]) {
						return false;
					}
				}
				for (var i = 0; i < allProperties.length; i++) {
					if (allProperties[i].indexOf(item) >= 0) {
						return true;
					}
				}
				return false;
			},

			/**
			 * 视频的方法
			 */
			videoOper() {
				if (this.isPlaying) {
					this.stopPlay()
				} else {
					this.startPlay()
				}
			},
			playEnd() {
				this.stopPlay()
			},

			startPlay() {
				this.setData({
					isPlaying: true,
				})
				setTimeout(() => {
					this.videoContext.seek(0)
					this.videoContext.play()
				}, 200)
			},
			stopPlay() {
				this.setData({
					isPlaying: false
				})
				setTimeout(() => {
					this.videoContext.seek(0)
					this.videoContext.stop()
				}, 200)
			},

			/**
			 * 规格点击事件
			 */
			toChooseItem(skuLineItem, key, event) {
				this.selectedPropObj[key] = skuLineItem;
				this.parseSelectedObjToVals(this.skuList);
			},

			//判断数组是否包含某对象
			array_contain: function(array, obj) {
				for (var i = 0; i < array.length; i++) {
					if (array[i] == obj) //如果要求数据类型也一致，这里可使用恒等号===
						return true;
				}
				return false;
			},

			/**
			 * 减数量
			 */
			onCountMinus: function() {
				var prodNum = this.prodNum;

				if (prodNum > 1) {
					this.setData({
						prodNum: prodNum - 1
					});
				}
			},

			/**
			 * 加数量
			 */
			onCountPlus: function() {
				this.getskdet();
				var prodNum = this.prodNum;
				var seckillTotalStocks = this.defaultSku.seckillStocks;
				if (this.maxNum == -1) {
					// 此秒杀不限购
					if (seckillTotalStocks > 1 && prodNum < seckillTotalStocks) {
						this.setData({
							prodNum: prodNum + 1
						});
					} else {
						uni.showToast({
							title: this.i18n.insufficientStock,
							icon: 'none',
							duration: 1000,
							// #ifndef MP-TOUTIAO
							mask: true
              // #endif
						});
					}
				} else {
					// 限购
					if (seckillTotalStocks < 1 || seckillTotalStocks <= prodNum ) {
						uni.showToast({
							title: this.i18n.insufficientStock,
							icon: 'none',
							duration: 1000,
							// #ifndef MP-TOUTIAO
							mask: true
              // #endif
						});
						return
					}
					if (prodNum < this.maxNum) {
						// 数量未达限购数量
						this.setData({
							prodNum: prodNum + 1
						});
					} else {
						uni.showToast({
							title: this.i18n.purchaseLimit + this.maxNum + this.i18n.piece,
							icon: 'none',
							duration: 1000,
							// #ifndef MP-TOUTIAO
							mask: true
              // #endif
						});
					}
				}
			},

			// 确认秒杀商品
			seckillconfirm() {
				if (!this.findSku) {
					return;
				}
				if (!this.defaultSku.seckillStocks || this.defaultSku.seckillStocks < this.prodNum) {
					uni.showToast({
						title: this.i18n.insufficientStock,
						icon: 'none',
						duration: 1000,
						// #ifndef MP-TOUTIAO
						mask: true
						// #endif
					});
					return
				}
				if (this.prodNum < 1 || !this.prodNum) {
					uni.showToast({
						title: this.i18n.leastTips,
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.maxNum!=-1 && this.prodNum > this.maxNum) {
					uni.showToast({
						title: this.i18n.purchaseLimit + this.maxNum + this.i18n.piece,
						icon: 'none',
						duration: 1000,
						// #ifndef MP-TOUTIAO
							mask: true
						// #endif
					});
					return
				}
				// 虚拟商品
				if (this.mold === 1) {
					if (this.virtualRemarks.find(el => el.value && !el.value.trim())) {
						uni.showToast({
							title: this.i18n.mesgCannotBeAllSpaces,
							icon: 'none'
						})
						return
					}
					if(this.virtualRemarks.find(el => !el.value && el.isRequired)) {
						uni.showToast({
							title: this.i18n.requiredMessage,
							icon: 'none'
						})
						return
					}
					let remarks = []
					this.virtualRemarks.forEach(el => {
						if (el.value) {
							remarks.push(el)
						}
					})
					uni.setStorageSync('virtualRemark', JSON.stringify(remarks))
				}
				var secKillObj = {};
				secKillObj.addrId = 0;
				secKillObj.prodCount = this.prodNum;
				secKillObj.seckillSkuId = this.defaultSku.seckillSkuId;
        secKillObj.prodId = this.prodId
        secKillObj.shopId = this.shopId
				wx.setStorageSync("secKillObj", secKillObj);
				uni.navigateTo({
					url: '/packageActivities/pages/confirmOrder/confirmOrder'
				});
			},

			//  零售价购买
			toProdDetailsPage: function(e) {
				uni.navigateTo({
					url: '/pages/prod/prod?prodid=' + this.seckilldet.prodId
				});
			},

			/**
			 * 显示遮罩层
			 */
			showModal: function() {
				var ths = this;
				if(!!this.orderNumber) {
					ths.showMoadlFun()
				}
				else {
					util.checkAuthInfo(ths.showMoadlFun);
				}
			},

			showMoadlFun() {
				var ths = this;
				ths.setData({
					hideModal: false,
					popupShowHiden: true
				});
				var animation = wx.createAnimation({
					duration: 600,
					//动画的持续时间 默认400ms 数值越大，动画越慢 数值越小，动画越快
					timingFunction: 'ease' //动画的效果 默认值是linear

				});
				ths.animation = animation;
				setTimeout(function() {
					ths.fadeIn(); //调用显示动画
				}, 100);
			},

			/**
			 * 获取用户信息
			 */
			onGotUserInfo: function(e) {
				if (e.detail.errMsg != "getUserInfo:fail auth deny") {
					this.setData({
						isAuthInfo: true
					});
					http.updateUserInfo();
				}
			},

			/**
			 * 隐藏遮罩层
			 */
			hideModalFun: function() {
				var that = this;
				var animation = wx.createAnimation({
					duration: 800,
					//动画的持续时间 默认400ms 数值越大，动画越慢 数值越小，动画越快
					timingFunction: 'ease' //动画的效果 默认值是linear

				});
				this.animation = animation;
				that.fadeDown(); //调用隐藏动画

				setTimeout(function() {
					that.setData({
						hideModal: true,
						popupShowHiden: false
					});
				}, 720); //先执行下滑动画，再隐藏模块
			},

			/**
			 * 动画集
			 */
			fadeIn: function() {
				this.animation.translateY(0).step();
				this.setData({
					animationData: this.animation.export() //动画实例的export方法导出动画数据传递给组件的animation属性

				});
			},
			fadeDown: function() {
				this.animation.translateY(300).step();
				this.setData({
					animationData: this.animation.export()
				});
			},

			/**
			 * 跳转到首页
			 */
			toHomePage: function() {
				uni.switchTab({
					url: '/pages/index/index'
				});
			},

			/**
			 * 跳转到购物车
			 */
			toCartPage: function() {
				uni.switchTab({
					url: '/pages/basket/basket'
				});
			},

			/**
			 * 跳转直播
			 */
			/**
			 * 前往直播页面
			 */
			toLivePage: function(e) {
				console.log(e)
				this.roomId = e.currentTarget.dataset.roomid // 填写具体的房间号
				this.url = e.currentTarget.dataset.url
				util.checkAuthInfo(this.toLivePlayer)
			},
			toLivePlayer: function() {
				console.log('isWechat:', this.isWechat)
				let roomId = this.roomId
				let url = this.url
				console.log('roomId:', roomId, '，', 'url:', url)
				if (this.isWechat) {
					// 开发者在直播间页面路径上携带自定义参数（如示例中的path和pid参数），后续可以在分享卡片链接和跳转至商详页时获取
					let customParams = encodeURIComponent(JSON.stringify({
						path: url
					}))
					wx.navigateTo({
						url: `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${roomId}&custom_params=${customParams}`
					}) // 其中wx2b03c6e691cd7370是直播组件appid不能修改
				} else {
					uni.showToast({
						title: this.i18n.pleaseOpenInWechat,
						icon: 'none'
					})
				}
			},



		},
	};
</script>
<style>
	@import "./snapUpDetail.css";
</style>

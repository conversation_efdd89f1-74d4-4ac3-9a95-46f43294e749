
.so-wrap {
  padding-bottom: 16rpx;
}
.so-header {
  background: linear-gradient(142deg, #F67D82 0%, #F14C4A 59.15%);
  padding: 48rpx;
  margin-bottom: 32rpx;
}

.so-header__info {
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  padding: 36rpx 48rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px;
}

.so-header__name {
  flex: 1;
  flex-shrink: 0;
  color: #000;
  font-size: 24rpx;
  font-weight: 600;
  display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	word-break: break-word;
	overflow: hidden;
	text-overflow: ellipsis;
}

.so-header__rest-time {
  min-width: 50%;
  flex-shrink: 0;
  color: #000;
  font-size: 24rpx;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.so-header__rest-time__tip {
  color: var(--primary-color);
  font-size: 20rpx;
  font-weight: 400;
  flex-shrink: 0;
}

.so-header__counter {
  flex: 1;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.so-header__counter__item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rpx 8rpx;
  border-radius: 2px;
  background: #313131;
}

.so-header__counter__item__num {
  color: #FFF;
  font-size: 24rpx;
  font-weight: 400;
}
.so-header__counter__spliter {
  margin: 0 8rpx;
}

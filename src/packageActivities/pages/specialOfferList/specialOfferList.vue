<template>
  <!-- 优惠活动列表列表 -->
  <view class="so-wrap">
    <view class="so-header">
      <view class="so-header__info">
        <view class="so-header__name">{{ pageParams.activityName }}</view>
        <view class="so-header__rest-time">
          <view class="so-header__rest-time__tip">
            {{ 
              status === 0 ? '距开始：' : 
              status === 1 ? '本场结束：' : 
              '已结束'
            }}
          </view>
          <view class="so-header__counter" v-if="status !== 2">
            <view class="so-header__counter__item" v-if="times.day !== '00'">
              <view class="so-header__counter__item__num">{{ times.day }}</view>
            </view>
            <view class="so-header__counter__spliter" v-if="times.day !== '00'">天</view>
            <view class="so-header__counter__item">
              <view class="so-header__counter__item__num">{{ times.hou }}</view>
            </view>
            <view class="so-header__counter__spliter">:</view>
            <view class="so-header__counter__item">
              <view class="so-header__counter__item__num">{{ times.min }}</view>
            </view>
            <view class="so-header__counter__spliter">:</view>
            <view class="so-header__counter__item">
              <view class="so-header__counter__item__num">{{ times.sec }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <GoodsContainer>
      <VerticalCard
        v-for="(item, index) in specialOfferList"
        :key="index"
        :goods-item="item"
        :show-price="false"
        @tap-goods="toAbulkListPage"
      >
        <template #bottom>
          <SpecialOfferBottom :goods-item="item" />
        </template>
      </VerticalCard>
    </GoodsContainer>


    <pageBottomTips :isAll="loadAll" :current='current>1?true:false' />
  </view>
</template>


<script>
import pageBottomTips from '../../components/pageBottomTips/pageBottomTips'
import GoodsContainer from '@/components/goods-card/goods-container'
import VerticalCard from '@/components/goods-card/vertical-card'
import SpecialOfferBottom from '@/components/goods-card/special-offer-bottom';
import util from '@/utils/util.js'
const http = require("@/utils/http.js")

export default {
  data() {
    return {
      specialOfferList: [],
      //商品列表
      current: 1,
      size: 20,
      pages: "",
      loadAll: false, 
      times: {},
      status: 0, //活动状态 0未开始 1进行中 2已结束
      // 秒杀商品列表
      countDownList: [],
      pageParams: {},
    };
  },

  components: {
		pageBottomTips,
    GoodsContainer,
    VerticalCard,
    SpecialOfferBottom,
	},
  props: {},
  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },
  onLoad: function (options) {
    this.pageParams.shopId = options.shopId
    this.pageParams.activityId = options.activityId
    this.pageParams.activityName = decodeURIComponent(decodeURIComponent(options.activityName))
    this.pageParams.startTime = decodeURIComponent(decodeURIComponent(options.startTime))
    this.pageParams.endTime = decodeURIComponent(decodeURIComponent(options.endTime))
    this.getSpecialOfferList();
    //头部导航标题
	  uni.setNavigationBarTitle({
	      title: this.i18n.specialOfferList
	  });
  },
  onShow: function () {
	  
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.current < this.pages) {
      this.setData({
        current: this.current + 1
      });
      this.getSpecialOfferList();
    }else {
      this.setData({
        loadAll: true
      })
    }
  },
  methods: {
    /**
     * 获取优惠活动列表
     */
    getSpecialOfferList: function () {
      uni.showLoading();
			const pages = getCurrentPages()
			const currPage = pages[pages.length - 1].options
      var params = {
        url: "/marking/discount/prodList",
        method: "GET",
        data: {
          current: this.current,
          size: this.size,
          discountId: this.pageParams.activityId,
          discountType: 4,
					...(!!currPage.shopId && currPage.shopId !== '0' ? { shopId: currPage.shopId } : {}),
        },
        callBack: res => {
          uni.hideLoading()
					var specialOfferList = [];
          if (params.data.current == 1) {
            specialOfferList = res.records;
          } else {
            specialOfferList = this.specialOfferList.concat(res.records);
          }
          if(res.total<=4 && res.total>0){
            this.setData({
              loadAll:true
            })
          }
          this.setData({
            specialOfferList: specialOfferList,
            pages: res.pages
          });
          this.countdown();
        }
      };
      http.request(params);
    },
    countdown() {
      // 获取当前时间，同时得到活动结束时间数组
      const startTime = this.pageParams.startTime.replace(/-/g, '/');
      const endTime = this.pageParams.endTime.replace(/-/g, '/');
      const nowTimeStamps = new Date().getTime();
      const startTimeStamps = new Date(startTime).getTime();
      const endTimeStamps = new Date(endTime).getTime();
      if (nowTimeStamps < startTimeStamps) {
        //活动未开始，倒计时
        const times = util.endOfStartTime(nowTimeStamps, startTimeStamps);
        this.setData({
          status: 0, //活动未开始
          times
        });
        setTimeout(this.countdown, 1000);
      } else if (nowTimeStamps < endTimeStamps) {
        //活动正在进行，倒计时
        const times = util.endOfStartTime(nowTimeStamps, endTimeStamps);
        this.setData({
          status: 1, //活动正在进行
          times
        });
        setTimeout(this.countdown, 1000);
      } else {
        //活动已结束，清空倒计时
        this.setData({
          status: 2 //活动已结束
        });
        return;
      }
    },
    //小于10的格式化函数
    timeFormat(times) {
      return Number(times) < 10 ? '0' + times : times;
    },
    /**
     * 跳转到商品详情
     */
    toAbulkListPage: function (e) {
      var prodId = e.prodId;
      this.$Router.push({ path: '/pages/prod/prod', query: { prodid: prodId }})
    },
    // 去到店铺首页
    goShop(shopId, renovationId) {
      let url
      url = renovationId ? '/pages/shop-feature-index/shop-feature-index0?shopId=' + shopId +
            '&renovationId=' + renovationId :  '/packageShop/pages/shopPage/shopPage?shopId=' + shopId
      uni.navigateTo({url})
    },
  }
};
</script>
<style>
@import "./specialOfferList.css";
</style>

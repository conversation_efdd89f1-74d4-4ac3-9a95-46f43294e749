/* pages/discountDetail/discountDetail.wxss */

/* 特惠详情 */

page {
  background: #f7f7f7;
  height: 100%;
}

.discount-detail {
  padding: 20rpx;
  position: relative;
}

.discount-detail .banner {
  width: 100%;
  height: 260rpx;
  border-radius: 10rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.discount-detail .banner.no-bg {
  background: #999;
}

.discount-detail .banner .bg {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 10rpx;
  width: 100%;
  height: 100%;
}

.discount-detail .banner .text-box {
  padding: 30rpx;
  position: relative;
	width: 100%;
	box-sizing: border-box;
}

.discount-detail .banner .text-box .time {
  font-size: 28rpx;
  color: #fff;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.discount-detail .banner .text-box .text {
  margin-right: 20rpx;
}

.discount-detail .banner .text-box .time .number {
  font-family: arial;
  padding: 2rpx 6rpx;
  text-align: center;
  background: rgba(0,0,0,.7);
  border-radius: 4rpx;
  font-size: 26rpx;
}

.discount-detail .banner .text-box .time .colon {
  font-family: arial;
  margin: 0 10rpx;
}

.discount-detail .banner .info {
  font-size: 38rpx;
  font-family: arial;
  color: #fff;
  font-weight: 600;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.discount-detail .banner .shop {
  margin: 10rpx 0;
  font-size: 28rpx;
  color: #fff;
}

.discount-detail .discount-prod .item {
  margin-top: 20rpx;
  background: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
}

.discount-detail .discount-prod .item .img {
  font-size: 0;
  width: 220rpx;
  height: 220rpx;
  border-radius: 10rpx;
}

.discount-detail .discount-prod .item .img image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.discount-detail .discount-prod .item .msg {
  flex: 1;
  margin-left: 20rpx;
	word-break: break-all;
}

.discount-detail .discount-prod .item .msg .name-des {
  height: 114rpx;
}

.discount-detail .discount-prod .item .msg .name-des .name {
  font-size: 28rpx;
  max-height: 72rpx;
  line-height: 36rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
	word-break: break-word;	
}

.discount-detail .discount-prod .item .msg .name-des .des {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
  height: 32rpx;
  line-height: 32rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.discount-detail .discount-prod .item .msg .price-btn {
  height: 82rpx;
  margin-top: 20rpx;
  padding-bottom: 4rpx;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.discount-detail .discount-prod .item .msg .price-btn .price-box {
  margin-right: 10rpx;
}

.discount-detail .discount-prod .item .msg .price-btn .price-box .price {
  color: #e43130;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 10rpx;
}

.discount-detail .discount-prod .item .msg .price-btn .price-box .price .big {
  font-size: 36rpx;
  line-height: 36rpx;
}

.discount-detail .discount-prod .item .msg .price-btn .price-box .old-price {
  color: #999;
  text-decoration: line-through;
  font-size: 24rpx;
}

.discount-detail .discount-prod .item .msg .price-btn .btn {
  width: 152rpx;
  height: 52rpx;
  line-height: 52rpx;
  text-align: center;
  font-size: 24rpx;
  background: var(--primary-color);
  color: #fff;
  border-radius: 56rpx;
}

/* 特惠详情 end */
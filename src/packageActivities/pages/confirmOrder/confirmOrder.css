/* pages/confirmOrder/confirmOrder.wxss */
.clearfix::after {
  content: '';
  display: block;
  clear: both;
}
image {
  width: 100%;
  height: 100%;
}
.process {
  display: flex;
  justify-content: space-around;
  font-size: 23rpx;
  border-bottom: 1px solid #f2f2f2;
  padding: .5em 0;
}
.process-item {
  display: inline-block;
}
.process-icon {
  display: block;
  width: 45rpx;
  height: 45rpx;
  margin: 0 auto;
}
.process-text {
  padding: .5em 0;
}
.next-icon {
  width: 20rpx;
  height: 20rpx;
  margin-top: 25rpx;
}

/***** 地址栏 ******/
.add-addr {
	display: inline-block;
	margin-left: 60rpx;
}
.address-part{
  position: relative;
  display: block;
  width: 100%;
  height: auto;
  box-sizing: border-box;
  border-bottom: 14rpx solid #f9f8f8;
}
.address-box {
  position: relative;
  font-size: 28rpx;
  padding: 20rpx 0;
}
.recipient {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
	display: flex;
	justify-content: space-between;
}
.recipient,
.addr-text {
  padding: 0 60rpx;
}
.username {
	justify-content: space-between;
	max-width: 80%;
	overflow: hidden;
	word-break: keep-all;
	text-overflow: ellipsis;
}
.cellphone {
  float: right;
}
.addr-text {
  color: #888;
	padding-bottom: 10rpx;
	word-break: break-word;
}
/* 地址图标 */
.addr-icon {
  position: absolute;
  top: 25rpx;
  left: 20rpx;
  display: block;
  width: 28rpx;
  height: 30rpx;
}
/* 右箭头 */
.more-icon {
  position: absolute;
  right: 25rpx;
  top: 50%;
  transform: translateY(-50%);
  display: block;
  width: 20rpx;
  height: 20rpx;
}
/* 虚线 */
.dotted-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2rpx;
}
.dotted-line >>> image {
  display: block;
  width: 100%;
  height: 100%;
}

/***** 商品栏 *****/
.goods-box {
  font-size: 28rpx;
  border-bottom: 15rpx solid #f9f8f8;
  padding-bottom: 24rpx;
}
/* 店铺 */
.goods-shop {
  padding: 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.shop-icon {
  display: inline-block;
  width: 35rpx;
  height: 35rpx;
  vertical-align: bottom;
  margin-right: 15rpx;
}
/* 商品 */
.goods-msg {
  padding: 20rpx;
}
.goods-img {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  display: inline-block;
  background: #eee;
  margin-right: 25rpx;
  vertical-align: middle;
}
.miaosha {
  /* position: absolute; */
  /* top: 10rpx;
  left: 0; */
  /* display: inline-block; */
  display: block;
  width: 80rpx;
  height: 30rpx;
  margin-bottom: 5px;
}
.goods-text {
  position: relative;
  display: inline-block;
  width: 70%;
  vertical-align: middle;
}
.goods-name {
  margin-bottom: 10rpx;
	font-size: 30rpx;
	overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  word-break: break-all;
}
.goods-sku{
  color: #999;
  margin-top: 5px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  word-break: break-all;
}
.goods-price {
  color: #e43130;
  padding-right: 10rpx;
  font-size: 32rpx;
}
.goods-price-del {
  font-size: 28rpx;
  text-decoration: line-through;
  color: #888888;
}
.goods-amount {
  float: right;
  font-size: 28rpx;
}

/****** 配送&留言 ******/
.row {
  font-size: 30rpx;
  border-bottom: 15rpx solid #f9f8f8;
}
.dispatching {
  margin: 0 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
}
.dispatching-way {
  float: right;
}
.live-message {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 12px 0;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  padding: 20rpx 20rpx;
}
.message-int {
  flex: 1;
  font-size: 28rpx;
}

/****** 商品总额 ******/
.sum {
  font-size: 30rpx;
}
.cost-box {
  margin: 0 20rpx;
  padding-right: 15rpx;
  padding-top: 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.goods-table {
  padding-bottom: 20rpx;
}
.goods-table-way {
  float: right;
  font-size: 32rpx;
}
.goods-table-way.shine {
  color: #e43130;
}
.total-cost {
  padding: 20rpx 35rpx;
  text-align: right;
  font-size: 28rpx;
}
.total-num {
  color: #e43130;
  font-weight: bold;
  font-size: 32rpx;
}


/****** 底部 ******/
.foot-box {
  margin-top: 90rpx;
}
.foot {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 30rpx;
  text-align: right;
  border-top: 3rpx solid #eee;
}
.total-price {
  display: inline-block;
  margin-right: 20rpx;
}
.submit-btn {
  display: inline-block;
  background: #e43130;
  color: #fff;
  padding: 0 50rpx;
  border: 1rpx solid #e43130;
  border-radius: 60rpx;
  height: 2.8rm;
  line-height: 2.8em;
  margin-right: 20rpx;
  font-size: 28rpx;
}
.total-price-num {
  font-size: 32rpx;
  font-weight: bold;
  color: #e43130;
}
.invoice {
	position: relative;
	display: flex;
	justify-content: space-between;
	padding: 20rpx 20rpx;
}
.invoice .tit {
  flex-wrap: wrap;
}
.item.invoice .item {
  flex: 1;
  margin-left: auto;
}
.text-arrow {
	position: relative;
	padding-right: 28rpx;
  word-break: break-word;
}
.text-arrow ::after{
	position: absolute;
	right: 8rpx;
	top: 45%;
	display: block;
	width: 14rpx;
	height: 14rpx;
	border: 2rpx solid #666;
	border-width: 2rpx 2rpx 0 0;
	content: " ";
	font-size: 0;
	transform: rotate(45deg) translateY(-50%);
}

/* 虚拟商品-留言 */
.all-msg {
  border-top: 1px solid #eee;
}
.all-msg .item {
  max-width: 70%;
}
.all-msg .text-arrow .text {
  display: flex;
}
.all-msg .text-arrow .text .msg-name {
  flex-wrap: wrap;
}
.all-msg .text-arrow .text .msg-con {
  flex: 1;
  padding-left: 10rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/* 留言弹窗 */
.popup-hide {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: rgba(0, 0, 0, 0.3);
}
.popup-box {
	position: absolute;
	bottom: 0;
	width: 100%;
	height: 80%;
	overflow: hidden;
	background-color: #fff;
	border-radius: 10rpx 10rpx 0 0;
}
.popup-hide .con-tit {
	display: flex;
	justify-content: space-around;
	align-items: center;
	font-size: 28rpx;
	font-weight: bold;
	padding: 30rpx;
}
.popup-hide .con-tit .sure {
	font-size: 0;
	width: 32rpx;
	height: 32rpx;
}
.popup-hide .con-tit .sure image {
	width: 100%;
	height: 100%;
}
.popup-hide .con-tit .tit-text {
	flex: 1;
	text-align: center;
}
.close {
	color: #666;
	font-size: 32rpx;
}
.close::before {
	content: "\2715";
}
.popup-hide .virtual-goods-msg-pop {
	height: auto;
}
.popup-hide .virtual-goods-msg-pop .con-tit .tit-text {
	text-align: left;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .msg-list {
	padding: 20rpx 30rpx;
	margin-bottom: 140rpx;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .msg-item {
	display: flex;
	margin-bottom: 30rpx;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .item-con {
	font-size: 24rpx;
	word-wrap: break-word;
	word-break: break-word;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .item-con.weak {
	color: #999999;
	margin-right: 20rpx;
	width: 180rpx;
	min-width: 180rpx;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .pop-foot {
	position: absolute;
	bottom: 0;
	width: 100%;
	text-align: center;
	padding: 0 20rpx;
	margin-bottom: 30rpx;
	margin-top: 20rpx;
	box-sizing: border-box;
	background: #fff;
}
.popup-hide .virtual-goods-msg-pop .msg-pop-con .pop-foot .foot-btn {
	font-size: 26rpx;
	color: #fff;
	width: 100%;
	background: #E43130;
	border-radius: 140rpx;
	padding: 20rpx 0;
	box-sizing: border-box;
}

.physical-remark-list {
  margin-bottom: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
  padding-top: 30rpx;
  font-size: 12px;
}
.physical-remark-list .physical-remark-item {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #F2F2F2;
  padding: 20rpx;
}
.physical-remark-list .physical-remark-item .physical-remark-item-tit {
  flex-wrap: wrap;
  width: 180rpx;
  margin-right: 20rpx;
  word-break: break-word;
}
.physical-remark-list .physical-remark-item .physical-remark-item-int {
  font-size: 14px;
}
.physical-remark-list .physical-remark-item .stress {
  color: #E43130;
  margin-right: 10rpx;
  font-size: 26rpx;
}
.physical-remark-list .physical-remark-item .physical-remark-item-tit .uni-input-placeholder {
  color: #aaa;
  font-size: 14px;
}

/* 赠品 */

.gift-item {
  margin-left: 216rpx;
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
}
.gift-item:last-child {
  margin-bottom: 0;
}
.gift-name {
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 82%;
}
.gift-name .gift-sku{
  color: #999;
  margin-left: 20rpx;
}
.gift-count {
	font-size: 24rpx;
	color: #999;
	margin-right: 10rpx;
}

/* 赠品end */
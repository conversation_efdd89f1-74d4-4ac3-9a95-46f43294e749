<template>
	<view v-if="fullItemObj.actualTotal">
		<!--地址栏 -->
		<view v-if="mold !== 1" class="address-part">
			<view class="address-box" @tap="toAddrListPage">
				<!-- 地址图标 -->
				<view class="addr-icon">
					<image :src="`${staticPicDomain}images/icon/addr.png`"></image>
				</view>
				<!-- 地址为空 -->
				<view class="add-addr" v-if="!fullItemObj.userAddr">{{i18n.addShippingAddress}}</view>
				<!-- 地址信息 -->
				<block v-if="fullItemObj.userAddr">
					<view class="recipient clearfix">
						<text class="username">{{fullItemObj.userAddr.receiver}}</text>
						<text class="cellphone">{{fullItemObj.userAddr.mobile}}</text>
					</view>
					<view class="addr-text">{{i18n.shippingAddress}}：{{fullItemObj.userAddr.province}}{{fullItemObj.userAddr.city}}{{fullItemObj.userAddr.area}}{{fullItemObj.userAddr.addr}}</view>
				</block>

				<!-- 右箭头 -->
				<view class="more-icon">
					<image :src="`${staticPicDomain}images/icon/more.png`"></image>
				</view>
			</view>
			<!-- 下虚线 -->
			<view class="dotted-line">
				<image :src="`${staticPicDomain}images/icon/dotted-line.png`"></image>
			</view>
		</view>
		<!--地址栏end -->


		<!-- 商品信息 -->
		<view class="goods-box">
			<!-- <view class="goods-shop">
    <view class="shop-icon"><image src="../../images/icon/shop.png"></image></view>
    <text class="shop-name">笑橙小店190827</text>
  </view>
  -->
			<!-- 商品信息 -->
			<view class="goods-msg">
				<view class="goods-img">
					<image :src="fullItemObj.shopCartItem.pic"></image>
					<!-- <view class="miaosha">
        <image src="../../images/icon/miaosha.png"></image>
      </view> -->
				</view>
				<view class="goods-text clearfix">
					<view class="goods-name">{{fullItemObj.shopCartItem.prodName}}</view>
					<view class="miaosha">
						<image :src="`${staticPicDomain}images/icon/miaosha.png`"></image>
					</view>
					<view class="goods-sku">{{fullItemObj.shopCartItem.skuName}}</view>
					<text class="goods-price">￥{{toPrice(fullItemObj.shopCartItem.seckillPrice)}}</text>
					<text class="goods-price-del">￥{{toPrice(fullItemObj.shopCartItem.price)}}</text>
					<text class="goods-amount">x {{fullItemObj.shopCartItem.prodCount}}</text>
				</view>
			</view>

      <view class="physical-remark-list-wrap" v-if="mold != 1">
        <view class="physical-remark-list">
          <template v-for="physicalRemarkItem in physicalRemarkList">
            <view class="physical-remark-item">
              <view class="physical-remark-item-tit"><text v-if="physicalRemarkItem.isRequired" class="stress">*</text>{{physicalRemarkItem.name}}</view>
              <input class="physical-remark-item-int" v-model="physicalRemarkItem.value" :placeholder="i18n.pleaseEnter + `${physicalRemarkItem.name}`" maxlength="20" />
            </view>
          </template>
        </view>
      </view>

      <!-- 赠品信息 -->
      <view v-if="fullItemObj.giveaway" class="gift-con">
        <view v-for="(giveawayItem, giveawayIndex) in fullItemObj.giveaway.giveawayProds" :key="giveawayIndex" class="gift-item">
          <view class="gift-name">【{{i18n.Giveaways}}】{{ giveawayItem.prodName }}<text v-if="giveawayItem.skuName" class="gift-sku">{{giveawayItem.skuName||''}}</text></view>
          <view class="gift-count">×{{ giveawayItem.giveawayNum }} </view>
        </view>
      </view>

		</view>
		<!-- 商品信息end -->


		<!-- 配送&留言 -->
		<view class="row">
			<!-- <view class="dispatching clearfix">
    <text class="dispatching-tit">配送方式</text>
    <text class="dispatching-way">快递 免运费</text>
  </view> -->
			<view class="live-message">
				<text class="message-tit">{{i18n.OrderNotes}}：</text>
				<input class="message-int" maxlength="100" :placeholder="i18n.buyerTips" :value="remarks" @input="onRemarksInput" />
			</view>
			<!-- <view class="item invoice">
				<view class="tit">{{i18n.invoice.onvoiceIssuance}}：</view>
				<view v-if="!invoiceDataFrom.invoiceType || invoiceDataFrom.invoiceType === 2" class="item"
					@tap="showInvoicePopup(fullItemObj.shopId)">
					<view class="text-arrow">
						<view class="text">{{i18n.invoice.noInvoice}}</view>
					</view>
				</view>
				<view v-else class="item" @tap="showInvoicePopup(fullItemObj.shopId,invoiceDataFrom)">
					<view class="text-arrow">
						<view class="text">本次不开具发票</view> 这里有一行注视
						<view class="text">{{ i18n.invoice.electronic }}({{i18n.invoice.productDetails}}-{{ invoiceDataFrom.headerName || i18n.invoice.personal }})</view>
					</view>
				</view>
			</view> -->
			<view v-if="mold == 1 && virtualRemarkList.length" class="item invoice all-msg">
				<view class="tit">全部留言：</view>
				<view class="item">
					<view class="text-arrow" @tap="showViewMsgPopup">
						<view class="text">
							<view class="msg-name">{{virtualRemarkList[0].name}}</view>
							<view class="msg-con">{{virtualRemarkList[0].value}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 配送&留言 end -->


		<!-- 商品总额 -->
		<view class="sum">
			<view class="cost-box">
				<view class="goods-table clearfix">
					<text class="goods-table-tit">{{i18n.goodsAmount}}</text>
					<text class="goods-table-way">￥{{toPrice(goodsPrice)}}</text>
				</view>
				<view v-if="mold != 1" class="goods-table clearfix">
					<text class="goods-table-tit">{{i18n.shipping}}</text>
					<text class="goods-table-way">￥{{toPrice(transfee)}}</text>
				</view>
				<view class="goods-table clearfix">
					<text class="goods-table-tit">{{i18n.seckillReduce}}</text>
					<text class="goods-table-way shine">-￥{{toPrice(fullItemObj.seckillReduce)}}</text>
				</view>
			</view>
			<!-- 合计 -->
			<view class="total-cost">{{i18n.total}}：<text class="total-num">￥{{toPrice(totalPrice)}}</text>
			</view>
		</view>
		<!-- 商品金额 end -->


		<!-- 底部 -->
		<view class="foot-box">
			<view class="foot">
				<view class="total-price">{{i18n.total}}：<text class="total-price-num">￥{{toPrice(totalPrice)}}</text></view>
				<!-- 提交按钮 -->
				<view class="submit-btn" @tap="submitOrder">{{i18n.submitOrders}}</view>
			</view>
		</view>
		<!-- 底部end -->
		<invoiceEdit v-if="isShowInvoicePopup" :invoice-data-from="invoiceDataFrom" :shop-id="invoiceShopId"
			:invoice-id="invoiceId" @closePopup="closePopup" @getInvoiceData="getInvoiceData" />

		<!-- 查看留言（虚拟商品） -->
		<view class="popup-hide" :hidden="!showViewMsg">
			<view class="popup-box virtual-goods-msg-pop">
				<view class="con-tit">
					<view class="tit-text">{{i18n.viewMsg}}</view>
					<view class="close" @tap="closePopup"></view>
				</view>
				<view class="msg-pop-con">
					<view class="msg-list">
						<view v-for="(item, index) in virtualRemarkList" :key="index" class="msg-item">
							<view class="item-con weak">{{item.name}}</view>
							<view class="item-con">{{item.value}}</view>
						</view>
					</view>
					<view class="pop-foot">
						<view class="foot-btn" @tap="closePopup">{{i18n.gotIt}}</view>
					</view>
				</view>
			</view>
		</view>
    <Pay ref="payRef" />
	</view>
</template>

<script>
	// pages/confirmOrder/confirmOrder.js
	var http = require("../../../utils/http.js");
	import invoiceEdit from '../../../components/invoiceEdit/index'
  import Pay from '@/components/pay/pay'
	export default {
		data() {
			return {
				orderPath: '',
				fullItemObj: {},
				totalPrice: 0,
				goodsPrice: 0,
				transfee: 0,
				userAddr: null,
				remarks: "",
				seckillOrderNumber: '',
				pollingTimes: 0,
				seckillOrderStsTimer: '',

				invoiceId: '', // 发票id
				invoiceShopId: '',
				invoiceDataFrom: {},
				isShowInvoicePopup: false,

				mold: null, // 1虚拟商品
				// 留言（虚拟商品）
				virtualRemarkList: [],
				// 查看留言弹窗显隐
				showViewMsg: false,
        physicalRemarkList: [], // 留言（实物商品）
			};
		},

		components: { invoiceEdit, Pay },
		props: {},
		computed:{
			i18n() {
				return this.$t('index')
			}
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.getSeckill()

			// 虚拟商品留言缓存
			const virtualRemarkList = uni.getStorageSync('virtualRemark')
			if (virtualRemarkList && virtualRemarkList.length) {
				this.virtualRemarkList = JSON.parse(virtualRemarkList)
			} else {
				this.virtualRemarkList = []
			}
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			//头部导航标题
			uni.setNavigationBarTitle({
				title:this.i18n.makeSureOrder
			})
			var pages = getCurrentPages();  //当前页面栈
			var currPage = pages[pages.length - 1];  //当前页面
			if (currPage.selAddress == "yes") {
			  this.setData({
			    //将携带的参数赋值
			    userAddr: currPage.item
			  });
			  this.confirmOrder()
			}
		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			// 发票信息弹窗
			showInvoicePopup(shopId, invoiceDataFrom) {
				if (invoiceDataFrom) {
					this.invoiceDataFrom = invoiceDataFrom
				}
				this.invoiceShopId = shopId
				this.isShowInvoicePopup = true
			},
			getInvoiceData(data) {
				// invoiceDataFrom
				this.invoiceDataFrom = data
				console.log(data)
			},
			closePopup: function() {
				this.setData({
					isShowInvoicePopup: false,
					showViewMsg: false
				});
			},
			onRemarksInput: function(e) {
				this.setData({
					remarks: e.detail.value
				});
			},

			// 获取orderPath
			getSeckill() {
				http.request({
					url: '/p/seckill/orderPath',
					method: 'GET',
					callBack: res => {
						this.setData({
							orderPath: res
						});
						this.confirmOrder()
					}
				});
			},

			// 查看留言弹窗
			showViewMsgPopup: function() {
				this.showViewMsg = true
			},

			// 确认订单
			confirmOrder() {
				var secKillObj = wx.getStorageSync("secKillObj");
				if (this.mold !== 1 && this.userAddr) {
					secKillObj.addrId = this.userAddr.addrId;
				}
				http.request({
					url: `/p/seckill/${this.orderPath}/confirm`,
					method: 'POST',
					data: secKillObj,
					callBack: res => {
						this.setData({
							fullItemObj: res,
							goodsPrice: res.shopCartItem.prodCount * res.shopCartItem.seckillPrice,
							transfee: res.transfee,
							totalPrice: res.transfee + res.shopCartItem.prodCount * res.shopCartItem.seckillPrice,
							userAddr: res.userAddr,
							mold: res.mold, // 1虚拟商品
              physicalRemarkList: res.virtualRemarkList, // 实物商品备注列表，后端和虚拟商品备注共用一个字段
						});
					}
				});
			},

			/**
			 * 提交订单
			 */
			submitOrder() {
        // 实物留言判断
        if (this.mold !== 1 && this.physicalRemarkList.length > 0) {
          if (this.physicalRemarkList.find(el => el.value && !el.value.trim())) {
            uni.showToast({
              title: this.i18n.mesgCannotBeAllSpaces,
              icon: 'none'
            })
            return
          }
          if(this.physicalRemarkList.find(el => !el.value && el.isRequired)) {
            uni.showToast({
              title: this.i18n.requiredMessage,
              icon: 'none'
            })
            return
          }
        }
				let orderInvoiceDTO = null // invoiceDataFrom
				if (this.invoiceDataFrom && this.invoiceDataFrom.invoiceType === 1) {
					orderInvoiceDTO = this.invoiceDataFrom
				}
        const remarkList = this.mold === 1 ? this.virtualRemarkList : this.physicalRemarkList
				let orderShopParam = {
					remarks: this.remarks.trim() ? this.remarks : '',
					shopId: this.fullItemObj.shopCartItem.shopId,
					orderInvoice: orderInvoiceDTO,
					virtualRemarkList: remarkList
				}
				let params = {
					url: `/p/seckill/${this.orderPath}/submit`,
					method: 'POST',
					data: orderShopParam,
					callBack: res => {
						this.seckillOrderNumber = res.orderNumbers
						uni.showLoading({
							title: this.i18n.desperatelyBuying,
							// #ifndef MP-TOUTIAO
							mask: true
              // #endif
						})
						setTimeout(()=> {
							this.checkSeckillOrderSubmitStatus()
						}, 3000)
					}
				}
				http.request(params)
			},

			/**
			 * 检查秒杀订单提交状态
			 */
			checkSeckillOrderSubmitStatus() {
				if (this.pollingTimes < 10) {
					const params = {
						url: '/p/seckill/createOrderStatus',
						method: 'GET',
						data: {
							orderNumber: this.seckillOrderNumber
						},
						callBack: res => {
							if (res) {
								uni.hideLoading()
								clearTimeout(this.seckillOrderStsTimer)
                this.$refs.payRef.init({
                  orderNumbers: this.seckillOrderNumber,
                  orderType: 2
                })
							} else {
								this.pollingTimes++
								this.seckillOrderStsTimer = setTimeout(this.checkSeckillOrderSubmitStatus, 3000)
							}
						}
					}
					http.request(params)
				} else {
					uni.hideLoading()
					clearTimeout(this.seckillOrderStsTimer)
					uni.showToast({
						title: this.i18n.secFailTips,
						icon: 'none',
						// #ifndef MP-TOUTIAO
						mask: true
						// #endif
					})
				}
			},

			/**
			 * 选择地址
			 */
			toAddrListPage: function() {
				uni.navigateTo({
					url: '/packageUser/pages/delivery-address/delivery-address?order=0'
				});
			}
		}
	};
</script>
<style scoped>
	@import "./confirmOrder.css";
</style>

<template>
<!-- 我的优惠券 -->
<view class="container" style="background: #fff;height: 100vh">
  <view class="h-tabs">
    <!-- 我的优惠券状态(优惠券状态 0:失效 1:有效 2:使用过) -->
    <view :class="'h-tab ' + (type==1?'on':'')" data-type="1" @tap="changeTab">{{i18n.unused}}({{unUseCount}})</view>
    <view :class="'h-tab ' + (type==2?'on':'')" data-type="2" @tap="changeTab">{{i18n.usageRecord}}({{useCount}})</view>
    <view :class="'h-tab ' + (type==0?'on':'')" data-type="0" @tap="changeTab">{{i18n.expired}}({{expiredCount}})</view>
  </view>
  <view class="coupons">
    <!-- 由于「我的优惠券」可以有相同 id 的券，所以 id 取 index -->
    <view 
      v-for="(item, index) in couponList"
      :key="item.couponUserId" 
      class="coupon-item"
    >
      <ComplexCouponCard 
        :coupon="item"
        :isShowBtn="type == 1"
        @changeShowDetail="changeShowDetail"
        @longtap="delCouponHandle(item.couponId)"
      />
      <!-- 已使用 -->
      <image class="tag-img" v-if="type==2" :src="`${staticPicDomain}images/icon/coupon-used.png`"></image>
    </view>
  </view>

  <!-- 空 -->
  <view class="empty" v-if="!couponList.length">
    <view class="empty-icon">
      <image :src="`${staticPicDomain}images/icon/empty.png`"></image>
    </view>
    <view class="empty-text">{{i18n.couponTips}}</view>
  </view>

</view>
</template>

<script>
// pages/myCoupon/myCoupon.js
var http = require("../../../utils/http.js");
import ComplexCouponCard from '@/components/coupon-card/complex-coupon-card'

export default {
  data() {
    return {
      type: 1,
      couponList: [],
      unUseCount: 0,
      useCount: 0,
      expiredCount: 0,
      pages: 0,
      current: 1,
      size: 20
    };
  },
  components: {
    ComplexCouponCard,
  },
  props: {},
  computed:{
  	i18n() {
  		return this.$t('index')
  	}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.couponCount();
    this.loadMyCouponData(this.type);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
	  uni.setNavigationBarTitle({
	      title:this.i18n.myDiscountCoupon
	  });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if (this.current < this.pages) {
      this.loadOrderData(this.sts, this.current + 1);
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.current < this.pages) {
      this.setData({
        current: this.current + 1
      });
      this.loadMyCouponData(this.type);
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    /**
     * 获取我的优惠券列表
     */
    loadMyCouponData(type) {
			uni.showLoading()
      // 我的优惠券状态(status优惠券状态 0:失效 1:有效 2:使用过)
      var params = {
        url: "/p/myCoupon/getCouponList",
        method: "GET",
        data: {
          status: type,
          current: this.current,
          size: this.size
        },
        callBack: res => {
					uni.hideLoading()
          let list = [];
          if (res.current == 1) {
            list = res.records;
          } else {
            list = this.couponList.concat(res.records);
          }
          this.initCouponFlag(list);
          this.setData({
            couponList: list,
            pages: res.pages
          });
        }
      };
      http.request(params);
    },

    /**
     *  标签切换事件
     */
    changeTab(e) {
      this.setData({
        current: 1,
        type: e.currentTarget.dataset.type,
        couponList: []
      });
      this.loadMyCouponData(this.type);
    },

    /**
     *  获取各个状态下优惠券数量
     */
    couponCount() {
      var ths = this;
      var params = {
        url: "/p/myCoupon/getMyCouponsStatusCount",
        method: "GET",
        data: {},
        callBack: function (res) {
          ths.setData({
            unUseCount: res.unUseCount,
            expiredCount: res.expiredCount,
            useCount: res.useCount
          },()=>{
            uni.setStorageSync("unUseCouponCount", ths.unUseCount) 
          });
        }
      };
      http.request(params);
    },

    /**
     * 删除优惠券
     */
    delCouponHandle(couponId) {
      let ths = this;
      uni.showModal({
        title: '',
        content: ths.i18n.deleteCouponTips,
				cancelText: ths.i18n.cancel,
				confirmText: ths.i18n.confirm,
        confirmColor: "#eb2444",

        success(res) {
          if (res.confirm) {
            uni.showLoading();
            let params = {
              url: "/p/myCoupon/deleteCoupon/" + couponId,
              method: 'POST',
              data: {},
              callBack: function (res) {
                ths.loadMyCouponData(ths.type);
                uni.hideLoading()              
              }
            };
            http.request(params);
          } 
        }

      });
    },

    initCouponFlag(couponList) {
      couponList.forEach(coupon => {
        coupon.canGoUse = true;
        coupon.isShowDetail = false
      });
    },
    changeShowDetail(coupon) {
      var tempCouponList = this.couponList;
      let index = tempCouponList.findIndex(item => item.couponUserId === coupon.couponUserId)
      tempCouponList[index].isShowDetail = !tempCouponList[index].isShowDetail
      this.setData({
        couponList: tempCouponList
      })
    },

  }
};
</script>
<style>
@import "./myCoupon.css";
</style>
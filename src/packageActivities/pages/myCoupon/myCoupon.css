.container {
  padding-top: 20rpx;
}
.h-tabs {
  border-bottom: 1px solid #f2f2f2;
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  position: fixed;
  top: 0;
  z-index: 1000;
  width: 100%;
  background: #fff;
}

.h-tab {
  height: 88rpx;
  line-height: 88rpx;
}

.h-tab.on {
  border-bottom: 3px solid var(--primary-color);
  font-weight: bold;
  color: var(--primary-color);
}

.coupons {
  margin-top: 68rpx;
  padding: 20rpx;
}

.coupon-item {
  margin-bottom: 32rpx;
  position: relative;
}

.coupon-item .tag-img{
  position: absolute;
  bottom: -6px;
  right: -6px;
  width: 120rpx;
  height: 120rpx;
}

/* .empty {
  text-align: center;
  padding-top: 50rpx;
  color: #999999;
  font-size: 28rpx;
} */
/* 列表为空 */
.empty {
  margin-top: 200rpx;
  text-align: cneter;
}
.empty-icon {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.empty-icon > image {
  width: 100%;
  height: 100%;
}
.empty-text {
  font-size: 28rpx;
  text-align: center;
  color: #999;
  line-height: 2em;
}
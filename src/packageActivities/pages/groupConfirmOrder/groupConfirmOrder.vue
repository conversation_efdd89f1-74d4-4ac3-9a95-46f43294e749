<template>
	<view v-if="showPage">
		<!-- 确认订单 -->

		<!-- 参团流程 -->
		<view class="process">

			<view class="process-item choose-prod">
				<view class="process-icon">
					<image :src="`${staticPicDomain}images/icon/gift.png`"></image>
				</view>
				<view class="process-text">{{i18n.group1}}</view>
			</view>

			<view class="next-icon">
				<image :src="`${staticPicDomain}images/icon/more.png`"></image>
			</view>

			<view class="process-item invent">
				<view class="process-icon">
					<image :src="`${staticPicDomain}images/icon/chat.png`"></image>
				</view>
				<view class="process-text">{{i18n.group2}}</view>
			</view>

			<view class="next-icon">
				<image :src="`${staticPicDomain}images/icon/more.png`"></image>
			</view>

			<view class="process-item full">
				<view class="process-icon">
					<image :src="`${staticPicDomain}images/icon/enough.png`"></image>
				</view>
				<view class="process-text">{{i18n.group3}}</view>
			</view>
		</view>
		<!-- 流程end -->


		<!--地址栏 -->
		<view v-if="mold !== 1" class="address-box" @tap="toAddrListPage">
			<!-- 地址图标 -->
			<view class="addr-icon">
				<image :src="`${staticPicDomain}images/icon/addr.png`"></image>
			</view>
			<view class="add-addr" v-if="!userAddrDto">{{i18n.addShippingAddress}}</view>
			<block v-if="userAddrDto">
				<!-- 地址信息 -->
				<view class="recipient clearfix">
					<text class="username">{{userAddrDto.receiver}}</text>
					<text class="cellphone">{{userAddrDto.mobile}}</text>
				</view>
				<view class="addr-text">
					{{i18n.shippingAddress}}：{{userAddrDto.province}}{{userAddrDto.city}}{{userAddrDto.area}}{{userAddrDto.addr}}
				</view>
			</block>

			<!-- 右箭头 -->
			<view class="more-icon">
				<image :src="`${staticPicDomain}images/icon/more.png`"></image>
			</view>
			<!-- 下虚线 -->
			<view class="dotted-line">
				<image :src="`${staticPicDomain}images/icon/dotted-line.png`"></image>
			</view>
		</view>
		<!--地址栏end -->


		<!-- 商品信息 -->
		<view class="prod-box">
			<!-- <view class="prod-shop">
      <view class="shop-icon"><image src="../../images/icon/shop.png"></image></view>
      <text class="shop-name">笑橙小店190827</text>
    </view>
    -->
			<!-- 商品信息 -->
			<view class="prod-msg">
				<view class="prod-img">
					<image :src="orderInfo.prodPic"></image>
				</view>
				<view class="prod-text clearfix">
					<view class="prod-name">{{orderInfo.prodName}}</view>
					<view v-if="orderInfo.skuName" class="prod-sku">{{orderInfo.skuName}}</view>
					<view class="prod-price-box">
						<text class="prod-price">￥{{orderInfo.actPrice}}</text>
						<text class="prod-price-del">￥{{orderInfo.price}}</text>
						<text class="prod-amount">x {{orderInfo.prodTotalCount}}</text>
					</view>
				</view>
			</view>

      <view class="physical-remark-list-wrap" v-if="mold != 1">
        <view class="physical-remark-list">
          <template v-for="physicalRemarkItem in physicalRemarkList">
            <view class="physical-remark-item">
              <view class="physical-remark-item-tit"><text v-if="physicalRemarkItem.isRequired" class="stress">*</text>{{physicalRemarkItem.name}}</view>
              <input class="physical-remark-item-int" v-model="physicalRemarkItem.value" :placeholder="i18n.pleaseEnter + `${physicalRemarkItem.name}`" maxlength="20" />
            </view>
          </template>
        </view>
      </view>

      <!-- 赠品信息 -->
      <view v-if="orderInfo.giveaway" class="gift-con">
        <view v-for="(giveawayItem, giveawayIndex) in orderInfo.giveaway.giveawayProds" :key="giveawayIndex" class="gift-item">
          <view class="gift-name">【{{i18n.Giveaways}}】{{ giveawayItem.prodName }}<text v-if="giveawayItem.skuName" class="gift-sku">{{giveawayItem.skuName||''}}</text></view>
          <view class="gift-count">×{{ giveawayItem.giveawayNum }} </view>
        </view>
      </view>
		</view>
		<!-- 商品信息end -->

		<!-- 商家优惠券 -->
			<view class="item coupon live-message" @tap="showCouponPopup" :data-index="1">
				<view class="text-box">
					<text class="text">商家{{i18n.coupon}}：</text>
					<text class="number" v-if="canUseLength <= 0 ">{{i18n.notAvailable}}</text>
					<text class="number" v-else>{{canUseLength}}&nbsp;{{i18n.zhangAvailable}}</text>
				</view>
				<view class="amount" v-if="couponPrice > 0">
					<text class="symbol">-￥</text>
					<text class="big-num">{{parsePrice(couponPrice)[0]}}</text>
					<text class="small-num">.{{parsePrice(couponPrice)[1]}}</text>
				</view>
			</view>

		<!-- 配送&留言 -->
		<view class="row">
			<!-- <view class="dispatching clearfix">
      <text class="dispatching-tit">配送方式</text>
      <text class="dispatching-way">快递 免运费</text>
    </view> -->
			<view class="live-message">
				<text class="message-tit">{{i18n.OrderNotes}}：</text>
				<input class="message-int" maxlength="100" :placeholder="i18n.buyerTips" :value="remarks"
					@input="onRemarksInput"/>
			</view>
			<!-- <view class="item invoice">
				<view class="tit">{{i18n.invoice.onvoiceIssuance}}：</view>
				<view v-if="!invoiceDataFrom.invoiceType || invoiceDataFrom.invoiceType === 2" class="item"
					@tap="showInvoicePopup(orderInfo.shopId)">
					<view class="text-arrow">
						<view class="text">{{i18n.invoice.noInvoice}}</view>
					</view>
				</view>
				<view v-else class="item" @tap="showInvoicePopup(orderInfo.shopId,invoiceDataFrom)">
					<view class="text-arrow">
						<view class="text">本次不开具发票</view> 这里有一行注释
						<view class="text">{{ i18n.invoice.electronic }}({{i18n.invoice.productDetails}}-{{ invoiceDataFrom.headerName || i18n.invoice.personal }})</view>
					</view>
				</view>
			</view> -->
			<view v-if="mold == 1 && virtualRemarkList.length" class="item invoice all-msg">
				<view class="tit">{{i18n.allMsg}}：</view>
				<view class="item">
					<view class="text-arrow" @tap="showViewMsgPopup">
						<view class="text">
							<view class="msg-name">{{virtualRemarkList[0].name}}</view>
							<view class="msg-con">{{virtualRemarkList[0].value}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 配送&留言 end -->


		<!-- 商品总额 -->
		<view class="sum">
			<view class="cost-box">
				<view class="prod-table clearfix">
					<text
						class="prod-table-tit">{{(orderInfo.hasLeaderPrice==1&&groupTeamId=="0")?i18n.headPrice:i18n.groupPurchasePrice}}</text>
					<text class="prod-table-way">￥<text v-if="orderInfo.hasLeaderPrice" class="leader-price">{{orderInfo.groupProdActualTotal}} </text>
					<text :class="orderInfo.hasLeaderPrice?'del-line':''">{{orderInfo.actPrice}}</text></text>
				</view>
				<view v-if="mold !== 1" class="prod-table clearfix">
					<text class="prod-table-tit">{{i18n.shipping}}</text>
					<text class="prod-table-way">￥{{orderInfo.transfee}}</text>
				</view>
        <view class="prod-table clearfix">
          <text class="prod-table-tit">{{i18n.groupPurchaseOffer}}</text>
          <text class="prod-table-way shine">-￥{{orderInfo.discountPrice}}</text>
        </view>
			</view>
			<!-- 合计 -->
			<view class="total-cost">{{i18n.total}}：<text class="total-num">￥{{orderInfo.orderTotalPrice}}</text>
			</view>
		</view>
		<!-- 商品金额 end -->


		<!-- 底部 -->
		<view class="foot-box">
			<view class="foot">
				<view class="total-price">{{i18n.total}}：<text
						class="total-price-num">￥{{orderInfo.orderTotalPrice}}</text></view>
				<!-- 提交按钮 -->
				<view class="submit-btn" @tap="commitOrder">{{i18n.submitOrders}}</view>
			</view>
		</view>
		<!-- 底部end -->
		<invoiceEdit v-if="isShowInvoicePopup" :invoice-data-from="invoiceDataFrom" :shop-id="invoiceShopId"
			:invoice-id="invoiceId" @closePopup="closePopup" @getInvoiceData="getInvoiceData" />

		<!-- 查看留言（虚拟商品） -->
		<view class="popup-hide" :hidden="!showViewMsg">
			<view class="popup-box virtual-goods-msg-pop">
				<view class="con-tit">
					<view class="tit-text">{{i18n.viewMsg}}</view>
					<view class="close" @tap="closePopup"></view>
				</view>
				<view class="msg-pop-con">
					<view class="msg-list">
						<view v-for="(item, index) in virtualRemarkList" :key="index" class="msg-item">
							<view class="item-con weak">{{item.name}}</view>
							<view class="item-con">{{item.value}}</view>
						</view>
					</view>
					<view class="pop-foot">
						<view class="foot-btn" @tap="closePopup">{{i18n.gotIt}}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 选择优惠券弹窗 -->
		<view class="popup-hide" v-if="popupShow">
			<view class="popup-box">
				<view class="popup-tit">
					<text>{{i18n.coupon}}</text>
					<view class="close" @tap="closePopup"></view>
				</view>
				<view class="coupon-tabs">
					<view :class="'coupon-tab ' + (couponSts==1?'on':'')" @tap="changeCouponSts" data-sts="1">{{i18n.availableCoupons}}({{showCoupons.canUseCoupons.length}})</view>
					<view :class="'coupon-tab ' + (couponSts==2?'on':'')" @tap="changeCouponSts" data-sts="2">{{i18n.unavailableCoupons}}({{showCoupons.unCanUseCoupons.length}})</view>
				</view>
				<view :class="'popup-cnt ' + (couponSts==2? 'on':'')">
					<!-- 可用 -->
					<view class="coupon-con" v-if="couponSts == 1">
						<block v-for="(item, index) in showCoupons.canUseCoupons" :key="index">
							<coupon :item="item" :order="order" :index="index" :isShopCoupon="isShopCoupon" @checkCoupon="checkCoupon" :canUse="isCanUse"></coupon>
						</block>
					</view>
					<!-- 不可用 -->
					<view class="coupon-con" v-if="couponSts == 2">
						<block v-for="(item, index) in showCoupons.unCanUseCoupons" :key="index">
							<coupon :item="item" :order="order" :canUse="!isCanUse" :isShopCoupon="isShopCoupon"></coupon>
						</block>
					</view>
					<view class="botm-empty" v-if="couponSts==1 && !showCoupons.canUseCoupons.length">{{i18n.getCouponTips}}</view>
					<view class="botm-empty" v-if="couponSts==2 && !showCoupons.unCanUseCoupons.length">{{i18n.NoRelevantCoupons}}</view>
				</view>
				<view class="coupon-ok" v-if="couponSts==1">
					<view class="btn" @tap="choosedCoupon">{{i18n.confirm}}</view>
				</view>
			</view>
		</view>
    <PayComponent ref="payRef" />
	</view>
</template>

<script>
	// pages/confirmOrder/confirmOrder.js
	const http = require("../../../utils/http.js")
	import {
		PayType,
		AppType
	} from "../../../utils/constant.js"
	import Pay from "../../../utils/pay.js"
	import invoiceEdit from '../../../components/invoiceEdit/index'
	import coupon from "../../../components/orderCoupon/coupon";
  import PayComponent from '@/components/pay/pay'

	export default {
		data() {
			return {
				orderInfo: {},
				userAddr: null,
				remarks: "",
				userAddrDto: {},
				showPage: false, // 拼团信息展示
				groupTeamId: '', //判断开团还是拼团

				invoiceId: '', // 发票id
				invoiceShopId: '',
				invoiceDataFrom: {},
				isShowInvoicePopup: false,

				mold: null, // 1虚拟商品
				// 留言（虚拟商品）
				virtualRemarkList: [],
				// 查看留言弹窗显隐
				showViewMsg: false,
        physicalRemarkList: [], // 留言（实物商品）
				// 是否正在打开店铺优惠券弹窗
				isShopCoupon: null,
				isCanUse: true,
				order: true,
				//店铺切换可用/不可用优惠券列表
				couponSts: 1,
				userChangeCoupon:  false, // 用户有没有对优惠券进行改变
				popupShow: false,
				chooseCouponId: null,
				showCoupons: [],
				couponPrice: 0,
				canUseLength: 0,
			};
		},

		components: {
			invoiceEdit,
			coupon,
      PayComponent
		},
		props: {},
		computed: {
			i18n() {
				return this.$t('index')
			},
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			// 虚拟商品留言缓存
			const virtualRemarkList = uni.getStorageSync('virtualRemark')
			if (virtualRemarkList && virtualRemarkList.length) {
				this.virtualRemarkList = JSON.parse(virtualRemarkList)
			} else {
				this.virtualRemarkList = []
			}
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			//头部导航标题
			uni.setNavigationBarTitle({
				title: this.i18n.makeSureOrder
			});
			var pages = getCurrentPages();
			var currPage = pages[pages.length - 1];

			if (currPage.selAddress == "yes") {
				this.setData({
					//将携带的参数赋值
					userAddr: currPage.item,
				});
			}

		},
		mounted() {
			this.loadOrderData();
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			// 发票信息弹窗
			showInvoicePopup(shopId, invoiceDataFrom) {
				if (invoiceDataFrom) {
					this.invoiceDataFrom = invoiceDataFrom
				}
				this.invoiceShopId = shopId
				this.isShowInvoicePopup = true
			},
			getInvoiceData(data) {
				this.invoiceDataFrom = data
			},
			onRemarksInput: function(e) {
				this.setData({
					remarks: e.detail.value
				});
			},
			closePopup: function() {
				this.setData({
					isShowInvoicePopup: false,
					showViewMsg: false,
					popupShow: false,
				});
			},
			//加载订单数据
			loadOrderData: function() {
				var addrId = 0;

				if (this.userAddr != null) {
					addrId = this.userAddr.addrId;
				}

				uni.showLoading({
					// #ifndef MP-TOUTIAO
						mask: true
					// #endif
				});
				let groupOrderItem = JSON.parse(wx.getStorageSync("groupOrderItem"));
				this.groupTeamId = groupOrderItem.groupTeamId;
				var params = {
					url: "/p/group/order/confirm",
					method: "POST",
					data: {
						addrId: addrId,
						groupSkuId: groupOrderItem.groupSkuId,
						groupTeamId: groupOrderItem.groupTeamId,
						prodCount: groupOrderItem.prodCount,
						userChangeCoupon: this.userChangeCoupon,
						chooseCouponId: this.chooseCouponId,
					},
					callBack: res => {
						uni.hideLoading();
						this.shopCoupons = this.splitCouponAndPushCouponIds(res.coupons);
						this.setData({
							orderInfo: res,
							userAddrDto: res.userAddrDto,
							mold: res.mold,
              physicalRemarkList: res.virtualRemarkList, // 实物商品备注列表，后端和虚拟商品备注共用一个字段
							couponPrice: res.couponPrice,
							chooseCouponId: res.chooseCouponId,
							canUseLength: this.shopCoupons.canUseLength
						});
						this.showPage = true
					},
					errCallBack: errRes => {
						uni.showModal({
							showCancel: false,
							title: this.i18n.tips,
							content: errRes.data,
							cancelText: this.i18n.cancel,
							confirmText: this.i18n.confirm,
							success: successRes => {
								if (successRes.confirm) {
									uni.navigateBack({
										delta: 1,
									})
								}
							}
						})
					}
				};
				http.request(params);
			},

			// 查看留言弹窗
			showViewMsgPopup: function() {
				this.showViewMsg = true
			},

			// 提交订单
			commitOrder() {
				if (this.mold !== 1 && !this.userAddrDto) {
					uni.showToast({
						title: this.i18n.addAddressFirst,
						icon: 'none'
					})
					return
				}
        // 实物留言判断
        if (this.mold !== 1 && this.physicalRemarkList.length > 0) {
          if (this.physicalRemarkList.find(el => el.value && !el.value.trim())) {
            uni.showToast({
              title: this.i18n.mesgCannotBeAllSpaces,
              icon: 'none'
            })
            return
          }
          if(this.physicalRemarkList.find(el => !el.value && el.isRequired)) {
            uni.showToast({
              title: this.i18n.requiredMessage,
              icon: 'none'
            })
            return
          }
        }
				let orderInvoiceDTO = null // invoiceDataFrom
				if (this.invoiceDataFrom && this.invoiceDataFrom.invoiceType === 1) {
					orderInvoiceDTO = this.invoiceDataFrom
				}
        const remarkList = this.mold === 1 ? this.virtualRemarkList : this.physicalRemarkList
				var orderShopParam = {
					remarks: this.remarks.trim() ? this.remarks : '',
					virtualRemarkList: remarkList,
					orderInvoice: orderInvoiceDTO
				};
				uni.showLoading();
				http.request({
					url: `/p/group/order/submit`,
					method: 'POST',
					data: orderShopParam,
					callBack: res => {
						uni.hideLoading();
						const orderType = 1 // 1拼团 2秒杀
            this.$refs.payRef.init({
              orderNumbers: res.orderNumbers,
              orderType
            })
					},
					errCallBack: errRes => {
						uni.showModal({
							title: this.i18n.tips,
							content: errRes.data,
							confirmText: this.i18n.confirm,
							showCancel: false,
							success: (errModSuccess) => {
								if (errModSuccess.confirm) {
									uni.navigateBack({
										delta: 1,
									})
								}
							}
						})
					}
				});
			},

			/**
			 * 唤起微信支付
			 */
			calWeixinPay: function(orderNumbers) {
				const isOA = uni.getStorageSync('appType') == AppType.MP; // 是否微信内环境
				var payType = isOA ? PayType.WECHATPAY_MP : PayType.WECHATPAY_H5
				Pay.toOrderPay(payType, orderNumbers)
			},

			/**
			 * 去地址页面
			 */
			toAddrListPage: function() {
				uni.navigateTo({
					url: '/packageUser/pages/delivery-address/delivery-address?order=0'
				});
			},
			// 店铺优惠券弹框
			showCouponPopup: function(e) {
				var index = Number(e.currentTarget.dataset.index);
				this.setData({
					showCoupons: index + '' === '-1' ? this.platformCoupons : this.shopCoupons,
					popupShow: true,
				});
				this.showCoupons.canUseCoupons.forEach(item =>{
					if(this.chooseCouponId === item.couponId) {
						item.choose = true
					}
					else {
						item.choose = false
					}
				})
				this.isShopCoupon = index !== -1
			},
			// 店铺切换可用/不可用优惠券列表
			changeCouponSts: function(e) {
				this.setData({
					couponSts: e.currentTarget.dataset.sts
				});
			},
			/**
			 * 确定选择好的优惠券
			 */
			 choosedCoupon: function() {
				var chooseCouponId = null
				if (!this.showCoupons) {
					this.setData({
						popupShow: false
					})
					return
				}
				var canUseCoupons = this.showCoupons.canUseCoupons;
				for (var i in canUseCoupons) {
					var coupon = canUseCoupons[i];
					if (coupon.choose) {
						chooseCouponId = coupon.couponId
					}
				}
				this.setData({
					popupShow: false,
					chooseCouponId: chooseCouponId,
				});
				this.loadOrderData();
			},
			/**
			 * 优惠券子组件发过来
			 */
			 checkCoupon: function(e) {
				var showCoupons = this.showCoupons; // 店铺优惠券单选操作
				var canUseCoupons = showCoupons.canUseCoupons;

				for (var canUseCouponIndex in canUseCoupons) {
					if (e.couponUserId == canUseCoupons[canUseCouponIndex].couponUserId && canUseCouponIndex == e.index) {
						canUseCoupons[canUseCouponIndex].choose = !canUseCoupons[canUseCouponIndex].choose;
					} else {
						canUseCoupons[canUseCouponIndex].choose = false;
					}
				}
				this.setData({
					showCoupons: showCoupons,
					userChangeCoupon:  true
				});
			},

			/**
			 * 分割优惠券成
			 * 1. canUseCoupons 可使用优惠券列表
			 * 2. unCanUseCoupons 不可使用优惠券列表
			 * 3. canUseLength 可使用优惠券列表长度
			 * @param {*} coupons 优惠券列表
			 */
			 splitCouponAndPushCouponIds(coupons) {
				if (!coupons || !coupons.length) {
					return {
						canUseLength: 0,
						canUseCoupons: [],
						unCanUseCoupons: []
					}
				}

				let canUseCoupons = [];
				let unCanUseCoupons = [];
				coupons.forEach(coupon => {
					if (coupon.canUse) {
						canUseCoupons.push(coupon);
					} else {
						unCanUseCoupons.push(coupon);
					}
				});
				return {
					canUseLength: canUseCoupons.length,
					canUseCoupons: canUseCoupons,
					unCanUseCoupons: unCanUseCoupons
				};
			},
		}
	};
</script>
<style>
	@import "./groupConfirmOrder.css";
</style>

/* pages/SpellGroupDetails/spellGroupDetails.wxss */

.clearfix::after {
  content: '';
  display: block;
  clear: both;
}

.top-box {
  position: relative;
  background: #fff;
  border-bottom: 15rpx solid #f2f2f2;
	height: 400rpx;
	overflow: hidden;
}

.top-bg {
  width: 100%;
  height: 450rpx;
  border-radius: 50%;
  position: relative;
	/* left: -27%; */
  top: -135px;
  color: #fff;
  text-align: center;
  line-height: 100px;
	transform: scaleX(2) ;
  z-index: 1;
  background: -webkit-linear-gradient(to right, var(--primary-color), var(--primary-color)); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(to left, var(--primary-color), var(--primary-color)); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(to left, var(--primary-color), var(--primary-color)); /* Firefox 3.6 - 15 */
  background: linear-gradient(to right, var(--primary-color), var(--primary-color)); /* 标准*/
	overflow: hidden;
}

/* 商品内容 */

.goods-content {
  position: absolute;
  top: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 85%;
  margin: 0 auto;
  padding: 15rpx;
  z-index: 11;
  background: #fff;
  border-radius: 15rpx;
  box-shadow: 0 7rpx 12rpx 0 #e9e9e9;
}

.goods-img-box {
  display: inline-block;
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  background: #eee;
  vertical-align: middle;
  border-radius: 10rpx;
}

.goods-img {
  width: 100%;
  height: 100%;
}

.goods-msg {
  display: inline-block;
  height: 100%;
  vertical-align: top;
  font-size: 28rpx;
}

.goods-title {
  display: inline-block;
  width: 400rpx;
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}


.goods-price {
  position: relative;
  top: 50rpx;
}

.spell-group-price {
  color: red;
  margin-right: 0.5em;
  font-size: 30rpx;
}

.original-price {
  font-size: 23rpx;
  text-decoration: line-through;
  color: #888;
}

/* 拼团规则 */

.tips-text {
  font-size: 28rpx;
  position: absolute;
  bottom: .7em;
  width: 85%;
  left: 50%;
  transform: translateX(-50%);
  text-align: left;
	display: flex;
	align-items: center;
}
.tips-text .tips-text-cnt {
	max-width: 90%;
}

.thumb-icon {
  display: inline-block;
  width: 20rpx;
  height: 20rpx;
  margin-right: 15rpx;
}

.right-arrow-icon {
  width: 25rpx;
  height: 25rpx;
}

/* 下方 */

.condition {
  padding-top: 1em;
  background: #fff;
  font-size: 28rpx;
  text-align: center;
}

/* 剩余时间 */

.time-remaining {
  position: relative;
  text-align: center;
}

.time-text {
  padding: 0 3em;
  color: var(--primary-color);
	position: relative;
}



.line-left, .line-right {
  position: absolute;
  display: inline-block;
  top: 50%;
  transform: translateX(-50%);
  width: 3em;
  height: 5rpx;
  background-color: var(--primary-color);
}

.line-left::before, .line-right::before {
	content: ' ';
	display: block;
	position: absolute;
	top: -2rpx;
	right: -20rpx;
	width: 4px;
	height: 4px;
	border-radius: 50%;
	background: var(--primary-color);
}
.line-right::before{
	right: unset;
	left: -20rpx;
}

.countdown {
  text-align: center;
  padding: 1em 0;
  color: var(--primary-color);
}

.time-number {
  background: var(--primary-color);
  color: #fff;
  border-radius: 8rpx;
  padding: 0 4rpx;
}

/* 人员 */

.member {
  height: 120rpx;
  overflow: hidden;
  display: inline-block;
	margin: 20rpx 0;
	padding-left: 20rpx;
}

.captain, .group-member {
  display: inline-block;
  position: relative;
  width: 120rpx;
  height: 120rpx;
  z-index: 99;
}

.captain-img {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border: 4rpx solid var(--primary-color);
  border-radius: 50%;
}

.status {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
  font-size: 23rpx;
  background: var(--primary-color);
  color: #fff;
  border-radius: 10rpx;
	width: auto;
	padding: 0 10rpx;
	word-break: keep-all;
}
/* .group-member {
  left: -1em;
  z-index: 2;
} */

.group-member-img {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border: 3rpx dashed var(--primary-color);
  border-radius: 50%;
}

.group-member:last-child {
  left: -2em;
  z-index: 1;
}

.view-all {
  color: #fd3852;
  border: 1px solid #fd3852;
  width: 7em;
  margin: 0 auto;
  border-radius: 30rpx;
  padding: 0.2em 0;
  font-size: 22rpx;
  margin-bottom: 1.5em;
}

.invite-text {
  margin: 1em 0;
  font-size: 28rpx;
}

.invite-text > text {
  color: #fd3852;
}

.display {
  display: none;
}

/* 立即参团 */

.join-now, .check-order {
  box-sizing: border-box;
  width: 85%;
  margin: 1.5em auto;
  border-radius: 50rpx;
  font-size: 27rpx;
}

.join-now {
  color: #fff;
  height:80rpx;
  line-height: 80rpx;
  background: -webkit-linear-gradient(to right, var(--primary-color), var(--primary-color)); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(to left, var(--primary-color), var(--primary-color)); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(to left, var(--primary-color), var(--primary-color)); /* Firefox 3.6 - 15 */
  background: linear-gradient(to right, var(--primary-color), var(--primary-color)); /* 标准*/
	border: 0;
	padding: 0;
}
.join-now::after {
  border: 0;
}

.check-order {
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 0.7em 0;
}

/* 弹框 */

/*模态框*/

.modals {
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.modals-cancel {
  position: absolute;
  z-index: 190;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.bottom-dialog-body {
  position: absolute;
  z-index: 200;
  bottom: 0;
  left: 0;
  right: 0;
  height: auto;
  background-color: #fff;
}

/*动画前初始位置*/

.bottom-pos {
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
}

/* 上部内容 */

.top-content {
  position: relative;
  border-bottom: 1px solid #f2f2f2;
  font-size: 25rpx;
}

.top-content-img-box {
  position: absolute;
  top: -25rpx;
  width: 150rpx;
  height: 150rpx;
  background: #f2f2f2;
  margin-left: 30rpx;
}

.top-content-img-box > image {
  width: 100%;
  height: 100%;
}

.goods-msg-box {
  position: relative;
  margin-left: 200rpx;
  margin-bottom: 1em;
  margin-top: 0.3em;
}

.goods-names {
  line-height: 2em;
  color: #000;
  width: 85%;
}

.group-tips {
  font-size: 20rpx;
  color: #fd3852;
  padding: 0.3em 0;
  padding-top: 0;
}

.goods-prices {
  font-size: 30rpx;
  color: #fd3852;
  margin-top: 5rpx;
}

.rmb-symbol {
  margin-right: 0.5em;
}

.close-btn {
  position: absolute;
  display: block;
  right: 20rpx;
  top: 10rpx;
  width: 35rpx;
  height: 35rpx;
  /* background: url("../../static/icon/close.png");
  background-size: 100% 100%; */
  z-index: 250;
}

.close-btn > image {
  width: 100%;
  height: 100%;
}

/* sku字体 */

.sku-text {
  position: relative;
  padding: 1em;
  font-size: 25rpx;
  border-bottom: 1px solid #f2f2f2;
}

/* 数量选择 */

.quantity {
  position: relative;
  padding: 1em;
  font-size: 25rpx;
  border-bottom: 1px solid #f2f2f2;
}

.left-text {
  line-height: 1.5em;
}

.left-text-bottom {
  font-size: 20rpx;
  color: #bbb;
}

/* choose-quantity */

.right-choose {
  position: absolute;
  right: 1em;
  top: 1em;
}

.subtract, .show-num, .add {
  display: inline-block;
  background: #f5f5f5;
  color: #cfcfcf;
  padding: 7rpx 18rpx;
}

.show-num {
  margin: 0 3rpx;
}

.add {
  padding: 7rpx 12rpx;
}

.current-state {
  background: #ebebeb;
  color: #6d6d6e;
}

/* 确认按钮 */

.confirm-btn {
  width: 100%;
  font-size: 27rpx;
  padding: 0.8em 0;
  color: #fff;
  background: #f44;
  text-align: center;
  margin-top: 0.5em;
  margin-bottom: 200rpx;
}

/** 规格弹窗**/

.popup-hide {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.popup-box {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 375px;
  max-height: 475px;
  overflow: hidden;
  background-color: #fff;
}

.popup-tit {
  position: relative;
  height: 46px;
  line-height: 46px;
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  background-color: #f7f7f7;
}

.close {
  color: #aaa;
  border-radius: 12px;
  line-height: 20px;
  text-align: center;
  height: 20px;
  width: 20px;
  font-size: 18px;
  padding: 1px;
  top: 10px;
  right: 10px;
  position: absolute;
}

.close::before {
  content: "\2716";
}

.popup-cnt {
  max-height: 429px;
  overflow: auto;
  padding: 0 10px;
}

.pup-sku {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.pup-sku-main {
  position: absolute;
  bottom: 0;
  width: 100%;
  min-height: 375px;
  max-height: 475px;
  background-color: #fff;
}

.pup-sku-header {
  position: relative;
  line-height: 46px;
  padding-left: 10px;
  font-size: 16px;
  color: #333;
  height: 70px;
  padding: 0 0 10px 110px;
  background-color: #fff;
}

.pup-sku-img {
  position: absolute;
  left: 10px;
  top: -20px;
  border-radius: 4px;
  width: 90px;
  height: 90px;
  vertical-align: top;
  background: #fff;
  border: 2px solid #f8f8f8;
}

.pup-sku-price {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  color: #e4393c;
  font-size: 10px;
}

.pup-sku-price-int {
  font-size: 16px;
}

.pup-sku-prop {
  word-break: break-all;
  font-size: 12px;
  color: #333;
  line-height: 1.4em;
  padding-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.pup-sku-prop text:first-child {
  color: #999;
}

.pup-sku-body {
  box-sizing: border-box;
  max-height: 379px;
  padding-bottom: 100px;
  overflow: auto;
}
/*ipad适应样式 */
@media screen and (min-width: 500px) {
	/* 购买弹窗 */
	.pup-sku-body {
		max-height: 600px;
		padding-bottom: 120px;
	}
	.pup-sku-main {
		max-height: 775px;
	}
	/* 评论字体大小 */
	.cmt-tag text,.cmt-user,.cmt-cnt{
		font-size: 16px;
	}
}

.pup-sku-area .sku-kind {
  font-size: 12px;
  color: #999;
  margin: 0 10px;
  height: 40px;
  line-height: 40px;
}

.pup-sku-area .sku-choose {
  overflow: hidden;
  margin-bottom: 3px;
}

.sku-choose-item {
  display: inline-block;
  padding: 0 10px;
  min-width: 20px;
  max-width: 270px;
  overflow: hidden;
  height: 30px;
  line-height: 30px;
  text-align: center;
  margin-left: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  color: #333;
  background-color: #fff;
  font-size: 14px;
  border: 1px solid #E9EBED;
}

.sku-choose-item.active {
  background-color: #fff;
  color: var(--primary-color);
  border: 1px solid var(--primary-color) !important;
}

.sku-choose-item.gray {
  background-color: #f9f9f9;
  color: #ddd;
}
.sku-choose-item.dashed {
  border:1px dashed #ccc;
}

.pup-sku-count {
  padding: 0 10px 13px;
  font-size: 12px;
}

.pup-sku-count .count-name {
  color: #999;
  height: 31px;
  line-height: 31px;
  width: 100rpx;
}

.pup-sku-count .num-wrap {
  position: relative;
  z-index: 0;
  width: 110px;
  float: right;
  vertical-align: middle;
  display: flex;
}

.num-wrap .minus, .num-wrap .plus {
  position: relative;
  max-width: 30px;
  min-width: 30px;
  height: 30px;
  line-height: 30px;
  background: #f7f7f7;
  text-align: center;
}

.num-wrap .minus {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.num-wrap .plus {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.num-wrap .row {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -7px;
  margin-top: -1px;
  width: 14px;
  height: 2px;
  background-color: #ccc;
}

.num-wrap .col {
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -1px;
  margin-top: -7px;
  width: 2px;
  height: 14px;
  background-color: #999;
}

.pup-sku-count .text-wrap {
  position: relative;
  width: 45px;
  z-index: 0;
  margin: 0 1px;
}

.pup-sku-count .text-wrap input {
  height: 30px;
  width: 100%;
  color: #333;
  background: #fff;
  font-size: 12px;
  text-align: center;
  border: none;
  background: #f7f7f7;
}

.pup-sku-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row nowrap;
  height: 98rpx;
  z-index: 999;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.pup-sku-footer .btn {
  position: relative;
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  width: 0;
  background-color: #fff;
  font-size: 28rpx;
  flex-flow: column;
}

.pup-sku-footer .btn.cart {
  background: #584e61;
  color: #fff;
}

.pup-sku-footer .btn.buy {
  background: #e43130;
  color: #fff;
}
.pup-sku-footer .btn.buy.gray {
  background: #ddd;
}

.more-ellipsis {
	font-size: 50rpx;
	color: #AAAAAA;
}

/* 留言（虚拟商品） */
.virtual-goods-tips {
  display: block;
  background: #F9F9F9;
  padding: 20rpx;
  color: #999999;
  font-size: 24rpx;
  margin-bottom: 20rpx;
  line-height: 1.5em;
}
.pup-sku-count.virtual-goods-msg {
  margin-bottom: 30rpx;
  /* max-height: 600rpx;
  overflow-y: auto; */
}
.pup-sku-count.virtual-goods-msg .msg-item {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #F2F2F2;
  font-size: 24rpx;
  padding-bottom: 20rpx;
}
.pup-sku-count.virtual-goods-msg .msg-item:not(:first-child) {
  margin-top: 40rpx;
}
.pup-sku-count.virtual-goods-msg .msg-item .msg-tit {
  flex-wrap: wrap;
  width: 180rpx;
  margin-right: 20rpx;
  word-break: break-word;
}
.pup-sku-count.virtual-goods-msg .msg-item .stress {
  color: #E43130;
  margin-right: 10rpx;
}
.pup-sku-count.virtual-goods-msg .msg-item .msg-int {
  font-size: 24rpx;
  width: 100%;
}
.pup-sku-count.virtual-goods-msg .msg-item .msg-int .uni-input-placeholder {
  color: #aaa;
}
/* 留言（虚拟商品）/ */
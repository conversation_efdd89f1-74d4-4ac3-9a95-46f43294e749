// announcement.js
window.onload = function() {

  const content = `重庆电信公示：为提供更好的服务，我方将于2024年1月19日0点开始进行维护工作，正常情况下不影响使用，请用户提前登录避免受到意外情况影响`
  // Create announcement container
  var container = document.createElement('div');
  container.id = 'announcement-container';
  document.body.appendChild(container);

  // Create announcement bar
  var announcement = document.createElement('div');
  announcement.id = 'announcement';
  announcement.className = 'scroll';
  announcement.innerText = content;  // Change this line to your announcement
  var windowWidth = window.innerWidth;
  announcement.style.left = '0';
  container.appendChild(announcement);

  // Create modal
  var modal = document.createElement('div');
  modal.id = 'myModal';
  modal.className = 'modal';
  modal.innerHTML = `<div class="modal-content"><span class="close">&times;</span><p>${content}</p></div>`;  // Change the string inside <p> tags to your announcement details
  document.body.appendChild(modal);

  // Add styles
  var styles = `
      #announcement-container {
          position: fixed;
          top: 0;
          width: 100%;
          background-color: #3498db;
          color: #fff;
          z-index: 100;
          white-space: nowrap;
          overflow: hidden;
      }
      #announcement {
          position: relative;
          padding: 15px;
          cursor: pointer;
          width: fit-content;
      }
      .modal-content {
          background-color: #fefefe;
          margin: 5% auto;
          padding: 30px;
          border: 5px solid #3498db;
          border-radius: 20px;
          width: 50%;
          position: relative;
          box-shadow: 0px 5px 30px rgba(0,0,0,0.2);
      }
      .modal {
          display: none;
          position: fixed;
          z-index: 1000;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0,0,0,0.4);
      }
      .close {
          color: #3498db;
          float: right;
          font-size: 32px;
          font-weight: bold;
          position: absolute;
          right: 20px;
      }
      .close:hover,
      .close:focus {
          color: #000;
          text-decoration: none;
          cursor: pointer;
      }
  `;
  var styleSheet = document.createElement("style");
  styleSheet.type = "text/css";
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);

  // Scroll function
  function scrollAnnouncement() {
      var announcementElement = document.querySelector('#announcement');
      // 窗口宽度
      if (parseInt(announcementElement.style.left) <= -announcementElement.offsetWidth) {
          announcementElement.style.transition = 'none';
          announcementElement.style.left = `${windowWidth}px`;
      } else {
          announcementElement.style.transition = 'left 0.5s linear';
          announcementElement.style.left = parseInt(announcementElement.style.left) - 5 + 'px';
      }
  }

  // Start scrolling
  var scrollInterval = setInterval(scrollAnnouncement, 100);  // Adjust the speed here, smaller value = faster

  // Add event listeners
  document.querySelector('#announcement-container').addEventListener('click', () => {
      modal.style.display = 'block';
      clearInterval(scrollInterval);  // Stop scrolling when the modal is opened
  });

  document.querySelector('.close').addEventListener('click', () => {
      modal.style.display = 'none';
      scrollInterval = setInterval(scrollAnnouncement, 100);  // Start scrolling again when the modal is closed
  });

  window.onclick = function(event) {
      if (event.target == modal) {
          modal.style.display = 'none';
          scrollInterval = setInterval(scrollAnnouncement, 100);  // Start scrolling again when the modal is closed
      }
  }
}
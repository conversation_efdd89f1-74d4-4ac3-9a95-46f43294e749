#! /bin/bash

function openProject() {
  # 获取脚本执行时所在的目录的绝对路径
  projectRootPath=$(cd "$(dirname "$0")/.."; pwd)
  targetDirPath="$projectRootPath"/dist/build/mp-weixin
  echo "正在编译${mode}环境"
  cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --mode=$mode
  echo "正在打开微信开发者工具"
  $WX_DEV_TOOL_CLI_PATH close --project $targetDirPath
  $WX_DEV_TOOL_CLI_PATH open --project $targetDirPath
}

# 获取.env文件中的环境变量
mode=$1
source "$(dirname "$0")"/.env
# 接收$WX_DEV_TOOL_CLI_PATH islogin 的返回值
json_data=$($WX_DEV_TOOL_CLI_PATH islogin)
# loginResult的结构 {"login":true}  {"login":false}  通过loginResult.login判断是否登录, 不使用jq是因为不想安装jq
loginResult=${json_data:10:4}
if [ $loginResult == "true" ]; then
  echo "已登录"
  openProject
else
  echo "未登录"
  # 登录
  $WX_DEV_TOOL_CLI_PATH login
  openProject
fi

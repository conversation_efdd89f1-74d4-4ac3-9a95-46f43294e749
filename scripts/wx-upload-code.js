const ci = require('miniprogram-ci')
const minimist = require('minimist')
const dotenv = require('dotenv')
const path = require('path');

function initParams() {
  const argsObj = minimist(process.argv.slice(2))
  let mode = argsObj.mode || 'development';
  if (mode === 'prod') {
    mode = 'production'
  }
  const version = argsObj.version || '1.0.0';
  const desc = argsObj.desc || 'hello';
  const projectEnvPath = path.resolve(__dirname, `../.env.${mode}`);
  dotenv.config({ path: projectEnvPath });
  const appid = process.env.VUE_APP_WX_APP_ID;
  const projectPath = path.resolve(__dirname, '../dist/build/mp-weixin');
  return {
    appid,
    projectPath,
    version,
    desc
  }
}

function initProject({ appid, projectPath}) {
  const project = new ci.Project({
    appid,
    type: 'miniProgram',
    projectPath,
    privateKeyPath: path.resolve(__dirname, `./private.${appid}.key`),
    ignores: ['node_modules/**/*'],
  })
  return project;
}

async function uploadCode() {
  const { appid, projectPath, version, desc } = initParams();
  const project = initProject({ appid, projectPath });
  return await ci.upload({
    project,
    version,
    desc,
    setting: {
      es6: true,
      minifyJS: true,
      minifyWXML: true,
      minifyWXSS: true,
      minify: true,
    },
    onProgressUpdate: (status) => {
      console.log(status._msg, ' ', status._status);
    },
  })
}

function exec() {
  uploadCode().then(() => {
    console.log('上传成功');
  }).catch((err) => {
    console.log('上传失败', err);
  })
}

exec();
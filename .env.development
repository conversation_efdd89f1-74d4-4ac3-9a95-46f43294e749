VUE_APP_FLAG=dev
# api接口地址 
VUE_APP_API_URL=https://h5.online.shop.dev.wenet.group/api
# VUE_APP_API_URL=http://192.168.48.55:8112
# VUE_APP_API_URL=http://192.168.30.62:8086
# wenet h5地址，用户校验WENET账号
VUE_APP_WENET_URL=https://h5.online.shop.dev.wenet.group/bas
VUE_APP_WENET_API_URL=https://h5.online.shop.dev.wenet.group/wenet-api

# 代理到bas自理的地址，用于获取其内部的静态 json 资源
VUE_APP_BAS_WECHAT_SELF_STATIC_URL=https://h5.online.shop.dev.wenet.group/bas-wechat-self-static

# h5环境下的api接口地址
VUE_APP_H5_API_URL=/api
# VUE_APP_H5_API_URL=https://m.wenet.com.cn/api
# VUE_APP_H5_API_URL=http://192.168.30.62:8086
# VUE_APP_H5_API_URL=http://192.168.48.55:8086

# h5环境下的wenet h5地址，用户校验WENET账号
VUE_APP_H5_WENET_URL=/bas

# h5 环境下代理到bas自理的地址，用于获取其内部的静态 json 资源
VUE_APP_H5_BAS_WECHAT_SELF_STATIC_URL=/bas-wechat-self-static

# 客服
VUE_APP_IM_DOMAIN=https://im.wenet.com.cn
VUE_APP_IM_WS_DOMAIN=wss://im.wenet.com.cn

# h5域名
VUE_APP_H5_URL=https://h5.online.shop.dev.wenet.group

VUE_APP_IMAGE_URL=https://h5.online.shop.dev.wenet.group/shop-image/
# VUE_APP_IMAGE_URL=https://m.wenet.com.cn/resources/shop/
VUE_APP_ICON_URL=https://m.wenet.com.cn/resources/shop-static/

VUE_APP_WX_APP_ID=wx310a4a81d534256a

VUE_APP_WX_MP_NAME=WenetW

VUE_APP_WX_OFFICAL_ACCOUNT_APP_ID=wx6ddfaa11862cf9d0

#埋点统计
VUE_APP_TALKINGDATA_APP_ID=85F397CC13A04F7CA6B184E8A52C79C4

# 友盟统计
VUE_APP_UMENG_APPKEY=6617418e940d5a4c493cfcf1

VUE_APP_WECHAT_SELF_URL=https://crm-test.wenet.com.cn

# 济南大学会员中心微页面地址 /pages/vip/vip 
VUE_APP_UJN_VIP_RENOVATIONID=117
VUE_APP_UJN_VIP_SHOPID=0

#ics智能客服
VUE_APP_ICS_URL=https://jafer.top/chat

# 绑定运营商账号需要使用密码的学校
VUE_APP_OU_THAT_BIND_ISP_WITH_PASSWORD=13770,14621,12677,11600,14222,14239,10080

VUE_APP_FLAG=prod
# api接口地址 
VUE_APP_API_URL=https://m.wenet.com.cn/api
# wenet h5地址，用户校验WENET账号
VUE_APP_WENET_URL=https://m.wenet.com.cn/bas
VUE_APP_WENET_API_URL=https://m.wenet.com.cn/wenet-api
# 代理到bas自理的地址，用于获取其内部的静态 json 资源
VUE_APP_BAS_WECHAT_SELF_STATIC_URL=https://m.wenet.com.cn/bas-wechat-self-static

# h5环境下的api接口地址
VUE_APP_H5_API_URL=/api

# h5环境下的wenet h5地址，用户校验WENET账号
VUE_APP_H5_WENET_URL=/bas

# h5 环境下代理到bas自理的地址，用于获取其内部的静态 json 资源
VUE_APP_H5_BAS_WECHAT_SELF_STATIC_URL=/bas-wechat-self-static

# 客服
VUE_APP_IM_DOMAIN=https://im.wenet.com.cn
VUE_APP_IM_WS_DOMAIN=wss://im.wenet.com.cn

# h5域名
VUE_APP_H5_URL=https://m.wenet.com.cn



VUE_APP_IMAGE_URL=https://m.wenet.com.cn/resources/shop/
VUE_APP_ICON_URL=https://m.wenet.com.cn/resources/shop-static/

VUE_APP_WX_APP_ID=wx0446f031d02edb39

VUE_APP_WX_MP_NAME=校园Wenet

VUE_APP_WX_OFFICAL_ACCOUNT_APP_ID=wxe9cf559d22c52326

VUE_APP_TALKINGDATA_APP_ID=D1B1F49BAB0E47409AA4DA4A383D6AB1

# 友盟统计
VUE_APP_UMENG_APPKEY=662b50bd940d5a4c4948fdd1

VUE_APP_WECHAT_SELF_URL=https://mp.wenet.com.cn

# 济南大学会员中心微页面地址 /pages/vip/vip 
VUE_APP_UJN_VIP_RENOVATIONID=1241
VUE_APP_UJN_VIP_SHOPID=1
#ics智能客服
VUE_APP_ICS_URL=https://ics.wenet.com.cn/chat

# 绑定运营商账号需要使用密码的学校
VUE_APP_OU_THAT_BIND_ISP_WITH_PASSWORD=13770,14621,12677,11600,14222,14239,10080
